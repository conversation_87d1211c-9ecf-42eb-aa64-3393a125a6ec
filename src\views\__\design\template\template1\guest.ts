import type { IDesignTemplateSetup } from '../../types'
import { envUtils } from '~/src/utils/env'
import { layerUuid } from '../../utils'

export function setup(app: IDesignTemplateSetup) {
  if (!envUtils.isDev) return
  app.registry({
    label: '嘉宾',
    thumbnail: new URL(`./template1.png`, import.meta.url).href,
    defaultData() {
      return {
        type: 'group',
        uuid: layerUuid(),
        templateId: `template-ayvzyqa7fv`,
        name: '嘉宾',
        layers: [
          {
            uuid: layerUuid(),
            name: '头像',
            data: '//res.dev.hixianchang.com/qn/material/0/3be93ae3440452465e20cf997e7cd5c9.png',
            type: 'image',
            style: { width: '164px', height: '164px', left: 0, top: 0 },
          },
          {
            uuid: layerUuid(),
            name: '说明',
            type: 'text-rich',
            data: '<p><span style="font-size: 17px; color: black;">​姓名：张三</span></p><p><span style="font-size: 17px;">职位： CEO</span></p>',
            style: {
              'display': 'flex',
              'alignItems': 'start',
              'width': '165px',
              'height': '83px',
              'left': 0,
              'top': '164px',
              'padding': '10px 5px',
              'line-height': 1.3,
            },
          },
        ],
        style: {
          left: '19px',
          top: '122px',
          width: '165px',
          height: '247px',
          borderRadius: '6px',
          background: 'rgba(255, 255, 255, 0.58)',
        },
      }
    },
  })
}
