import type { IDesignLayer, IDesignSetup } from '../../types'
import { BisTypes, useDesignData } from '../..'
import { InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './lottery-ing2-setting.vue'
import Comp from './lottery-ing2.vue'
// 类型
export const type = 'lottery-ing2'
export const defaultWallHeadSize = 170
export const defaultrotateSpeed = 5
export const defaultPlaceHolderHeadImg = new URL('./assets/placehoder.png', import.meta.url).href
export const defaultShowCube = true
// 数据类型约束
export interface IDesignLotteryIng2 extends IDesignLayer {
  type: typeof type
  wallHeadSize?: number
  rotateSpeed?: number
  placeHolderHeadImg?: string
  showCube?: boolean
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.sportsIng,
    showType: ['pcwall'],
    status: ['ing', 'ready'],
    showInteractive: [InteractiveEnum.lotteryv3, InteractiveEnum.piclotteryv3],
    type,
    name: '抽奖3d效果',
    thumbnail: new URL('./lottery-ing2.png', import.meta.url).href,
    Comp,
    CompSetting,
    defaultData() {
      const designData = useDesignData()
      const [width, height] = designData.option.drafts
      return {
        type,
        uuid: layerUuid(),
        name: '抽奖3d效果',
        style: {
          width: `${width}px`,
          height: `${height}px`,
          top: 0,
          left: 0,
        },
      }
    },
  })
}
