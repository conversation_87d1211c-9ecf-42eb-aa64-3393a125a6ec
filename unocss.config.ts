import presetRemToPx from '@unocss/preset-rem-to-px'
import presetUno from '@unocss/preset-uno'
import { defineConfig } from 'unocss'

export default defineConfig({
  presets: [
    presetRemToPx({ baseFontSize: 4 }),
    presetUno({
      preflight: false,
    }),
  ],
  transformers: [],
  shortcuts: {
    'display-box': '', // 禁用该类名
    'wh-full': 'w-full h-full',

    // flex
    'flex-i': 'inline-flex',
    'flex-cc': 'justify-center items-center',
    'flex-a-c': 'items-center',
    'flex-a-fs': 'items-start',
    'flex-a-fe': 'items-end',
    'flex-a-s': 'items-stretch',
    'flex-j-c': 'justify-center',
    'flex-j-sa': 'justify-around',
    'flex-j-sb': 'justify-between',
    'flex-j-se': 'justify-evenly',
    'flex-j-fe': 'justify-end',
    'flex-d-c': 'flex-col',
    'flex-w': 'flex-wrap',

    // position
    'absolute-tl': 'absolute top-0 left-0',
    'absolute-tr': 'absolute top-0 right-0',
    'absolute-bl': 'absolute bottom-0 left-0',
    'absolute-br': 'absolute bottom-0 right-0',

    'fixed-tl': 'fixed top-0 left-0',
    'fixed-tr': 'fixed top-0 right-0',
    'fixed-bl': 'fixed bottom-0 left-0',
    'fixed-br': 'fixed bottom-0 right-0',

    // 文字
    'text-ellipsis': 'overflow-hidden text-ellipsis whitespace-nowrap',
    'text-ellipsis-2': 'overflow-hidden line-clamp-2',
    'text-ellipsis-3': 'overflow-hidden line-clamp-3',
    'text-ellipsis-4': 'overflow-hidden line-clamp-4',
    'text-ellipsis-5': 'overflow-hidden line-clamp-5',
    'text-ellipsis-6': 'overflow-hidden line-clamp-6',
    'text-ellipsis-7': 'overflow-hidden line-clamp-7',
    'text-ellipsis-8': 'overflow-hidden line-clamp-8',

    // 图片
    'img-cover': 'object-cover',
    'img-contain': 'object-contain',
    'img-fill': 'object-fill',
  },
})
