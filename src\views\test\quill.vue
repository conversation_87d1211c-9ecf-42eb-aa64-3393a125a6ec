<script setup lang="ts">
import type { Range } from 'quill'
import Quill from 'quill'
import 'quill/dist/quill.snow.css'

const FontAttributor = Quill.import('attributors/style/size') as any
FontAttributor.whitelist = null
Quill.register(FontAttributor, true)

const editorRef = ref<HTMLElement>()

let quill: Quill | null
const range = ref<Range>()
const lastRange = ref<Range>()

watch(
  () => editorRef.value,
  (v) => {
    if (v) {
      quill = new Quill(v, {
        theme: 'snow',
        modules: {
          toolbar: '.toolbar',
        },
      })
      quill.root.innerHTML = '哈哈哈水电费萨法维方ask立法违法'
      quill.on('selection-change', (r) => {
        console.log(r)
        range.value = r
        if (r) {
          lastRange.value = r
        } else {
          quill?.enable(false)
          if (editorRef.value) {
            editorRef.value.style.cssText = ''
          }
        }
      })
      quill.enable(false)
    } else {
      quill = null
    }
  },
)

function testFn() {
  if (quill) {
    quill.format('bold', true)
  }
}
function changeFn(v: number) {
  if (quill) {
    if (lastRange.value) {
      quill.formatText(lastRange.value.index, lastRange.value.length, 'size', `${v}px`)
    } else {
      quill.formatText(0, quill.getLength(), 'size', `${v}px`)
    }
  }
}
function dblclickFn() {
  if (!quill) return
  console.log('dblclick')
  quill.enable(true)

  editorRef.value?.style.setProperty('pointer-events', 'auto')

  quill.setSelection(0, quill.getLength())
}
</script>

<template>
  <div class="quill-box" @dblclick="dblclickFn">
    <div class="toolbar">
      <div @click="testFn">哈哈</div>
      <el-select placeholder="字号" style="width: 80px" @change="changeFn">
        <el-option v-for="item in 10" :key="item" :label="item" :value="item + 12" />
      </el-select>
    </div>
    <div ref="editorRef" class="editor"></div>
    <div>撒手动阀违法我发我</div>
  </div>
</template>

<style scoped lang="scss">
.editor {
  width: 450px;
  height: 180px;
  pointer-events: none;
}
.toolbar {
  > * {
    cursor: pointer;
  }
}
</style>
