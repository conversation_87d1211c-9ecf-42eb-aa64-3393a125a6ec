<script setup lang="ts">
import { ref } from 'vue'
import { useDataAttr } from '../__/design'

/* ------------------------------------------------------------- *
 * 场景 #1：基础尺寸（数字 + 单位 px）
 * ------------------------------------------------------------- */
const boxStyle = ref({ width: '300px', height: '300px' })
const width = useDataAttr(boxStyle, 'width', 300, 'px')
const height = useDataAttr(boxStyle, 'height', 300, 'px')

/* ------------------------------------------------------------- *
 * 场景 #2：动态单位切换（deg ↔ rad）
 * ------------------------------------------------------------- */
const rotateStyle = ref({ rotate: '20deg' })
const rotateUnit = ref<'deg' | 'rad'>('deg')
const rotate = useDataAttr(rotateStyle, 'rotate', 20, rotateUnit)

function toggleUnit() {
  if (rotateUnit.value === 'deg') {
    rotate.value = +(rotate.value * Math.PI / 180).toFixed(4)
    rotateUnit.value = 'rad'
  } else {
    rotate.value = +(rotate.value * 180 / Math.PI).toFixed(1)
    rotateUnit.value = 'deg'
  }
}

/* ------------------------------------------------------------- *
 * 场景 #3：数组深相等 & 删除逻辑
 * ------------------------------------------------------------- */
interface Chart { points: number[] }
const chart = ref<Chart>({ points: [10, 20, 30] })
const points = useDataAttr(chart, 'points', [0, 0, 0])
function resetPoints() {
  points.value = [0, 0, 0] // 与默认值深相等 → 属性应被删除
}

/* ------------------------------------------------------------- *
 * 场景 #4：深层对象（position.x / position.y）
 * ------------------------------------------------------------- */
const widget = ref({ position: { x: 0, y: 0 } })
const position = useDataAttr(widget, 'position', { x: 0, y: 0 })

/* 通过 computed 拆箱再整体回写 */
const posX = computed({
  get: () => position.value.x,
  set: (v: number) => {
    position.value = { ...position.value, x: v } // 关键：重建对象
  },
})
const posY = computed({
  get: () => position.value.y,
  set: (v: number) => {
    position.value = { ...position.value, y: v }
  },
})
</script>

<template>
  <div class="h-full overflow-scroll">
    <!-- 场景 #1 ------------------------------------------------- -->
    <section class="demo">
      <h2>#1 Basic (px)</h2>
      <div class="ctrl">
        <label>Width
          <input v-model="width" type="range" min="50" max="300" /> {{ width }}px
        </label>
        <label>Height
          <input v-model="height" type="range" min="50" max="300" /> {{ height }}px
        </label>
      </div>
      <div
        class="box"
        :style="{
          width: `${width}px`,
          height: `${height}px`,
        }"
      >
        {{ boxStyle }}
      </div>
    </section>

    <hr />

    <!-- 场景 #2 ------------------------------------------------- -->
    <section class="demo">
      <h2>#2 切换单位 ({{ rotateUnit }})</h2>
      <button @click="toggleUnit">
        切换到 {{ rotateUnit === 'deg' ? 'rad' : 'deg' }}
      </button>
      <label style="display:block;margin:1rem 0">
        旋转 ({{ rotateUnit }})
        <input v-model.number="rotate" type="number" />
      </label>
      <div
        class="rotate-box"
        :style="{ transform: `rotate(${rotateStyle.rotate || (rotate + rotateUnit)})` }"
      >
        {{ rotateStyle.rotate }}
        ------
        {{ rotate }}
      </div>
    </section>

    <hr />

    <!-- 场景 #3 ------------------------------------------------- -->
    <section class="demo">
      <h2>#3 数组双向绑定</h2>
      <template v-for="(_, i) in points" :key="i">
        <textarea v-model="points[i]" rows="2" style="width:260px" @blur.enter="points.push(11)" />
      </template>
      <button @click="resetPoints">设为默认 ([0,0,0])</button>
      <pre>chart = {{ JSON.stringify(chart, null, 2) }}</pre>
    </section>

    <hr />

    <!-- 场景 #4 ------------------------------------------------- -->
    <section class="demo">
      <h2>#4 深层对象双向绑定</h2>

      <label>
        X:
        <input v-model.number="posX" type="number" style="width:80px" />
      </label>

      <label style="margin-left:1rem">
        Y:
        <input v-model.number="posY" type="number" style="width:80px" />
      </label>

      <pre style="margin-top:1rem">
widget = {{ JSON.stringify(widget, null, 2) }}
    </pre>
    </section>
  </div>
</template>

<style scoped>
.demo       { margin-bottom: 2rem; }
.ctrl       { display: flex; gap: 1.5rem; margin-bottom: 1rem; }
.box        { background:#a6c8ff;border:1px solid #409eff;
              display:flex;align-items:center;justify-content:center; }
.rotate-box { width:120px;height:120px;border:2px dashed #f56c6c;
              display:flex;align-items:center;justify-content:center; }
textarea    { font-family: monospace; }
hr          { margin: 2.5rem 0; border: none; border-top: 1px dashed #ccc; }
</style>
