<script setup lang="ts">
import type { IDesignLayerEventBusiness } from '../../../types'
import { useDesignState, useDesignTemp } from '../../..'

defineOptions({ label: '业务事件' })

const event = defineModel<IDesignLayerEventBusiness>('event', { required: true })

const designState = useDesignState()
const deisgnTemp = useDesignTemp()

const nowStatus = computed(() => {
  return designState.statusList.find(item => item.value === designState.status)
})

const showEvents = computed(() => {
  const eventList = designState.layerEventList
  if (!eventList) return []
  return eventList.filter((i) => {
    if (!i.name) return false
    if (unref(i.status)) {
      return unref(i.status)?.includes(designState.status)
    }
    return true
  })
})

let ctrlKey = false
let shiftKey = false
function keyEvent(e: KeyboardEvent) {
  e.stopPropagation()
  const { type, key, repeat } = e
  if (repeat) return
  if (type === 'keydown') {
    if (key === 'Control') {
      ctrlKey = true
    } else if (key === 'Shift') {
      shiftKey = true
    }
  } else {
    if (key === 'Backspace') {
      event.value.keyboard = ''
    } else if (key === 'Control') {
      ctrlKey = false
    } else if (key === 'Shift') {
      shiftKey = false
    } else {
      // 其他按键
      const resultKey = []
      if (ctrlKey) {
        resultKey.push('ctrl')
      }
      if (shiftKey) {
        resultKey.push('shift')
      }
      resultKey.push(key)
      const result = resultKey.join('+')
      event.value.keyboard = result
    }
  }
}
function shortcutFocus() {
  // 监听键盘事件
  document.addEventListener('keydown', keyEvent)
  document.addEventListener('keyup', keyEvent)
}
function shortcutBlur() {
  // 移除键盘事件
  document.removeEventListener('keydown', keyEvent)
  document.removeEventListener('keyup', keyEvent)
}
const showKeyboard = computed(() => {
  const keyboard = event.value.keyboard
  if (keyboard === ' ') {
    return '空格'
  }
  return keyboard
})

onUnmounted(() => {
  document.removeEventListener('keydown', keyEvent)
  document.removeEventListener('keyup', keyEvent)
})
</script>

<template>
  <div class="flex gap-10">
    <el-select v-if="nowStatus" v-model="event.value!" placeholder="请选择事件">
      <el-option
        v-for="item in showEvents"
        :key="item.eventId"
        :label="item.name"
        :value="item.eventId"
      />
    </el-select>
    <!-- 快捷键 -->
    <el-input v-if="deisgnTemp.showType === 'pcwall'" placeholder="请输入快捷键" :model-value="showKeyboard" @focus="shortcutFocus" @blur="shortcutBlur"></el-input>
  </div>
</template>

<style scoped lang="scss">

</style>
