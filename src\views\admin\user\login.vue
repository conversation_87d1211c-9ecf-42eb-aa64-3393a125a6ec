<script setup lang="ts">
import api from '@/api'
import { envUtils } from '~/src/utils/env'

definePage({ meta: { label: '主办方登录' } })

const userInfo = useStorage('dev:admin:user:login', {
  agentId: '',
  phone: '',
  passwd: '',
})

const route = useRoute()
const router = useRouter()

async function loginFn() {
  if (!userInfo.value.phone || !userInfo.value.passwd) {
    return ElMessage.error('请输入用户名和密码')
  }

  if (envUtils.isOem && !userInfo.value.agentId) {
    return ElMessage.error('请输入agentId')
  }

  const params: Record<string, any> = { ...userInfo.value }
  if (params.agentId) {
    params.userName = params.phone
    delete params.phone
  } else {
    delete params.agentId
  }

  await api.admin.user.devLogin(params)

  ElMessage.success('登录成功')

  const redirect = route.query.redirect
  setTimeout(() => {
    if (redirect) {
      let url = decodeURIComponent(redirect as string)
      if (userInfo.value.agentId) {
        url = `${url}${url.includes('?') ? '&' : '?'}agentId=${userInfo.value.agentId}`
      }
      router.push(url)
    } else {
      let url = `/manage/wall/shakev3`
      if (userInfo.value.agentId) {
        url = `/manage/wall/shakev3?agentId=${userInfo.value.agentId}`
      }
      router.push(url)
    }
  }, 1000)
}
</script>

<template>
  <div class="content-box">
    <div class="w-400">
      <h1 class="text-center">主办方</h1>
      <el-form :label-width="100">
        <el-form-item v-if="envUtils.isOem" label="agentId">
          <el-input v-model="userInfo.agentId" />
        </el-form-item>
        <el-form-item label="用户名">
          <el-input v-model="userInfo.phone" />
        </el-form-item>
        <el-form-item label="密码">
          <el-input v-model="userInfo.passwd" type="password" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loginFn">登录</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<style scoped lang="scss">
.content-box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
