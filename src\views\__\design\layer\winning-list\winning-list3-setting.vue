<script setup lang="ts">
import { useDataAttr } from '../..'
import { DEFAULT_DATA, type IDesignWinningList3 } from './winning-list3'

const layer = defineModel<IDesignWinningList3>('layer', { required: true })

const bgColorBind = useDataAttr(layer.value.data, 'bgColor', DEFAULT_DATA.bgColor)
const itemsPerRowBind = useDataAttr(layer.value.data, 'itemsPerRow', DEFAULT_DATA.itemsPerRow)
const itemGapBind = useDataAttr(layer.value.data, 'itemGap', DEFAULT_DATA.itemGap)
const innerGapGapBind = useDataAttr(layer.value.data, 'innerGap', DEFAULT_DATA.innerGap)
const itemRadiusBind = useDataAttr(layer.value.data, 'itemRadius', DEFAULT_DATA.itemRadius)
const flexContentStyleBind = useDataAttr(layer.value.data, 'flexContentStyle', DEFAULT_DATA.flexContentStyle)
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>每行数量</h3>
        <el-input-number
          v-model="itemsPerRowBind"
          v-input-number
          :min="1"
          :max="10"
          controls-position="right"
        />
      </div>
      <div class="setting-item">
        <h3>内容间距</h3>
        <el-input-number
          v-model="itemGapBind"
          v-input-number
          :min="0"
          :max="100"
          controls-position="right"
        />
      </div>
      <div class="setting-item">
        <h3>文字间距</h3>
        <el-input-number
          v-model="innerGapGapBind"
          v-input-number
          :min="0"
          :max="100"
          controls-position="right"
        />
      </div>
      <div class="setting-item">
        <h3>内容背景</h3>
        <hi-color v-model="bgColorBind" />
      </div>
      <div class="setting-item">
        <h3>内容圆角</h3>
        <el-input-number
          v-model="itemRadiusBind"
          v-input-number
          :min="0"
          :max="100"
          controls-position="right"
        />
      </div>
      <div class="setting-item">
        <h3>竖向排列方式</h3>
        <el-select v-model="flexContentStyleBind" placeholder="请选择" style="width: 80px">
          <el-option label="上到下" value="start" />
          <el-option label="居中" value="center" />
        </el-select>
      </div>
      <div v-for="(item, index) in layer.data.contentStyle || []" :key="index" class="setting-item items-start!">
        <h3>文字{{ index + 1 }}</h3>
        <div>
          <div class="text-item">
            <h3>大小</h3>
            <el-input-number v-model="item.fontSize" v-input-number :max="100" :min="12" controls-position="right" />
          </div>
          <div class="text-item">
            <h3>颜色</h3>
            <hi-color v-model="item.fontColor" />
          </div>
          <div class="text-item">
            <h3>加粗</h3>
            <el-switch v-model="item.fonBold" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.text-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
</style>
