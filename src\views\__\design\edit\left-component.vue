<script setup lang="ts">
import { useInstanceRouter } from '~/src/hooks/useInstanceRouter'
import { BisTypes, useDesignSetup, useDesignState, useDesignTemp } from '..'
import { getInteractive } from '../utils'
import { createComponent, createTemplate } from './createLayer'

const designSetup = useDesignSetup()
const designState = useDesignState()
const designTemp = useDesignTemp()

const layerTypeRadio = ref('component')
const designComponentOptions = designSetup.getDesignComponentSetupOptions()
const designComponentOptionsArray = Object.values(designComponentOptions)

const designTemplate = designSetup.getDesignTemplateSetupOptions()

const router = useInstanceRouter()
const route = router.currentRoute.value
// const isManage = route.path.startsWith('/manage/')
const isTest = route.path.startsWith('/test/')

const interactive = getInteractive()
const designComponent = computed(() => {
  const basicTypes = new Set(['image', 'shape', 'text', 'text-rich', 'rotate'])
  return designComponentOptionsArray
    .filter(option => !basicTypes.has(option.type)) // 过滤掉基础组件
    .filter(option => !option.status || option.status.includes(designState.status)) // 过滤掉不支持当前状态的组件
    .filter(option => !option.showType || option.showType.includes(designTemp.showType!)) // 过滤掉不支持当前显示类型的组件
    .filter(option => isTest || !option.showInteractive || option.showInteractive.includes(interactive)) // 过滤掉不支持当前互动的组件
})

const componentGroup = computed(() => {
  return Object.values(BisTypes).map((item) => {
    return {
      name: item,
      children: designComponent.value.filter(i => i.bisType === item && i.dev !== true),
    }
  }).filter(item => item.children.length > 0)
})

const openGroupName = ref<Record<string, boolean>>({})

watch(componentGroup, () => {
  componentGroup.value.forEach((item) => {
    if (openGroupName.value[item.name] === null) {
      openGroupName.value[item.name] = true
    }
  })
}, { immediate: true, deep: true })

const isSidebarVisible = ref(true)

const leftComponentStyle = computed(() => {
  return {
    '--left-component-width': isSidebarVisible.value ? '250px' : '0px',
  }
})
</script>

<template>
  <div class="left-box">
    <div class="left-component" :style="leftComponentStyle">
      <div class="component-type flex justify-center py-14px">
        <el-radio-group v-model="layerTypeRadio">
          <el-radio-button value="component">
            <div class="w-82px">组件</div>
          </el-radio-button>
          <el-radio-button value="template">
            <div class="w-82px">模板</div>
          </el-radio-button>
        </el-radio-group>
      </div>
      <div class="content-box moveable-ignore">
        <el-scrollbar>
          <template v-if="layerTypeRadio === 'component'">
            <div v-for="(item) in componentGroup" :key="item.name">
              <header class="mx-6% mt-10px flex items-center justify-between">
                <p class="font-bold">{{ item.name }}</p>
              </header>
              <div class="component-list">
                <div
                  v-for="(value, key) in item.children"
                  :key="key"
                  :title="value.name"
                  :data-type="value.type"
                  class="bgblank thumbnail-box component-items"
                  @click="createComponent(value.type)"
                >
                  <img v-if="value.thumbnail" :src="value.thumbnail" alt="组件icon">
                  <span v-else>{{ value.name }}</span>
                </div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="template-box">
              <div
                v-for="(value, key) in designTemplate"
                :key="key"
                :title="value?.label"
                class="template-item"
                @click="createTemplate(value)"
              >
                <img v-if="value.thumbnail" :src="value.thumbnail" alt="模板icon">
              </div>
            </div>
          </template>
        </el-scrollbar>
      </div>
    </div>
    <button class="show-btn" @click.stop="isSidebarVisible = !isSidebarVisible">
      <icon-ph-caret-left-bold v-if="isSidebarVisible" />
      <icon-ph-caret-right-bold v-else />
    </button>
  </div>
</template>

<style scoped lang="scss">
.left-box {
  height: 100%;
  z-index: 9;
  position: relative;
  display: flex;
  align-items: center;

  .left-component {
    width: var(--left-component-width);
    height: 100%;
    position: relative;
    transition: all 0.3s ease;
    box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.08);
    background-color: #fff;
    overflow: hidden;
    --content-width: 250px;
    z-index: 2;
    .content-box {
      height: calc(100% - 60px);
      overflow: hidden;
    }
  }
  .component-type {
    // box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.3);
    border-bottom: 2px solid #ebeef5;
  }

  .component-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    height: 100%;
    overflow: hidden;
    padding: 10px;
    .component-items {
      width: calc(50% - 5px);
      cursor: pointer;
      height: 80px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 5px;
      color: #333;
      overflow: hidden;
      &:hover {
        box-shadow: 0px 2px 8px 0px rgba(18, 97, 255, 0.2);
        border-radius: 3px;
        color: #1261ff;
        font-weight: 600;
      }
    }
  }

  .template-box {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 10px;
    .template-item {
      width: calc(50% - 5px);
      aspect-ratio: 1;
      padding: 10px;
      background-color: #f1f5f9;
      border-radius: 3px;
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
  }
}

.show-btn {
  position: absolute;
  top: 50%;
  right: -20px;
  transform: translateY(-50%);
  width: 20px;
  height: 70px;
  z-index: 9999;
  background: url('@/assets/design/layout/slide-button.png') no-repeat center center / 100% 100%;
  filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.1));
  z-index: 1;
}

.thumbnail-box {
  outline: 1px solid #e6ebed;
  border-radius: 4px;
  position: relative;

  img,
  span {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  > img {
    object-fit: contain;
  }
}

.rotatez-90 {
  transform: rotateZ(90deg);
}
.-rotatez-90 {
  transform: rotateZ(-90deg);
}
</style>
