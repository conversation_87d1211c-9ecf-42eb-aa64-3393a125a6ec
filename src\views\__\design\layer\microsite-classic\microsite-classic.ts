import { BisTypes, useDesignData } from '../../'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './microsite-classic-setting.vue'
import Comp from './microsite-classic.vue'

// 类型
export const type = 'microsite-classic'

// 数据类型约束
export interface IDesignMicrositeClassic extends IDesignLayer {
  type: typeof type
  data: any
  // bgImg?: string
}

// 注册组件
export function setup(app: IDesignSetup) {
  const designData = useDesignData()
  app.registry({
    bisType: BisTypes.ready,
    type,
    showType: ['mobile'],
    showInteractive: [InteractiveEnum.microsite],
    name: '微站-经典版',
    thumbnail: new URL('./microsite-classic.png', import.meta.url).href,
    Comp,
    CompSetting,
    defaultData(): IDesignMicrositeClassic {
      return {
        uuid: layerUuid(),
        name: '微站-经典版',
        type,
        data: {},
        style: {
          width: `${designData.option.drafts[0] || 375}px`,
          height: `${(designData.option.drafts[1] || 630)}px`,
          top: '0',
        },
      }
    },
  })
}
