import type { IDesignLayer, IDesignSetup } from '../../types'
import { merge } from 'lodash-es'
import { layerUuid } from '../../utils'
import { BisTypes } from '../../'
import Comp from './{{fileNameKebabCase}}.vue'
import CompSetting from './{{fileNameKebabCase}}-setting.vue'
{{#if isNeedAttachment}}
import CompAttachment from './{{fileNameKebabCase}}-attachment.vue'
{{/if}}

// 类型
export const type = '{{fileNameKebabCase}}'

// 数据类型约束
export interface IDesign{{fileNamePascal}} extends IDesignLayer {
  type: typeof type
  data: any
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.common,
    type,
    name: '{{displayName}}',
    Comp,
    CompSetting,
    {{#if isNeedAttachment}}
    CompAttachment,
    {{/if}}
    // thumbnail: new URL('./{{fileNameKebabCase}}.png', import.meta.url).href,
    defaultData(options): IDesign{{fileNamePascal}} {
      return merge({
        uuid: layerUuid(),
        name: '{{displayName}}',
        type,
        style: {},
        data: {},
      }, options as IDesign{{fileNamePascal}})
    },
  })
}
