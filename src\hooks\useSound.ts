// @vueuse/sound
import type { Howl, HowlOptions } from 'howler'
import type { ComputedRef, Ref } from 'vue'

type MaybeRef<T> = T | Ref<T> | ComputedRef<T>
interface SpriteMap {
  [key: string]: [number, number]
}
type ComposableOptions = {
  volume?: MaybeRef<number>
  playbackRate?: MaybeRef<number>
  interrupt?: boolean
  soundEnabled?: boolean
  autoplay?: boolean
  sprite?: SpriteMap
  onload?: () => void
} & Omit<HowlOptions, 'src'>
interface PlayOptions {
  id?: number
  forceSoundEnabled?: boolean
  playbackRate?: number
}
type PlayFunction = (options?: PlayOptions) => void
interface ReturnedValue {
  play: PlayFunction
  sound: Ref<Howl | null>
  stop: (id?: number) => void
  pause: (id?: number) => void
  isPlaying: Ref<boolean>
  duration: Ref<number | null>
}

function useSound(url: MaybeRef<string>, { volume = 1, playbackRate = 1, soundEnabled = true, interrupt = true, autoplay = false, onload, ...delegated }: ComposableOptions = {}): ReturnedValue {
  const HowlConstructor: Ref<Howl | any> = ref(null)
  const isPlaying: Ref<boolean> = ref(false)
  const duration: Ref<number | null> = ref(null)
  const sound: Ref<Howl | null> = ref(null)
  function handleLoad() {
    if (typeof onload === 'function')
      // @ts-ignore
      onload.call(this)
    duration.value = (duration.value || sound.value?.duration() || 0) * 1e3
    if (autoplay === true) {
      isPlaying.value = true
    }
  }
  onMounted(async () => {
    const Howler = await import('howler/dist/howler.core.min')
    HowlConstructor.value = Howler.default.Howl
    const Howl = HowlConstructor.value
    sound.value = new Howl({
      src: unref(url),
      volume: unref(volume),
      rate: unref(playbackRate),
      onload: handleLoad,
      ...delegated,
    })
  })
  watch(
    () => [url],
    () => {
      if (HowlConstructor.value && HowlConstructor.value && sound && sound.value) {
        const Howl = HowlConstructor.value
        sound.value = new Howl({
          src: unref(url),
          volume: unref(volume),
          rate: unref(playbackRate),
          onload: handleLoad,
          ...delegated,
        })
      }
    },
  )
  watch(
    () => [unref(volume), unref(playbackRate)],
    () => {
      if (sound.value) {
        sound.value.volume(unref(volume))
        sound.value.rate(unref(playbackRate))
      }
    },
  )
  const play = (options: PlayOptions) => {
    if (typeof options === 'undefined') {
      options = {}
    }
    if (!sound.value || (!soundEnabled && !options.forceSoundEnabled)) {
      return
    }
    if (interrupt) {
      sound.value.stop()
    }
    if (options.playbackRate) {
      sound.value.rate(options.playbackRate)
    }
    sound.value.play(options.id)
    sound.value.once('end', () => {
      if (sound.value && sound.value && !sound.value.playing()) {
        isPlaying.value = false
      }
    })
    isPlaying.value = true
  }
  const stop = (id?: number) => {
    if (!sound.value) {
      return
    }
    sound.value.stop(typeof id === 'number' ? id : void 0)
    isPlaying.value = false
  }
  const pause = (id?: number) => {
    if (!sound.value) {
      return
    }
    sound.value.pause(typeof id === 'number' ? id : void 0)
    isPlaying.value = false
  }
  const returnedValue = {
    play,
    sound,
    isPlaying,
    duration,
    pause,
    stop,
  }
  return returnedValue as ReturnedValue
}

export { useSound }
