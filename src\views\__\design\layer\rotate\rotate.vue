<script setup lang="ts">
import type { IDesignRotate } from './rotate'

const layer = defineModel<IDesignRotate>('layer', { required: true })

const duration = computed(() => layer.value.data.duration)
const src = computed(() => layer.value.data.src)
</script>

<template>
  <div
    class="rotate-box"
    :style="{
      '--duration': `${duration}s`,
    }"
  >
    <img class="img ani-rotate" :src="src">
  </div>
</template>

<style scoped lang="scss">
.rotate-box {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  overflow: hidden;
}
.img {
  width: 100%;
  max-height: 100%;
  object-fit: contain;

  &.ani-rotate {
    animation: rotate var(--duration, 5s) linear infinite;
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
