import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

// 日期时间格式化
export function dateNormalize(val: number | string | Date, format = 'YYYY-MM-DD HH:mm:ss') {
  return dayjs(val).format(format)
}

/**
 * 时间校准只对 Date.now() 方法进行校准，通过 getTime() 方法不做处理， 其他方法也不做处理
 * 1. 使用实时时间还是使用vueuse的时间获取方法 const timestamp = useTimestamp()
 * 2. 非实时时间可以直接使用 Date.now() 此方法也是校准后的时间
 * 3. 业务中尽量避免使用 new Date().getTime() 这种方式获取的时间为本地时间，不准确
 * 4. 如果有地方要可视化显示当前时间请使用 dateNormalize(new Date(Date.now())) ,这样返回的内容为服务器的时间也就是校准后的时间
 */
// 对原生Date进行改写，增加偏移量
const OriginDateNow = window.Date.now

export const offsetInfo = ref({
  startTime: 0,
  serverTime: 0,
  endTime: 0,
  offset: 0,
})

// 接口时间校准
export async function setupDate() {
  let isCalc = false
  while (!isCalc) {
    const startTime = new Date().getTime()
    const serverTime = await api.pcwall.common.getTime({})
    const endTime = new Date().getTime()

    const requestTime = endTime - startTime
    if (requestTime < 500) {
      isCalc = true
      const flag = OriginDateNow()
      // 重写Date.now()方法
      const offset = Math.floor(flag - serverTime + requestTime / 2)
      window.Date.now = () => OriginDateNow() - offset

      offsetInfo.value = { startTime, serverTime, endTime, offset }
    }
  }
}
