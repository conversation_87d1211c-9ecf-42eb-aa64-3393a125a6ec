<script setup lang="ts">
import type { IDesignPiclotteryIng2 } from './piclottery-ing2'
import gsap from 'gsap'
import { sample, throttle } from 'lodash-es'
import * as THREE from 'three'
import { CSS3DObject, CSS3DRenderer } from 'three/examples/jsm/renderers/CSS3DRenderer.js'
import { useDesignState } from '../..'
import { defaultAnimateSpeed, defaultHeadSizeH, defaultHeadSizeW, defaultImgMode, defaultItemBorderColor, defaultPlaceHolderImg, defultRowCount } from './piclottery-ing2'

const designState = useDesignState()
// 参与人奖池
const regeditList = computed(() => {
  return designState.getLayerData('regeditList') || []
})
const layer = defineModel<IDesignPiclotteryIng2>('layer', { required: true })

const threeRoot = ref<HTMLElement>()
const threeRootSize = useElementSize(threeRoot)

let requestAnimationFrameId: number | null = null

let camera: THREE.PerspectiveCamera
let scene: THREE.Scene
let renderer: CSS3DRenderer
let group: THREE.Group | null = null
const lineGroups: THREE.Group[] = [] // Renamed from lineGroup for clarity, stores each row's group
const timeline = gsap.timeline({ repeat: -1, paused: false })

const headSizeW = computed(() => layer.value.headSizeW ?? defaultHeadSizeW)
const headSizeH = computed(() => layer.value.headSizeH ?? defaultHeadSizeH)
const animiteSpeed = computed(() => layer.value.animiteSpeed ?? defaultAnimateSpeed)
const itemBorderColor = computed(() => layer.value.itemBorderColor || defaultItemBorderColor)
const itemBorderWidth = computed(() => layer.value.itemBorderWidth || 0)
const imgMode = computed(() => layer.value.imgMode || defaultImgMode)
const placeHolderImg = computed(() => {
  return layer.value.placeHolderImg ?? defaultPlaceHolderImg
})

function getItem() {
  const tem = sample(regeditList.value)
  const itemData = Object.assign({}, tem)
  return itemData
}

function createElement(itemData?: any) {
  const element = document.createElement('div')
  element.setAttribute('class', 'css3d')
  element.style = `
    width: ${headSizeW.value - Math.min(10, headSizeW.value * 0.05)}px;
    height:${headSizeH.value - Math.min(10, headSizeH.value * 0.05)}px;
    border: ${itemBorderWidth.value}px solid ${itemBorderColor.value};
  `
  const imgStyle = `
    object-fit: ${imgMode.value};
  `
  const str = `
      <img style="${imgStyle}" src="${itemData?.avatar || placeHolderImg.value}" alt="头像" />
  `
  element.innerHTML = str
  return element
}

function initShape() {
  if (!scene) {
    return
  }
  if (group) {
    lineGroups.forEach((rowGroup) => {
      while (rowGroup.children.length > 0) {
        const child = rowGroup.children[0]
        if (child instanceof CSS3DObject) {
          child.element.remove()
        }
        rowGroup.remove(child)
      }
      group?.remove(rowGroup)
    })
    lineGroups.length = 0

    if (scene.children.includes(group)) {
      scene.remove(group)
    }
    group = null
  }

  group = new THREE.Group()
  scene.add(group)

  const startAngle = 0// 起始角度
  const endAngle = Math.PI * 2 // 结束角度
  const rangeAngle = endAngle - startAngle
  const n = 1
  const viewSizeAtOrigin = getDisplayArea(0)
  const radius = viewSizeAtOrigin.width * 1.6 * n // 调整系数以控制半径

  // 使用配置的行数，如果未配置，则默认为3行
  const actualRowCount = layer.value.rowCount ?? defultRowCount

  const colCount = Math.floor(rangeAngle * radius / headSizeW.value)
  const itemR = rangeAngle / colCount

  for (let i = 0; i < actualRowCount; i++) {
    const yPosForRowGroup = (i - (actualRowCount - 1) / 2) * headSizeH.value

    const currentRowGroup = new THREE.Group()
    currentRowGroup.position.y = yPosForRowGroup
    group.add(currentRowGroup)
    lineGroups.push(currentRowGroup)

    for (let j = 0; j < colCount; j++) {
      const currentItemAngle = startAngle + (j + 0.5) * itemR
      const x = radius * Math.cos(currentItemAngle)
      const z = -radius * Math.sin(currentItemAngle)

      const itemData = getItem()
      if (!itemData) continue

      const element = createElement(itemData)
      const object = new CSS3DObject(element)
      object.position.set(x, 0, z)

      const lookAtTarget = new THREE.Vector3(0, 0, 0)
      object.lookAt(lookAtTarget)

      currentRowGroup.add(object)
    }
  }

  timeline.clear()
  play()
  timeline.timeScale(animiteSpeed.value)
}
function play() {
  if (!group) {
    return
  }
  lineGroups.forEach((rowGroup, index) => {
    const n = index % 2 === 0 ? 1 : -1
    timeline.to(rowGroup.rotation, {
      duration: 50,
      y: -Math.PI * 2 * n,
      ease: 'none',
      repeat: -1,
    }, 0)
  })
}
function getDisplayArea(z = 0) {
  const radian = Math.PI / 180
  const distance = Math.abs(camera.position.z - z)
  const height = 2 * Math.tan((camera.fov / 2) * radian) * distance
  const width = camera.aspect * height
  return {
    width,
    height,
  }
}
function init() {
  if (!threeRoot.value) {
    return
  }
  scene = new THREE.Scene()
  camera = new THREE.PerspectiveCamera(45, window.innerWidth / window.innerHeight, 1, 1000)
  camera.position.z = 500
  renderer = new CSS3DRenderer()
  renderer.setSize(0, 0)
  threeRoot.value.appendChild(renderer.domElement)
}

function resize(width: number, height: number) {
  if (renderer) {
    renderer.setSize(width, height)
  }
  if (camera) {
    camera.aspect = width / height
    camera.updateProjectionMatrix()
  }
}

function render() {
  requestAnimationFrameId = requestAnimationFrame(render)
  renderer.render(scene, camera)
}

watch(
  () => [threeRootSize.width.value, threeRootSize.height.value],
  ([w, h]) => {
    resize(w, h)
  },
)

watch(
  () => [animiteSpeed.value],
  () => {
    timeline.timeScale(animiteSpeed.value)
  },
)

const initReady = ref(false)
watch(
  () => [regeditList.value, headSizeW.value, headSizeH.value, layer.value.itemBorderColor, layer.value.itemBorderWidth, layer.value.imgMode, layer.value.rowCount],
  () => {
    if (!initReady.value || !regeditList.value.length) {
      return
    }
    throttle(() => {
      initShape()
    }, 200, { leading: false })()
  },
  { deep: true },
)

onMounted(async () => {
  init()
  render()
  initReady.value = true
  initShape()
})
onUnmounted(() => {
  // 清理资源
  if (requestAnimationFrameId) {
    cancelAnimationFrame(requestAnimationFrameId)
    requestAnimationFrameId = null
  }
})
</script>

<template>
  <div ref="threeRoot" class="three-root"></div>
</template>

<style scoped lang="scss">
.three-root {
  width: 100%;
  height: 100%;
  overflow: hidden;
  :deep() {
    .css3d {
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      color: #fff;
      text-align: center;
      transform: translateZ(0);
      will-change: transform;
      backface-visibility: hidden;
      border-radius: 3%;
      img {
        width: 100%;
        height: 100%;
        object-position: center;
      }
    }
  }
}
</style>
