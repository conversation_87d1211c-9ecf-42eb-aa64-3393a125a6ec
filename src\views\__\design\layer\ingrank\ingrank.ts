import type { IDesignLayer, IDesignSetup } from '../../types'
import { BisTypes } from '../..'
import { InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './ingrank-setting.vue'
import Comp from './ingrank.vue'
// 类型
export const type = 'ingrank'
export const defaultColor = '#fff'
export const defaultShowIndex = false
export const defaultData = {
  bgTop: new URL(`./assets/rank-t.png`, import.meta.url).href,
  bgBody: new URL(`./assets/rank-m.png`, import.meta.url).href,
  bgBottom: new URL(`./assets/rank-b.png`, import.meta.url).href,
}
// 数据类型约束
export interface IDesignIngRank extends IDesignLayer {
  type: typeof type
  textColor?: string
  bgTop?: string
  bgBody?: string
  bgBottom?: string
  rankBgColor: string
  paddingLR?: number
  paddingTB?: number
  showIndex?: boolean
  data: {
    name: string
    avatar: string
    score: number
  }[]
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.sportsIng,
    showType: ['pcwall'],
    status: ['ing'],
    showInteractive: [InteractiveEnum.shakev3, InteractiveEnum.moneyv3, InteractiveEnum.goldcoinv3, InteractiveEnum.diglettv3, InteractiveEnum.firev3],
    type,
    name: '进行中小排行',
    thumbnail: new URL('./ingrank.png', import.meta.url).href,
    Comp,
    CompSetting,
    defaultData() {
      return {
        type,
        uuid: layerUuid(),
        name: '进行中排行',
        textColor: '#fff',
        rankBgColor: '#ff0d91',
        paddingLR: 15,
        data: [],
        style: {
          right: '10px',
          top: '10px',
          width: '300px',
          height: '500px',
        },
      }
    },
  })
}
