import { autoAnimatePlugin } from '@formkit/auto-animate/vue'
import ResizeObserver from 'resize-observer-polyfill'
import { createApp } from 'vue'
import AppRoot from './App.vue'
import { setupDirective } from './directive'
import i18n from './locale/index'
import { setupRouter } from './router'
import { setupStore } from './store'
import { setupDate } from './utils/date'
import { setupEcode } from './utils/ecode'
import './utils/polyfill'
import './utils/resizeObserverHak'
import './assets/scss/index.scss'
import 'animate.css'
import 'polyfill-object.fromentries'

if (['uat', 'dev'].includes(import.meta.env.MODE)) {
  import('./utils/debug')
}

// 增加判断如果不支持当前依赖再设置
if (window.ResizeObserver === undefined) {
  window.ResizeObserver = ResizeObserver
}

setupDate()

async function bootstrap() {
  // @ts-expect-error
  if (window.__bootstrap__) {
    console.error('禁止重复启动')
    return
  }
  const app = createApp(AppRoot)
  // @ts-expect-error
  window.__bootstrap__ = app
  // router
  setupRouter(app)
  // i18n
  app.use(i18n)
  // autoAnimatePlugin
  app.use(autoAnimatePlugin)
  // store
  setupStore(app)
  // 自定义指令
  setupDirective(app)
  // ecode
  setupEcode(app)
  // 挂载
  app.mount('#app')
}
bootstrap()
