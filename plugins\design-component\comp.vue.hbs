<script setup lang="ts">
  import type { IDesign{{fileNamePascal}} } from './{{fileNameKebabCase}}'
  import { defineCustomEmits, useDesignState, useDesignTemp } from '../../index'

  const layer = defineModel<IDesign{{fileNamePascal}}>('layer', { required: true })

  const designTemp = useDesignTemp()
  const designState = useDesignState()
  const customEmits = defineCustomEmits(layer)

</script>

<template>
  <div class="{{fileNameKebabCase}}-box"></div>
</template>

<style scoped lang="scss"></style>
