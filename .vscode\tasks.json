{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "vuejs: start",
      "type": "npm",
      // 需要执行的命令（对应于 package.json 中的 scripts 命令）
      "script": "dev:pro:iframe",
      "isBackground": true,
      "problemMatcher": {
        "owner": "vuejs",
        "source": "vuejs",
        "applyTo": "allDocuments",
        "fileLocation": [
          "relative",
          "${workspaceFolder}"
        ],
        "background": {
          "activeOnStart": true,
          "beginsPattern": "^\\s*$",
          "endsPattern": "^\\s*$"
        },
        "pattern": [
          {
            "regexp": "^\\s*$",
            "file": 1,
            "location": 2,
            "message": 3
          },
          {
            "regexp": "^(.*)\\((\\d+),(\\d+)\\):\\s+(.*)$",
            "file": 1,
            "location": 2,
            "message": 4
          },
          {
            "regexp": "^\\s*at\\s+(.*)\\((.*):(\\d+):(\\d+)\\)$",
            "file": 2,
            "location": 3,
            "message": 1
          }
        ]
      }
    }
  ]
}