<script setup lang="ts">
import type { IDesignPiclotteryIng2 } from './piclottery-ing2'
import { getDefaultMaterial, openSelectMaterial, useDataAttr } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'
import { defaultAnimateSpeed, defaultHeadSizeH, defaultHeadSizeW, defaultImgMode, defaultItemBorderColor, defaultPlaceHolderImg, defultRowCount } from './piclottery-ing2'

const layer = defineModel<IDesignPiclotteryIng2>('layer', { required: true })

const headSizeWBlind = useDataAttr(layer.value, 'headSizeW', defaultHeadSizeW)
const headSizeHBlind = useDataAttr(layer.value, 'headSizeH', defaultHeadSizeH)
const animiteSpeedBlind = useDataAttr(layer.value, 'animiteSpeed', defaultAnimateSpeed)
const itemBorderColorBind = useDataAttr(layer.value, 'itemBorderColor', defaultItemBorderColor)
const itemBorderWidthBind = useDataAttr(layer.value, 'itemBorderWidth', 0)
const rowCountBlind = useDataAttr(layer.value, 'rowCount', defultRowCount)
const placeHolderImgBlind = computed(() => {
  return layer.value.placeHolderImg ?? defaultPlaceHolderImg
})
const imgModeBind = useDataAttr(layer.value, 'imgMode', defaultImgMode)

type IType = 'placeHolderImg'
async function updateMaterialFn(name: IType, isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, `${name}`)
    : await openSelectMaterial('PIC')
  if (result) {
    layer.value[name] = result
  } else if (isReset) {
    delete layer.value[name]
  }
}
const modes: { label: string, value: 'contain' | 'fill' | 'cover' }[] = [
  { label: '适应', value: 'contain' },
  { label: '拉伸', value: 'fill' },
  { label: '裁剪', value: 'cover' },
]
onMounted(() => {
})
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>元素尺寸</h3>
      </div>
      <div class="setting-item">
        <h3></h3>
        <div class="flex flex-a-c">
          <p>宽：</p>
          <el-input-number v-model="headSizeWBlind" v-input-number :max="1000" :min="100" controls-position="right" />
          <p class="ml-10">高：</p>
          <el-input-number v-model="headSizeHBlind" v-input-number :max="1000" :min="100" controls-position="right" />
        </div>
      </div>
      <div class="setting-item">
        <h3>元素边框</h3>
        <el-input-number v-model="itemBorderWidthBind" v-input-number :max="100" :min="0" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3></h3>
        <div class="setting-item">
          <h3>颜色</h3>
          <hi-color v-model="itemBorderColorBind" />
        </div>
      </div>
      <div class="setting-item">
        <h3>行数</h3>
        <el-input-number v-model="rowCountBlind" v-input-number :max="30" :min="0" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>动画速度</h3>
        <el-input-number v-model="animiteSpeedBlind" v-input-number :max="30" :min="0" controls-position="right" />
      </div>
      <div class="setting-item mt-5!">
        <h3>默认图</h3>
        <div class="flex items-end">
          <div class="bgblank relative h-50 w-64">
            <MaterialThumbnail @select="updateMaterialFn('placeHolderImg')" @reset="updateMaterialFn('placeHolderImg', true)">
              <img v-if="placeHolderImgBlind" :src="placeHolderImgBlind" alt="" class="h-full w-full object-cover" />
            </MaterialThumbnail>
          </div>
        </div>
      </div>
      <div class="setting-item">
        <h3>图片显示模式</h3>
        <el-select v-model="imgModeBind" placeholder="选择填充方式" class="w-100">
          <el-option v-for="item in modes" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.text-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.flex-a-start {
  align-items: flex-start !important;
}
</style>
