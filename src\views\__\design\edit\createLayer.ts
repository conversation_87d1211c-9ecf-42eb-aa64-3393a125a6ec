import type { CSSProperties } from 'vue'
import type { IDesignLayer, IDesignTemplateSetupOption } from '../types'
import { useDesignData, useDesignSetup, useDesignState, useDesignTemp } from '..'
import { layerUuid } from '../utils'

const designData = useDesignData()
const designSetup = useDesignSetup()
const designState = useDesignState()
const designTemp = useDesignTemp()

export async function createComponent(type: string, layerDefaultData?: any) {
  const setupOption = designSetup.getComponentSetupOption(type)
  if (!setupOption) return

  let data
  if (!setupOption.base) {
    data = designTemp.findParentLayerByType(type)
    if (data) {
      data.uuid = layerUuid()
    }
  }
  if (!data) {
    data = await setupOption.defaultData(layerDefaultData ? { data: layerDefaultData } : undefined)
  }
  if (!data) return

  // 只在当前状态下显示
  if (designState.statusList.length > 1) {
    data.show = [designState.status]
  }

  // 如果当前选中了组，新建的图层默认放到组里
  let parentGroup: { layers: IDesignLayer[], style?: CSSProperties } = designData
  const uuid = designTemp.activeList[0]
  if (uuid) {
    const group = designData.getLayerByUuid(uuid)
    if (group?.type === 'group' && !group.templateId) {
      parentGroup = group
    }
  }

  // 校准尺寸和位置
  const { drafts } = designData.option
  const pWidth = Number.parseFloat(`${parentGroup.style?.width || drafts[0]}`)
  const pHeight = Number.parseFloat(`${parentGroup.style?.height || drafts[1]}`)

  const width = Math.floor(Number.parseFloat(`${data.style.width || pWidth * 0.3}`))
  const height = Math.floor(Number.parseFloat(`${data.style.height || pHeight * 0.3}`))
  const left = Math.floor((pWidth - width) / 2)
  const top = Math.floor((pHeight - height) / 2)

  if (!data.style.width) {
    data.style.width = `${width}px`
  }
  if (!data.style.height) {
    data.style.height = `${height}px`
  }
  if (!data.style.left) {
    data.style.left = `${left}px`
  }
  if (!data.style.top) {
    data.style.top = `${top}px`
  }

  // 校准name
  if (data.name) {
    const len = parentGroup.layers.filter(layer => layer.type === type).length
    data.name += len || ''
  }

  parentGroup.layers.unshift(data)

  designTemp.reset()
  designTemp.activeList.unshift(data.uuid)
}

export async function createTemplate(val: IDesignTemplateSetupOption) {
  // 遍历递归，重新生成uuid
  const data = val.defaultData()

  // 只在当前状态下显示
  if (designState.statusList.length > 1) {
    data.show = [designState.status]
  }

  // 如果当前选中了组，新建的图层默认放到组里
  let parentGroup: { layers: IDesignLayer[], style?: CSSProperties } = designData
  const uuid = designTemp.activeList[0]
  if (uuid) {
    const group = designData.getLayerByUuid(uuid)
    if (group?.type === 'group' && !group.templateId) {
      parentGroup = group
    }
  }

  // 校准位置
  const { drafts } = designData.option
  const pWidth = Number.parseFloat(`${parentGroup.style?.width || drafts[0]}`)
  const pHeight = Number.parseFloat(`${parentGroup.style?.height || drafts[1]}`)

  const width = Math.floor(Number.parseFloat(`${data.style.width || pWidth * 0.3}`))
  const height = Math.floor(Number.parseFloat(`${data.style.height || pHeight * 0.3}`))
  const left = Math.floor((pWidth - width) / 2)
  const top = Math.floor((pHeight - height) / 2)

  if (!data.style.left) {
    data.style.left = `${left}px`
  }
  if (!data.style.top) {
    data.style.top = `${top}px`
  }

  // 校准name
  if (data.name) {
    const len = parentGroup.layers.filter(layer => layer.type === 'group').length
    data.name += len || ''
  }

  parentGroup.layers.unshift(data)

  designTemp.reset()
  designTemp.activeList.unshift(data.uuid)
}
