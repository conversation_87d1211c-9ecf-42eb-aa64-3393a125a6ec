<script setup lang="ts">
import { getDefaultMaterial, openSelectMaterial, useDataAttr } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'
import { DEFAULT_DATA, type IDesignWinningList2 } from './winning-list2'

const layer = defineModel<IDesignWinningList2>('layer', { required: true })

const avatarSizeBind = useDataAttr(layer.value.data, 'avatarSize', DEFAULT_DATA.avatarSize)
const defaultAvatarBind = useDataAttr(layer.value.data, 'defaultAvatar', DEFAULT_DATA.defaultAvatar)
const avatarDecorationBind = useDataAttr(layer.value.data, 'avatarDecoration', DEFAULT_DATA.avatarDecoration)
const nameColorBind = useDataAttr(layer.value.data, 'nameColor', DEFAULT_DATA.nameColor)
const nameFontSizeBind = useDataAttr(layer.value.data, 'nameFontSize', DEFAULT_DATA.nameFontSize)
const yGapBind = useDataAttr(layer.value.data, 'yGap', DEFAULT_DATA.yGap)

async function updateMaterialFn(name: 'defaultAvatar' | 'avatarDecoration', isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, `data.${name}`)
    : await openSelectMaterial('PIC')
  if (result) {
    // 有些为空可以赋值空（useDataAttr写法），有些为空需要清空（使用解构写法）
    layer.value.data[name] = result
  } else if (isReset) {
    delete layer.value.data[name]
  }
}
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <span>尺寸</span>
        <el-input-number v-model="avatarSizeBind" v-input-number controls-position="right" :min="10" :step="1" class="ml-20 w-80px!" />
      </div>
      <div class="setting-item">
        <h3>默认头像</h3>
        <div class="bgblank relative h-60 w-160">
          <MaterialThumbnail @select="updateMaterialFn('defaultAvatar')" @reset="updateMaterialFn('defaultAvatar', true)">
            <img :src="defaultAvatarBind">
          </MaterialThumbnail>
        </div>
      </div>
      <div class="setting-item">
        <h3>头像背景</h3>
        <div class="bgblank relative h-60 w-160">
          <MaterialThumbnail @select="updateMaterialFn('avatarDecoration')" @reset="updateMaterialFn('avatarDecoration', true)">
            <img :src="avatarDecorationBind">
          </MaterialThumbnail>
        </div>
      </div>
      <div class="setting-item">
        <span>昵称颜色</span>
        <hi-color v-model="nameColorBind" />
      </div>
      <div class="setting-item">
        <span>昵称大小</span>
        <el-input-number v-model="nameFontSizeBind" v-input-number controls-position="right" :min="10" :step="1" class="ml-20 w-80px!" />
      </div>
      <div class="setting-item">
        <span>上下间距</span>
        <el-input-number v-model="yGapBind" v-input-number controls-position="right" :min="10" :step="1" class="ml-20 w-80px!" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
