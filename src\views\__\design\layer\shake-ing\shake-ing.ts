import { BisTypes, useDesignData } from '../..'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './shake-ing-setting.vue'
import Comp from './shake-ing.vue'

// 类型
export const type = 'shake-ing'

export const defaultCount = 10
export const defaultScoreColor = '#f4ff48'
export const defaultNameColor = '#ffffff'
export const defaultAvatarDecoration = new URL('./assets/decoration.png', import.meta.url).href

export interface IDesignShakeIng extends IDesignLayer {
  type: typeof type
  flag: string[]
  avatarDecoration?: string // 头像装饰
  contestant: string | string[] // 参赛物
  count?: number
  scoreColor?: string
  nameColor?: string
  reverse?: boolean // 反向
  data: {
    name: string
    avatar: string
    score: number
    progress: number
  }[]
}

export function setup(app: IDesignSetup) {
  let status: string[]
  switch (app.interactive) {
    case InteractiveEnum.shakev3:
      status = ['ing']
      break
    default:
      status = ['ing']
      break
  }

  app.registry({
    showType: ['pcwall'],
    bisType: BisTypes.sportsIng,
    showInteractive: [InteractiveEnum.shakev3, InteractiveEnum.moneyv3, InteractiveEnum.goldcoinv3, InteractiveEnum.diglettv3, InteractiveEnum.firev3],
    type,
    name: '进行中排行',
    thumbnail: new URL('./shake-ing.png', import.meta.url).href,
    status,
    Comp,
    CompSetting,
    defaultData() {
      const designData = useDesignData()
      const [width, height] = designData.option.drafts

      return {
        uuid: layerUuid(),
        name: '进行中排行',
        type,
        flag: [
          new URL(`./assets/flag1.png`, import.meta.url).href,
          new URL(`./assets/flag2.png`, import.meta.url).href,
          new URL(`./assets/flag3.png`, import.meta.url).href,
        ],
        contestant: [
          new URL(`./assets/contestant1.gif`, import.meta.url).href,
          new URL(`./assets/contestant2.gif`, import.meta.url).href,
          new URL(`./assets/contestant3.gif`, import.meta.url).href,
        ],
        data: [],
        style: {
          width: `${width}px`,
          height: `${height * 0.52}px`,
          top: `${height * 0.425}px`,
          left: 0,
        },
      }
    },
  })
}
