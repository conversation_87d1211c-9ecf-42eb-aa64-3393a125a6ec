<script setup lang="ts">
import type { IDesignImgVideo } from './img-video'
import { defaultBgType } from './img-video'
import imgBg from './img.vue'
import videoBg from './video.vue'

const layer = defineModel<IDesignImgVideo>('layer', { required: true })
const bgType = computed(() => {
  return layer.value.bgType ?? defaultBgType
})

onMounted(() => {

})
</script>

<template>
  <img-bg v-if="bgType === 'img'" v-model:layer="layer" />
  <video-bg v-if="bgType === 'video'" v-model:layer="layer" />
</template>

<style scoped lang="scss">
</style>
