import { BisTypes } from '../..'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './regedit-setting.vue'
import Comp from './regedit.vue'

// 类型
export const type = 'regedit'

export const defaultAvatarSize = 80
export const defaultAnimation = true

export interface IDesignRegedit extends IDesignLayer {
  type: typeof type
  default: string
  data: { name: string, avatar: string }[]
  decoration: string
  itemWidth?: number
  gap?: number
  avatarSize?: number // 头像大小 默认 80%
  animation?: boolean // 是否开启动画
}

export function setup(app: IDesignSetup) {
  let status: string[]
  switch (app.interactive) {
    case InteractiveEnum.shakev3:
      status = ['ready']
      break
    default:
      status = ['ready']
      break
  }

  app.registry({
    showType: ['pcwall'],
    bisType: BisTypes.regedit,
    type,
    name: '参与人列表',
    thumbnail: new URL('./regedit.png', import.meta.url).href,
    status,
    Comp,
    CompSetting,
    defaultData() {
      return {
        uuid: layerUuid(),
        name: '参与人列表',
        type,
        default: new URL(`./assets/default.png`, import.meta.url).href,
        data: [],
        decoration: new URL(`./assets/decoration.png`, import.meta.url).href,
        style: {
          width: '950px',
          height: '170px',
        },
      }
    },
  })
}
