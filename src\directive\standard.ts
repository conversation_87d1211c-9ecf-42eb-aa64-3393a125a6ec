import type { DirectiveBinding } from 'vue'

function deal(dom: HTMLElement, value: string) {
  if (value.startsWith('http:') || value.startsWith('https:') || value.startsWith('//')) {
  } else {
    if (!value.startsWith('/')) {
      value = `/${value}`
    }
    value = `//res.dev.hichoujiang.com/design${value}`
  }
  dom.setAttribute('src', value)
}

export default {
  mounted: (el: HTMLElement, binding: DirectiveBinding) => {
    deal(el, binding.value)
  },
  updated: (el: HTMLElement, binding: DirectiveBinding) => {
    deal(el, binding.value)
  },
  // unmounted: (el: HTMLElement, binding: DirectiveBinding) => {},
}
