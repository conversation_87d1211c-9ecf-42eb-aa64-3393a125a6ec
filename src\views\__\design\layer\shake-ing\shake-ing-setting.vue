<script setup lang="ts">
import { getDefaultMaterial, openSelectMaterial, useDataAttr } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'
import { defaultAvatarDecoration, defaultCount, defaultNameColor, defaultScoreColor, type IDesignShakeIng } from './shake-ing'

const layer = defineModel<IDesignShakeIng>('layer', { required: true })

type IType = 'flag' | 'contestant'

async function updateMaterialFn(name: IType | 'avatarDecoration', index?: number, isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, `${name}${index !== undefined ? `.${index}` : ''}`)
    : await openSelectMaterial('PIC')
  if (result || (isReset && index === undefined)) {
    if (name === 'avatarDecoration') {
      layer.value.avatarDecoration = result
    } else if (index !== undefined) {
      const arr = layer.value[name] as string[]
      arr[index] = result
    }
  }
}

function remove(name: IType, index: number) {
  const arr = layer.value[name] as string[]
  arr.splice(index, 1)
}
function add(name: IType, index: number, defaultValue: string) {
  const arr = layer.value[name] as string[]
  arr.splice(index + 1, 0, defaultValue)
}

const countBind = useDataAttr(layer.value, 'count', defaultCount)
const reverseBind = useDataAttr(layer.value, 'reverse', false)
const scoreColorBind = useDataAttr(layer.value, 'scoreColor', defaultScoreColor)
const nameColorBind = useDataAttr(layer.value, 'nameColor', defaultNameColor)
const avatarDecorationBind = useDataAttr(layer.value, 'avatarDecoration', defaultAvatarDecoration)

const router = useRouter()
const isManage = computed(() => router.currentRoute.value.path.startsWith('/manage'))

const maxCount = computed(() => {
  const wallId = router.currentRoute.value?.query?.wallId as string
  const customWallIds = ['2721592']
  if (wallId && customWallIds.includes(wallId)) {
    return 700
  }
  return isManage.value ? Infinity : 50
})
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>数量</h3>
        <el-input-number v-model="countBind" v-input-number class="w-100px!" :min="1" :max="maxCount" :step="1"></el-input-number>
      </div>
      <div class="setting-item">
        <h3>方向</h3>
        <el-radio-group v-model="reverseBind">
          <el-radio-button label="左" :value="true" />
          <el-radio-button label="右" :value="false" />
        </el-radio-group>
      </div>
      <div class="setting-item">
        <h3>积分颜色</h3>
        <hi-color v-model="scoreColorBind" type="both" />
      </div>
      <div class="setting-item">
        <h3>昵称颜色</h3>
        <hi-color v-model="nameColorBind" type="both" />
      </div>
      <div class="setting-item flex-justify-between">
        <h3>头像装饰</h3>
        <MaterialThumbnail class="mr-6px" @select="updateMaterialFn('avatarDecoration')" @reset="updateMaterialFn('avatarDecoration', undefined, true)">
          <img :src="avatarDecorationBind" class="bgblank size-60px object-contain">
        </MaterialThumbnail>
      </div>
      <div class="setting-item flex-items-start!">
        <h3>名次标识</h3>
        <div>
          <div
            v-for="(item, index) in layer.flag"
            :key="index"
            class="relative mb-10 h-60 w-120 flex"
          >
            <MaterialThumbnail @select="updateMaterialFn('flag', index)" @reset="updateMaterialFn('flag', index, true)">
              <img :src="item" class="bgblank w-90 object-contain">
            </MaterialThumbnail>
            <div class="ml-6 w-20 pt-10">
              <icon-ph:minus-bold v-if="layer.flag.length > 1" @click.stop="remove('flag', index)" />
              <icon-ph:plus-bold @click.stop="add('flag', index, item)" />
            </div>
          </div>
        </div>
      </div>
      <div class="setting-item flex-items-start!">
        <h3>参赛物</h3>
        <div>
          <div
            v-for="(item, index) in layer.contestant"
            :key="index"
            class="relative mb-10 h-60 w-120 flex"
          >
            <MaterialThumbnail @select="updateMaterialFn('contestant', index)" @reset="updateMaterialFn('contestant', index, true)">
              <img :src="item" class="bgblank w-90 object-contain">
            </MaterialThumbnail>
            <div class="ml-6 w-20 pt-10">
              <icon-ph:minus-bold v-if="layer.contestant.length > 1" @click.stop="remove('contestant', index)" />
              <icon-ph:plus-bold @click.stop="add('contestant', index, item)" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
