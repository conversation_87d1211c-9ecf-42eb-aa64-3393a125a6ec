import type { IDesignLayer, IDesignSetup } from '../../types'
import { BisTypes, useDesignData } from '../..'
import { InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './listlottery-ing1-setting.vue'
import Comp from './listlottery-ing1.vue'
// 类型
export const type = 'listlottery-ing1'
export const defaultHeadCount = 100
export const defaultrotateSpeed = 2
export const defaultHeadSize = 300
export const defaultIsScaleAnimation = true
export const defalutTextStyle: textItemStyle = {
  fontSize: 40,
  fontColor: '#fff',
  fonBold: true,
}
export const defaultItemDirection = 'sphereCentre'
// 数据类型约束
export interface IDesignListlotteryIng1 extends IDesignLayer {
  type: typeof type
  headCount?: number
  headSize?: number
  rotateSpeed?: number
  isScaleAnimation?: boolean
  contentStyle: textItemStyle[]
  itemBgColors: string[]
  itemDirection?: 'sphereCentre' | 'screen'
}

export interface textItemStyle {
  fontSize: number
  fontColor: string
  fonBold: boolean
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.sportsIng,
    showType: ['pcwall'],
    status: ['ing', 'ready'],
    showInteractive: [InteractiveEnum.listlotteryv3],
    type,
    name: '名单抽奖3d动效',
    thumbnail: new URL('./listlottery-ing1.png', import.meta.url).href,
    Comp,
    CompSetting,
    defaultData() {
      const designData = useDesignData()
      const [width, height] = designData.option.drafts
      return {
        type,
        uuid: layerUuid(),
        name: '名单抽奖3d动效',
        contentStyle: [],
        itemBgColors: [''],
        style: {
          width: `${width}px`,
          height: `${height}px`,
          top: 0,
          left: 0,
        },
      }
    },
  })
}
