<script setup lang="ts">
import type { CSSProperties } from 'vue'
import { useVisitType } from '@/hooks/visittype'
import { defineCustomEmits, useDesignState, useDesignTemp } from '../../index'
import singleKonck from './components/digletsingle.vue'
import singlePull from './components/signlePull.vue'
import { defaultData, type IDesignDiglettIng } from './diglett-ing'

const layer = defineModel<IDesignDiglettIng>('layer', { required: true })

const designTemp = useDesignTemp()
const designState = useDesignState()
const customEmits = defineCustomEmits(layer)

const domRef = ref<HTMLElement>()
const wrapDomSize = useElementSize(domRef)
const singleRefs = ref()

const { isMobileUse } = useVisitType()
const isMobile = computed(() => isMobileUse.value || (designTemp.showType === 'mobile'))

const itemSize = computed(() => {
  let wSize = 100
  let hSize = 100
  const baceSize = 100
  if (wrapDomSize.width.value < baceSize * 3) {
    wSize = wrapDomSize.width.value / 3
  }
  if (wrapDomSize.width.value > 400) {
    wSize = (wrapDomSize.width.value / 400) * baceSize
  }
  hSize = (wrapDomSize.height.value / (3 * baceSize)) * baceSize
  return Math.floor(Math.min(wSize, hSize))
})

const holeImg = computed(() => {
  return layer.value.holeImg ?? defaultData.holeImg
})
const beHitList = computed(() => {
  return layer.value.beHitList ?? []
})
const hitImg = computed(() => {
  return layer.value.hitImg ?? defaultData.hitImg
})
const scoreColor = computed(() => {
  return layer.value.scoreColor ?? defaultData.scoreColor
})
const behitBtomPos = computed(() => {
  return layer.value.behitBtomPos ?? defaultData.behitBtomPos
})
const gameMode = computed(() => {
  return layer.value.gameMode ?? defaultData.gameMode
})
const comp = computed(() => {
  return gameMode.value === 'hit' ? singleKonck : singlePull
})
const bownImg = computed(() => {
  return layer.value.bownImg ?? defaultData.bownImg
})
const successAudio = computed(() => {
  return layer.value.successAudio ?? defaultData.successAudio
})
const failAudio = computed(() => {
  return layer.value.failAudio ?? defaultData.failAudio
})
const autoInterval = ref<NodeJS.Timeout>()
function autoHit() {
  autoInterval.value && clearInterval(autoInterval.value)
  autoInterval.value = setInterval(() => {
    if (isMobile.value || designState.status !== 'ing') return
    const index = Math.ceil(Math.floor(Math.random() * 9))
    singleRefs.value[index].hitIt()
  }, 1000)
}

function updateScore(score: number) {
  customEmits('updateScore', score)
}

function getLine(l: number) {
  if (!isMobile.value && designState.status === 'ready' && l === 4) {
    return 2
  }
  return (l % 2 === 1) ? 2 : 3
}

const diglettIngStyle = computed(() => {
  const style: CSSProperties = {
    // paddingTop: `${itemSize.value * 0.6}px`,
  }
  return style
})

onMounted(() => {
  autoHit()
})

onUnmounted(() => {
  clearInterval(autoInterval.value)
})
</script>

<template>
  <div ref="domRef" class="diglett-ing-box" :style="diglettIngStyle">
    <div v-for="l in 4" :key="l" class="line" :class="[`line${l}`]">
      <component
        :is="comp"
        v-for="i in getLine(l)"
        ref="singleRefs"
        :key="i"
        :size="itemSize"
        :be-hit-list="beHitList!"
        :hit-img="hitImg"
        :hole-img="holeImg"
        :behit-btom-pos="behitBtomPos"
        :score-color="scoreColor"
        :hit-shake="layer.hitShake"
        :bown-img="bownImg"
        :behit-hide="layer.behitHide"
        :success-audio="successAudio"
        :fail-audio="failAudio"
        :is-mobile="isMobile"
        @update-score="updateScore"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.diglett-ing-box {
  width: 100%;
  height: 100%;
  padding-top: 15%;
  position: relative;
  .line {
    width: 100%;
    display: flex;
    justify-content: space-around;
    align-items: center;
    position: absolute;
  }
  .line1 {
    bottom: 64%;
    left: 0;
  }
  .line2 {
    bottom: 42.5%;
    left: 0;
  }
  .line3 {
    bottom: 21.25%;
    left: 0;
  }
  .line4 {
    bottom: 0;
    left: 0;
  }
}
</style>
