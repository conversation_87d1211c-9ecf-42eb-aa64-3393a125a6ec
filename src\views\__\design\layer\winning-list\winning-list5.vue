<script setup lang="ts">
import { defineCustomEmits, injectScale, useDesignState, useDesignTemp } from '../../index'
import { processStyle } from '../../utils'

import { DEFAULT_DATA, type IDesignWinningList5 } from './winning-list5'

const layer = defineModel<IDesignWinningList5>('layer', { required: true })

const designTemp = useDesignTemp()
const designState = useDesignState()
const customEmits = defineCustomEmits(layer)

const allRecordList = computed(() => {
  return designState.getLayerData('allRecordList') || []
  // return designState.getLayerData('onceRecordList') || []
})

const scale = injectScale()

const data = computed(() => {
  return {
    ...DEFAULT_DATA,
    ...layer.value.data,
  }
})

const cardStyle = computed(() => processStyle({
  backgroundColor: data.value.backgroundColor,
  borderRadius: `${data.value.borderRadius}px`,
  width: `${data.value.cardWidth}px`,
  height: `${data.value.cardHeight}px`,
}, scale.value))

const rootVars = computed(() => {
  return processStyle({
    '--num-color': data.value.numColor,
    '--num-font-size': `${Math.round(data.value.numFontSize)}px`,
    '--num-font-weight': data.value.numFontWeight ? 'bold' : 'normal',
    '--card-border-color': data.value.cardBorderColor,
    '--card-border-width': data.value.cardBorderShow ? `${data.value.cardBorderWidth}px` : 0, // 宽度不缩放，保持清晰
    '--card-border-radius': `${Math.round(data.value.borderRadius)}px`,
  }, scale.value)
})

function onDeleteItem(item: any) {
  ElMessageBox.confirm('确定要取消当前用户的中奖资格吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '再想想',
    type: 'warning',
  }).then(() => {
    if (designTemp.isEdit) {
      ElMessageBox.alert('当前中奖名单是模拟数据，无法取消中奖，请创建活动后在大屏幕中操作', '需要在大屏幕中操作')
    } else {
      customEmits('winning-delete', item.id)
    }
  }).catch(() => {
    // do nothing
  })
}
</script>

<template>
  <div
    class="seat-card-flex winning-list5-box"
    :style="{ gap: `${data.gap}px`, ...rootVars }"
  >
    <div
      v-for="(item, index) in allRecordList"
      :key="`card-${index}`"
      class="seat-card relative"
      :style="cardStyle"
    >
      <span v-for="(t, ti) in item.namesList" :key="t" class="num line-clamp-1">
        {{ t }}
        {{ ti === item.namesList.length - 1 ? '' : '-' }}
      </span>
      <div
        class="delete absolute inset-0 z-10 hidden cursor-pointer items-center justify-center bg-black bg-opacity-80 text-white"
        :style="cardStyle"
        @click="onDeleteItem(item)"
      >
        <icon-ph:trash-bold />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.seat-card-flex {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  overflow: auto;
}

.seat-card {
  display: flex;
  align-items: center;
  justify-content: center;
  border: var(--card-border-width) solid var(--card-border-color);
  border-radius: var(--card-border-radius);
  &:hover {
    .delete {
      display: flex;
    }
  }
  .num {
    white-space: nowrap;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: var(--num-font-size);
    font-weight: var(--num-font-weight);
    color: var(--num-color);
  }
}
</style>
