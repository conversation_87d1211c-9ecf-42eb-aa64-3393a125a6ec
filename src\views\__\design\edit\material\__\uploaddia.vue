<script setup lang="ts">
import type { UploadProps } from 'element-plus'
import type { IDesignMaterialType } from '../../../types'
import type { Label } from './label.vue'
import { deaQiniulUrl } from '@/utils'

export interface Material {
  url: string
  name: string
  type: string
}

export interface MaterialLabel {
  dataList: Material[]
  labelIdList: number[]
}

const props = defineProps<{
  materialType: IDesignMaterialType
  actionUrl: string
  labelList: Label[]
}>()
const emit = defineEmits<{
  (e: 'cancle'): void
  (e: 'save', val: MaterialLabel): void
}>()

const route = useRoute()

const dialogVisible = ref(true)
const fileType = computed(() => {
  switch (props.materialType) {
    case 'PIC':
      return 'image/*'
    case 'VIDEO':
      return 'video/*'
    case 'MUSIC':
      return 'audio/*'
    case 'SHAPE':
      return 'image/svg+xml'
    default:
      return '*'
  }
})
const selectedParentLabels = ref<number[]>([])
const selectedChildLabels = ref<number[]>([])
const isManage = computed(() => route.path.startsWith('/manage'))
const isOem = computed(() => route.path.startsWith('/oem'))

const parentLabel = computed(() => props.labelList.filter(item => !item.pId))
const childLabel = computed(() => props.labelList.filter(item => selectedParentLabels.value.includes(item.pId ?? 0)))
const isList = computed(() => [...selectedParentLabels.value, ...selectedChildLabels.value])

const postData = ref<Material[]>([])
const handleExceed: UploadProps['onExceed'] = () => {
  ElMessage.error(`单次最多只能上传50个文件`)
}
const successUpload: UploadProps['onSuccess'] = (res) => {
  const data = res.data
  postData.value.push({
    url: data.url,
    name: data.fileName,
    type: props.materialType,
  })
}

function removeParentTag(tagValue: any) {
  const list = props.labelList.filter(item => tagValue === item.pId)
  selectedChildLabels.value = selectedChildLabels.value.filter(item => !list.map(i => i.id).includes(item))
}

function query() {
  emit('save', {
    dataList: postData.value,
    labelIdList: isList.value,
  })
}

function deleteItem(item: Material) {
  postData.value = postData.value.filter(i => i.url !== item.url)
}

const isDragging = ref(false)

function handleDragOver(event: DragEvent) {
  event.preventDefault()
  isDragging.value = true
}

function handleDragLeave(event: DragEvent) {
  event.preventDefault()
  isDragging.value = false
}

function handleDrop(event: DragEvent) {
  event.preventDefault()
  isDragging.value = false
  const files = event.dataTransfer?.files
  if (files) {
    Array.from(files).forEach((file) => {
      if (file.type.startsWith(fileType.value.split('/')[0])) {
        const formData = new FormData()
        formData.append('file', file)
        fetch(props.actionUrl, {
          method: 'POST',
          body: formData,
        })
          .then(response => response.json())
          .then(successUpload as any)
          .catch(() => ElMessage.error('上传失败'))
      }
    })
  }
}
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="上传素材"
    width="800px"
    destroy-on-close
    :close-on-click-modal="false"
    @close="$emit('cancle')"
  >
    <div
      class="content"
      :class="{ 'is-dragging': isDragging }"
      @dragover="handleDragOver"
      @dragleave="handleDragLeave"
      @drop="handleDrop"
    >
      <div class="drag-overlay" :class="{ 'is-visible': isDragging }">
        <p>拖拽到这里上传</p>
      </div>
      <div class="flex flex-a-c">
        <p class="item-label">上传素材：</p>
        <div v-if="['PIC', 'SHAPE'].includes(materialType)" class="img-loadlist">
          <div v-for="item in postData" :key="item.url" class="img-item">
            <img :src="deaQiniulUrl(item.url)" alt="" />
            <div class="onface" @click="deleteItem(item)"> <icon-ph:trash-bold /></div>
          </div>
          <el-upload
            list-type="picture-card"
            :action="actionUrl"
            :on-success="successUpload"
            :show-file-list="false"
            multiple
            :accept="fileType"
            :limit="50"
            :on-exceed="handleExceed"
          >
          </el-upload>
        </div>
        <div v-if="materialType === 'VIDEO'" class="video-loadlist">
          <div v-for="item in postData" :key="item.url" class="video-item">
            <video :src="deaQiniulUrl(item.url)" alt="" />
            <div class="onface" @click="deleteItem(item)"> <icon-ph:trash-bold /></div>
          </div>
          <el-upload
            list-type="picture-card"
            :action="actionUrl"
            :on-success="successUpload"
            :show-file-list="false"
            multiple
            :accept="fileType"
            :limit="50"
            :on-exceed="handleExceed"
          >
            <icon-ph-plus-bold class="ml-3 size-15" />
          </el-upload>
        </div>
        <div v-if="materialType === 'MUSIC'">
          <el-upload
            class="mb-5"
            :action="actionUrl"
            :on-success="successUpload"
            :show-file-list="false"
            multiple
            :accept="fileType"
            :limit="50"
            :on-exceed="handleExceed"
          >
            <el-button type="primary">上传</el-button>
          </el-upload>

          <div v-for="item in postData" :key="item.url" class="flex items-center">
            <audio v-if="postData.length" class="my-5" controls :src="deaQiniulUrl(item.url)" />
            <el-button type="danger" class="mx-5" @click="deleteItem(item)">删除</el-button>
            <span class="mx-5 w-250 overflow-hidden text-ellipsis whitespace-nowrap">名称: {{ item.name }}</span>
          </div>
        </div>
      </div>
      <div class="mt-15 flex flex-a-c">
        <p class="item-label">一级分类：</p>
        <el-select v-model="selectedParentLabels" class="add-select" multiple filterable allow-create default-first-option placeholder="请选择一级分类" @remove-tag="removeParentTag">
          <el-option
            v-for="item in parentLabel"
            :key="item.id"
            :label="item.name"
            :value="item.id ?? 0"
          />
        </el-select>
      </div>
      <div v-if="isManage || isOem" class="mt-15 flex flex-a-c">
        <p class="item-label">二级分类：</p>
        <el-select v-model="selectedChildLabels" class="add-select" multiple filterable allow-create default-first-option placeholder="请选择二级分类">
          <el-option
            v-for="item in childLabel"
            :key="item.id"
            :label="item.name"
            :value="item.id ?? 0"
          />
        </el-select>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('cancle')">取消</el-button>
        <el-button type="primary" @click="query">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.content {
  padding: 10px 15px;
  border-radius: 6px;
  transition: border-color 0.3s;
  position: relative;

  &.is-dragging {
    border-color: #409eff;
  }

  .drag-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(64, 158, 255, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
    opacity: 0;
    visibility: hidden;
    transition:
      opacity 0.3s ease,
      visibility 0.3s ease;

    &.is-visible {
      opacity: 1;
      visibility: visible;
    }

    p {
      font-size: 20px;
      color: #409eff;
      font-weight: bold;
    }
  }

  .item-label {
    width: 90px;
  }
  .img-loadlist {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    :deep() {
      .el-upload--picture-card {
        width: 80px;
        height: 80px;
        line-height: 80px;
      }
    }
    .img-item {
      width: 80px;
      height: 80px;
      line-height: 80px;
      position: relative;
      &:hover {
        .onface {
          display: flex;
        }
      }
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
  .video-loadlist {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    :deep() {
      .el-upload--picture-card {
        width: 80px;
        height: 80px;
        line-height: 80px;
      }
    }
    .video-item {
      width: 80px;
      height: 80px;
      line-height: 80px;
      position: relative;
      border: 1px solid #ebeef5;
      &:hover {
        .onface {
          display: flex;
        }
      }
      video {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
  .onface {
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    position: absolute;
    display: none;
    justify-content: center;
    align-items: center;
    left: 0;
    top: 0;
    cursor: pointer;
    font-size: 20px;
    color: #fff;
  }
}
</style>
