import type { IDesignLayer, IDesignSetup } from '../../types'
import { merge } from 'lodash-es'
import { openSelectMaterial, ResourceUtil } from '../..'
import { layerUuid } from '../../utils'
import CompSetting from './shape-setting.vue'
import Comp from './shape.vue'

export const type = 'shape'

export interface IDesignShape extends IDesignLayer {
  type: typeof type
  data?: string
  colors?: string[] // 用于定义svg的中的颜色
}

export function setup(app: IDesignSetup) {
  app.registry({
    type,
    name: '形状',
    Comp,
    CompSetting,
    async defaultData(options) {
      const data = (options as IDesignShape)?.data || await openSelectMaterial('SHAPE')
      if (!data) return
      const { width, height, top, left } = await ResourceUtil.loadImage(data, { draftScale: 0.2 })

      return merge({
        uuid: layerUuid(),
        name: '新形状',
        type,
        base: true,
        data,
        style: {
          width: `${width}px`,
          height: `${height}px`,
          top: `${top}px`,
          left: `${left}px`,
          overflow: 'hidden',
        },
      }, options as IDesignShape)
    },
  })
}
