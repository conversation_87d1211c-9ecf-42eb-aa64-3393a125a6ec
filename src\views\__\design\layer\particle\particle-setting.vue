<script setup lang="ts">
import MaterialThumbnail from '../../components/material-thumbnail.vue'
import { getDefaultMaterial, openSelectMaterial, useDataAttr } from '../../index'
import { defaultValues, type IDesignParticle } from './particle'

const layer = defineModel<IDesignParticle>('layer', { required: true })

const full = useDataAttr(layer.value, 'full', defaultValues.full)
const countBind = useDataAttr(layer.value, 'maxParticles', defaultValues.maxParticles)
const gravityBind = useDataAttr(layer.value, 'gravity', defaultValues.gravity)
const collisionBind = useDataAttr(layer.value, 'collision', defaultValues.collision)
const sprayCountBind = useDataAttr(layer.value, 'sprayCount', defaultValues.sprayCount)
const fanAngleBind = useDataAttr(layer.value, 'fanAngle', defaultValues.fanAngle)
const emitAngleBind = useDataAttr(layer.value, 'emitAngle', defaultValues.emitAngle)
const maxSizeBind = useDataAttr(layer.value, 'maxSize', defaultValues.maxSize)
const minSizeBind = useDataAttr(layer.value, 'minSize', defaultValues.minSize)
const emissionSpeedBind = useDataAttr(layer.value, 'emissionSpeed', defaultValues.emissionSpeed)

async function updateMaterialFn(index?: number, isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, `contestant.${index}`)
    : await openSelectMaterial('PIC')
  if (result || (isReset && index === undefined)) {
    const arr = layer.value.contestant as string[]
    if (index !== undefined) {
      arr[index] = result
    }
  }
}
function remove(index: number) {
  const arr = layer.value.contestant as string[]
  arr.splice(index, 1)
}
function add(index: number, defaultValue: string) {
  const arr = layer.value.contestant as string[]
  arr.splice(index + 1, 0, defaultValue)
}

// 位置
const { emitterX, emitterY } = layer.value
const mOrATabs = [{ label: '手动' }, { label: '自动' }]
const mOrA = ref(mOrATabs[`${emitterX}_${emitterY}`.includes('%') ? 1 : 0].label)
const positionUnit = computed(() => mOrA.value === '自动' ? '%' : '')
const leftBind = useDataAttr(layer.value, 'emitterX', 0, positionUnit)
const topBind = useDataAttr(layer.value, 'emitterY', 0, positionUnit)
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item flex-items-start!">
        <h3>掉落物</h3>
        <div>
          <div
            v-for="(item, index) in layer.contestant"
            :key="index"
            class="relative mb-10 h-60 w-120 flex"
          >
            <MaterialThumbnail @select="updateMaterialFn(index)" @reset="updateMaterialFn(index, true)">
              <img :src="item" class="bgblank w-90 object-contain">
            </MaterialThumbnail>
            <div class="ml-6 w-20 pt-10">
              <icon-ph:minus-bold v-if="layer.contestant.length > 1" @click.stop="remove(index)" />
              <icon-ph:plus-bold @click.stop="add(index, item)" />
            </div>
          </div>
        </div>
      </div>
      <div class="setting-item justify-end!">
        <h3 class="ml-10">最大</h3>
        <el-input-number v-model="maxSizeBind" v-input-number controls-position="right" />
        <h3 class="ml-10">最小</h3>
        <el-input-number v-model="minSizeBind" v-input-number controls-position="right" />
      </div>
      <div class="setting-item">
        <h3 class="flex items-center">
          数量
          <el-tooltip effect="dark" content="控制每秒钟从发射器中发射出的粒子数量，数值越大粒子越密集，建议根据实际效果需求调整">
            <icon-ph-question-bold class="ml-5" />
          </el-tooltip>
        </h3>
        <el-input-number v-model="sprayCountBind" v-input-number :min="1" :max="1000" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3 class="flex items-center">
          渲染数量
          <el-tooltip effect="dark" content="控制可视区域内同时显示的粒子总数量，数值越大粒子越多，建议根据设备性能和视觉效果适当调整">
            <icon-ph-question-bold class="ml-5" />
          </el-tooltip>
        </h3>
        <el-input-number v-model="countBind" v-input-number :min="1" :max="1000" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3 class="flex items-center">
          发射速度
          <el-tooltip effect="dark" content="控制粒子从发射器发射出的速度，数值越大粒子发射越快，建议根据实际效果需求调整">
            <icon-ph-question-bold class="ml-5" />
          </el-tooltip>
        </h3>
        <el-input-number v-model="emissionSpeedBind" v-input-number :min="1" :max="150" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3 class="flex items-center">
          重力
          <el-tooltip effect="dark" content="控制粒子在重力作用下的下落速度，数值越大粒子下落越快，建议根据实际效果需求调整">
            <icon-ph-question-bold class="ml-5" />
          </el-tooltip>
        </h3>
        <el-input-number v-model="gravityBind" v-input-number :min="0" :max="2" :step="0.01" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>碰撞</h3>
        <el-switch v-model="collisionBind" />
      </div>
      <div class="setting-item">
        <h3>全屏</h3>
        <el-switch v-model="full" />
      </div>
      <template v-if="!full">
        <div class="setting-item">
          <h3 class="flex items-center">
            夹角
            <el-tooltip effect="dark" content="控制粒子发射的夹角范围，数值越大粒子发射范围越广，建议根据实际效果需求调整">
              <icon-ph-question-bold class="ml-5" />
            </el-tooltip>
          </h3>
          <el-slider v-model.number="fanAngleBind" class="mx-10" :max="360" :step="1"></el-slider>
        </div>
        <div class="setting-item">
          <h3 class="flex items-center">
            角度
            <el-tooltip effect="dark" content="控制粒子发射的角度，数值越大粒子发射角度越广，建议根据实际效果需求调整">
              <icon-ph-question-bold class="ml-5" />
            </el-tooltip>
          </h3>
          <el-slider v-model.number="emitAngleBind" class="mx-10" :max="360" :step="1"></el-slider>
        </div>
        <div class="flex items-center justify-between">
          <label class="flex items-center">
            位置
            <el-tooltip effect="dark" content="位置为百分比时图层不在组中时相对的浏览器整个窗口居中显示。">
              <icon-ph-question-bold class="ml-5" />
            </el-tooltip>
          </label>
          <hi-tabs v-model="mOrA" :tabs="mOrATabs" />
        </div>
        <template v-if="mOrA === '手动'">
          <div class="setting-item justify-end!">
            <h3 class="ml-10">左</h3>
            <el-input-number v-model="leftBind" v-input-number controls-position="right" />
            <h3 class="ml-10">上</h3>
            <el-input-number v-model="topBind" v-input-number controls-position="right" />
          </div>
        </template>
        <template v-else>
          <div class="setting-item justify-end!">
            <h3 class="ml-10">横</h3>
            <el-select v-model="leftBind">
              <el-option label="左" :value="0" />
              <el-option label="中" :value="50" />
              <el-option label="右" :value="100" />
            </el-select>
            <h3 class="ml-10">纵</h3>
            <el-select v-model="topBind">
              <el-option label="上" :value="0" />
              <el-option label="中" :value="50" />
              <el-option label="下" :value="100" />
            </el-select>
          </div>
        </template>
      </template>
    </div>
  </div>
</template>

<style scoped lang="scss">
.thumbnail-box {
  width: 120px;
  height: 120px;
  outline: 1px solid #e6ebed;
  border-radius: 4px;
  position: relative;

  img {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }

  > img {
    object-fit: contain;
  }
}
</style>
