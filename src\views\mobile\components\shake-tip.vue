<script lang="ts" setup>
export interface IShapeTipProps {
  title?: string
  tips: string
  btnText?: string
}

const props = withDefaults(defineProps<IShapeTipProps>(), {
  title: '提示',
})
const emits = defineEmits(['ok'])
</script>

<template>
  <div class="fixed-tl z-99 size-full flex items-center justify-center bg-[rgba(0,0,0,.3)]">
    <div class="modal">
      <section class="body">
        <h3 class="header">{{ title }}</h3>
        <p>{{ props.tips }}</p>
      </section>
      <div v-if="btnText" class="bottom flex flex-a-c flex-j-c">
        <button class="btn" @click="emits('ok')">{{ btnText }}</button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.modal {
  width: 70%;
  margin-bottom: 20vh;
  background-color: #fff;
  pointer-events: auto;
  border-radius: 6px;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
  transition: all 0.18s;

  h3 {
    margin-bottom: 8px;
    font-size: 16px;
    text-align: center;
    color: #333;
  }

  .body {
    margin-bottom: 10px;
    padding: 10px 6px 0 10px;
    color: #8c8c8c;
    font-size: 18px;
    text-align: center;

    p {
      line-height: 24px;
    }
  }

  .bottom {
    width: 100%;
    border-top: 1px solid #e5e5e5;
  }

  .btn {
    margin: 7.5px;
    width: 100%;
    line-height: 36px;
    height: 36px;
    text-align: center;
    white-space: nowrap;
    font-size: 18px;
    color: #576b95;
  }
}
</style>
