{
  "compilerOptions": {
    "target": "ESNext",
    "jsx": "preserve",
    "jsxImportSource": "vue",
    "lib": [
      "ES2020",
      "DOM",
      "DOM.Iterable"
    ],
    "useDefineForClassFields": true,
    // 解析非相对模块名的基准目录
    "baseUrl": ".",
    "module": "ESNext",
    /* Bundler mode */
    "moduleResolution": "Bundler",
    // 模块名到基于 baseUrl的路径映射的列表
    "paths": {
      "~/*": ["./*"],
      "@/*": ["./src/*"],
      "&/*": ["./src/views/__/*"],
      "design/*": ["./src/views/__/design/*"],
      "pcwall/*": ["./src/views/pcwall/*"],
      "mobile/*": ["./src/views/mobile/*"]
    },
    "resolveJsonModule": true,
    // 要包含的类型声明文件名列表
    "types": [
      "node",
      "vite/client",
      "element-plus/global",
      "unplugin-icons/types/vue",
      "unplugin-vue-router/client"
    ],
    "allowImportingTsExtensions": true,
    /* Linting */
    "strict": true,
    "noFallthroughCasesInSwitch": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    // 从 tslib 导入辅助工具函数
    "importHelpers": true,
    "noEmit": true,
    // 生成相应的 .map文件
    "sourceMap": true,
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true,
    "skipLibCheck": true
  },
  "references": [
    {
      "path": "./tsconfig.node.json"
    }
  ],
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "tests/**/*.tsx",
    "types/**/*.d.ts",
    "src/views/__/design/layer/fire-ing/fighter.ts",
    "src/utils/useDraggable.js"
  ],
  "exclude": [
    "node_modules",
    "dist"
  ]
}
