import { merge } from 'lodash-es'
import { BisTypes } from '../../'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './piclottery-ing-setting.vue'
import Comp from './piclottery-ing.vue'

// 类型
export const type = 'piclottery-ing'

// 数据类型约束
export interface IDesignPiclotteryIng extends IDesignLayer {
  type: typeof type
  data: {
    // 每个网格单元的期望最大尺寸
    targetCellSize?: number
    // 动画模式
    animationMode?: string
    // 动画速度
    animationSpeed?: number
    placeholderHeadImg?: string // 占位头像
  }
}

export const DEFAULT_DATA: IDesignPiclotteryIng['data'] = {
  targetCellSize: 40, // 默认每个网格单元的期望最大尺寸
  animationMode: 'center-out', // 默认动画模式为随机
  animationSpeed: 2,
  placeholderHeadImg: new URL('./assets/placehoder.png', import.meta.url).href, // 默认占位头像
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.sportsIng,
    type,
    name: '动态照片墙',
    Comp,
    CompSetting,
    showInteractive: [InteractiveEnum.piclotteryv3],
    showType: ['pcwall'],
    status: ['ing', 'ready'],
    thumbnail: new URL('./piclottery-ing.png', import.meta.url).href,
    defaultData(options): IDesignPiclotteryIng {
      return merge({
        uuid: layerUuid(),
        name: '动态照片墙',
        type,
        style: {
          width: '1100px',
          height: '600px',
        },
        data: {},
      }, options as IDesignPiclotteryIng)
    },
  })
}
