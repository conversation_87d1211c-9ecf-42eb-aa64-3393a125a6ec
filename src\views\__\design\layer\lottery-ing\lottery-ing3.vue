<script setup lang="ts">
import type { IDesignLotteryIng3 } from './lottery-ing3'
import useThree from '@/views/__/three/useThree'
import BezierEasing from 'bezier-easing'
import gsap from 'gsap'
import { sample } from 'lodash-es'
import * as THREE from 'three'
import { useDesignState } from '../..'
import { defaultHeadCount, defaultPlaceHolderHeadImg, defaultrotateSpeed } from './lottery-ing3'

const layer = defineModel<IDesignLotteryIng3>('layer', { required: true })
const designState = useDesignState()
const status = computed(() => designState.status)

// 参与人奖池
const regeditList = computed(() => {
  return designState.getLayerData('regeditList') || []
})

const headCount = computed(() => layer.value.headCount ?? defaultHeadCount)
const rotateSpeed = computed(() => layer.value.rotateSpeed ?? defaultrotateSpeed)

const threeRef = ref<HTMLElement | null>(null)
const { scene, loadTexture, camera } = useThree({
  threeRef,
  hasHelp: false,
  hasControls: false,
})

const cache: Record<string, any> = {}
// 生成纹理
function createTexture(url: string) {
  let texture = cache[url]
  if (texture) {
    return texture
  }
  texture = loadTexture(url)
  cache[url] = texture
  return texture
}

class BaseShape {
  name = 'BaseShape'
  group: THREE.Group
  intervalHandler: NodeJS.Timeout | null = null
  constructor() {
    this.group = new THREE.Group()
    this.group.visible = false
    scene.add(this.group)
  }

  disposeItem(item: THREE.Object3D<THREE.Object3DEventMap>) {
    if (item instanceof THREE.Mesh || item instanceof THREE.Sprite) {
      item.material?.dispose()
      item.geometry?.dispose()
    } else if (item instanceof THREE.Group) {
      item.children.forEach((child) => {
        this.disposeItem(child)
      })
    }
  }

  init() {
    this.intervalHandler && clearInterval(this.intervalHandler)
    this.group.traverse((item) => {
      this.disposeItem(item)
    })
    this.group.clear()
  }

  play(person?: number) {
    console.log('play', person)
  }

  getEase(value = [0.66, 0.2, 0.45, 0.79] as [number, number, number, number]) {
    return BezierEasing(...value)
  }

  destory() {
    this.intervalHandler && clearInterval(this.intervalHandler)
    this.group.traverse((item) => {
      this.disposeItem(item)
    })
    this.group.clear()
    this.group.visible = false
  }
}

// 时空穿越
class ShapeTimeshift extends BaseShape {
  constructor() {
    super()
    this.name = 'ShapeGalaxy'
  }

  async init() {
    super.init()
    // 相机
    camera.fov = 100
    camera.position.y = 0
    camera.position.z = 1200
    camera.updateProjectionMatrix()
    camera.lookAt(0, 0, 0)
    // 可视区大小
    const count = headCount.value

    for (let i = 0; i < count; i++) {
      const itemData = getItem()

      if (!itemData) return
      const threeMesh = new THREE.Mesh(
        new THREE.CircleGeometry(60, 32),
        new THREE.MeshBasicMaterial({
          map: createTexture(itemData.avatar),
          fog: false,
          transparent: true,
        }),
      )
      const x = 1500 * Math.sin(Math.PI * 2 * Math.random())
      const y = 1500 * Math.cos(Math.PI * 2 * Math.random())
      threeMesh.position.set(x, y, -18000)
      threeMesh.scale.set(0.1, 0.1, 0.1)
      this.group.add(threeMesh)
    }
  }

  play() {
    const tl = gsap.timeline()
    tl.addLabel('start')
    this.group.children.forEach((object) => {
      const duration = (Math.random() * 10 + 10) / rotateSpeed.value
      tl.to(object.position, { z: 1300, repeat: Infinity, duration, ease: 'none' }, 'start')
      tl.to(object.scale, { x: 1.5, y: 1.5, z: 1.5, repeat: Infinity, duration, ease: 'none', onRepeat: () => {
        if (status.value !== 'ready') return
        if (Math.random() > 0.3 && object instanceof THREE.Mesh) {
          const itemData = getItem()
          if (!itemData) return
          object.material.map = createTexture(itemData.avatar)
        }
      } }, 'start')
    })
    if (status.value !== 'ready') {
      tl.progress(0.6)
    }
  }
}

const shapeObj = ref<BaseShape | null>(null)
async function runAnimit() {
  shapeObj.value = new ShapeTimeshift()
  shapeObj.value.group.visible = true
  shapeObj.value.init()
  shapeObj.value.play(20)
}

const defaultHeadImg = computed(() => {
  return layer.value.placeHolderHeadImg ?? defaultPlaceHolderHeadImg
})
function getItem() {
  if (regeditList.value.length === 0) {
    return {
      avatar: defaultHeadImg.value,
    }
  }
  const tem = sample(regeditList.value)
  const itemData = Object.assign({}, tem)
  return itemData
}

watch(
  () => [headCount.value, rotateSpeed.value, status.value],
  () => {
    shapeObj.value?.destory()
    runAnimit()
  },
  { deep: true, immediate: true },
)

onUnmounted(() => {
  shapeObj.value?.destory()
})
</script>

<template>
  <div class="lottery-ing3-box">
    <div ref="threeRef" class="three-box"></div>
  </div>
</template>

<style scoped lang="scss">
.lottery-ing3-box {
  width: 100%;
  height: 100%;
  .three-box {
    width: 100%;
    height: 100%;
  }
}
</style>
