import type { Page } from '@playwright/test'

export async function freezePageForSnapshot(page: Page) {
  // 1. 注入样式，禁用所有 CSS 动画与过渡
  await page.addStyleTag({
    content: `
      *, *::before, *::after {
        animation: none !important;
        transition: none !important;
        caret-color: transparent !important;
        scroll-behavior: auto !important;
      }
      html, body {
        animation: none !important;
        transition: none !important;
      }
    `,
  })

  // 2. JS 层处理：冻结 rAF、Three.js、Lottie、video、动态图像 + 冻结背景图
  await page.evaluate(async () => {
    const freezeImage = async (img: HTMLImageElement) => {
      const src = img.src
      img.crossOrigin = 'anonymous'
      if (!src.match(/\.(gif|png|webp)(\?|$)/i)) return
      if (img.getAttribute('data-frozen') === '1') return

      await new Promise<void>((resolve) => {
        if (img.complete && img.naturalWidth > 0) {
          requestAnimationFrame(() => resolve())
        } else {
          img.onload = () => requestAnimationFrame(() => resolve())
          img.onerror = () => resolve()
        }
      })

      try {
        const canvas = document.createElement('canvas')
        canvas.width = img.naturalWidth || img.width
        canvas.height = img.naturalHeight || img.height
        const ctx = canvas.getContext('2d', { alpha: true })

        ctx?.drawImage(img, 0, 0)

        try {
          const dataURL = canvas.toDataURL('image/png')
          img.src = dataURL
          img.setAttribute('data-frozen', '1')
        } catch {
          console.warn('[freezeImage] skipped tainted image:', src)
        }
      } catch (e) {
        console.warn('[freezeImage] failed on', src, e)
      }
    }

    const freezeBackgroundImages = async () => {
      const elements = Array.from(document.querySelectorAll<HTMLElement>('.design-image'))
      await Promise.all(elements.map(async (el) => {
        const style = window.getComputedStyle(el)
        const bgImage = style.backgroundImage

        if (bgImage.startsWith('url(')) {
          const urlMatch = bgImage.match(/url\(["']?(.*?)["']?\)/)
          if (!urlMatch) return
          const imageUrl = urlMatch[1]
          if (!imageUrl.match(/\.(gif|png|webp)(\?|$)/i)) return

          try {
            const img = new Image()
            img.crossOrigin = 'anonymous'
            img.src = imageUrl

            await new Promise<void>((resolve) => {
              if (img.complete && img.naturalWidth > 0) {
                requestAnimationFrame(() => resolve())
              } else {
                img.onload = () => requestAnimationFrame(() => resolve())
                img.onerror = () => resolve()
              }
            })

            const canvas = document.createElement('canvas')
            canvas.width = img.naturalWidth
            canvas.height = img.naturalHeight
            const ctx = canvas.getContext('2d')
            ctx?.drawImage(img, 0, 0)

            try {
              const dataURL = canvas.toDataURL('image/png')
              el.style.backgroundImage = `url('${dataURL}')`
            } catch {
              console.warn('[freezeBackground] tainted image', imageUrl)
            }
          } catch (err) {
            console.warn('[freezeBackground] failed', err)
          }
        }
      }))
    }

    const waitForImagesToLoadAndFreeze = async () => {
      const imgEls = Array.from(document.querySelectorAll('img')) as HTMLImageElement[]
      await Promise.all(imgEls.map(freezeImage))
    }

    const freezeAllVideos = () => {
      document.querySelectorAll('video').forEach((video) => {
        try {
          video.pause()
          video.currentTime = 0
        } catch (e) {
          console.warn('[freezeVideo] failed on', video, e)
        }
      })
    }

    await waitForImagesToLoadAndFreeze()
    freezeAllVideos()
    await freezeBackgroundImages()

    const observer = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        mutation.addedNodes.forEach((node) => {
          if (node instanceof HTMLImageElement) {
            freezeImage(node)
          } else if (node instanceof HTMLVideoElement) {
            try {
              node.pause()
              node.currentTime = 0
            } catch { }
          } else if (node instanceof HTMLElement) {
            node.querySelectorAll('img').forEach(img => freezeImage(img as HTMLImageElement))
            node.querySelectorAll('video').forEach((video) => {
              try {
                video.pause()
                video.currentTime = 0
              } catch { }
            })
          }
        })

        if (
          mutation.type === 'attributes'
          && mutation.attributeName === 'src'
          && mutation.target instanceof HTMLImageElement
        ) {
          freezeImage(mutation.target)
        }
      }
    })

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['src'],
    })
  })

  await page.waitForTimeout(300)
}
