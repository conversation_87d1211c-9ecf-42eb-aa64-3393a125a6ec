<script setup lang="ts">
import type { CascaderValue } from 'element-plus'
import type { IDesignLayerEventInteract } from '../../../types'
import { useWallStore } from '~/src/store/wall'
import { actList } from '../../../data/actList'

defineOptions({ label: '跳转互动' })

const route = useRoute()
const isAdmin = computed(() => route.path.startsWith('/admin/'))

const event = defineModel<IDesignLayerEventInteract>('event', { required: true })

const nowAct = computed({
  get() {
    const { module, moduleId } = event.value.value || {}
    return [module, moduleId].filter(Boolean) as string[]
  },
  set(val: string[]) {
    const [module, moduleId] = val
    event.value.value = { module, moduleId }
  },
})

async function fetchAdminActList(module: 'ninegrids' | 'wheelsurf' | 'packetwall' | 'mysterybox') {
  if (!isAdmin.value) return
  let list: any[] = await api.admin[module]?.list()
  if (!list?.length) {
    return
  }
  if (['packetwall', 'mysterybox'].includes(module)) {
    // 过滤掉不需要手机端参与的轮次
    list = list.filter(item => item.gameMode === 'MOBILE')
  }
  if (['ninegrids', 'wheelsurf'].includes(module)) {
    // 过滤掉不需要手机端参与的轮次
    list = list.filter((item) => {
      return item.screenstatusLimit === 'N' || (item.screenstatusLimit === 'Y' && item.gameMode === 'SAMETIME')
    })
  }
  return list
}

async function lazyLoad(node: any, resolve: any) {
  const { value } = node

  if (['ninegrids', 'wheelsurf', 'packetwall', 'mysterybox'].includes(value)) {
    if (node.children.length) {
      return resolve()
    }
    // 需要关联轮次。只有主办方才能关联，其他角色无法操作
    if (isAdmin.value) {
      const list = await fetchAdminActList(value)
      if (!list?.length) {
        resolve([])
        return
      }
      resolve(list.map(item => ({ value: item.id, label: item.title, leaf: true })))
    } else {
      resolve([])
    }

    return
  }

  resolve()
}

const cascaderActOptions = computed(() => {
  return actList.map(item => ({ value: item.actType, label: item.actName, leaf: !['wheelsurf', 'ninegrids', 'mysterybox', 'packetwall'].includes(item.actType) }))
})

const nowExpandModule = ref<string>('')

function onCreateAct() {
  const { wallConfig } = useWallStore()
  const matchs = location.href.match(/wallFlag=([^&]+)/) || []
  const wallFlag = wallConfig?.wallFlag || matchs[1]
  const url = `/pro/admin/wall/interact/${nowExpandModule.value}.html?wallFlag=${wallFlag}`
  window.open(url)
}

function cascaderExpandChange(value: CascaderValue) {
  nowExpandModule.value = (Array.isArray(value) ? value[0] : value) as string
}
</script>

<template>
  <div>
    <el-cascader
      v-model="nowAct"
      :props="{
        lazy: true,
        lazyLoad,
        multiple: false,
      }"
      :options="cascaderActOptions"
      placeholder="请选择互动"
      @expand-change="cascaderExpandChange"
    >
      <template #empty>
        <div v-if="isAdmin" class="w-120px flex flex-col items-center justify-center">
          <p class="mb-20px">选择的互动需要关联轮次才能使用</p>
          <el-button type="primary" @click="onCreateAct">
            点击去创建轮次
          </el-button>
        </div>
        <div v-else class="w-120px text-justify">
          当前互动需要关联轮次才能使用，需要主办方身份操作。请更换其他互动
        </div>
      </template>
    </el-cascader>
  </div>
</template>

<style scoped lang="scss">
.box {
  .item {
    border-radius: 6px;
    padding: 10px;
    background-color: #fff;
    &:not(:first-child) {
      margin-top: 10px;
    }
    p {
      font-size: 14px;
      color: #666;
    }
  }
}
.item-box {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 10px;
  padding: 0 10px;

  span {
    flex: 1;
    white-space: nowrap;
  }
}
</style>
