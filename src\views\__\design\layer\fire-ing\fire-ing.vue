<script setup lang="ts">
import type { Howl } from 'howler'
import type { IDesignFireIng } from './fire-ing'
import { useVisitType } from '@/hooks/visittype'
import { loadImg, timer } from '@/utils'
import TWEEN from '@tweenjs/tween.js'
import { useDocumentVisibility } from '@vueuse/core'
import { useImageInfo } from '~/src/hooks/useImageInfo'
import { playAudio } from '~/src/utils/audio'
import { defineCustomEmits, useDesignTemp } from '../../index'

const designTemp = useDesignTemp()
const layer = defineModel<IDesignFireIng>('layer', { required: true })

const customEmits = defineCustomEmits(layer)

const fighterDom = ref<HTMLElement>()

const visibility = useDocumentVisibility()
const { isMobileUse } = useVisitType()
const isMobile = computed(() => isMobileUse.value || (designTemp.showType === 'mobile'))
const scale = computed(() => isMobile.value ? 0.5 : 1)
type TimerOut = NodeJS.Timeout | undefined
interface BulletContInfo {
  w: number
  h: number
  imgUrl: string
  offsetH: number
}

interface AircraftInfo {
  imgUrl: string
  w: number
  h: number
}

interface EnemyInfo {
  imgUrl: string
  w: number
  h: number
  score: number
}

interface BulletInfo {
  imgUrl: string
  w: number
  h: number
  type: string
}

const fighterDomSize = useElementSize(fighterDom)
let animationId: any = null
// 分数变更回调
function updateCallback(score: any): void {
  customEmits('updateScore', score)
}
function bombCallback() { } // 怪物爆炸回调
// 切换枪回调
const bulltChangeCallback: (type: string) => void = () => { }
// 子弹信息
const bulletimgInfo = useImageInfo(computed(() => layer.value.bullet))
const bulletInfo: Ref< BulletInfo> = computed(() => {
  return {
    imgUrl: layer.value.bullet,
    w: bulletimgInfo.width.value * scale.value,
    h: bulletimgInfo.height.value * scale.value,
    type: 'DEFAULT',
  }
})
const bombImgs: Ref<string[]> = computed(() => layer.value.bombImgs)
// 飞机信息
const planeImg = useImageInfo(computed(() => layer.value.plane))
const planeInfo: Ref< AircraftInfo> = computed(() => {
  return {
    imgUrl: layer.value.plane,
    w: planeImg.width.value * scale.value,
    h: planeImg.height.value * scale.value,
  }
})
let enemyArr: EnemyInfo[] = []
const bulletSuperimgInfo = useImageInfo(computed(() => layer.value.bulletSuper))
const bulletContInfo: Ref<BulletContInfo> = computed(() => {
  return {
    w: bulletSuperimgInfo.width.value * scale.value || 0,
    h: bulletSuperimgInfo.height.value * scale.value || 0,
    imgUrl: layer.value.bulletSuper || '',
    offsetH: 10,
  }
})

const bulletSuper = computed(() => layer.value.bulletSuper)

let createArmyInterval: TimerOut

function rand(m: number, n: number, b = false): number {
  const result = Math.ceil(Math.random() * (n - m + 1) + m - 1)
  return b ? (Math.random() > 0.5 ? result : -result) : result
}

function randItem<T>(arr: T[]): T {
  return arr[rand(0, arr.length - 1)]
}

function getPath(offset = 0) {
  const stepY = 80
  const count = Math.ceil(fighterDomSize.height.value / stepY)
  const safe = fighterDomSize.width.value / 10
  const baseX = rand(safe, fighterDomSize.width.value - safe)
  const x0 = baseX + rand(safe / 2, safe, true)
  const y0 = -offset
  const xA: number[] = []
  const yA: number[] = []
  let tmp = 0

  for (let i = 1; i < count; i++) {
    const tmpX = baseX + rand(safe / 2, safe, true)
    xA.push(tmpX)
    if (
      i > count / 4
      && i < (count / 4) * 3
      && Math.random() > 0.6
      && Math.abs(tmp - tmpX) > safe
    ) {
      yA.push(Math.min((i - 1) * stepY - rand(stepY / 4, stepY / 2), fighterDomSize.height.value))
    } else {
      yA.push(Math.min(i * stepY, fighterDomSize.height.value))
    }
    tmp = tmpX
  }

  xA.push(Math.min(baseX + rand(safe / 2, safe, true), fighterDomSize.width.value))
  yA.push(fighterDomSize.height.value + offset)

  return { x0, y0, xA, yA }
}

class Base {
  static cacheObj: { [key: string]: Base[] } = {}
  el: HTMLElement = document.createElement('div')
  position = { t: 0, l: 0 }
  speed = 2
  survivalHandler: NodeJS.Timeout | undefined
  isDestoryed = false
  type: string
  size = { w: 50, h: 50 }

  constructor(type: string) {
    this.type = type
    this.position = { t: 0, l: 0 }
  }

  async destory() {
    if (this.isDestoryed) return
    await this.onDestory()
    this.isDestoryed = true
    try {
      fighterDom.value?.removeChild(this.el)
    } catch (error) {
      console.log(error)
    }
    if (Base.cacheObj[this.type]) {
      const index = Base.cacheObj[this.type].indexOf(this)
      if (index >= 0) {
        Base.cacheObj[this.type].splice(index, 1)
      }
    }
    clearInterval(this.survivalHandler)
  }

  getByType(type: string) {
    return Base.cacheObj[type] || []
  }

  setPosition(position: { t?: number, l?: number }) {
    if (position.t !== undefined) {
      this.position.t = position.t
      this.el.style.top = `${position.t}px`
    }
    if (position.l !== undefined) {
      this.position.l = position.l
      this.el.style.left = `${position.l}px`
    }
  }

  setSize(size: { w?: number, h?: number }) {
    if (size.w !== undefined) {
      this.size.w = size.w
      this.el.style.width = `${size.w}px`
    }
    if (size.h !== undefined) {
      this.size.h = size.h
      this.el.style.height = `${size.h}px`
      this.el.style.lineHeight = `${size.h}px`
    }
  }

  setContent(content: string) {
    this.el.innerHTML = content
  }

  survival() { }
  async onCreate() { }
  onDestory() { }
  async onMount() {
    this.el.classList.add('substance', this.type)
    fighterDom.value?.appendChild(this.el)

    let arr = Base.cacheObj[this.type]
    if (!arr) {
      arr = []
      Base.cacheObj[this.type] = arr
    }
    arr.push(this)
    this.survivalHandler = setInterval(() => {
      if (visibility.value === 'hidden') return
      this.survival()
    }, 100)
  }
}

class Aircraft extends Base {
  handler: TimerOut
  bulletChangeTimer: TimerOut
  normalTimer: TimerOut
  bulletType: string | undefined
  isDragging = false
  BulletCont: BulletCont | null = null
  imgUrl: string
  ingBulletChange: boolean
  bulletAudio: Howl | null = null
  bulletSuperAudio: Howl | null = null

  constructor(airInfo: AircraftInfo, { ingBulletChange }: { ingBulletChange: boolean }) {
    super('aircraft')
    this.imgUrl = airInfo.imgUrl
    this.ingBulletChange = ingBulletChange
    const { w, h } = airInfo
    this.setSize({ w, h })
    this.onCreate()
  }

  async onCreate() {
    this.setContent(`<img src="${this.imgUrl}">`)
    const x0 = fighterDomSize.height.value - this.size.h - 150
    const y0 = fighterDomSize.width.value / 2 - this.size.w / 2
    this.setPosition({ t: x0, l: y0 })
    await this.onMount()
    this.setBullet('normal')
    if (bulletInfo.value.type === 'DEFAULT' && bulletSuper.value) {
      this.bulletChangeTimer = setInterval(async () => {
        if (!bulletSuper.value) {
          this.bulletChangeTimer && clearInterval(this.bulletChangeTimer)
          clearInterval(this.normalTimer)
          return
        }
        if (visibility.value === 'hidden') return
        this.setBullet('continuous')
        this.normalTimer = setInterval(() => this.setBullet('normal'), 3000)
      }, 10000)
    }
    if (isMobile.value) {
      // 根据手势移动飞机
      let yDifference = 0
      let xDifference = 0
      useDraggable(this.el, {
        preventDefault: true,
        onStart: () => {
          this.isDragging = true
          if (!yDifference) {
            const rect = this.el.getBoundingClientRect()
            yDifference = rect.top - this.position.t
          }
          if (!xDifference) {
            const rect = this.el.getBoundingClientRect()
            xDifference = rect.left - this.position.l
          }
        },
        onMove: (position) => {
          this.move({
            x: position.x - xDifference,
            y: position.y - yDifference,
          })
        },
        onEnd: () => {
          this.isDragging = false
        },
      })
    } else {
      autoGo(this, x0, y0)
    }
  }

  move(position: { x: number, y: number }) {
    let t = Math.max(0, position.y)
    let l = Math.max(0, position.x)
    if (t + this.size.h > fighterDomSize.height.value) t = fighterDomSize.height.value - this.size.h
    if (l + this.size.w > fighterDomSize.width.value) l = fighterDomSize.width.value - this.size.w
    this.setPosition({ t, l })
    if (this.BulletCont) {
      this.BulletCont.setPosition({
        t: this.position.t - bulletContInfo.value.h + bulletContInfo.value.offsetH || 0,
        l: this.position.l + this.size.w / 2 - bulletContInfo.value.w / 2,
      })
    }
  }

  async setBullet(type: string) {
    bulltChangeCallback(type)
    this.bulletType = type
    this.BulletCont?.destory()
    this.BulletCont = null
    clearInterval(this.handler)
    clearInterval(this.normalTimer)

    if (type === 'continuous') {
      if (layer.value.bulletSuperAudio && !this.bulletSuperAudio) {
        this.bulletSuperAudio = playAudio(layer.value.bulletSuperAudio)
      }
      this.BulletCont = new BulletCont()
      this.bulletSuperAudio?.play()
    } else if (type === 'normal') {
      if (layer.value.bulletAudio && !this.bulletAudio) {
        this.bulletAudio = playAudio(layer.value.bulletAudio)
      }
      this.handler = setInterval(async () => {
        if (visibility.value === 'hidden') return
        const bullet = new Bullet()
        this.bulletAudio?.play()
        bullet.setPosition({
          t: this.position.t - bullet.size.h,
          l: this.position.l + (this.size.w - bullet.size.w) / 2,
        })
      }, 300)
    }
  }

  onDestory() {
    this.bulletChangeTimer && clearInterval(this.bulletChangeTimer)
    this.handler && clearInterval(this.handler)
    this.normalTimer && clearInterval(this.normalTimer)
  }
}

class Bullet extends Base {
  handler: TimerOut
  imgUrl: string

  constructor() {
    super('bullet')
    this.imgUrl = bulletInfo.value.imgUrl
    this.onCreate()
  }

  async onCreate() {
    const { w, h } = bulletInfo.value
    this.setSize({ w, h: h * 0.85 })
    this.setContent(`<img src="${this.imgUrl}">`)
    await this.onMount()

    this.handler = setInterval(() => {
      if (visibility.value === 'hidden') return
      this.setPosition({ t: this.position.t - 15 })
      if (this.position.t < -this.size.h) {
        clearInterval(this.handler)
        this.destory()
      }
    }, 40)
  }

  onDestory() {
    clearInterval(this.handler)
  }

  survival() {
    this.getByType('enemy').forEach((enemy: any) => {
      if (!enemy.isDestoryed && checkCollision(this, enemy) && !enemy.isDead) {
        this.destory()
        enemy.beHeart(1)
      }
    })
  }
}

class BulletCont extends Base {
  handler: TimerOut

  constructor() {
    super('bulletcont')
    this.onCreate()
  }

  async onCreate() {
    this.setSize({ w: bulletContInfo.value.w, h: bulletContInfo.value.h })
    this.setContent(`<img src="${bulletContInfo.value.imgUrl}">`)
    const { position, size } = Base.cacheObj.aircraft[0]
    this.setPosition({
      t: position.t - this.size.h + bulletContInfo.value.offsetH || 0,
      l: position.l + size.w / 2 - bulletContInfo.value.w / 2,
    })
    await this.onMount()
  }

  onDestory() { }

  survival() {
    this.getByType('enemy').forEach((enemy: any) => {
      if (!enemy.isDestoryed && checkCollision(this, enemy) && !enemy.isDead) {
        enemy.beHeart(1)
      }
    })
  }
}

class Enemy extends Base {
  // 被击中得分
  hitScore = 1
  // 和飞机碰撞得分
  collideScore = -1
  blood = 2
  imgUrl: string | null = null
  isDead = false
  tween: any = null
  bombAudios: Howl | null = null

  constructor(options: { score: number, collideScore: number }, enemyInfo: EnemyInfo) {
    super('enemy')
    this.imgUrl = enemyInfo.imgUrl
    this.speed = Number.parseInt((Math.random() * 5).toString())
    this.hitScore = options.score
    this.collideScore = options.collideScore
    this.setSize({ w: enemyInfo.w, h: enemyInfo.h })
    this.onCreate()
  }

  async dead() {
    if (!this.bombAudios && layer.value.bombAudios.length) {
      this.bombAudios = playAudio(randItem(layer.value.bombAudios))
    }

    this.bombAudios?.play()
    bombCallback()
    this.tween?.stop()
    this.isDead = true
    const img = bombImgs.value[rand(0, bombImgs.value.length - 1)]
    const zoom = this.size.w / 250
    this.setContent(`<div class="bomb" style="background-image:url(${img});zoom:${zoom}"></div>`)
    setTimeout(() => {
      this.destory()
      updateCallback(this.hitScore)
    }, 500)
  }

  beHeart(n = 1) {
    this.blood -= n
    if (this.blood <= 0) {
      this.dead()
    }
  }

  async onCreate() {
    this.setContent(`<img src="${this.imgUrl}">`)
    this.setPosition({
      t: -this.size.h,
      l: Math.random() * (fighterDomSize.width.value - this.size.w),
    })
    await this.onMount()
    const { x0, y0, xA, yA } = getPath(this.size.h)
    const baseSpeed = (10 - (layer.value.enemySpeed || 5)) * 1000
    this.tween = new TWEEN.Tween({ x: x0, y: y0 })
      .to({ x: xA, y: yA }, rand(Math.max(1000, baseSpeed - 3000), baseSpeed + 3000))
      .onUpdate((object: any) => {
        this.setPosition({ t: object.y, l: object.x - this.size.w / 2 })
      })
      .interpolation(TWEEN.Interpolation.CatmullRom)
      .easing(TWEEN.Easing.Linear.None)
      .start()
      .onComplete(() => this.destory())
  }

  onDestory() {
  }

  survival() {
    this.getByType('aircraft').forEach(async (aircraft) => {
      if (!aircraft.isDestoryed && checkCollision(this, aircraft) && !aircraft.el.classList.contains('airhead')) {
        updateCallback(this.collideScore)
        aircraft.el.classList.add('airhead')
        await timer(1000)
        aircraft.el.classList.remove('airhead')
      }
    })
  }
}

function checkCollision(rect1: Base, rect2: Base): boolean {
  const l1 = { x: rect1.position.l, y: rect1.position.t }
  const r1 = { x: rect1.position.l + rect1.size.w, y: rect1.position.t + rect1.size.h }
  const l2 = { x: rect2.position.l, y: rect2.position.t }
  const r2 = { x: rect2.position.l + rect2.size.w, y: rect2.position.t + rect2.size.h }
  if (l1.x > r2.x || l2.x > r1.x || l1.y > r2.y || l2.y > r1.y) return false
  return true
}

function getRadomAirPer(): number {
  return Math.max(Math.min(Math.random(), 0.85), 0.15)
}
function autoGo(aircraft: Aircraft, x0: number, y0: number) {
  const xA = getRadomAirPer() * fighterDomSize.width.value
  const yA = getRadomAirPer() * fighterDomSize.height.value
  new TWEEN.Tween({ x: x0, y: y0 })
    .to({ x: xA, y: yA }, rand(3000, 4500))
    .onUpdate((object: any) => {
      aircraft.move({ x: object.x, y: object.y })
    })
    .interpolation(TWEEN.Interpolation.CatmullRom)
    .easing(TWEEN.Easing.Linear.None)
    .start()
    .onComplete(() => autoGo(aircraft, xA, yA))
}

async function initEnemyData() {
  const arr: EnemyInfo[] = []
  layer.value.enemys.forEach(async (it: any) => {
    const imgInfo = await loadImg(it.img)
    arr.push({
      imgUrl: it.img,
      w: imgInfo.width * scale.value,
      h: imgInfo.height * scale.value,
      score: it.score,
    })
  })
  enemyArr = arr
}

let planeItem: Aircraft | null = null
async function initPlane() {
  planeItem?.destory()
  const planeImgInfo = await loadImg(layer.value.plane)
  planeInfo.value.w = planeImgInfo.width * scale.value
  planeInfo.value.h = planeImgInfo.height * scale.value
  planeItem = new Aircraft(planeInfo.value, { ingBulletChange: true })
}
async function fighterStart() {
  initPlane()
  initEnemyData()
  createArmyInterval = setInterval(() => {
    if (visibility.value === 'hidden') return
    const enemyDensity = computed(() => layer.value.enemyDensity)
    for (let i = 0; i < enemyDensity.value / 2; i++) {
      const enemyInfo = enemyArr[rand(0, enemyArr.length - 1)]

      enemyInfo && new Enemy({ collideScore: -3, score: enemyInfo.score }, enemyInfo)
    }
  }, 1000)
}

function FighterEnd() {
  animationId && cancelAnimationFrame(animationId)
  clearInterval(createArmyInterval!)
  Object.keys(Base.cacheObj).forEach((type) => {
    Base.cacheObj[type].forEach(item => item.destory())
  })
}

watch(
  () => [fighterDomSize],
  async () => {
    if (fighterDomSize.width.value === 0 || fighterDomSize.height.value === 0) return
    FighterEnd()
    animate()
    fighterStart()
  },
  {
    deep: true,
    immediate: true,
  },
)
watch (
  () => layer.value.enemys,
  async () => {
    initEnemyData()
  },
  {
    deep: true,
  },
)
watch(
  () => [layer.value.plane, layer.value.bulletSuper],
  async () => {
    initPlane()
  },
  {
    deep: true,
  },
)

function animate() {
  animationId = requestAnimationFrame(animate)
  TWEEN.update()
}

onMounted(() => {
})

onUnmounted(() => {
  FighterEnd()
})
</script>

<template>
  <div ref="fighterDom" class="fire-ing-box" @touchstart.stop.prevent></div>
</template>

<style scoped lang="scss">
.fire-ing-box {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;

  :deep() {
    .substance {
      text-align: center;
      display: inline-block;
      position: absolute;
      color: #fff;
    }
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
    .aircraft {
      z-index: 10;

      img {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }

    .airhead {
      animation: heated 1000ms infinite linear;
    }

    .bullet {
      z-index: 8;

      img {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }

    .bulletcont {
      z-index: 7;

      img {
        width: 100%;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }

    .enemy {
      z-index: 6;
      display: flex;
      justify-content: center;
      align-items: center;

      .bomb {
        width: 250px;
        height: 250px;
        background-repeat: no-repeat;
        background-position: center;
        background-size: contain;
        zoom: 0.4;
      }
    }
  }
  @keyframes heated {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
    100% {
      opacity: 1;
    }
  }
}
</style>
