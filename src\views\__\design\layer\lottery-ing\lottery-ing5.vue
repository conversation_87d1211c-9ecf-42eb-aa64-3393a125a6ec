<script setup lang="ts">
import type { IDesignLotteryIng5 } from './lottery-ing5'
import useThree from '@/views/__/three/useThree'
import BezierEasing from 'bezier-easing'
import gsap from 'gsap'
import { cloneDeep, sample } from 'lodash-es'
import * as THREE from 'three'
import { useDesignState } from '../..'
import { defaultanimateSpeed, defaultHeadSize, defaultPlaceHolderHeadImg } from './lottery-ing5'

const layer = defineModel<IDesignLotteryIng5>('layer', { required: true })
const designState = useDesignState()

// 参与人奖池
const regeditList = computed(() => {
  return designState.getLayerData('regeditList') || []
})

const headSize = computed(() => layer.value.headSize ?? defaultHeadSize)
const animateSpeed = computed(() => layer.value.animateSpeed ?? defaultanimateSpeed)
const maskImg = computed(() => layer.value.maskImg ?? '')

const threeRef = ref<HTMLElement | null>(null)
const { scene, loadTexture, camera } = useThree({
  threeRef,
  hasHelp: false,
  hasControls: false,
})

const cache: Record<string, any> = {}
// 生成纹理
function createTexture(url: string) {
  let texture = cache[url]
  if (texture) {
    return texture
  }
  texture = loadTexture(url)
  cache[url] = texture
  return texture
}

class BaseShape {
  name = 'BaseShape'
  group: THREE.Group
  intervalHandler: NodeJS.Timeout | null = null
  constructor() {
    this.group = new THREE.Group()
    this.group.visible = false
    scene.add(this.group)
  }

  disposeItem(item: THREE.Object3D<THREE.Object3DEventMap>) {
    if (item instanceof THREE.Mesh) {
      gsap.killTweensOf(item.scale)
      if (item.material instanceof THREE.ShaderMaterial) {
        gsap.killTweensOf(item.material.uniforms.opacity)
        item.material.uniforms.map?.value?.dispose()
        item.material.uniforms.maskMap?.value?.dispose()
        item.material.dispose()
      } else {
        item.material?.dispose()
      }
      item.geometry?.dispose()
    } else if (item instanceof THREE.Group) {
      item.children.forEach((child) => {
        this.disposeItem(child)
      })
    }
  }

  init() {
    this.intervalHandler && clearInterval(this.intervalHandler)
    this.group.traverse((item) => {
      this.disposeItem(item)
    })
    this.group.clear()
  }

  play() {
  }

  getEase(value = [0.66, 0.2, 0.45, 0.79] as [number, number, number, number]) {
    return BezierEasing(...value)
  }

  destory() {
    this.intervalHandler && clearInterval(this.intervalHandler)
    this.group.traverse((item) => {
      this.disposeItem(item)
    })
    this.group.clear()
    this.group.visible = false
  }
}

class ShapeMatrax extends BaseShape {
  tickerCallback: ((time: number, deltaTime: number) => void) | null = null // 用于存储 ticker 回调

  // --- 配置参数 ---
  gridDimensions = { wc: 5, hc: 5, dc: 10 } // 网格维度 (宽, 高, 深)
  gridSpacing = 40 * 3 // 网格间距
  cameraZ = 300 // 相机 Z 位置
  frontThresholdRelative = -30 // 元素消失/循环的 Z 轴阈值 (相对于相机)
  baseMoveDuration = 20 // 基础移动周期 (秒，大致穿越整个深度所需时间，会被 animateSpeed 调整)
  fadeInEndProgress = 0.3 // 淡入动画结束的位置比例 (0 到 1)
  fadeOutStartProgress = 0.95 // 中心元素淡出动画开始的位置比例 (0 到 1)
  initialScale = 0.1 // 初始缩放
  maxScale = 2 // 最大缩放
  centerToleranceFactor = 0.6 // 判断是否为中心元素的容差因子 (乘以 gridSpacing)
  alwaysFaceScreen = true // true则面向屏幕(XY平面)，false则面向摄像机
  maskTextureUrl = maskImg.value // 遮罩纹理 URL
  // ---

  maskTexture: THREE.Texture | null = null // 用于存储加载的遮罩纹理

  constructor() {
    super()
    this.name = 'cubematrix'
  }

  async loadMask() {
    if (this.maskTextureUrl) {
      try {
        this.maskTexture = await loadTexture(this.maskTextureUrl)
        this.maskTexture.wrapS = THREE.ClampToEdgeWrapping // 根据需要设置纹理环绕方式
        this.maskTexture.wrapT = THREE.ClampToEdgeWrapping
      } catch (error) {
        console.error('Failed to load mask texture:', error)
        this.maskTexture = null // 加载失败则不使用遮罩
      }
    } else {
      this.maskTexture = null
    }
  }

  async init() {
    super.init()
    await this.loadMask() // 加载遮罩纹理

    // 相机
    camera.fov = 70
    camera.position.set(0, 0, this.cameraZ)
    camera.updateProjectionMatrix()
    camera.lookAt(0, 0, 0)

    const { wc, hc, dc } = this.gridDimensions
    const size = this.gridSpacing

    for (let d = 0; d < dc; d++) {
      for (let h = 0; h < hc; h++) {
        for (let w = 0; w < wc; w++) {
          const itemData = getItem()
          if (!itemData) continue

          const material = new THREE.MeshBasicMaterial({
            map: createTexture(itemData.avatar),
            fog: false,
            transparent: true,
            alphaMap: this.maskTexture,
          })
          material.onBeforeCompile = (shader) => {
            shader.fragmentShader = shader.fragmentShader.replace(
              '#include <alphamap_fragment>',
              `
          #ifdef USE_ALPHAMAP
              float alpha = texture2D( alphaMap, vAlphaMapUv ).a;
              diffuseColor.a = alpha;
          #endif
        `,
            )
          }
          const threeObject = new THREE.Mesh(
            new THREE.PlaneGeometry(headSize.value, headSize.value),
            material,
          )
          // 计算初始位置
          const x = (w - wc / 2 + 0.5) * size
          const y = (h - hc / 2 + 0.5) * size
          const z = (d - dc / 2 + 0.5) * size // Z 轴从负到正排列
          threeObject.position.set(x, y, z)
          threeObject.scale.set(this.initialScale, this.initialScale, this.initialScale)

          // 根据配置决定初始朝向
          if (!this.alwaysFaceScreen) {
            threeObject.lookAt(camera.position)
          }
          // 如果 alwaysFaceScreen 为 true，则保持默认朝向（平行于XY平面）

          this.group.add(threeObject)
        }
      }
    }
  }

  animite() {
    // --- 从配置计算动画参数 ---
    const { dc } = this.gridDimensions
    const size = this.gridSpacing
    const totalDepth = dc * size
    const frontThreshold = this.cameraZ + this.frontThresholdRelative // 绝对 Z 阈值
    // 计算最后层后面的 Z 位置，确保元素生成在视图之外
    const backPosition = (0 - dc / 2 + 0.5) * size - size
    // 根据 animateSpeed 调整移动速度
    const effectiveDuration = this.baseMoveDuration / animateSpeed.value
    const moveSpeed = totalDepth / effectiveDuration // 每秒移动的距离
    const centerTolerance = size * this.centerToleranceFactor // 中心判断的具体像素容差

    // 移除旧的 ticker 回调
    if (this.tickerCallback) {
      gsap.ticker.remove(this.tickerCallback)
    }

    // 创建新的 ticker 回调
    this.tickerCallback = (time: number, deltaTime: number) => {
      if (!this.group.visible) return

      const dtSeconds = deltaTime / 1000 // 增量时间 (秒)

      this.group.children.forEach((object) => {
        if (!(object instanceof THREE.Mesh && object.material instanceof THREE.MeshBasicMaterial)) return

        const material = object.material as THREE.MeshBasicMaterial

        // 1. 更新 Z 轴位置
        object.position.z += moveSpeed * dtSeconds

        // 判断是否为中心元素
        const isCenter = Math.abs(object.position.x) < centerTolerance && Math.abs(object.position.y) < centerTolerance

        // 2. 检查是否超过前方阈值，需要循环
        if (object.position.z > frontThreshold) {
          // 重置到后方
          object.position.z = backPosition + (object.position.z - frontThreshold) % totalDepth // Use modulo for smoother looping if needed

          // 更新纹理 (更新 map uniform)
          const itemData = getItem()
          if (itemData) {
            material.map = createTexture(itemData.avatar)
          }

          // 重置缩放和透明度 uniform
          object.scale.set(this.initialScale, this.initialScale, this.initialScale)
          if (material.opacity) {
            material.opacity = this.initialScale
          }
        }

        // 3. 根据 Z 轴位置更新缩放和透明度
        const progress = THREE.MathUtils.inverseLerp(backPosition, frontThreshold, object.position.z)

        let scaleProgress = 0
        const growEndProgress = 0.7 // Point where max scale is reached
        if (progress < growEndProgress) {
          const growPhaseProgress = THREE.MathUtils.mapLinear(progress, 0, growEndProgress, 0, 1)
          scaleProgress = THREE.MathUtils.lerp(this.initialScale, this.maxScale, Math.sqrt(growPhaseProgress))
        } else {
          const shrinkPhaseProgress = THREE.MathUtils.mapLinear(progress, growEndProgress, 1, 0, 1)
          scaleProgress = THREE.MathUtils.lerp(this.maxScale, this.maxScale * 0.8, shrinkPhaseProgress)
        }
        scaleProgress = THREE.MathUtils.clamp(scaleProgress, this.initialScale, this.maxScale) // Ensure bounds
        object.scale.set(scaleProgress, scaleProgress, scaleProgress)

        let targetOpacity = 1.0
        if (progress < this.fadeInEndProgress) {
          const fadeIn = THREE.MathUtils.smoothstep(progress, 0, this.fadeInEndProgress) // Use smoothstep for smoother fade
          targetOpacity = THREE.MathUtils.lerp(this.initialScale, 1.0, fadeIn)
        } else if (isCenter && progress > this.fadeOutStartProgress) {
          const fadeOut = THREE.MathUtils.smoothstep(progress, this.fadeOutStartProgress, 1.0) // Use smoothstep
          targetOpacity = THREE.MathUtils.lerp(1.0, 0.0, fadeOut)
        }
        targetOpacity = THREE.MathUtils.clamp(targetOpacity, 0.0, 1.0)

        if (material.opacity) {
          material.opacity = targetOpacity
        }
      })
    }

    // 添加 ticker 回调
    gsap.ticker.add(this.tickerCallback)
  }

  play() {
    this.group.visible = true
    this.animite() // 启动动画循环
  }

  disposeItem(item: THREE.Object3D<THREE.Object3DEventMap>) {
    if (item instanceof THREE.Mesh) {
      item.material?.dispose()
      item.geometry?.dispose()
    } else if (item instanceof THREE.Group) {
      item.children.forEach((child) => {
        this.disposeItem(child)
      })
    }
  }

  destory() {
    if (this.tickerCallback) {
      gsap.ticker.remove(this.tickerCallback)
      this.tickerCallback = null
    }

    gsap.killTweensOf(this.group.children.map(child => child.position).flat())
    gsap.killTweensOf(this.group.children.map(child => child.scale).flat())
    gsap.killTweensOf(this.group.children
      .filter(child => child instanceof THREE.Mesh || child instanceof THREE.Sprite)
      .map(child => (child as THREE.Mesh | THREE.Sprite).material)
      .flat())

    this.maskTexture?.dispose()
    this.maskTexture = null

    this.intervalHandler && clearInterval(this.intervalHandler)
    this.group.traverse((item) => {
      this.disposeItem(item)
    })
    this.group.clear()
    this.group.visible = false
  }
}

const shapeObj = ref<BaseShape | null>(null)
async function runAnimit() {
  shapeObj.value = new ShapeMatrax()
  shapeObj.value.group.visible = true
  shapeObj.value.init()
  shapeObj.value.play()
}
const defaultHeadImg = computed(() => {
  return layer.value.placeHolderHeadImg ?? defaultPlaceHolderHeadImg
})
function getItem() {
  if (regeditList.value.length === 0) {
    return {
      avatar: defaultHeadImg.value,
    }
  }
  const tem = cloneDeep(sample(regeditList.value))
  if (!tem.avatar) tem.avatar = defaultHeadImg.value
  const itemData = Object.assign({}, tem)
  return itemData
}

watch(
  () => [headSize.value, animateSpeed.value, maskImg.value],
  () => {
    shapeObj.value?.destory()
    runAnimit()
  },
  { deep: true, immediate: true },
)

onUnmounted(() => {
  shapeObj.value?.destory()
})
</script>

<template>
  <div class="lottery-ing5-box">
    <div ref="threeRef" class="three-box"></div>
  </div>
</template>

<style scoped lang="scss">
.lottery-ing5-box {
  width: 100%;
  height: 100%;
  .three-box {
    width: 100%;
    height: 100%;
  }
}
</style>
