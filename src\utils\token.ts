import type { AxiosRequestConfig } from 'axios'
import { useWallStore } from '../store/wall'

export function setupToken(config: AxiosRequestConfig = {}) {
  if (config?.headers) {
    const { wallConfig } = useWallStore()
    const url = location.href
    const isAdmin = url.includes('/admin/')
    const isPcwall = url.includes('/pcwall/') || (isAdmin && config.url?.startsWith('/pro/hxc/web/'))
    const isMobile = url.includes('/mobile/') || (isAdmin && config.url?.startsWith('/pro/hxc/mobile/'))
    if (isPcwall) {
      const matchs = url.match(/wallFlag=([^&]+)/) || []
      config.headers.wf = wallConfig?.wallFlag || matchs[1]
    } else if (isMobile) {
      const matchs = url.match(/mobileFlag=([^&]+)/) || []
      config.headers.mf = wallConfig?.mobileFlag || matchs[1]
    }
  }
}
