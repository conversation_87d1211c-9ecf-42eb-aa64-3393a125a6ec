import type { App } from 'vue'
import NProgress from 'nprogress'
import { createRouter, createWebHistory } from 'vue-router'
import { routes } from 'vue-router/auto-routes'

NProgress.configure({ showSpinner: false, minimum: 0.2 })

declare module 'vue-router' {
  interface RouteMeta {
    label?: string
  }
}

const layoutTitle: { [k: string]: string } = {
  test: '测试系统',
  admin: '页面设计',
  pcwall: '大屏幕',
  mobile: '移动端',
}

const router = createRouter({
  routes,
  history: createWebHistory('/next'),
})

router.beforeEach(async (to, from, next) => {
  NProgress.start()
  const label = to.meta.label
  const path = to.path
  const alias = layoutTitle[path.split('/')[1]]

  const titleArr: string[] = []
  if (label) {
    titleArr.push(label)
  }
  if (alias) {
    titleArr.push(alias)
  }
  document.title = titleArr.length ? titleArr.join(' | ') : ''

  next()
})
router.afterEach(() => {
  NProgress.done()
})

export function setupRouter(app: App) {
  app.use(router)
}
