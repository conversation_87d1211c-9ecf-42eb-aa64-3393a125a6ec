<script setup lang="ts">
import type { CSSProperties } from 'vue'
import type { IDesignText } from './text'
import DOMPurify from 'dompurify'
import { cloneDeep } from 'lodash-es'
import { hasAuth } from '~/src/utils/auth'
import { injectScale, useDesignState, useDesignTemp } from '../..'
import { defaultStyle } from './text'

const layer = defineModel<IDesignText>('layer', { required: true })
const scale = injectScale()
const designTemp = useDesignTemp()
const designState = useDesignState()

const textRef = ref<HTMLElement>()
const textSize = useElementSize(textRef)

watch(
  () => [textSize.width.value, textSize.height.value],
  () => {
    const key = layer.value.style.writingMode ? 'width' : 'height'
    const dom = layer.value.$dom as HTMLElement
    // fix: section 节点尺寸不会刷新，强制刷新，暂时只处理 width
    if (key === 'width' && dom) {
      const old = dom.style[key]
      dom.style[key] = '1px'
      dom.getBoundingClientRect()
      dom.style[key] = old
    }
    designTemp.bus.emit(`${layer.value.uuid}:moveable:updateRect`)
  },
)

const isEditing = ref(false)
function settingCursor() {
  const dom = textRef.value as HTMLElement
  if (!dom)
    return
  dom.focus()
  const range = document.createRange()
  range.selectNodeContents(dom)
  range.collapse(false)
  const sel = window.getSelection()
  sel?.removeAllRanges()
  sel?.addRange(range)
}

// 编辑和保存使用
const originHtml = computed(() => {
  let data = cloneDeep(layer.value.data)
  data = DOMPurify.sanitize(data, {
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: [],
  })
  return data
})
const renderHtml = computed(() => {
  let data = cloneDeep(originHtml.value)
  // 占位符替换（编辑中禁止使用，因为会引起响应失效）
  for (const [key, value] of Object.entries(designState.layerData)) {
    const val = unref(value)
    if (val !== undefined) {
      data = data.replace(new RegExp(key, 'g'), val)
    }
  }
  return data
})

watch(isEditing, async (v) => {
  if (!textRef.value) return
  if (v) {
    textRef.value.innerHTML = originHtml.value
    await nextTick()
    settingCursor()
  } else {
    textRef.value.innerHTML = renderHtml.value
  }
})

watch(
  () => [textRef.value, renderHtml.value] as const,
  ([dom, v]) => {
    if (!dom) return
    dom.innerHTML = v
  },
  { immediate: true },
)

const textStyle = computed(() => {
  const style: CSSProperties = {}
  const { fontSize, color, lineHeight, padding, textAlign } = layer.value.style
  if (fontSize === undefined) {
    style.fontSize = `${defaultStyle.fontSize * scale.value}px`
  }
  if (color === undefined) {
    style.color = defaultStyle.color
  }
  if (lineHeight === undefined) {
    style.lineHeight = defaultStyle.lineHeight
  }
  if (padding === undefined) {
    style.padding = `${Number.parseInt(defaultStyle.padding) * scale.value}px`
  }
  if (textAlign === undefined) {
    style.textAlign = defaultStyle.textAlign
  }
  return style
})

async function dblclickFn() {
  if (!designTemp.isEdit) return
  if (!hasAuth(['man', 'oem', 'spo.hi.vip', 'spo.ft']) && !layer.value.spoVisible) return
  isEditing.value = true
  await nextTick()

  // 焦点
  const dom = textRef.value as HTMLElement
  if (!dom) return
  dom.focus()
  await nextTick()

  // 全选
  const range = document.createRange()
  range.selectNodeContents(dom)
  const sel = window.getSelection()
  sel?.removeAllRanges()
  sel?.addRange(range)
}
function blurFn() {
  // 取消选中
  isEditing.value = false
  const sel = window.getSelection()
  sel?.removeAllRanges()

  const dom = textRef.value as HTMLElement
  if (!dom) return
  layer.value.data = dom.innerHTML
}
function wheelFn(e: MouseEvent) {
  // 判断是否有滚动条， 有滚动条阻止冒泡
  const dom = textRef.value as HTMLElement
  if (!dom) return
  if (dom.scrollHeight > dom.clientHeight) {
    e.stopPropagation()
  }
}
function pasteFn(e: ClipboardEvent) {
  e.preventDefault()

  const clipboardData = e.clipboardData
  if (!clipboardData) return
  const plainText = clipboardData.getData('text/plain')

  const selection = window.getSelection()
  if (!selection) return
  if (selection.rangeCount > 0) {
    selection.deleteFromDocument()
    const textNode = document.createTextNode(plainText)
    selection.getRangeAt(0).insertNode(textNode)
    selection.collapseToEnd()
  }
}

onMounted(() => {
  const dom = textRef.value as HTMLElement
  if (!dom) return
  dom.addEventListener('keydown', (event) => {
    if (event.key === 'Enter') {
      event.preventDefault() // 阻止默认行为
      document.execCommand('insertLineBreak') // 插入 <br> 标签
    }
  })
})
</script>

<template>
  <div
    ref="textRef"
    class="text-box"
    :contenteditable="isEditing"
    :style="textStyle"
    @dblclick="dblclickFn"
    @wheel="wheelFn"
    @blur="blurFn"
    @paste="pasteFn"
  />
</template>

<style scoped lang="scss">
.text-box {
  user-select: text;
  width: auto;
  height: auto;
  word-break: break-all;
  outline: 0;
  overflow: hidden;
  white-space: pre-wrap;
}
</style>
