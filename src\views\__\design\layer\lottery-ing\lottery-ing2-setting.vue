<script setup lang="ts">
import type { IDesignLotteryIng2 } from './lottery-ing2'
import { getDefaultMaterial, openSelectMaterial, useDataAttr } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'
import { defaultPlaceHolderHeadImg, defaultrotateSpeed, defaultShowCube, defaultWallHeadSize } from './lottery-ing2'

const layer = defineModel<IDesignLotteryIng2>('layer', { required: true })

const wallHeadSizeBlind = useDataAttr(layer.value, 'wallHeadSize', defaultWallHeadSize)
const rotateSpeedBlind = useDataAttr(layer.value, 'rotateSpeed', defaultrotateSpeed)
const showCubeBlind = useDataAttr(layer.value, 'showCube', defaultShowCube)

const placeHolderHeadImgBlind = computed(() => {
  return layer.value.placeHolderHeadImg ?? defaultPlaceHolderHeadImg
})

type IType = 'placeHolderHeadImg'
async function updateMaterialFn(name: IType, isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, `${name}`)
    : await openSelectMaterial('PIC')
  if (result) {
    layer.value[name] = result
  } else if (isReset) {
    delete layer.value[name]
  }
}
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>墙上头像高度</h3>
        <el-input-number v-model="wallHeadSizeBlind" v-input-number :max="1000" :min="100" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>转动速度</h3>
        <el-input-number v-model="rotateSpeedBlind" v-input-number :max="30" :min="0" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>显示中间立方体</h3>
        <el-switch v-model="showCubeBlind" />
      </div>
      <div class="setting-item mt-5!">
        <h3>头像默认图</h3>
        <div class="flex items-end">
          <div class="bgblank relative h-50 w-64">
            <MaterialThumbnail @select="updateMaterialFn('placeHolderHeadImg')" @reset="updateMaterialFn('placeHolderHeadImg', true)">
              <img v-if="placeHolderHeadImgBlind" :src="placeHolderHeadImgBlind" alt="" class="h-full w-full object-cover" />
            </MaterialThumbnail>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>
