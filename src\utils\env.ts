// const userAgent = navigator.userAgent

let isOem = false
const envProject = import.meta.env.VITE_PROJECT

if (import.meta.env.MODE === 'dev' && envProject === 'oem') {
  isOem = true
} else {
  // 根据域名自动判断
  const hostname = window.location.hostname
  if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname.startsWith('192.168.')) {
    isOem = false
  } else {
    isOem = !(hostname.startsWith('manage') || hostname.endsWith('.hixianchang.com'))
  }
}

export const envUtils = {
  isDev: import.meta.env.MODE === 'dev' || import.meta.env.DEV,
  isUat: import.meta.env.MODE === 'uat',
  isPro: import.meta.env.MODE === 'pro',
  isOem,
  resdomain: isOem ? import.meta.env.VITE_APP_OEM_RESDOMAIN : import.meta.env.VITE_APP_RESDOMAIN, // 静态资源域名
  // @ts-ignore 是否e2e测试
  isPlayWright: !!window.__IS_PLAYWRIGHT__,
}
