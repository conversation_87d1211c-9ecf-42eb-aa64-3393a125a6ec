import type { IDesignLayer, IDesignSetup } from '../../types'
import { BisTypes, useDesignData } from '../..'
import { InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './lottery-ing1-setting.vue'
import Comp from './lottery-ing1.vue'
// 类型
export const type = 'lottery-ing1'
export const defaultHeadCount = 80
export const defaultrotateSpeed = 5
export const defaultHeadSize = 25
export const defaultPlaceHolderHeadImg = new URL('./assets/placehoder.png', import.meta.url).href
// 数据类型约束
export interface IDesignLotteryIng1 extends IDesignLayer {
  type: typeof type
  headCount?: number
  headSize?: number
  rotateSpeed?: number
  placeHolderHeadImg?: string
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.sportsIng,
    showType: ['pcwall'],
    status: ['ing', 'ready'],
    showInteractive: [InteractiveEnum.lotteryv3],
    type,
    name: '抽奖3d效果',
    thumbnail: new URL('./lottery-ing1.png', import.meta.url).href,
    Comp,
    CompSetting,
    defaultData() {
      const designData = useDesignData()
      const [width, height] = designData.option.drafts
      return {
        type,
        uuid: layerUuid(),
        name: '抽奖3d效果',
        style: {
          width: `${width}px`,
          height: `${height}px`,
          top: 0,
          left: 0,
        },
      }
    },
  })
}
