<script setup lang="ts">
import type { IDesignSwiper } from './swiper'
import { timer } from '~/src/utils'
import { useDesignTemp } from '../..'
import { toAct, toLink } from '../../utils'
import { defaultAutoPlay, defaultDelay, defaultShowPagination, specialModuleList } from './swiper'

const layer = defineModel<IDesignSwiper>('layer', { required: true })

const designTemp = useDesignTemp()

const domRef = ref<HTMLElement>()
const swiperRef = ref<HTMLElement>()

const autoplay = computed(() => {
  const autoPlayOpen = layer.value.autoPlay ?? defaultAutoPlay
  if (!autoPlayOpen) return 0
  const delay = layer.value.delay ?? defaultDelay
  return delay * 1000
})

const allowTouchMove = computed(() => {
  if (designTemp.isEdit) return false
  return layer.value.swiperData.length > 1
})

const showPagination = computed(() => {
  return layer.value.showPagination ?? defaultShowPagination
})
async function openBigImg(index: number) {
  if (document.querySelector('#swiper_bigImg')) return
  // 插入body下一个节点，用来展示大图
  const bigImg = document.createElement('div')
  bigImg.id = 'swiper_bigImg'
  bigImg.className = 'absolute top-0 left-0 w-full h-full bg-black bg-opacity-60 flex justify-center items-center z-100'
  bigImg.innerHTML = `<img src="${layer.value.swiperData[index].img}" alt="bigImg" />`
  // const rootDom = document.querySelector('[data-id="design_page"]')
  const rootDom = document.querySelector('body')
  if (rootDom) {
    rootDom.appendChild(bigImg)
  }
  await timer(100)
  bigImg.addEventListener('click', () => {
    bigImg.remove()
  })
}

function click(event: any) {
  if (designTemp.isEdit) return false
  const target = event.target as HTMLElement | null
  if (!target) return
  const index = target?.getAttribute('data-id') || (target?.parentNode as HTMLElement)?.getAttribute('data-id')
  const itemData = layer.value.swiperData[index as unknown as number]
  const linkType = itemData.linkType
  if (linkType === 'big') {
    openBigImg(index as unknown as number)
  }
  if (linkType === 'url') {
    toLink(itemData.linkUrl, true)
  }
  if (linkType === 'page') {
    if (specialModuleList.includes(itemData.linkPage)) {
      if (!itemData.linkPageRoundId) {
        console.log('无轮次')
        return
      }
      toAct(itemData.linkPage, { id: itemData.linkPageRoundId as string })
    } else {
      toAct(itemData.linkPage)
    }
  }
}

const wrapDomSize = useElementSize(domRef)
const swiperHeight = computed(() => {
  return wrapDomSize.height.value
})
const swiperWidth = computed(() => {
  return wrapDomSize.width.value
})
onMounted(() => {
})
</script>

<template>
  <div ref="domRef" class="swiper-box">
    <div ref="swiperRef" class="swiper">
      <van-swipe :loop="true" :show-indicators="showPagination" :autoplay="autoplay" :touchable="allowTouchMove" :height="swiperHeight" :width="swiperWidth" :vertical="layer.direction === 'vertical'">
        <van-swipe-item v-for="(item, index) in layer.swiperData" :key="index" class="swiper-slide" :data-id="index" @click="click">
          <img class="item" :src="item.img" />
        </van-swipe-item>
      </van-swipe>
    </div>
  </div>
</template>

<style scoped lang="scss">
.swiper-box {
  width: 100%;
  height: 100%;
}
.swiper {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.van-swipe {
  width: 100%;
  height: 100%;
}
.swiper-slide {
  width: 100%;
  height: 100%;
}
.item {
  width: 100%;
  height: 100%;
}
</style>
