<script setup lang="ts">
import type { IDesignGroup } from './group'
import { injectScale, useDataAttr } from '../..'
import { defaultOverflow } from './group'
import HiImageSetting from './template/image-setting.vue'
import HiShapeSetting from './template/shape-setting.vue'
import HiTextRichSetting from './template/text-rich-setting.vue'
import HiTextSetting from './template/text-setting.vue'

type ITemplateType = 'text' | 'text-rich' | 'image' | 'shape'
const templateSetting: Record<ITemplateType, any> = {
  'image': HiImageSetting,
  'shape': HiShapeSetting,
  'text': HiTextSetting,
  'text-rich': HiTextRichSetting,
}
const layer = defineModel<IDesignGroup>('layer', { required: true })
const scale = injectScale()

const isTemplate = computed(() => !!layer.value.templateId)

const overflowValues = ref([
  { label: '隐藏', value: 'hidden' },
  { label: '自动', value: 'auto' },
  { label: '显示', value: 'scroll' },
])
const layoutValues = ref([
  { label: '自动', value: 'auto' },
  { label: '手动', value: 'manual' },
])
const layoutModel = ref<string>(layer.value.layout ? 'manual' : 'auto')
const directions = ref([
  { label: '垂直', value: 'column' },
  { label: '水平', value: 'row' },
])

let tmp: any = null
watch(layoutModel, (v) => {
  if (v === 'auto') {
    tmp = layer.value.layout
    delete layer.value.layout
  } else {
    layer.value.layout = tmp || {
      direction: 'column',
      gap: 0,
      items: {},
    }
  }
})

const overflowXBind = useDataAttr(layer.value.style, 'overflowX', defaultOverflow)
const overflowYBind = useDataAttr(layer.value.style, 'overflowY', defaultOverflow)

interface LayoutItem { uuid: string, name: string, flex: number | undefined }
const layoutItemValues = ref<LayoutItem[]>([])

//
watch(
  () => layer.value.layers,
  (v) => {
    const layoutItems = layer.value.layout?.items || {}
    const tmp: LayoutItem[] = []
    for (const item of v) {
      tmp.push({ uuid: item.uuid, name: item.name, flex: layoutItems[item.uuid]?.flex })
    }
    if (JSON.stringify(tmp) === JSON.stringify(layoutItemValues.value)) return
    layoutItemValues.value = tmp
  },
  { immediate: true, deep: true },
)
watch(
  () => layoutItemValues.value,
  (v) => {
    if (!layer.value.layout) return
    const tmp: Record<string, { flex: number }> = {}
    v.forEach((item) => {
      if (item.flex) {
        tmp[item.uuid] = { flex: item.flex }
      }
    })
    layer.value.layout.items = tmp
  },
  { deep: true },
)

// 模板名称
const templateLabelName: Record<string, number> = {}
function getLabel(type: string) {
  let idx = templateLabelName[type]
  if (idx) {
    templateLabelName[type] = idx + 1
  } else {
    templateLabelName[type] = 1
  }
  idx = templateLabelName[type]
  switch (type) {
    case 'text':
      return `文字${idx}`
    case 'text-rich':
      return `富文本${idx}`
    case 'image':
      return `图片${idx}`
    case 'shape':
      return `形状${idx}`
    default:
      return ''
  }
}
</script>

<template>
  <div class="setting-block" :style="{ '--design-scale': scale }">
    <template v-if="isTemplate">
      <component
        :is="templateSetting[item.type as ITemplateType]"
        v-for="(item, idx) in layer.layers"
        :key="item.uuid"
        v-model:setting="layer.layers[idx]"
        :label="getLabel(item.type)"
      />
    </template>
    <template v-else>
      <div class="setting-wrap">
        <h3>滚动条</h3>
        <div class="setting-item justify-end!">
          <span class="mx-10">横向</span>
          <el-select v-model="overflowXBind" placeholder="请选择" class="w-60">
            <el-option v-for="item in overflowValues" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <span class="mx-10">纵向</span>
          <el-select v-model="overflowYBind" placeholder="请选择" class="w-60">
            <el-option v-for="item in overflowValues" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
      </div>
      <div class="setting-wrap">
        <div class="flex items-center justify-between">
          <h3>布局</h3>
          <el-radio-group v-model="layoutModel">
            <el-radio-button
              v-for="item of layoutValues"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-radio-group>
        </div>
        <div v-if="layer.layout?.direction" class="setting-item justify-end!">
          <span class="mx-10">方向</span>
          <el-select v-model="layer.layout!.direction" placeholder="请选择" class="w-60">
            <el-option v-for="item in directions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div v-if="layer.layout" class="setting-item justify-end!">
          <span class="mx-10">间隔</span>
          <el-input-number v-model="layer.layout.gap" v-input-number :min="0" :step="1" controls-position="right" />
        </div>
        <div v-if="layoutModel === 'manual'" class="layout-setting">
          <div v-for="item in layoutItemValues" :key="item.uuid" class="item">
            <div class="label">{{ item.name }}</div>
            <el-input-number v-model="item.flex" v-input-number :step="1" controls-position="right" />
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<style scoped lang="scss">
.layout-setting {
  width: 80%;
  margin-left: 20%;
  .item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;
    .label {
      flex: 1;
      text-align: right;
    }
  }
}
</style>
