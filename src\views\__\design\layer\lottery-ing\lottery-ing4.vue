<script setup lang="ts">
import type { IDesignLotteryIng4 } from './lottery-ing4'
import { loadImg } from '@/utils'
import { sample } from 'lodash-es'
import { injectScale, useDesignState, useDesignTemp } from '../..'
import { defaultDecorateAnimation, defaultGapColumn, defaultGapRow, defaultHeadSize, maxCout } from './lottery-ing4'

interface JoinItem {
  name: string
  avatar: string
  id: string | number
  img: HTMLImageElement | null
  decorateImg: HTMLImageElement | null
  angle: number
  updateTime: number
}
const designTemp = useDesignTemp()
const isDesignEdit = designTemp.isEdit

const layer = defineModel<IDesignLotteryIng4>('layer', { required: true })
const scale = injectScale()
const canvasRef = ref<HTMLCanvasElement | null>(null)
const ctx = ref<CanvasRenderingContext2D | null>(null)
const canvasSize = useElementSize(canvasRef, undefined, { box: 'border-box' })

const designState = useDesignState()
// 参与人奖池
const regeditList = computed(() => {
  return designState.getLayerData('regeditList') || []
})

const onceNum = computed(() => {
  return designState.getLayerData('#每次抽取人数#')
})

const gapColumn = computed(() => layer.value.gapColumn ?? defaultGapColumn)

const headSize = computed(() => layer.value.headSize ?? defaultHeadSize)
const gapRow = computed(() => layer.value.gapRow ?? defaultGapRow)
const decorateAnimation = computed(() => layer.value.decorateAnimation ?? defaultDecorateAnimation)

const decorateImg = ref<HTMLImageElement | null>(null)
const updateTimer = ref<NodeJS.Timeout | null>(null)
const animationId = ref<number | null>(null)

const headCountObj = computed(() => {
  let headCount = 0
  let gridCols = 0 // 列数
  let gridRows = 0 // 行数

  if (canvasSize.width.value && canvasSize.height.value) {
    const canvasWidth = canvasSize.width.value / scale.value
    const canvasHeight = canvasSize.height.value / scale.value

    gridCols = Math.floor((canvasWidth + gapColumn.value) / (headSize.value + gapColumn.value))
    gridRows = Math.floor((canvasHeight + gapRow.value) / (headSize.value + gapRow.value))
    headCount = gridCols * gridRows
  }
  if (!isDesignEdit) {
    headCount = onceNum.value || 0
    // 不能超过headCount
    if (headCount > gridCols * gridRows) {
      headCount = gridCols * gridRows
    } else {
      // 少于最大值，居中排列算出几排几列，
      gridRows = Math.ceil(headCount / gridCols) // 行数
      gridCols = Math.ceil(headCount / gridRows) // 列数
    }
    console.log('headCount', headCount, gridRows, gridCols)
  }
  maxCout[layer.value.uuid] = headCount
  return {
    headCount,
    gridCols,
    gridRows,
  }
})

const defaultAvatar = new URL('./assets/default.png', import.meta.url).href
const itemDataList = ref<JoinItem[]>([])

// 创建新的随机用户数据
function getItem() {
  const tem = sample(regeditList.value)
  const itemData = Object.assign({}, tem)
  return itemData
}

/// 初始化用户数据并预加载图片
async function initItems() {
  try {
    // 创建所有需要的 item 对象
    const newItems = Array.from(
      { length: headCountObj.value.headCount },
      () => getItem(),
    )

    // 并行加载所有头像和装饰图片
    await Promise.all(
      newItems.map(async (item) => {
        try {
          // 并行加载头像和装饰图片
          const loadPromises = [
            loadImg(item.avatar || defaultAvatar),
          ]
          // 如果有装饰图片，也添加到并行加载中
          if (layer.value.decorateImg) {
            loadPromises.push(loadImg(layer.value.decorateImg))
          }
          // 等待所有图片加载完成
          const [avatarImg, decorateImg] = await Promise.all(loadPromises)
          // 设置加载好的图片
          item.img = avatarImg
          if (decorateImg) {
            item.decorateImg = decorateImg
          }
        } catch (err) {
          console.error('Failed to load individual image:', err)
          // 加载失败时尝试使用默认头像
          try {
            item.img = await loadImg(defaultAvatar)
          } catch {
            // 如果默认头像也加载失败，保持为null
          }
        }
      }),
    )

    // 一次性更新整个数组
    itemDataList.value = newItems
  } catch (error) {
    console.error('Failed to initialize items:', error)
  }
}
// 更新用户数据

// const avatarObj = ref<JoinItem | null>(null)
async function updateRandomItem() {
  if (headCountObj.value.headCount === 0) return

  try {
    // 创建所有新的 item 对象
    const newItems = Array.from({ length: headCountObj.value.headCount }, () => getItem())

    // 并行加载所有头像
    await Promise.all(newItems.map(async (item) => {
      try {
        item.img = await loadImg(item.avatar || defaultAvatar)
      } catch (err) {
        console.error('Failed to load individual image:', err)
        // 单个图片加载失败时尝试使用默认头像
        try {
          item.img = await loadImg(defaultAvatar)
        } catch {
          // 如果默认头像也加载失败，保持为null
        }
      }
    }))

    // 一次性更新整个数组
    newItems.forEach((item, index) => {
      item.decorateImg = decorateImg.value
      item.angle = itemDataList.value[index]?.angle ?? 0
    })
    itemDataList.value = newItems
  } catch (error) {
    console.error('Failed to load images for update:', error)
  }
}

// 绘制 Canvas
function draw() {
  if (!canvasRef.value || !ctx.value) return
  // 清空画布
  ctx.value.clearRect(0, 0, canvasSize.width.value, canvasSize.height.value)

  const scaledHeadSize = headSize.value * scale.value
  const scaledGapColumn = gapColumn.value * scale.value
  const scaledGapRow = gapRow.value * scale.value

  // 计算起始位置，使内容居中
  const startX = (canvasSize.width.value - (headCountObj.value.gridCols * scaledHeadSize + (headCountObj.value.gridCols - 1) * scaledGapColumn)) / 2
  const startY = (canvasSize.height.value - (headCountObj.value.gridRows * scaledHeadSize + (headCountObj.value.gridRows - 1) * scaledGapRow)) / 2

  // 计算最后一行的头像数量
  const lastRowCount = headCountObj.value.headCount % headCountObj.value.gridCols || headCountObj.value.gridCols
  const lastRowIndex = Math.floor((headCountObj.value.headCount - 1) / headCountObj.value.gridCols)

  // 绘制每个头像
  for (let index = 0; index < headCountObj.value.headCount; index++) {
    const item = itemDataList.value[index] || getItem()
    if (!item) continue
    const row = Math.floor(index / headCountObj.value.gridCols)
    const col = index % headCountObj.value.gridCols

    // 基础位置计算
    let x = startX + col * (scaledHeadSize + scaledGapColumn)
    const y = startY + row * (scaledHeadSize + scaledGapRow)

    // 如果是最后一行且不满，则水平居中处理
    if (row === lastRowIndex && lastRowCount < headCountObj.value.gridCols) {
      // 计算额外偏移量
      const extraOffset = (headCountObj.value.gridCols - lastRowCount) * (scaledHeadSize + scaledGapColumn) / 2
      x += extraOffset
    }

    // 保存当前上下文状态
    ctx.value!.save()

    // 绘制装饰图片（带旋转效果）
    if (decorateImg.value) {
      // 设置旋转中心点
      ctx.value!.translate(x + scaledHeadSize / 2, y + scaledHeadSize / 2)
      ctx.value!.rotate(item.angle)

      // 绘制装饰图片
      ctx.value!.drawImage(
        decorateImg.value,
        -scaledHeadSize / 2,
        -scaledHeadSize / 2,
        scaledHeadSize,
        scaledHeadSize,
      )

      // 重置变换
      ctx.value!.resetTransform()
    }

    // 创建圆形剪切区域用于头像
    if (item.img) {
      ctx.value!.beginPath()
      ctx.value!.arc(
        x + scaledHeadSize / 2,
        y + scaledHeadSize / 2,
        scaledHeadSize * 0.45, // 90% 大小，除以2
        0,
        Math.PI * 2,
      )
      ctx.value!.closePath()
      ctx.value!.clip()

      // 绘制头像
      ctx.value!.drawImage(
        item.img,
        x + scaledHeadSize * 0.05,
        y + scaledHeadSize * 0.05,
        scaledHeadSize * 0.9,
        scaledHeadSize * 0.9,
      )
    }

    // 恢复上下文状态
    ctx.value!.restore()

    // 更新旋转角度
    if (decorateAnimation.value) {
      item.angle += 0.02
    }
  }
}

function play() {
  draw()
  requestAnimationFrame(play)
}
async function init() {
  decorateImg.value = await loadImg(layer.value.decorateImg || '')
  await initItems()
  requestAnimationFrame(play)
}
// 生命周期钩子
onMounted(async () => {
  if (!canvasRef.value) return
  ctx.value = canvasRef.value.getContext('2d')
  if (!ctx.value) return
  init()
  // 设置定时更新
  updateTimer.value && clearInterval(updateTimer.value)
  updateTimer.value = setInterval(() => {
    updateRandomItem()
  }, 50)
})

onUnmounted(() => {
  // 清理工作
  if (updateTimer.value) clearInterval(updateTimer.value)
  if (animationId.value) cancelAnimationFrame(animationId.value)
})

watch(
  () => layer.value.decorateImg,
  async () => {
    decorateImg.value = await loadImg(layer.value.decorateImg || '')
  },
  { deep: true, immediate: true },
)
</script>

<template>
  <div class="lottery-ing4-canvas-box">
    <canvas ref="canvasRef" class="canvas-box" :width="canvasSize.width.value || 0" :height="canvasSize.height.value || 0"></canvas>
  </div>
</template>

<style scoped lang="scss">
.lottery-ing4-canvas-box {
  width: 100%;
  height: 100%;

  .canvas-box {
    width: 100%;
    height: 100%;
    display: block;
  }
}
</style>
