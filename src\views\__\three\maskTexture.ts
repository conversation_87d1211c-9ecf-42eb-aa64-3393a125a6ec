import type * as THREE from 'three'

export default function userMaskTexture({
  avatarTexture,
  maskTexture,
}: {
  avatarTexture: THREE.Texture
  maskTexture?: THREE.Texture | null
}) {
  // --- Shader Code ---
  const vertexShader = `
    varying vec2 vUv;
    void main() {
      vUv = uv;
      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
    }
  `

  const fragmentShader = `
    uniform sampler2D map; // Avatar texture
    uniform sampler2D maskMap; // Mask texture
    uniform float opacity; // Overall opacity controlled by animation
    varying vec2 vUv;

    void main() {
      vec4 mapColor = texture2D(map, vUv);
      vec4 maskColor = texture2D(maskMap, vUv);

      // Use the mask's alpha channel (or red channel if it's grayscale) to control transparency
      // Assuming a mask where white/opaque areas should be visible, black/transparent areas hidden.
      float maskValue = maskColor.a; // Or maskColor.r if using red channel

      // Combine map color with mask and overall opacity
      gl_FragColor = vec4(mapColor.rgb, mapColor.a * maskValue * opacity);
    }
    `

  // 创建 ShaderMaterial
  const uniforms: { map: { value: any }, opacity: { value: number }, maskMap?: { value: THREE.Texture | null } } = {
    map: { value: avatarTexture },
    opacity: { value: this.initialScale }, // 初始透明度
  }
  if (maskTexture) {
    uniforms.maskMap = { value: maskTexture } // 传递遮罩纹理
  }
}
