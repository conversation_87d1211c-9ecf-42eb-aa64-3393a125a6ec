<script setup lang="ts">
import type { IDesignRankings2 } from './rankings2'
import { cloneDeep } from 'lodash-es'
import { defineCustomEmits, injectScale, useDesignState } from '../..'
import { processStyle } from '../../utils'

const layer = defineModel<IDesignRankings2>('layer', { required: true })
const customEmits = defineCustomEmits(layer)
const scale = injectScale()
const designState = useDesignState()

const boxRef = ref<HTMLElement>()

const liStyle = computed(() => {
  return processStyle({
    width: ``,
    height: ``,
  }, scale)
})

const rankings = computed(() => {
  const list = cloneDeep(designState.getLayerData('endRankings') || [])
  return list
})

onMounted(() => {
  customEmits('finishRankingCount', 3)
})
</script>

<template>
  <ul ref="boxRef">
    <li v-for="(item, index) in rankings" :key="index" :style="liStyle">
      <img :src="item.avatar" class="img1" alt="">
      <div class="name">
        <span>{{ item.name }}</span>
      </div>
    </li>
  </ul>
</template>

<style scoped lang="scss">
ul {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  align-content: center;
  li {
    position: relative;
    img,
    .name,
    .num {
      position: absolute;
      left: 0;
    }
    .img1 {
      width: 66%;
      aspect-ratio: 1;
      border-radius: 50%;
      top: 8%;
      left: 17%;

      border: 2px solid #eecd62;
      outline: 2px dashed #eecd62;
      outline-offset: 4px;
    }
    .name {
      width: 100%;
      height: 26%;
      bottom: 0;
      display: flex;
      justify-content: center;
      color: #fff000;
      align-items: center;
      span {
        width: 100%;
        white-space: nowrap;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .num {
      color: #eecd62;
      font-weight: bold;
      left: 5%;
    }
  }
}
</style>
