import type { AxiosRequestConfig, AxiosResponse } from 'axios'
import lzstring from 'lz-string'
import { envUtils } from './env'

// 生产默认加密,其他不需要加密
const isSecret = !(envUtils.isDev || !!localStorage.getItem('d'))

// 接口加密
export function encryptRequest(config: AxiosRequestConfig) {
  const url = config.url
  if (url?.startsWith('/pro/')) {
    if (isSecret) {
      config.data = lzstring.compressToBase64(JSON.stringify(config.data))
    }
  }
}
// 接口解密
export function decryptResponse(res: AxiosResponse) {
  const data = res.data
  if (data && typeof data === 'string') {
    res.data = JSON.parse(lzstring.decompressFromBase64(data))
  }
}
