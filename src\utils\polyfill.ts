// @ts-nocheck

// json ///////////////////////////////////////////////////////
// 兼容 ios12.5 手机版本json序列化时报错问题

// 解析当前ios版本
const iosVersion = (() => {
  const matchs = navigator.userAgent.match(/iphone os (\d+)/i)
  if (matchs) {
    const version = Number.parseInt(matchs[1])
    if (!Number.isNaN(version)) {
      return version
    }
  }
})()

if (iosVersion && iosVersion < 13) {
  // 重写 JSON.stringify
  const oldStringify = JSON.stringify
  JSON.stringify = function (obj) {
    const seen: any = []
    return oldStringify(obj, (key, val) => {
      if (!val || typeof val !== 'object') {
        return val
      }
      if (seen.includes(val)) {
        return '[Circular]'
      }
      seen.push(val)
      return val
    })
  }
}

// at ///////////////////////////////////////////////////////
function at(n) {
  // ToInteger() abstract op
  n = Math.trunc(n) || 0
  // Allow negative indexing from the end
  if (n < 0) n += this.length
  // OOB access is guaranteed to return undefined
  if (n < 0 || n >= this.length) return undefined
  // Otherwise, this is just normal property access
  return this[n]
}

const TypedArray = Reflect.getPrototypeOf(Int8Array)
for (const C of [Array, String, TypedArray]) {
  if (C.prototype.at) continue
  Object.defineProperty(C.prototype, 'at', {
    value: at,
    writable: true,
    enumerable: false,
    configurable: true,
  })
}

// Promise.withResolvers ///////////////////////////////////////////////////////
if (!Promise.withResolvers) {
  Promise.withResolvers = function () {
    let resolve, reject
    const promise = new Promise((res, rej) => {
      resolve = res
      reject = rej
    })
    return { promise, resolve, reject }
  }
}

// Promise.allSettled ///////////////////////////////////////////////////////
if (!Promise.allSettled) {
  Promise.allSettled = function (promises) {
    // 将传入的可迭代对象转换为数组
    const promiseArray = Array.from(promises)
    // 存储每个 Promise 处理结果的数组
    const results = Array.from({ length: promiseArray.length })
    // 记录已经完成的 Promise 数量
    let completedCount = 0

    return new Promise((resolve) => {
      // 遍历每个 Promise
      promiseArray.forEach((promise, index) => {
        // 将非 Promise 对象转换为已解决的 Promise
        Promise.resolve(promise)
          .then((value) => {
            // 处理成功解决的情况
            results[index] = {
              status: 'fulfilled',
              value,
            }
          })
          .catch((reason) => {
            // 处理被拒绝的情况
            results[index] = {
              status: 'rejected',
              reason,
            }
          })
          .finally(() => {
            // 无论成功还是失败，完成数量加 1
            completedCount++
            // 当所有 Promise 都完成时，解决返回的 Promise
            if (completedCount === promiseArray.length) {
              resolve(results)
            }
          })
      })
    })
  }
}

// Array.prototype.toSorted ///////////////////////////////////////////////////////
if (!Array.prototype.toSorted) {
  // eslint-disable-next-line no-extend-native
  Array.prototype.toSorted = function (compareFn) {
    // 创建原数组的浅拷贝
    const copy = this.slice()
    // 对拷贝数组使用原生的 sort 方法进行排序
    return copy.sort(compareFn)
  }
}
