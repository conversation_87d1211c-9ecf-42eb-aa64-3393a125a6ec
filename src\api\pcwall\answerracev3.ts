import { HiRequest } from '../request'

export default {
  configRead: (params: any) => HiRequest.post('/pro/hxc/web/proanswerracev3config/read.htm', params),

  read: (params: any) => HiRequest.post('/pro/hxc/web/proanswerracev3/read.htm', params),
  go: (params?: any) => HiRequest.post('/pro/hxc/web/proanswerracev3/go.htm', params),
  pause: (params?: any) => HiRequest.post('/pro/hxc/web/proanswerracev3subject/pause.htm', params),
  resume: (params?: any) => HiRequest.post('/pro/hxc/web/proanswerracev3subject/resume.htm', params),
  subjectRead: (params?: any) => HiRequest.post('/pro/hxc/web/proanswerracev3subject/read.htm', params),
  nextSubject: (params?: any) => HiRequest.post('/pro/hxc/web/proanswerracev3subject/next.htm', params),
  ranking: (params: any) => HiRequest.post('/pro/hxc/web/proanswerracev3/ranking.htm', params),

  regeditPage: (params: any) => HiRequest.post('/pro/hxc/web/proanswerracev3regedit/page.htm', params),
  regeditMembersCnt: (params: any) => HiRequest.post('/pro/hxc/web/proanswerracev3regedit/membersCnt.htm', params),
  updateState: (params: any) => HiRequest.post('/pro/hxc/web/proanswerracev3/updateState.htm', params),

  teamList: (params: any) => HiRequest.post('/pro/hxc/web/proanswerracev3team/list.htm', params),
  switch: (params: any) => HiRequest.post('/pro/hxc/web/proanswerracev3/switch.htm', params),
  current: (params: any) => HiRequest.post('/pro/hxc/web/proanswerracev3subject/current.htm', params),
}
