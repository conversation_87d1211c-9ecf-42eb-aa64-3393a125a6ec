<script setup lang="ts">
import type { IDesignDynamicBackground } from './dynamic-background'

const layer = defineModel<IDesignDynamicBackground>('layer', { required: true })
// const scale = injectComputed('scale', 1)

const dynamicBackgroundRef = ref<HTMLElement>()
const shakeSize = useElementSize(dynamicBackgroundRef)

const bgStyle = computed(() => {
  const { front, back, race } = layer.value.data
  const width = shakeSize.width.value
  const height = shakeSize.height.value

  const topHeight = height * 0.425
  const bottomHeight = height * 0.575

  return {
    'width': '100%',
    'height': '100%',
    'backgroundImage': `url(${front}), url(${back}), url(${race})`,
    'backgroundRepeat': 'repeat-x, repeat-x, repeat-x',
    'backgroundPosition': `0 0, 0 0, 0 ${topHeight}px`,
    'backgroundSize': `100% ${topHeight}px, 100% ${topHeight}px, 100% ${bottomHeight}px`,
    '--width': `-${width * 2}px`,
    '--top-height': `${topHeight}px`,
    '--bottom-height': `${bottomHeight}px`,
  }
})
</script>

<template>
  <div ref="dynamicBackgroundRef" class="dynamic-background-box bg-ani" :style="bgStyle" />
</template>

<style scoped lang="scss">
.dynamic-background-box {
  --width: 0px;
  --top-height: 0px;
  --bottom-height: 0px;
}

.bg-ani {
  animation: bg-ani 10s infinite linear;
}

@keyframes bg-ani {
  from {
    background-position:
      0 0,
      0 0,
      0 var(--top-height);
  }

  to {
    background-position:
      var(--width) 0,
      calc(var(--width) * 1.5) 0,
      var(--width) var(--top-height);
  }
}
</style>
