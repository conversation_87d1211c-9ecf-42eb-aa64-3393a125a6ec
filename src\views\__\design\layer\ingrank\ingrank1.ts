import type { IDesignLayer, IDesignSetup } from '../../types'
import { BisTypes } from '../..'
import { InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './ingrank1-setting.vue'
import Comp from './ingrank1.vue'

// 类型
export const type = 'ingrank1'
export const defaultWidth = 300
// 数据类型约束
export interface DefaultIngRank1 {
  textColor?: string
  itemBgImg?: string
  avatarX?: number
  avatarY?: number
  avatarSize?: number
  textSize?: number
  textContentWidth?: number
  textContentX?: number
  textContentY?: number
}
export interface IDesignIngRank1 extends DefaultIngRank1, IDesignLayer {
  type: typeof type
  data: {
    name: string
    avatar: string
    score: number
  }[]
}

export const defaultData: DefaultIngRank1 = {
  textColor: '#fff',
  itemBgImg: new URL(`./assets/s_bg.png`, import.meta.url).href,
  avatarX: 20,
  avatarY: 24,
  avatarSize: 58,
  textContentWidth: 200,
  textContentX: 88,
  textContentY: 42,
  textSize: 20,
}
// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.sportsIng,
    showType: ['pcwall'],
    status: ['ing'],
    showInteractive: [InteractiveEnum.shakev3, InteractiveEnum.moneyv3, InteractiveEnum.goldcoinv3, InteractiveEnum.diglettv3, InteractiveEnum.firev3],
    type,
    name: '进行中小排行',
    thumbnail: new URL('./ingrank1.png', import.meta.url).href,
    Comp,
    CompSetting,
    defaultData() {
      return {
        type,
        uuid: layerUuid(),
        name: '进行中小排行',
        data: [],
        style: {
          right: '10px',
          top: '10px',
          width: `${defaultWidth}px`,
          height: '540px',
        },
      }
    },
  })
}
