import { merge } from 'lodash-es'
import { BisTypes } from '../../'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './seglottery-ing1-setting.vue'
import Comp from './seglottery-ing1.vue'

// 类型
export const type = 'seglottery-ing1'

// 数据类型约束
export interface IDesignSeglotteryIng1 extends IDesignLayer {
  type: typeof type
  data: {

    // 动画模式：默认、老虎机模式
    animationMode?: 'default' | 'slotMachine'

    maxRenderCount: number
    gap: number

    cardGap: number // 卡片间距
    backgroundColor: string
    borderRadius: number

    labelColor: string
    labelFontSize: number
    labelFontWeight: boolean
    labelLeftMargin: number
    labelWidth: number
    labelShow: boolean

    numColor: string
    numFontSize: number
    numFontWeight: boolean
    numWidth: number
    numHeight: number

    numBorderShow: boolean
    numBorderColor: string
    numBorderWidth: number
    numBorderRadius: number
  }
}

export const DEFAULT_DATA: IDesignSeglotteryIng1['data'] = {
  animationMode: 'default',

  labelLeftMargin: 0,
  labelFontWeight: false,
  labelShow: true,
  labelColor: '#ffffff',
  labelWidth: 80,

  numBorderColor: 'rgb(255, 255,255)',
  maxRenderCount: 12,
  labelFontSize: 24,
  numBorderShow: false,
  numHeight: 48,
  numFontSize: 28,
  numFontWeight: true,
  numWidth: 45,
  cardGap: 18,

  gap: 18,
  borderRadius: 8,
  backgroundColor: 'rgba(0, 0, 0, 0.5)',

  numColor: 'rgba(0, 255, 246, 1)',
  numBorderWidth: 1,
  numBorderRadius: 6,

}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.sportsIng,
    showInteractive: [InteractiveEnum.seglottery],
    showType: ['pcwall'],
    type,
    name: '排座抽奖进行中',
    Comp,
    CompSetting,
    thumbnail: new URL('./seglottery-ing1.png', import.meta.url).href,
    defaultData(options): IDesignSeglotteryIng1 {
      return merge({
        uuid: layerUuid(),
        name: '排座抽奖进行中',
        type,
        style: {
          left: '0px',
          top: '100px',
          width: '1280px',
          height: '550px',
        },
        data: {},
      }, options as IDesignSeglotteryIng1)
    },
  })
}
