<script setup lang="ts">
import type { IDesignGroup } from '../layer/group/group'
import type { IDesignLayer, ILayerTypes } from '../types'
import { remove } from 'lodash-es'
import { hasAuth } from '~/src/utils/auth'
import { isHideInHierarchy, isLayerDisable, isLayerHide, isLockedInHierarchy, useDesignState, useDesignTemp } from '..'
import { checkGroupVisible } from '../utils'

defineOptions({ name: 'RightLayerItem' })

const props = defineProps<{
  depth: number
  index: number[]
}>()

const designTemp = useDesignTemp()
const designState = useDesignState()

const layer = defineModel<ILayerTypes>('layer', { required: true })

const hasActionAuth = computed(() => hasAuth(['man', 'oem', 'spo.hi.vip', 'spo.ft']))

const hasLayers = computed(() => {
  if ('layers' in layer.value && !layer.value.templateId) {
    return layer.value.layers
  }
  return false
})

const groupItemStyle = computed(() => {
  return {
    paddingLeft: `${(props.depth) * 20}px`,
  }
})

function eyeToggle(layer: IDesignLayer) {
  if (isLayerDisable(layer))
    return
  if (!layer.show) {
    layer.show = []
    return
  }
  if (layer.show.includes(designState.status)) {
    remove(layer.show, v => v === designState.status)
  } else {
    layer.show.push(designState.status)
  }
}

function lockToggle(layer: ILayerTypes) {
  // 如果父级被锁定，不允许修改子级
  if (!layer.lock && isLockedInHierarchy(layer)) {
    return
  }
  layer.lock = !layer.lock
}

const isFold = ref(false)
const isInputing = ref(!layer.value.name)

const foldBus = useEventBus<'unfold' | 'fold'>('FOLD_BUS')
foldBus.on((type, uuid: string = 'ALL') => {
  if (uuid === 'ALL' || layer.value.uuid === uuid) {
    isFold.value = type === 'fold'
  }
})

const renderLayers = computed(() => {
  if (hasLayers.value && layer.value.type === 'group') {
    return layer.value.layers
      .map((item, index) => {
        return {
          item,
          layerIndex: index,
        }
      })
      .filter(({ item }) => {
        const layerAuth = hasActionAuth.value || item.spoVisible
        if (layerAuth) {
          return true
        }
        if (item.type === 'group') {
          // 如果是组，判断组内是否有显示权限的图层
          return checkGroupVisible(item as IDesignGroup)
        }
        return false
      })
  }
  return []
})

function onItemClick(event: MouseEvent) {
  if (!designTemp.activeList.includes(layer.value.uuid)) {
    // 点击的时候按住 ctrl 键，多选
    if (event.ctrlKey) {
      designTemp.activeList.push(layer.value.uuid)
    } else {
      designTemp.activeList = [layer.value.uuid]
    }
  } else {
    designTemp.activeList = designTemp.activeList.filter(uuid => uuid !== layer.value.uuid)
  }
}

function spoVisibleToggle(layer: ILayerTypes) {
  layer.spoVisible = !layer.spoVisible
}

function onInputClose() {
  if (layer.value.name) {
    isInputing.value = false
  }
}

const inputRef = ref<HTMLInputElement>()

watch(isInputing, async (value) => {
  if (value) {
    await nextTick()
    inputRef.value?.focus()
  }
})
</script>

<template>
  <div
    class="group-item"
    :class="{
      // 图层在当前状态不应显示，禁用
      'disable': isLayerDisable(layer),
      // 隐藏时置灰
      'text-gray': isLayerHide(layer),
    }"
    :data-index="props.index"
    :data-uuid="layer.uuid"
    :data-type="layer.type"
    @click.stop="onItemClick"
  >
    <div
      class="group-info"
      :class="{
        active: designTemp.activeList.includes(layer.uuid),
      }"
      :style="groupItemStyle"
    >
      <div class="icon-box" @click.stop>
        <template v-if="hasLayers">
          <icon-ph-folder v-if="isFold" @click="isFold = !isFold" />
          <icon-ph-folder-open v-else @click="isFold = !isFold" />
        </template>
        <template v-else>
          <!-- 文本 -->
          <icon-ph:text-t-light v-if="layer.type === 'text'" />
          <!-- 富文本 -->
          <icon-ph:text-t v-else-if="layer.type === 'text-rich'" />
          <!-- 图片 -->
          <icon-ph:image v-else-if="layer.type === 'image'" />
          <!-- 视频 -->
          <icon-ph:file-video-duotone v-else-if="layer.type === 'video'" />
          <!-- 图片、视频 -->
          <icon-ic:outline-perm-media v-else-if="layer.type === 'img-video'" />
          <!-- 模板 -->
          <icon-ph:layout v-else-if="layer.type === 'group' && layer?.templateId" />
          <!-- 组件 -->
          <icon-ph:stack v-else />
        </template>
      </div>

      <div
        class="h-full flex flex-1 items-center overflow-hidden px-2px"
        @dblclick="hasActionAuth && (isInputing = true)"
      >
        <input
          v-if="isInputing"
          ref="inputRef"
          v-model="layer.name"
          class="h-96% w-full outline-0"
          placeholder="请输入名称"
          @blur.capture="onInputClose"
          @keyup.enter="onInputClose"
        />
        <span v-else class="text-ellipsis whitespace-nowrap">{{ layer.name }}</span>
      </div>

      <!-- 默认不显示，只有存在差异时才显示 -->
      <div
        v-if="!isInputing"
        v-show="(layer.spoVisible || isLockedInHierarchy(layer) || isHideInHierarchy(layer))"
        class="action flex items-center gap-2"
        @click.stop
      >
        <!-- 普通用户可见 -->
        <template v-if="!hasLayers && hasAuth('man')">
          <icon-ph:user-fill
            v-if="layer.spoVisible"
            class="size-110%"
            @click="spoVisibleToggle(layer)"
          />
          <icon-ph:user
            v-else
            class="size-110% opacity-0"
            @click="spoVisibleToggle(layer)"
          />
        </template>

        <!-- 锁定/解锁 -->
        <template v-if="hasActionAuth">
          <icon-ph:dot-outline-fill v-if="isLockedInHierarchy(layer, false)" />
          <icon-ph:lock-key-fill v-else-if="layer.lock " @click="lockToggle(layer)" />
          <icon-ph:lock-open-bold
            v-else
            class="opacity-0"
            @click="lockToggle(layer)"
          />
        </template>

        <!-- 隐藏/显示 -->
        <icon-ph:dot-outline-fill v-if="isHideInHierarchy(layer, false)" />
        <el-tooltip
          v-else-if="isLayerHide(layer)"
          :hide-after="0"
          :content="isLayerDisable(layer) ? '当前互动状态不可操作' : '已隐藏'"
          placement="top"
          size="small"
        >
          <icon-ph-eye-slash-bold @click="eyeToggle(layer)" />
        </el-tooltip>
        <icon-ph-eye-bold
          v-else
          class="opacity-0"
          @click="eyeToggle(layer)"
        />
      </div>
    </div>
    <div
      v-if="hasLayers && layer.type === 'group'"
      v-show="!isFold "
      class="group"
      :data-index="props.index"
    >
      <RightLayerItem
        v-for="({ item, layerIndex }) in renderLayers"
        :key="item.uuid"
        v-model:layer="layer.layers[layerIndex]"
        :depth="depth + 1"
        :index="[...props.index, layerIndex]"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.group-item {
  position: relative;
  display: block;
  background-color: #fff;
  outline: 1px solid var(--border-color);

  &:has(.group) {
    padding: 0;
  }
}
.group-info {
  display: flex;
  align-items: center;
  height: 36px;
  padding: 0 6px;
  white-space: nowrap;
  font-size: 14px;
  &.active {
    color: #409eff;
  }
}

.icon-box {
  margin-bottom: -6px;
  margin-left: 6px;
  > * {
    margin: 0 2px;
  }
}

.group-info {
  .action {
    color: #555;
    * {
      cursor: pointer;
      padding: 4px;
      width: 24px;
      height: 24px;
    }
  }
  &:hover {
    .action {
      display: flex !important;
    }
    .action * {
      opacity: 1;
    }
  }
}
</style>
