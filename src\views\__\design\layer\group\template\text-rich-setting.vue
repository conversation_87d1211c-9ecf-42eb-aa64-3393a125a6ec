<script setup lang="ts">
import type { IDesignTextRich } from '../../text/text-rich'
import Quill from 'quill'

defineProps<{
  label: string
}>()
const editRef = ref<HTMLElement>()
const layer = defineModel<IDesignTextRich>('setting', { required: true })
const customFontSizes = (function () {
  const arr: string[] = []
  // 12 ~ 38 间隔2
  for (let i = 12; i <= 64; i += 2) {
    arr.push(`${i}px`)
  }
  // 64 ~ 128 间隔4
  for (let i = 66; i <= 128; i += 4) {
    arr.push(`${i}px`)
  }
  // 128 ~ 256 间隔6
  for (let i = 130; i <= 256; i += 6) {
    arr.push(`${i}px`)
  }
  return arr
})()

onMounted(() => {
  if (!editRef.value) return
  editRef.value.innerHTML = layer.value.data
  const quill = new Quill(editRef.value, {
    theme: 'snow',
    modules: {
      toolbar: [
        [{ align: [] }, { color: [] }, { background: [] }, 'bold', 'italic', 'underline', 'strike'],
        [{ size: customFontSizes }],
      ],
    },
  })
  quill.on('text-change', () => {
    layer.value.data = quill.root.innerHTML
  })
})
</script>

<template>
  <div class="setting-wrap">
    <h3>{{ label }}</h3>
    <div ref="editRef" class="edit-box"></div>
  </div>
</template>

<style scoped lang="scss">
@use 'sass:list';
// 定义一个函数来生成字体大小列表
@function generate-font-sizes() {
  $sizes: ();
  // 12 ~ 64 间隔2
  $i: 12;
  @while $i <= 64 {
    $sizes: list.append($sizes, $i + px);
    $i: $i + 2;
  }
  // 66 ~ 128 间隔4
  $i: 66;
  @while $i <= 128 {
    $sizes: list.append($sizes, $i + px);
    $i: $i + 4;
  }
  // 130 ~ 256 间隔6
  $i: 130;
  @while $i <= 256 {
    $sizes: list.append($sizes, $i + px);
    $i: $i + 6;
  }
  @return $sizes;
}

// 调用函数生成字体大小列表
$customFontSizes: generate-font-sizes();

.setting-wrap {
  padding: 0 !important;
  :deep() {
    .ql-snow {
      &.ql-toolbar {
        padding: 0;
      }

      .ql-size {
        .ql-picker-options {
          max-height: 200px;
          overflow: auto;
        }
        @each $size in $customFontSizes {
          .ql-picker-item[data-value='#{$size}']::before {
            content: '#{$size}';
          }
        }
      }
    }
  }
}
.edit-box {
  min-height: 200px;
  margin: 0 !important;
}
</style>
