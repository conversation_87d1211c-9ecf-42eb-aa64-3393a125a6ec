<script setup lang="ts">
import { useDesignData, useDesignTemp, useDisplayInfo } from '.'
import HiBackground from './background.vue'
import HiDesignLayer from './layer/index.vue'

const designData = useDesignData()
const designTemp = useDesignTemp()
const windowSize = useWindowSize()

const rootRef = ref<HTMLElement>()
const designPageRef = ref<HTMLElement>()
const designPageSize = useElementSize(designPageRef)

watch(
  () => [designPageSize.width.value, designPageSize.height.value] as const,
  ([width, height]) => {
    designTemp.designSize.width = width
    designTemp.designSize.height = height
  },
  { immediate: true },
)

const displayInfo = useDisplayInfo({ rootRef })
const scale = computed(() => displayInfo.value.scale)
provide('scale', scale)

const sortedLayers = computed(() => {
  const len = designData.layers.length
  return Array.from({ length: len }).map((_, i) => i).reverse()
})

const rootStyle = computed(() => {
  return {
    '--window-width': windowSize.width.value,
    '--window-height': windowSize.height.value,
    ...displayInfo.value.rootStyle,
  }
})
const designPageStyle = computed(() => {
  return {
    '--design-width': Number.parseFloat(`${designTemp.designSize.width}`),
    '--design-height': Number.parseFloat(`${designTemp.designSize.height}`),
    ...displayInfo.value.pageStyle,
  }
})
</script>

<template>
  <div ref="rootRef" class="preview-page" :style="rootStyle">
    <HiBackground />
    <div v-if="displayInfo.ready" ref="designPageRef" data-id="design_page" class="design-page" :style="designPageStyle">
      <HiDesignLayer v-for="(val, index) in sortedLayers" :key="designData.layers[val].uuid" v-model:layer="designData.layers[val]" :data-index="index" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.preview-page {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 5;
}
</style>
