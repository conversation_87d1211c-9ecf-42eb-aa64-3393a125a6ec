<script setup lang="ts">
import { useDesignData, useDesignTemp } from '../../..'
import TriggerItem from './index-item.vue'

const designData = useDesignData()
const designTemp = useDesignTemp()

const currentLayers = computed(() => {
  return designData.getLayerByUuids(designTemp.activeList)
})

const baseLayers = computed(() => {
  const base = currentLayers.value[0]
  // 如果是模板分组，修改其每一个子级图层。否则修改自身
  if (base.type === 'group' && base.templateId) {
    return [base, ...base.layers]
  }
  return [base]
})
</script>

<template>
  <div v-if="currentLayers.length === 1">
    <TriggerItem
      v-for="(_, index) in baseLayers.length"
      :key="baseLayers[index].uuid"
      v-model:layer="baseLayers[index]"
    />
  </div>
</template>
