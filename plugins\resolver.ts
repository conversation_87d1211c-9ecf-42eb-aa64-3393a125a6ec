import type { ComponentInfo, ComponentResolver } from 'unplugin-vue-components/types'
import * as path from 'node:path'
import process from 'node:process'
import chokidar from 'chokidar'

const sep = path.sep

// 转成驼峰
function hyphenToHumpLower(s: string | undefined) {
  if (!s)
    return s
  return s.replace(/_(\w)/g, ($0, $1) => {
    return $1.toUpperCase()
  })
}

// 转换大驼峰
function hyphenToHumpUpper(s: string | undefined) {
  if (!s)
    return s
  const r = hyphenToHumpLower(s)
  if (!r)
    return r
  return r.replace(/^[a-z]/, ($0) => {
    return $0.toUpperCase()
  })
}

const isBuild = process.argv.includes('build')

// 保存扫描到的组件对应关系
const components: { [k: string]: string } = {}

const watcher = chokidar
  .watch('./src/views/')
  .on('all', (event: 'add' | 'unlink', filepath: string) => {
    if (event === 'add' || event === 'unlink') {
      if (filepath.endsWith('.vue') && filepath.includes(`${sep}components${sep}`)) {
        // 路径转换成组件名
        const arr = filepath.slice(10).slice(0, -4).replace(`${sep}components`, '').replace(/-/g, '').split(sep)
        // 采用全名导入
        // arr.splice(1, arr.length - 3)
        let name = `Hi${hyphenToHumpUpper(arr.join('_'))}`
        if (name.endsWith('Index')) {
          name = name.slice(0, -5)
        }
        components[name] = `@${filepath.substring(3).replace(/\\/g, '/')}`
        // components[name] = `./../${path}`
      }
    }
  })
  .on('ready', () => {
    if (isBuild) {
      watcher.close()
    }
  })

function resolveComponent(name: string): ComponentInfo | undefined {
  if (name.match(/^ElIcon[A-Z]/)) {
    // 自定义el图标
    return {
      as: name,
      name: name.slice(6),
      from: '@element-plus/icons-vue',
    }
  }
  if (name.match(/^Hi[A-Z]/)) {
    const from = components[name]
    if (from) {
      return {
        as: name,
        name: 'default',
        from,
      }
    }
  }
}

// function resolveDirective(name: string): ComponentInfo | undefined {

// }

export function HiResolver(): ComponentResolver[] {
  return [
    {
      type: 'component',
      resolve: (name: string) => {
        return resolveComponent(name)
      },
    },
    // {
    //   type: 'directive',
    //   resolve: async (name: string) => {
    //     return resolveDirective(name)
    //   },
    // },
  ]
}
