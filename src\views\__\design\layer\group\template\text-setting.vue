<script setup lang="ts">
import type { IDesignText } from '../../text/text'
import { useDataAttr } from '../../..'

defineProps<{
  label: string
}>()
const layer = defineModel<IDesignText>('setting', { required: true })
const fontColorBind = useDataAttr(layer.value.style, 'color', 'rgba(0, 0, 0, 0)')
const bgColorBind = useDataAttr(layer.value.style, 'background', 'rgba(0, 0, 0, 0)')
</script>

<template>
  <div class="setting-wrap">
    <h3>{{ label }}</h3>
    <div class="setting-item justify-end!">
      <el-input
        v-model.trim="layer.data"
        type="textarea"
        :autosize="{ minRows: 3, maxRows: 6 }"
        placeholder="请输入文字内容"
      />
    </div>
    <div class="setting-item">
      <h3>文字颜色</h3>
      <hi-color v-model="fontColorBind" />
    </div>
    <div class="setting-item">
      <h3>背景色</h3>
      <hi-color v-model="bgColorBind" type="both" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.setting-wrap {
  padding: 0 !important;
  .setting-item {
    justify-content: flex-end !important;
  }
}
</style>
