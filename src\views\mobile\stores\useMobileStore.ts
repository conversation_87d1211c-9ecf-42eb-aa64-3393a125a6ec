import { envUtils } from '~/src/utils/env'

type AnyKeyObjet<T = string> = Record<string, T>

export const useMobileStore = defineStore('mobileStore', () => {
  // 暂时都交给互动的useImData通讯赋值
  const wall = ref<AnyKeyObjet<string>>({})
  const featureConfig = ref<AnyKeyObjet<string>>({})
  const applySignConfig = ref<AnyKeyObjet<string>>({})

  const isSignName = computed(() => {
    return applySignConfig.value.showName === 'signname'
  })

  const isShowAd = computed(() => {
    if (envUtils.isOem) {
      return false
    }
    if (featureConfig.value.phoneAdvertisementSwitch === 'Y') {
      return false
    }
    return true
  })

  const isWedding = computed(() => {
    return wall.value?.wallProperty === 'wedding'
  })

  return {
    wall,
    featureConfig,
    applySignConfig,
    isSignName,
    isShowAd,
    isWedding,
  }
})
