<script setup lang="ts">
import type { IDesignRankings3 } from './rankings3'
import { cloneDeep } from 'lodash-es'
import { defineCustomEmits, injectScale, useAdaptionQuantity, useDesignState } from '../..'
import { processStyle } from '../../utils'

const layer = defineModel<IDesignRankings3>('layer', { required: true })
const customEmits = defineCustomEmits(layer)
const scale = injectScale()
const designState = useDesignState()

const boxRef = ref<HTMLElement>()
const displaySize = useElementSize(boxRef)

const adaptionQuantity = useAdaptionQuantity({
  displaySize: computed(() => {
    return {
      width: displaySize.width.value / scale.value,
      height: displaySize.height.value / scale.value,
    }
  }),
  itemWidth: computed(() => layer.value.itemWidth),
  aspectRatio: 0.9,
  gap: computed(() => layer.value.gap),
})

const ulStyle = computed(() => {
  return processStyle({
    gap: `${adaptionQuantity.gap}px`,
    fontSize: `${adaptionQuantity.width * 0.15}px`,
  }, scale)
})
const liStyle = computed(() => {
  return processStyle({
    width: `${adaptionQuantity.width}px`,
    height: `${adaptionQuantity.height}px`,
  }, scale)
})

const rankings = computed(() => {
  const list = cloneDeep(designState.getLayerData('endRankings') || [])
  const startRanking = layer.value.startRanking || 0

  // 排序
  list.sort((a: any, b: any) => b.score - a.score)
  // 凑够
  const needCount = adaptionQuantity.count + startRanking
  const lack = list.length < needCount ? needCount - list.length : 0
  for (let i = list.length; i < lack; i++) {
    list.push({ name: '', avatar: layer.value.default, score: 0 })
  }
  // 截取
  list.splice(0, startRanking)
  return list.slice(0, adaptionQuantity.count)
})

watch(
  () => [layer.value.startRanking, adaptionQuantity.count],
  ([start = 0, count = 1]) => {
    customEmits('finishRankingCount', start + count)
  },
)
</script>

<template>
  <ul ref="boxRef" :style="ulStyle">
    <li v-for="(item, index) in rankings" :key="index" :style="liStyle">
      <img :src="item.avatar" class="img1" alt="">
      <div class="name">
        <span>{{ item.name }}</span>
      </div>
      <span class="num" :style="{ fontSize: `${adaptionQuantity.width * 0.2}px` }">{{ (layer.startRanking || 0) + index + 1 }}</span>
    </li>
  </ul>
</template>

<style scoped lang="scss">
ul {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  align-content: center;
  li {
    position: relative;
    img,
    .name,
    .num {
      position: absolute;
      left: 0;
    }
    .img1 {
      width: 66%;
      aspect-ratio: 1;
      border-radius: 50%;
      top: 8%;
      left: 17%;

      border: 2px solid #eecd62;
      outline: 2px dashed #eecd62;
      outline-offset: 4px;
    }
    .name {
      width: 100%;
      height: 26%;
      bottom: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #fff000;
      span {
        width: 100%;
        white-space: nowrap;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .num {
      color: #eecd62;
      font-weight: bold;
      left: 5%;
    }
  }
}
</style>
