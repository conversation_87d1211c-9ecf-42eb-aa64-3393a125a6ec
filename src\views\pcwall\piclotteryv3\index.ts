import { randomAvatar, timer } from '@/utils'
import { useDesignData, useDesignState, useDesignTemp } from '@/views/__/design'
import { faker } from '@faker-js/faker/locale/zh_CN'
import { useImData } from '~/src/hooks/useImData'
import { Toast } from '~/src/utils/toast'

// 类型
interface IPage<T> {
  count: number
  dataList: T[]
}
interface IConfig {
  piclotteryv3Id: number
  listlotteryThemeLimit: 'Y' | 'N'
  listlotteryAdvancedLimit: 'N' | 'Y'
}
interface IRound {
  id: number
  themeId: number
  title: string
  startTime?: number
  endTime?: number
  batchNo?: string
  sort?: number
  onceNum?: number // Added onceNum property
}
interface IRegedit {
  nameD0: string
  nameD1: string
  nameD2: string
}
type Status = 'NOT_STARTED' | 'WIN_LIST' | 'STARTING' | 'IN' | 'FINISH'
interface IRound {
  id: number
  themeId: number
  title: string
  startTime?: number
  state?: Status
}
interface IAward {
  id: number
  type: 'KIND' | 'CASH'
  name: string
  img: string
  mode: 'piclotteryv3'
  count: number
}
type FormType = 'txt' | 'num' | 'phone' | 'idcard' | 'name' | 'group'
interface IFormItem {
  key: string
  name: string
  id: number
  sort: number
  showSwitch: 'Y' | 'N'
  privacySwitch: 'Y' | 'N'
  type: FormType
  [key: string]: number | string | boolean
}
export function usePcwallPiclotteryv3() {
  const designTemp = useDesignTemp()
  const designData = useDesignData()
  const designState = useDesignState()

  const isDesignEdit = designTemp.isEdit
  const isManage = designTemp.isManage
  const isOem = designTemp.isOem

  const config = ref<IConfig>()
  const round = ref<IRound>()
  const roundList = ref<IRound[]>([])
  // 参与人
  const regeditPage = ref<IPage<IRegedit>>()

  // 奖品
  const awards = ref<IAward[]>()
  const awardLeftCount = ref<Record<number, number>>({})
  const totleWinCount = ref(0) // 总中奖人数
  const importFormList = ref<IFormItem[]>([]) // 导入表格格式
  // 一次抽奖人数
  const onceNum = ref(1)
  const importformShowOption = computed(() => {
    const list = [true, true, true]
    importFormList.value.forEach((item, index) => {
      if (item.showSwitch === 'Y') {
        list[index] = true
      } else {
        list[index] = false
      }
    },
    )
    return list
  })

  // 状态
  const status = computed(() => {
    const statusObj = {
      NOT_STARTED: 'ready',
      WIN_LIST: 'winlist',
      STARTING: 'staring',
      IN: 'ing',
      FINISH: 'finish',
    }
    if (!round.value) return ''

    if (isDesignEdit) {
      return designState.status
    }
    return round.value?.state ? statusObj[round.value.state] : ''
  })

  // 获取配置
  async function fetchConfig() {
    if (isDesignEdit) {
      await timer(200)
      config.value = { piclotteryv3Id: 1, listlotteryThemeLimit: 'Y', listlotteryAdvancedLimit: 'Y' }
    }
  }

  // 轮次
  async function fetchRound() {
    round.value = undefined
    if (isDesignEdit) {
      await timer(200)
      round.value = {
        id: 1,
        themeId: 1,
        title: '图片抽奖',
      }
    }
  }
  async function fetchRoundList() {
    roundList.value = []
    if (isDesignEdit) {
      await timer(200)
      roundList.value = [{
        id: 1,
        themeId: 1,
        title: '图片抽奖',
        sort: 1,
      }]
    } else {
      const data = await api.pcwall.piclotteryv3.list({ sort: { sort: 'asc' } })
      roundList.value = data
    }
  }

  // 查询主题
  async function fetchTheme() {
    if (isDesignEdit) {
      designTemp.fetchTheme()
    } else {
      const { themeId } = round.value || {}
      if (!themeId) return
      const theme = await api.pcwall.theme.readSimple({ where: { id: themeId } })
      designTemp.theme = theme
      if (theme) {
        designData.setState(JSON.parse(theme.webContent))
      }
    }
  }
  // 查询导入表格式
  async function fetchImportForm() {
    try {
      // 开始
      if (isManage || isOem) {
        importFormList.value = [
          { key: 'd1', name: '图片名称', sort: 1, showSwitch: 'Y', privacySwitch: 'N', type: 'txt', id: 1 },
        ]
      } else {
        const list = await api.pcwall.piclotteryv3.importformList({ piclotteryv3Id: round.value?.id })
        importFormList.value = list.sort((a: IFormItem, b: IFormItem) => a.sort - b.sort)
      }
    } catch (error: any) {
      Toast.message(error?.message || error?.data?.msg || '操作失败')
      console.error(error)
    }
  }
  /**
   * 字符串脱敏处理
   * @param {string} str - 原始字符串
   * @param {'phone' | 'idcard' | 'name' | 'text' | 'number' | 'group'} type - 字符串类型
   * @returns {string} 脱敏后的字符串
   */
  function maskString(str: string, type: FormType, privacySwitch: 'Y' | 'N') {
    if (privacySwitch !== 'Y') return str
    str = String(str) // 确保输入为字符串
    const len = str.length
    if (len === 0) return str
    switch (type) {
      // 手机号：隐藏中间4位（格式：138****5678）
      case 'phone':
        // 取前三位和后4位，中间不管有几位，都加4个*
        return str.replace(/^(\d{3})\d*(\d{4})$/, '$1****$2')

      // 身份证号：隐藏年月日8位（格式：110101;********7654）
      case 'idcard':
        return str.replace(/^(\d{6})\d*([\dX]{4})$/i, '$1********$2')

      // 姓名：保留首尾，2字时只留首（示例：张*丰、李*）
      case 'name':
        if (len <= 1) return str
        if (len === 2) return `${str[0]}*`
        return `${str[0]}*${str.slice(-1)}`

      // 文本：保留首尾，2字时只留首（规则同姓名，示例：这****本、文*）
      case 'txt':
        if (len <= 1) return str
        if (len === 2) return `${str[0]}*`
        return `${str[0]}*${str.slice(-1)}`

      // 数字：保留首尾数字，2位时只留首（示例：1****6、1*）
      case 'num':
        return len <= 1 ? str : str[0] + '*'.repeat(len > 2 ? len - 2 : 1) + (len > 2 ? str.slice(-1) : '')

      // 分组：保留首尾，单字时不变（示例：分****例、分组）
      case 'group':
        return len <= 1 ? str : str[0] + '*'.repeat(len - 2) + str.slice(-1)

      default:
        return str // 未知类型直接返回
    }
  }

  function dealShowData(item: any) {
    const { key: key0, type: type0, privacySwitch: privacySwitch0 } = importFormList.value[0] || {}
    const { key: key1, type: type1, privacySwitch: privacySwitch1 } = importFormList.value[1] || {}
    const { key: key2, type: type2, privacySwitch: privacySwitch2 } = importFormList.value[2] || {}
    const nameD0 = maskString(item[key0], type0, privacySwitch0)
    const nameD1 = maskString(item[key1], type1, privacySwitch1)
    const nameD2 = maskString(item[key2], type2, privacySwitch2)
    return { nameD0, nameD1, nameD2 }
  }
  function generateRandomPhoneNumber() {
    // 定义手机号前三位可能的数字段
    const prefix = [13, 14, 15, 16, 17, 18, 19]
    // 随机选择一个前三位
    const randomPrefix = prefix[Math.floor(Math.random() * prefix.length)]
    // 生成剩余的8位随机数字
    let randomNumber = ''
    for (let i = 0; i < 9; i++) {
      randomNumber += Math.floor(Math.random() * 10)
    }
    // 拼接完整的手机号
    return `${randomPrefix}${randomNumber}`
  }
  function generateRandomIdCard() {
    // 生成一个随机的身份证号码
    for (let i = 0; i < 18; i++) {
      // 生成随机数字
      const randomDigit = Math.floor(Math.random() * 10)
      // 拼接身份证号码
      if (i === 17) {
        // 最后一位可以是数字或字母X
        return `${Math.floor(Math.random() * 10000000000000000)}${randomDigit === 9 ? 'X' : randomDigit}`
      }
    }
    return `${Math.floor(Math.random() * 10000000000000000)}`
  }
  function createVirtualInfo() {
    const obj: {
      [key: string]: string | number
    } = { avatar: randomAvatar() }
    importFormList.value.forEach((item) => {
      switch (item.type) {
        case 'txt':
          obj[item.key] = faker.string.alpha({ length: faker.number.int({ min: 3, max: 6 }), casing: 'lower' })
          break
        case 'num':
          obj[item.key] = Math.floor(Math.random() * 10000).toString()
          break
        case 'phone':
          obj[item.key] = generateRandomPhoneNumber()
          break
        case 'idcard':
          obj[item.key] = generateRandomIdCard()
          break
        case 'name':
          obj[item.key] = faker.person.fullName()
          break
        default:
          obj[item.key] = faker.string.alpha({ length: faker.number.int({ min: 3, max: 6 }), casing: 'lower' })
      }
    })
    return obj
  }
  // 查询奖池列表
  async function fetchRegedit() {
    if (isDesignEdit) {
      if ((regeditPage.value?.count || 0) > 200) return
      const count = 200
      // 生成列表
      const dataList = []
      for (let i = 0; i < count; i++) {
        const obj = { id: i + 1, ...createVirtualInfo() }
        dataList.push(obj)
      }
      const list: any = []
      dataList.forEach((item: any) => {
        const { nameD0, nameD1, nameD2 } = dealShowData(item)
        list.push({ nameD0, nameD1, nameD2, id: item.id, avatar: item.avatar })
      })
      regeditPage.value = {
        count,
        dataList: list,
      }
    } else {
      // 查询参与人
      const piclotteryv3Id = round.value?.id
      if (!piclotteryv3Id) return
      const page = await api.pcwall.piclotteryv3.poolList({
        where: { piclotteryv3Id, showNum: 200 },
      })

      const list: any = []
      const imgSize = 300
      page.dataList.forEach((item: any) => {
        const { nameD0, nameD1, nameD2 } = dealShowData(item)
        list.push({ nameD0, nameD1, nameD2, id: item.id, avatar: item.imgPath ? `${item.imgPath}?imageView2/3/w/${imgSize}/h/${imgSize}` : null })
      })
      page.dataList = list
      regeditPage.value = page
    }
  }

  // 查询奖品
  async function fetchAward() {
    if (isDesignEdit) {
      awards.value = [{
        type: 'KIND',
        name: '实物奖品',
        img: '',
        mode: 'piclotteryv3',
        count: 1,
        id: 0,
      }]
    } else {
      const moduleId = round.value?.id
      if (!moduleId) return
      const data = await api.pcwall.awards.list({
        where: {
          moduleId,
          module: 'piclotteryv3',
        },
      })
      awards.value = data
    }
  }
  // 开始
  async function startFn() {
    try {
      if (!round.value) return
      if ((regeditPage.value?.count ?? 0) < onceNum.value) {
        Toast.message('参与人数不足')
        return
      }
      // 开始
      if (isDesignEdit) {

      } else {
        await api.pcwall.piclotteryv3.start({ piclotteryv3Id: round.value.id, onceNum: onceNum.value })
      }
    } catch (error: any) {
      Toast.message(error?.message || error?.data?.msg || '操作失败')
      console.error(error)
    }
  }
  // 结束
  async function stopFn() {
    try {
      if (!round.value) return
      // 开始
      if (isDesignEdit) {

      } else {
        await api.pcwall.piclotteryv3.stop({ piclotteryv3Id: round.value.id })
      }
    } catch (error: any) {
      Toast.message(error?.message || error?.data?.msg || '操作失败')
      console.error(error)
    }
  }

  const winningList = ref<any[]>([])
  async function fetchWinningList(status: string, batchNo?: string) {
    if (status !== 'ready') {
      winningList.value = []
    }
    // 中奖结果
    if (isDesignEdit) {
      const list: any[] = []

      // 生成列表
      const dataList = []
      for (let i = 0; i < 10; i++) {
        const obj = { id: i + 1, ...createVirtualInfo() }
        dataList.push(obj)
      }
      dataList.forEach((item: any) => {
        const { nameD0, nameD1, nameD2 } = dealShowData(item)
        list.push({ nameD0, nameD1, nameD2, id: item.id, avatar: item.avatar })
      })

      winningList.value = [
        {
          prizeName: '奖品1',
          awardList: list,
        },
      ]
      totleWinCount.value = 10
    } else {
      if (!round.value) return
      const where: any = {
        moduleId: round.value.id,
        module: 'piclotteryv3',
      }
      if (batchNo) {
        where.uuid = round.value.batchNo
      }
      const page = await api.pcwall.awards.recordpage({ where, pageIndex: 1, pageSize: 200, sort: { id: 'desc' } })
      totleWinCount.value = page.total
      const list: any = []

      page.dataList.forEach((item: any) => {
        const copyItem = JSON.parse(JSON.stringify(item))
        const remark = item.remark ? JSON.parse(item.remark) : {}
        const { nameD0, nameD1, nameD2 } = dealShowData(remark)
        copyItem.nameD0 = nameD0
        copyItem.nameD1 = nameD1
        copyItem.nameD2 = nameD2
        copyItem.avatar = remark.imgPath ? `${remark.imgPath}?imageView2/3/w/300/h/300` : ''
        list.push(copyItem)
      })
      winningList.value = [
        {
          prizeName: awards.value?.[0]?.name || '奖品名称',
          awardList: list,
        },
      ]
    }
  }
  function showWinlistFn() {
    if (isDesignEdit) {
      designState.setStatus('winlist')
    } else {
      updateRemoteState('WIN_LIST')
    }
  }
  function hideWinFn() {
    if (isDesignEdit) {
      designState.setStatus('ready')
    } else {
      updateRemoteState('NOT_STARTED')
    }
  }
  async function deleteRemoteWinning(awardId: number) {
    if (isDesignEdit) return
    await api.pcwall.awards.recorddelete({
      where: { moduleId: round.value?.id, module: 'piclotteryv3', id: awardId },
    })
    winningList.value.forEach((item) => {
      item.awardList = item.awardList.filter((i: { id: number }) => i.id !== awardId)
    })
  }
  // 更新状态
  async function updateRemoteState(state: Status) {
    await api.pcwall.piclotteryv3.updateState({ piclotteryv3Id: round.value?.id, state })
  }
  function onceNumChange(num: number) {
    return () => {
      if (onceNum.value + num < 1) return
      onceNum.value += num
    }
  }

  const selRound = computed({
    get: () => round.value?.id,
    set: (v) => {
      v && updateLottery(v)
    },
  })
  async function updateLottery(piclotteryv3Id: number) {
    if (isDesignEdit) return
    await api.pcwall.piclotteryv3.updateLottery({ piclotteryv3Id })
  }
  async function fetchAwardsleft() {
    if (isDesignEdit) return
    const data = await api.pcwall.awards.leftCnt({ where: { moduleId: round.value?.id, module: 'piclotteryv3' } })
    return awardLeftCount.value = data
  }
  async function switchRound(type: 'down' | 'up') {
    if (isDesignEdit) return
    if (['staring', 'in'].includes(status.value)) {
      Toast.message('当前状态不允许切换轮次')
      return
    }
    await api.pcwall.piclotteryv3.switch({ type })
  }

  // 主要作用是将数据暴露给图层组件
  designState
    .setStatusList([
      { label: '准备页', value: 'ready' },
      { label: '全部中奖名单', value: 'winlist' },
      { label: '启动中', value: 'staring' },
      { label: '进行中', value: 'ing' },
      { label: '单次中奖名单', value: 'finish' },
    ])
    .setLayerData({
      '#轮次标题#': computed(() => {
        return round.value?.title || ''
      }),
      '#参与人数#': computed(() => {
        return regeditPage.value?.count || 0
      }),
      'regeditList': computed(() => {
        return regeditPage.value?.dataList || []
      }),
      'importformShowOption': computed(() => {
        return importformShowOption.value
      }),
      // 单次抽取数量
      '#每次抽取人数#': onceNum,
      'selectDataSources': computed(() => [
        {
          label: '轮次列表',
          // value是LayerData的key，需要符合[{label,value}]格式
          value: 'roundList',
        },
      ]),
      '%轮次%': selRound,
      // 轮次列表
      'roundList': computed(() => {
        return roundList.value.map(item => ({ label: item.title, value: item.id }))
      }),
      // 中奖名单
      'winningList': computed(() => {
        return winningList.value
      }),
      'winningList2': computed(() => {
        return winningList.value[0]?.awardList || []
      }),
      // 奖品列表
      'awards': computed(() => {
        return awards.value?.map(item => (Object.assign({}, item, { count: awardLeftCount.value[item.id] ?? item.count }))) || []
      }),
      // 总中奖人数
      '#总中奖人数#': computed(() => {
        return totleWinCount.value
      }),
      '#奖品名称#': computed(() => {
        return awards.value?.[0]?.name || ''
      }),
      '#奖品数量#': computed(() => {
        const award = awards.value?.[0]
        if (!award) return 0
        return awardLeftCount.value[award.id] || award.count
      }),
    })
    // 图层组件事件
    .setLayerEventList([
      // 事件
      {
        eventId: 'start',
        name: '开始',
        value: startFn,
        status: ['ready'],
      },
      {
        eventId: 'stop',
        name: '停止',
        value: stopFn,
        status: ['ing'],
      },
      {
        eventId: 'showWinlist',
        name: '打开中奖名单',
        value: showWinlistFn,
        status: ['ready'],
      },
      {
        eventId: 'hideWinlist',
        name: '关闭中奖名单',
        value: hideWinFn,
        status: ['winlist', 'finish'],
      },
      {
        eventId: 'addOnceNum',
        name: '单次抽取数量+1',
        value: onceNumChange(1),
        status: ['ready'],
      },
      {
        eventId: 'minOnceNum',
        name: '单次抽取数量-1',
        value: onceNumChange(-1),
        status: ['ready'],
      },
      {
        eventId: 'upRound',
        name: '上一轮',
        value: () => switchRound('up'),
        status: ['ready'],
      },
      {
        eventId: 'downRound',
        name: '下一轮',
        value: () => switchRound('down'),
        status: ['ready'],
      },
      {
        // 中奖名单组件，取消中奖
        eventId: 'winning-delete',
        value: async (awardId) => {
          await deleteRemoteWinning(awardId)
          if (status.value === 'ready') {
            fetchAwardsleft()
          }
        },
      },
    ])

  // 变化监控
  watch(
    () => round.value?.id,
    async (n, o) => {
      if (n === o) return
      onceNum.value = round.value?.onceNum || 1
    },
    { immediate: true },
  )
  watch(() => config.value?.piclotteryv3Id, () => {
    fetchRound()
    fetchRoundList()
  })
  watch(() => round.value?.id, async () => {
    fetchAward()
    if (isDesignEdit) return
    // 重置数据
    regeditPage.value = undefined
  })

  watch(() => round.value?.themeId, () => {
    fetchTheme()
  })
  const starTime = ref(0)
  const t = 2000
  // 变化监控
  watch(
    () => [status.value, round.value?.id] as const,
    async (current, previous) => {
      if (!round.value) return
      const [v, newId] = current || []
      const [o, oldId] = previous || []
      if (v === o && newId === oldId) return
      if (!importFormList.value.length) {
        await fetchImportForm()
      }
      if (v === 'ready') {
        await Promise.all([fetchAwardsleft(), fetchRegedit()])
        fetchWinningList(v)
      }
      if (v === 'winlist') {
        fetchWinningList(v)
      }
      if (v === 'staring') {
        if (!isDesignEdit) {
          starTime.value = Date.now()
        }
      }
      if (v === 'ing') {
        await fetchRegedit()
        if (!isDesignEdit) {
          // 如果是从启动中过来的，那么保证启动中至少进行了t毫秒
          if (o === 'staring') {
            const time = Date.now() - starTime.value // 计算时间差
            if (time < t) {
              await timer(t - time)
            }
          }
        }
      }
      if (v === 'finish') {
        await fetchWinningList(v, round.value?.batchNo)
      }
      designState.setStatus(status.value)
    },
    { immediate: true },
  )
  // 数据同步
  useImData({
    'im:piclotteryv3:config': config,
    'im:piclotteryv3': computed({
      get() { return round.value },
      async set(v) {
        if (v?.id !== round.value?.id) {
          round.value = undefined
          await nextTick()
        }
        round.value = v
      },
    }),
  })

  tryOnMounted(async () => {
    if (isDesignEdit) {
      fetchConfig()
    }
  })
}
