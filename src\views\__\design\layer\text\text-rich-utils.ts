import type { Property } from 'csstype'
import type { Arrayable } from 'element-plus/es/utils/index.mjs'
import type { Range } from 'quill'
import type { WatchStopHandle } from 'vue'
import type { IDesignTextRich } from './text-rich'
import Color from 'color'
import DOMPurify from 'dompurify'
import { cloneDeep } from 'lodash-es'
import Quill, { Delta } from 'quill'
import { injectScale, openSelectMaterial, useDesignState, useDesignTemp } from '../..'
import { processStyle } from '../../utils'

const FontAttributor = Quill.import('attributors/style/size') as any
FontAttributor.whitelist = null
Quill.register(FontAttributor, true)

const ImageBlot = Quill.import('formats/image') as any
class StyleImageBlot extends ImageBlot {
  static create(value: string) {
    const node = super.create(value)
    // 获取图片尺寸
    const img = new Image()
    img.src = value
    img.onload = () => {
      const width = img.width
      const height = img.height
      node.setAttribute('width', `${width}px`)
      node.setAttribute('height', `${height}px`)
    }
    img.onerror = () => {
      node.remove()
    }
    return node
  }
}
StyleImageBlot.blotName = 'image'
StyleImageBlot.tagName = 'img'
Quill.register({ 'formats/styleImage': StyleImageBlot })

const quillMap = new Map<string, any>()
export function useQuillManager(layer: Ref<IDesignTextRich>) {
  const scale = injectScale()
  const designState = useDesignState()
  const designTemp = useDesignTemp()

  // 渲染使用
  class HiQuill {
    uuid: string
    layer: Ref<IDesignTextRich>
    quill?: Quill
    range?: Range
    isEnabled = ref(false)

    currentImageWidth = ref<number | undefined>()
    #currentImageWatcher?: WatchStopHandle

    originHtml = computed(() => {
      let data = cloneDeep(layer.value.data)

      data = processStyle(data, scale)

      data = DOMPurify.sanitize(data, {
        ALLOWED_TAGS: ['p', 'span', 'strong', 'em', 's', 'u', 'br', 'img'],
        ALLOWED_ATTR: ['class', 'style', 'src', 'width', 'height'],
      })
      return data
    })

    renderHtml = computed(() => {
      let data = cloneDeep(this.originHtml.value)
      // 占位符替换（编辑中禁止使用，因为会引起响应失效）
      for (const [key, value] of Object.entries(designState.layerData)) {
        const val = unref(value)
        if (val !== undefined) {
          data = data.replace(new RegExp(key, 'g'), val)
        }
      }
      return data
    })

    // 样式信息
    sectionStyle = ref({
      color: '',
      fontSize: 32,
      isBold: false,
      isItalic: false,
      isUnderline: false,
      isStrike: false,
      align: '',
      alignItems: '',
      textIndent: false,
      writingMode: '',
    })

    constructor(layer: Ref<IDesignTextRich>) {
      this.uuid = layer.value.uuid
      this.layer = layer
    }

    init(quill: Quill) {
      quill.disable()
      quill.root.innerHTML = this.renderHtml.value
      this.quill = quill

      quill.on('selection-change', (range) => {
        if (range) {
          this.range = range
        }
        this.updateSectionStyle()
        // 获取当前选中的图片对象
        if (this.#currentImageWatcher) {
          this.#currentImageWatcher()
          this.#currentImageWatcher = undefined
        }
        const [img] = quill.getLeaf(range?.index || 0)
        if (img && img instanceof ImageBlot) {
          // 获取图片dom对象
          const imgDom = img.domNode as HTMLImageElement

          const width = imgDom.getAttribute('width') || '0'
          this.currentImageWidth.value = Math.round(Number.parseFloat(width) / scale.value)

          // 监控修改
          this.#currentImageWatcher = watch(
            () => this.currentImageWidth.value,
            (v) => {
              if (!v) return
              const width = imgDom.getAttribute('width') || '0'
              const height = imgDom.getAttribute('height') || '0'
              const ow = Number.parseFloat(width)
              const oh = Number.parseFloat(height)
              const ratio = ow / oh

              v *= scale.value
              imgDom.setAttribute('width', `${v.toFixed(2)}px`)
              imgDom.setAttribute('height', `${(v / ratio).toFixed(2)}px`)
            },
          )
        } else {
          this.currentImageWidth.value = undefined
        }
      })
      quill.on('text-change', () => {
        if (this.isEnabled.value) {
          this.textChange()
        }
      })
      quill.clipboard.addMatcher(Node.ELEMENT_NODE, (node) => {
        // 只保留纯文本
        return new Delta().insert(node.textContent || '')
      })

      // quill的focus和blur事件存在错误的问题，所以只能在鼠标抬起时强制focus
      const sectionDom = quill.root.closest(`section[data-uuid="${layer.value.uuid}"]`)
      sectionDom?.addEventListener('mouseup', () => {
        if (this.isEnabled.value) {
          quill.focus()
        }
      })

      watch(
        () => [this.sectionStyle.value.alignItems, this.sectionStyle.value.writingMode],
        ([v1, v2]) => {
          const alignItems = layer.value.style.alignItems || 'start'
          if (v1 !== alignItems) {
            this.layer.value.style.alignItems = v1
          }
          if (v2 !== this.layer.value.style.writingMode) {
            this.layer.value.style.writingMode = v2 as Property.WritingMode
          }
        },
      )
      watch(
        () => designTemp.activeList.includes(layer.value.uuid),
        (v) => {
          if (v) {
            this.updateSectionStyle()
          }
        },
      )
      watch(
        () => this.renderHtml.value,
        (v) => {
          if (!this.isEnabled.value) {
            quill.root.innerHTML = v
          }
        },
        { immediate: true, deep: true },
      )
    }

    async enable() {
      if (!this.quill) return

      this.quill.root.innerHTML = this.originHtml.value
      this.quill.enable()
      await nextTick()
      this.quill.setSelection(0, this.quill.getLength(), Quill.sources.USER)

      this.isEnabled.value = true
    }

    updateSectionStyle() {
      if (!this.quill) return
      const quill = this.quill
      const range = this.getRange()
      const format = quill.getFormat(range)
      if (format.size) {
        // 适配缩放
        this.sectionStyle.value.fontSize = Math.round(Number.parseFloat(`${format.size}`) / scale.value)
      }

      let color = format.color as string | string[] | undefined
      if (color) {
        if (Array.isArray(color)) {
          color = color[0]
        }
        if (color.startsWith('#')) {
          const tmp = Color(color.split(',')[0]).rgb()
          this.sectionStyle.value.color = `rgba(${tmp.red()}, ${tmp.green()}, ${tmp.blue()}, ${tmp.alpha()})`
        } else {
          this.sectionStyle.value.color = color
        }
      }

      this.sectionStyle.value.isBold = !!format.bold
      this.sectionStyle.value.isItalic = !!format.italic
      this.sectionStyle.value.isUnderline = !!format.underline
      this.sectionStyle.value.isStrike = !!format.strike

      // 对齐方式
      this.sectionStyle.value.align = (format.align || 'left') as string

      const alignItems = layer.value.style.alignItems || 'start'
      this.sectionStyle.value.alignItems = alignItems

      const [line] = quill.getLine(quill.getSelection()?.index || 0)
      if (line) {
        const block = line.domNode
        this.sectionStyle.value.textIndent = !!block.style.textIndent
      }

      const writingMode = layer.value.style.writingMode || ''
      this.sectionStyle.value.writingMode = writingMode
    }

    textChange() {
      if (!this.quill) return
      this.layer.value.data = processStyle(this.quill.root.innerHTML, 1 / scale.value, 0)
    }

    getRange() {
      if (!this.quill) {
        return { index: 0, length: 0 }
      }
      if (!this.isEnabled.value) {
        return { index: 0, length: this.quill.getLength() || 0 }
      }
      if (this.range) {
        return this.range
      }
      return { index: 0, length: this.quill.getLength() || 0 }
    }

    disable() {
      if (!this.quill) return
      // 取消选中
      this.quill.setSelection(0)
      this.quill.disable()
      this.isEnabled.value = false
      this.textChange()

      this.quill.root.innerHTML = this.renderHtml.value
    }

    destroy() {
      quillMap.delete(this.uuid)
    }

    insertText(val: string) {
      if (!this.quill) return
      const range = this.getRange()
      this.quill.insertText(range.index, val, {}, Quill.sources.USER)
      this.quill.setSelection(range.index + val.length, 0, Quill.sources.SILENT)
      this.quill.focus()
    }

    async insertImage() {
      const data = await openSelectMaterial()
      if (!data) return
      if (!this.quill) return
      const range = this.getRange()
      this.quill.insertEmbed(range.index, 'image', data, Quill.sources.USER)
      this.quill.setSelection(range.index + 1, 0, Quill.sources.SILENT)
      this.quill.focus()
    }

    async setStyle(name: string, value: boolean | string | number | Arrayable<number> | undefined) {
      if (!this.quill) return
      const quill = this.quill

      let isDeal = false
      if (!this.isEnabled.value) {
        isDeal = true
        this.quill.root.innerHTML = this.originHtml.value
        await nextTick()
      }

      const range = this.getRange()
      if (range.length === 0) {
        range.index = 0
        range.length = this.quill.getLength()
      }

      if (name === 'fontSize') {
        console.log(name, value)
        quill.formatText(range.index, range.length, { size: `${Number.parseInt(`${value}`) * scale.value}px` })
      } else if (name === 'align') {
        quill.formatLine(range.index, range.length, { [name]: value })
      } else if (name === 'indent') {
        const [line] = quill.getLine(quill.getSelection()?.index || 0)
        if (line) {
          const block = line.domNode
          block.style.textIndent = value ? '2em' : ''
        }
      } else {
        quill.formatText(range.index, range.length, { [name]: value })
      }

      if (isDeal) {
        this.textChange()
        await nextTick()
        quill.root.innerHTML = this.renderHtml.value
      }

      this.updateSectionStyle()
    }
  }
  const { uuid } = layer.value
  if (!quillMap.has(uuid)) {
    quillMap.set(uuid, new HiQuill(layer))
  }
  return quillMap.get(uuid) as HiQuill
}
