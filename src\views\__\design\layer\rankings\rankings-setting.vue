<script setup lang="ts">
import type { IDesignRankings } from './rankings'
import { getDefaultMaterial, openSelectMaterial } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'

const layer = defineModel<IDesignRankings>('layer', { required: true })

async function updateMaterialFn(name: 'default' | 'podium' | 'star' | 'light', isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, `${name}`)
    : await openSelectMaterial('PIC')
  if (result) {
    layer.value[name] = result
  } else if (isReset) {
    delete layer.value[name]
  }
}
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>领奖台</h3>
        <div class="bgblank relative h-60 w-160">
          <MaterialThumbnail @select="updateMaterialFn('podium')" @reset="updateMaterialFn('podium', true)">
            <img :src="layer.podium">
          </MaterialThumbnail>
        </div>
      </div>
      <div class="setting-item">
        <h3>默认头像</h3>
        <div class="bgblank relative h-60 w-160">
          <MaterialThumbnail @select="updateMaterialFn('default')" @reset="updateMaterialFn('default', true)">
            <img :src="layer.default">
          </MaterialThumbnail>
        </div>
      </div>
      <div class="setting-item">
        <h3>闪烁装饰</h3>
        <div class="bgblank relative h-60 w-160">
          <MaterialThumbnail @select="updateMaterialFn('star')" @reset="updateMaterialFn('star', true)">
            <img :src="layer.star">
          </MaterialThumbnail>
        </div>
      </div>
      <div class="setting-item">
        <h3>背景光环</h3>
        <div class="bgblank relative h-60 w-160">
          <MaterialThumbnail @select="updateMaterialFn('light')" @reset="updateMaterialFn('light', true)">
            <img :src="layer.light">
          </MaterialThumbnail>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
