<script setup lang="ts">
import type { IDesignShape } from './shape'
import { openSelectMaterial } from '../../index'
import { useShapeUtils } from './shape-utils'

const layer = defineModel<IDesignShape>('layer', { required: true })
// 识别出所有颜色

async function updateMaterialFn() {
  const result = await openSelectMaterial('SHAPE')
  if (result) {
    layer.value.data = result
  }
}

const { colors, reset } = useShapeUtils(layer)
</script>

<template>
  <div v-if="layer.data" class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <div class="thumbnail-box bgblank" @click="updateMaterialFn">
          <img :src="layer.data">
        </div>
      </div>
      <div class="setting-item">
        <el-button type="danger" @click="reset">重置</el-button>
      </div>
      <div v-for="i in colors.length" :key="i" class="setting-item">
        <span>形状颜色{{ i }}</span>
        <hi-color v-model="colors[i - 1]" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.thumbnail-box {
  width: 120px;
  height: 120px;
  outline: 1px solid #e6ebed;
  border-radius: 4px;
  position: relative;

  img {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    object-fit: contain;
  }
}
</style>
