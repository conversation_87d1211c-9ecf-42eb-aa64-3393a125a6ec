// plop 已经内置了一些实用的 helper。
// 以下分别是对应的方法和转换后的格式示例：

// camelCase(驼峰命名): changeFormatToThis
// snakeCase(下划线分隔): change_format_to_this
// dashCase/kebabCase(短横线分隔): change-format-to-this
// dotCase(点分隔): change.format.to.this
// pathCase(斜杠分隔): change/format/to/this
// properCase/pascalCase(帕斯卡命名(每个单词的首字母大写)): ChangeFormatToThis
// lowerCase(转换为全小写): change format to this
// sentenceCase(首字母大写,句末添加逗号): Change format to this,
// constantCase(常量形式，全大写，使用下划线分隔): CHANGE_FORMAT_TO_THIS
// titleCase(标题形式): Change Format To This

export default (
  /** @type {import('plop').NodePlopAPI} */
  plop,
) => {
  plop.setGenerator('design component', {
    description: 'create a design component',
    prompts: [
      {
        type: 'input',
        name: 'name',
        message: '输入组件名([folder/]filename) : ',
        validate: (value) => {
          if (!value) {
            return '组件名称不能为空，规则是 "文件夹名/文件名" ，"文件夹名/" 可留空，留空使用 "文件名/文件名"'
          }
          return true
        },
      },
      {
        type: 'input',
        name: 'displayName',
        message: '输入设计器组件标题(中文) : ',
        default: '新组件',
      },
      {
        type: 'confirm',
        name: 'isNeedAttachment',
        message: '是否需要 Attachment ?',
        default: false,
      },
    ],
    actions(data) {
      const [folderName, nameName] = data.name.includes('/') ? data.name.split('/') : [data.name, data.name]
      const folderNameKebabCase = plop.getHelper('kebabCase')(folderName)
      const fileNameKebabCase = plop.getHelper('kebabCase')(nameName)
      const fileNamePascal = plop.getHelper('pascalCase')(fileNameKebabCase)

      Object.assign(data, {
        fileNameKebabCase,
        folderNameKebabCase,
        fileNamePascal,
      })

      const actions = [
        {
          type: 'add',
          path: `src/views/__/design/layer/${folderNameKebabCase}/${fileNameKebabCase}-setting.vue`,
          templateFile: './plugins/design-component/comp-setting.vue.hbs',
        },
        {
          type: 'add',
          path: `src/views/__/design/layer/${folderNameKebabCase}/${fileNameKebabCase}.ts`,
          templateFile: './plugins/design-component/comp.ts.hbs',
        },
        {
          type: 'add',
          path: `src/views/__/design/layer/${folderNameKebabCase}/${fileNameKebabCase}.vue`,
          templateFile: './plugins/design-component/comp.vue.hbs',
        },
        {
          type: 'modify',
          path: `src/views/__/design/types.ts`,
          transform: (content) => {
            let modifiedContent = content

            const ILAYER_TYPES_FLAG = '// ILayerTypes end'
            const IMPORT_LAYER_TYPES_FLAG = '// Import Layer Types Flag End'

            const ILayerTypes = `| IDesign${fileNamePascal}`
            const ImportLayerTypes = `import type { IDesign${fileNamePascal} } from './layer/${folderNameKebabCase}/${fileNameKebabCase}'`

            modifiedContent = modifiedContent
            // 在 //ILayerTypes end 前追加 name
              .replace(ILAYER_TYPES_FLAG, `${ILayerTypes}\n${ILAYER_TYPES_FLAG}`)
            // 在 // Import Layer Types Flag End 前追加 name2
              .replace(IMPORT_LAYER_TYPES_FLAG, `${ImportLayerTypes}\n${IMPORT_LAYER_TYPES_FLAG}`)

            return modifiedContent
          },
        },
      ]
      if (data.isNeedAttachment) {
        actions.splice(2, 0, {
          type: 'add',
          path: `src/views/__/design/layer/${folderNameKebabCase}/${fileNameKebabCase}-attachment.vue`,
          templateFile: './plugins/design-component/comp-attachment.vue.hbs',
        })
      }
      return actions
    },
  })

  // 创建oem admin manage互动路由组件
  plop.setHelper('eq', (v1, v2) => v1 === v2)
  plop.setHelper('includes', (array, value) => {
    return Array.isArray(array) && array.includes(value)
  })
  plop.setHelper('andIncludes', (array, val1, val2) => {
    return Array.isArray(array) && array.includes(val1) && array.includes(val2)
  })
  // ========== Generator ==========
  plop.setGenerator('router component', {
    description: 'create a router component',
    prompts: [
      {
        type: 'input',
        name: 'name',
        message: '请输入组件名称:',
        validate: value => !!value || '组件名称不能为空',
      },
      {
        type: 'input',
        name: 'displayName',
        message: '输入设计器组件标题(中文):',
        validate: value => !!value || '组件标题不能为空',
      },
      {
        type: 'checkbox',
        name: 'platforms',
        message: '请选择需要生成的平台：',
        choices: [
          { name: 'mobile', value: 'mobile' },
          { name: 'pcwall', value: 'pcwall' },
        ],
        validate: value => !!value.length || '请选择需要生成的平台',
      },
    ],
    actions: (data) => {
      const models = ['oem', 'manage', 'admin']
      const files= models.map(model => ({
        type: 'add',
        path: `src/views/${model}/wall/${data.name}/index.vue`,
        templateFile: './plugins/design-wall-route/wallrouter.hbs',
        data: { ...data, model },
        force: true,
      }))

      if(data.platforms.includes('mobile')){
        files.push(...models.map(model => ({
          type: 'add',
          path: `src/views/${model}/wall/${data.name}/components/mobile.vue`,
          templateFile: './plugins/design-wall-route/components/mobile.hbs',
          data: { ...data, model },
          force: true,
        })))
      }
      
      if(data.platforms.includes('pcwall')){
        files.push(...models.map(model => ({
          type: 'add',
          path: `src/views/${model}/wall/${data.name}/components/pcwall.vue`,
          templateFile: './plugins/design-wall-route/components/pcwall.hbs',
          data: { ...data, model },
          force: true,
        })))
      }

      return files
    },
  })
}
