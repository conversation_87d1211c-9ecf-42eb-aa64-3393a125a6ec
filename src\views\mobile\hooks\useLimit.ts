import { showDialog } from 'vant'
import 'vant/lib/dialog/style'

// 活动限制
export function useLimit() {
  const limit = ref(false)
  const dealLimit = (data: any) => {
    const state = data ? data.state : ''
    if (state === 'limit') {
      if (data.limitLevel === 'MODULELIMIT') {
        limit.value = true
      } else if (data.limitLevel === 'WALLBLACK') {
        window.parent.postMessage({ type: 'iframe:wall-black' }, '*')
      } else {
        window.parent.postMessage({ type: 'iframe:wall-limit' }, '*')
      }
    } else {
      limit.value = false
    }
  }
  watch(limit, (v) => {
    if (v) {
      showDialog({
        message: '已经这么牛了,也给别人一些机会吧',
        confirmButtonText: '我知道了',
        confirmButtonColor: '#FF6D73',
      })
    }
  }, {
    immediate: true,
  })
  return {
    limit,
    dealLimit,
  }
}
