<script setup lang="ts">
import { useMediaControls } from '@vueuse/core'
import { deaQiniulUrl } from '~/src/utils'

export interface AudioMaterial {
  url: string
  id: string
}

const props = defineProps<{
  isMan?: boolean
  menuType: string
  materialList: AudioMaterial[]
  toUpWallList?: string[]
}>()

const emit = defineEmits<{
  (e: 'selection', val: AudioMaterial): void
  (e: 'delete', val: string): void
  (e: 'edit', val: AudioMaterial): void
  (e: 'upWall', val: string): void
}>()
const route = useRoute()

const isManage = computed(() => route.path.startsWith('/manage'))
const isOem = computed(() => route.path.startsWith('/oem'))
const isSpo = computed(() => route.path.startsWith('/admin'))
const isMyMaterial = computed(() => props.menuType === 'my_material')
const showUpWall = computed(() => props.menuType === 'OFF')

const HiVideoComp = defineComponent({
  props: {
    material: {
      type: Object as PropType<AudioMaterial>,
      required: true,
    },
  },
  setup(props, { expose }) {
    const video = ref<HTMLVideoElement | null>(null)
    const { playing, volume } = useMediaControls(video, { src: deaQiniulUrl(props.material.url) })
    volume.value = 0
    expose({ playing, id: props.material.id })
    return () =>
      h('video', {
        ref: video,
        class: 'h-full w-full object-cover',
      })
  },
})

const videoRefs = ref<Record<string, any>>()

const isPlaying = (id: string) => videoRefs.value?.find((item: any) => item.id === id)?.playing
function handlePlayVideo(id: number | string) {
  const item = videoRefs.value?.find((item: any) => item.id === id)
  item.playing = !item.playing
}
function showSelected(item: AudioMaterial) {
  if (showUpWall.value) {
    return props.toUpWallList?.includes(item.id)
  }
}
function handleSelectMaterial(item: AudioMaterial) {
  if (showUpWall.value) {
    emit('selection', item)
  }
}
</script>

<template>
  <div class="video-box">
    <div v-for="item in materialList" :key="item.id" class="item-box" :class="{ selected: showSelected(item) }" @click="handleSelectMaterial(item)">
      <div class="item-top">
        <HiVideoComp ref="videoRefs" data-id="1" :material="item" />
        <div @click="handlePlayVideo(item.id)">
          <icon-ph-play-circle-duotone v-if="!isPlaying(item.id)" class="play-icon" :data-play="!isPlaying(item.id)" />
          <icon-ph-pause-circle-duotone v-else class="play-icon" :data-play="isPlaying(item.id)" />
        </div>
      </div>
      <div class="item-footer">
        <el-button v-if="!showUpWall" size="small" type="primary" @click="emit('selection', item)">立即使用</el-button>
        <el-button size="small" @click="emit('edit', item)">编辑</el-button>
        <el-button v-if="showUpWall" size="small" @click="emit('upWall', item.id)">上架</el-button>
        <el-button v-if="isManage || isOem || (isSpo && isMyMaterial)" size="small" type="danger" @click="emit('delete', item.id)">删除</el-button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
 .video-box {
  flex: 1;
  height: inherit;
  overflow-y: auto;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  gap: 20px;
  padding: 10px;
  .item-box {
    border-radius: 3px;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    border: 1px solid #e6ebed;
    cursor: pointer;
    // 比例长宽 16:9
    width: 250px;
    &.selected {
      border: 1px solid #1261ff;
    }
    .item-top {
      height: 145px;
      width: 100%;
      background: #f5f7fb;
      overflow: hidden;
      position: relative;

      .play-icon {
        transition: all 0.3s;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 30px;
        color: #fff;
        display: none;
      }

      &:hover {
        .play-icon[data-play='true'] {
          transition: all 0.3s;
          display: block;
        }
      }
    }
    .item-footer {
      height: 40px;
      padding: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .name {
        width: 70%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    &.cur {
      outline: 2px solid #1261ff;
    }
  }
}
</style>
