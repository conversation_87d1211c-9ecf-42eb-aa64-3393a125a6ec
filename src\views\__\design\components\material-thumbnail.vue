<script setup lang="ts">
const emits = defineEmits(['select', 'reset'])

function selectMaterialFn() {
  emits('select')
}

function resetDataFn() {
  emits('reset')
}
</script>

<template>
  <div class="group material-thumbnail relative h-full w-full">
    <slot />
    <div class="absolute bottom-0 left-0 right-0 top-0 hidden items-center justify-around rd-2 bg-[rgba(0,0,0,0.6)] text-20px text-white font-bold group-hover:flex">
      <el-tooltip content="选择素材" placement="top" :hide-after="0">
        <icon-ph:upload-simple-fill class="cursor-pointer" @click="selectMaterialFn" />
      </el-tooltip>

      <el-tooltip content="重置" placement="top" :hide-after="0">
        <icon-ph:arrows-counter-clockwise-light class="cursor-pointer" @click="resetDataFn" />
      </el-tooltip>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.material-thumbnail {
  :deep(img) {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
</style>
