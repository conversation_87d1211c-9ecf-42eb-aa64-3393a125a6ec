<script setup lang="tsx">
import type { MaybeReadonlyRefOrGetter, Placement } from '@floating-ui/vue'
import type { MaybePromise } from 'rollup'
import type { CSSProperties } from 'vue'
import { arrow, autoUpdate, flip, offset, shift, useFloating } from '@floating-ui/vue'
import { usePopperContainer, useZIndex } from 'element-plus'

const props = withDefaults(
  defineProps<{
    class?: any
    trigger?: 'click' | 'hover' | 'focus'
    placement?: MaybeReadonlyRefOrGetter<Placement | undefined>
    beforeClose?: (e: MouseEvent) => MaybePromise<boolean | undefined>
  }>(),
  {
    trigger: 'click',
    placement: 'bottom-start',
  },
)

const emits = defineEmits<{
  (e: 'afterLeave'): void
  (e: 'afterEnter'): void
  (e: 'beforeLeave'): void
  (e: 'beforeEnter'): void
}>()
const { nextZIndex } = useZIndex()
const zIndex = nextZIndex()
const container = usePopperContainer()

const id = `hi-floating-${Math.random().toString(36).slice(2, 12)}`
const visible = ref(false)
const reference = ref<HTMLElement>()
const floating = ref<HTMLElement>()
const floatingArrow = ref<HTMLElement>()

const {
  update,
  placement,
  floatingStyles,
  middlewareData,
} = useFloating(reference, floating, {
  transform: false,
  middleware: [
    offset(10),
    flip(),
    shift(),
    arrow({ element: floatingArrow }),
  ],
  whileElementsMounted: autoUpdate,
  placement: props.placement,
})

const tooltipStyle = computed(() => {
  return {
    ...floatingStyles.value,
    background: '#fff',
    padding: '6px',
    borderRadius: '4px',
    boxShadow: 'var(--el-box-shadow-light)',
    display: visible.value ? 'block' : 'none',
    zIndex,
  }
})

const arrowDirection = computed(() => {
  if (!placement) return 'top'
  const arr = unref(placement).split('-')
  return arr[0]
})
const arrowStyle = computed(() => {
  const style: CSSProperties = {
    position: 'absolute',
    width: '8px',
    height: '8px',
    background: '#fff',
    transform: 'rotate(45deg)',
    boxShadow: 'var(--el-box-shadow-light)',
  }
  const { x: arrowX, y: arrowY } = middlewareData.value.arrow || {}
  switch (arrowDirection.value) {
    case 'left':
    case 'right':
      style.top = arrowY != null ? `${arrowY}px` : ''
      break
    case 'top':
    case 'bottom':
      style.left = arrowX != null ? `${arrowX}px` : ''
      break
  }
  switch (arrowDirection.value) {
    case 'left':
      style.right = '-4px'
      break
    case 'top':
      style.bottom = '-4px'
      break
    case 'right':
      style.left = '-4px'
      break
    case 'bottom':
      style.top = '-4px'
      break
  }

  return style
})

async function outclickFn(e: MouseEvent) {
  if (!visible.value) return
  // 判断是否要关闭
  const target = e.target as HTMLElement
  if (target === reference.value) return
  if (target.closest(`[data-id="${id}"]`)) return
  // 在弹窗中不关闭
  if (target.closest('.el-popper')) return

  if (props.beforeClose) {
    const res = await props.beforeClose(e)
    if (res === false) return
  }
  visible.value = false
}
watch(
  () => visible.value,
  async (v) => {
    // before
    if (v) {
      emits('beforeEnter')
    } else {
      emits('beforeLeave')
    }
    await nextTick()
    // after
    if (v) {
      document.addEventListener('click', outclickFn)
      emits('afterEnter')
    } else {
      document.removeEventListener('click', outclickFn)
      emits('afterLeave')
    }
  },
)

const isntance = getCurrentInstance()

onMounted(() => {
  const children = isntance?.subTree.children
  // @ts-ignore
  const referenceDom = children?.[0]?.dynamicChildren?.[0]?.el as HTMLElement
  if (referenceDom) {
    reference.value = referenceDom
    reference.value.addEventListener(props.trigger, () => {
      visible.value = !visible.value
      update()
    })
  }
  update()
})
</script>

<template>
  <slot name="reference"></slot>
  <teleport :to="container.selector.value">
    <div ref="floating" class="el-popper" :data-id="id" :class="props.class" :style="tooltipStyle">
      <slot v-if="visible"></slot>
      <div ref="floatingArrow" :style="arrowStyle"></div>
    </div>
  </teleport>
</template>

<style scoped lang="scss">

</style>
