<script setup lang="ts">
import type { IDesignMaterialType } from '../../types'
import { locale } from '@/utils/element-plus'
import { deaQiniulUrl } from '~/src/utils'
import manage from './__/manage.vue'
import sponsor from './__/sponsor.vue'

defineProps<{
  materialType: IDesignMaterialType
}>()
const emit = defineEmits<{
  (e: 'cancle'): void
  (e: 'change', val: string): void
}>()

const route = useRoute()
const dialogVisible = ref(true)

const isManage = ref(route.path.startsWith('/manage') || route.path.startsWith('/test'))
const isOem = ref(route.path.startsWith('/oem'))

const dialogWidth = computed(() => {
  if (window.innerWidth < 768) return '95%'
  if (window.innerWidth < 1000) return '85%'
  if (window.innerWidth < 1440) return '1000px'
  return '1200px'
})

function selection(val: any) {
  emit('change', deaQiniulUrl(val.url))
  emit('cancle')
}
</script>

<template>
  <el-config-provider :locale="locale">
    <el-dialog
      v-model="dialogVisible"
      class="moveable-ignore"
      title="素材"
      :width="dialogWidth"
      destroy-on-close
      append-to-body
      :close-on-press-escape="false"
      @close="$emit('cancle')"
    >
      <manage v-if="isManage || isOem" :material-type="materialType" @selection="selection" />
      <sponsor v-else :material-type="materialType" @selection="selection" />
    </el-dialog>
  </el-config-provider>
</template>
