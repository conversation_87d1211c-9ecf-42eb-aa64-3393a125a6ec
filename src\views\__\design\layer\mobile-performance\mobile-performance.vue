<script setup lang="ts">
import type { IDesignMobilePerformance, IPerformanceForm } from './mobile-performance'
import { showToast } from 'vant'
import { defineCustomEmits, useDataAttr, useDesignState, useDesignTemp } from '../../index'
import { defaultData } from './mobile-performance'

const layer = defineModel<IDesignMobilePerformance>('layer', { required: true })

const designTemp = useDesignTemp()
const designState = useDesignState()
const customEmits = defineCustomEmits(layer)

const saveData = ref<Record<string, string | number>>({
  // 其余数据和rules一致
})

const sortForm = computed(() => {
  const form = (designState.getLayerData('performance-form') || []).filter((item: { type: string }) => item.type !== 'CUSTOM') as IPerformanceForm[]
  return form.sort((a, b) => {
    if (layer.value.data?.formSort?.[a.key] == null) {
      return 1
    }
    if (layer.value.data?.formSort?.[b.key] === null) {
      return -1
    }
    return layer.value.data?.formSort?.[a.key] - layer.value.data.formSort?.[b.key]
  })
})

watch(() => sortForm.value, (newValue) => {
  if (!newValue) return
  const data = newValue.reduce((acc, cur) => {
    if (cur.type === 'GROUP') {
      acc[cur.key] = JSON.parse(cur.value!)[0]
    } else {
      acc[cur.key] = cur.value || ''
    }
    return acc
  }, {} as Record<string, string>)
  saveData.value = { ...data, ...saveData.value }
}, {
  immediate: true,
})

const target = computed(() => {
  return designState.getLayerData('performance-target')
})

watch(() => target.value, (newValue) => {
  if (!newValue) return
  saveData.value = newValue
}, {
  immediate: true,
})

const isEdit = ref(true)

const saveButtonImgBind = useDataAttr(layer.value.data, 'saveButtonImg', defaultData.saveButtonImg)
const cancelButtonImgBind = useDataAttr(layer.value.data, 'cancelButtonImg', defaultData.cancelButtonImg)
const updateButtonImgBind = useDataAttr(layer.value.data, 'updateButtonImg', defaultData.updateButtonImg)
const isAllowEdit = computed(() => {
  return layer.value.data.isAllowEdit ?? defaultData.isAllowEdit
})

function showMessage(message: string) {
  if (designTemp.isEdit) {
    alert(message)
  } else {
    showToast(message)
  }
}

function saveTarget() {
  // 检测是否存在空值
  if (Object.entries(saveData.value)
    .map(([_key, value]) => value)
    .includes('')) {
    showMessage(`请填写完整再提交`)
    return
  }

  // 如果type是NUM，最多只能输入小数点后2位
  // eslint-disable-next-line regexp/no-unused-capturing-group
  const numReg = /^\d+(\.\d{0,2})?$/
  const allNum = sortForm.value.filter(item => item.type === 'NUM')
  allNum.forEach((item) => {
    saveData.value[item.key] = Number.parseFloat(`${saveData.value[item.key]}`)
  })
  const hasInvalidNumber = allNum.some(item => !numReg.test(String(saveData.value[item.key])))
  if (hasInvalidNumber) {
    showMessage('数字只能小数点后两位')
    return
  }

  isEdit.value = false
  console.log('saveTarget', saveData)
  customEmits('save', saveData.value)
}
</script>

<template>
  <div class="mobile-performance-box">
    <template v-if="Object.keys(saveData).length > 1">
      <div
        v-for="rule in sortForm"
        :key="rule.key"
        class="input-container"
      >
        <input
          v-if="['TEXT', 'NUM'].includes(rule.type)"
          v-model="saveData[rule.key]"
          :disabled="!isEdit"
          :placeholder="`请输入${rule.name}`"
          class="input"
          :type="rule.type === 'NUM' ? 'number' : 'text'"
        />
        <select
          v-else-if="rule.type === 'GROUP'"
          v-model="saveData[rule.key]"
          class="input"
          :disabled="!isEdit"
        >
          <option value="" disabled>请选择{{ rule.name }}</option>
          <option
            v-for="item in JSON.parse(rule.value!)"
            :key="item"
            :value="item"
          >
            {{ item }}
          </option>
        </select>

        <span v-if="rule.type === 'NUM' && rule.unit" class="unit">{{ rule.unit }}</span>
      </div>
      <template v-if="isEdit">
        <!-- 提交按钮 -->
        <img class="btn" :src="saveButtonImgBind" @click="saveTarget" />
        <!-- 取消按钮 -->
        <img v-if="saveData.id" class="btn" :src="cancelButtonImgBind" @click="isEdit = false" />
      </template>
      <!-- 修改按钮 -->
      <img v-else-if="isAllowEdit" class="btn" :src="updateButtonImgBind" @click="isEdit = true" />
    </template>
    <div v-else class="empty">
      <img src="./assets/empty.png" alt="" />
      <p>未创建业绩目标会活动</p>
    </div>
  </div>
</template>

<style scoped lang="scss">
.mobile-performance-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 20%;
  box-sizing: border-box;
  height: 100%;
  overflow-y: auto;
}

.input-container {
  width: 80%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  color: #fff;
  .input {
    flex: 1;
    border-radius: 6px;
    font-size: 14px;
    outline: none;
    padding: 10px;
    height: 40px;
    border: 1px solid #ccc;
    color: #000;
    &:disabled,
    &[disabled] {
      color: #000;
      opacity: 0.7 !important;
      background-color: #eee !important;
    }
  }

  .unit {
    max-width: 20%;
    margin-left: 10px;
    font-size: 14px;
  }
  .number {
    font-size: 20px;
    color: #ffef00;
    text-align: center;
  }
}

.btn {
  display: block;
  width: 60%;
  margin: 10px auto;
  cursor: pointer;
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 66%;
  font-size: 18px;
  color: #fff;
  img {
    width: 40%;
    margin-bottom: 20px;
  }
}
</style>
