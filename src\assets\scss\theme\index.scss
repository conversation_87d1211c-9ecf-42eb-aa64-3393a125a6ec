@use './function' as *;

// 修改以下颜色值即可，其他不用修改
$spo-theme-color: #3c55ff !default;

body,
html {
  background-color: #f2f3f5;
}

:root {
  //按钮色
  --el-button-hover-border-color: #6377ff;
  --el-button-hover-bg-color: #6377ff;
  --el-button-active-bg-color: #364ce5;
  --el-button-active-border-color: #364ce5;

  // 分割线边框色
  --el-border-color: #dcdfe6;
  --el-border-color-light: #dcdfe6;

  //斑马色
  --el-fill-color-lighter: #fafafa;

  // 警示色
  --el-color-danger: #f8544b;
  --el-color-error: #f8544b;
  --el-dialog-padding-primary: 24;

  //form 字体色
  --el-text-color-primary: #303133;
  --el-text-color-regular: #303133;

  --nprogress-color: #3c55ff;
}

// form表单错误提示间距
.el-form-item--default .el-form-item__error {
  padding-top: 4px;
}

// dialog包装
.el-dialog {
  border-radius: 8px;
  overflow: hidden;
  padding: 0;

  .el-dialog__header {
    border-top-right-radius: 8px;
    border-top-left-radius: 8px;
    background-color: #f2f3f5;
    padding-bottom: 10px;
    padding-top: 10px;
    margin-right: 0;

    .el-dialog__title {
      margin-left: 20px;
    }

    .el-dialog__headerbtn {
      top: 1px;

      .el-dialog__close {
        width: 23px;
        height: 23px;

        svg {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  .el-dialog__footer,
  .el-dialog__body {
    padding: calc(var(--el-dialog-padding-primary) - 5px) calc(var(--el-dialog-padding-primary) - 5px)
      var(--el-dialog-padding-primary) calc(var(--el-dialog-padding-primary) - 5px);
  }
}

.el-input__wrapper {
  background-color: #fff !important;
}

// table表头
.el-table__header-wrapper {
  .el-table__header {
    .el-table__cell {
      background-color: #f5f7fa;
      color: rgba(48, 49, 51, 0.9);
      font-size: 14px;
    }
  }
}

.el-tabs__item.is-active {
  font-weight: 700;
}

// 表格上面的操作栏
.select-box > * {
  margin-bottom: 16px;
  margin-right: 12px;
  margin-left: 0;
}

.select-box + .select-box > * {
  margin-right: 0;
  margin-left: 12px;
}

/* 也可以更具体地选择需要移除 outline 的元素 */
.el-tooltip__trigger:focus {
  outline: none;
}

audio {
  background-color: #f1f3f4;
  height: 35px;
  width: 280px;
  padding-right: 10px;
  border-radius: 50px;
  -webkit-user-drag: none;

  &::-webkit-media-controls {
    overflow: hidden !important;
  }

  &::-webkit-media-controls-enclosure {
    width: calc(100% + 40px);
    margin-left: auto;
  }
}
