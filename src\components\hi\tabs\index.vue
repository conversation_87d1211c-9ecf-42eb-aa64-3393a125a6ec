<script setup lang="ts">
const props = defineProps<{
  tabs: { label: string, value?: string | number }[]
}>()

const tab = defineModel<string | number>()
onMounted(() => {
  if (tab.value) {
    const item = props.tabs.find(item => (item.value ?? item.label) === tab.value)
    if (!item) {
      tab.value = props.tabs[0].value ?? props.tabs[0].label
    }
  } else {
    tab.value = props.tabs[0].value ?? props.tabs[0].label
  }
})
</script>

<template>
  <ul>
    <li v-for="item in tabs" :key="item.value" :class="{ cur: (item.value ?? item.label) === tab }" @click="tab = item.value ?? item.label">{{ item.label }}</li>
  </ul>
</template>

<style scoped lang="scss">
ul {
  display: inline-flex;
  height: 32px;
  background: #f0f2f8;
  border-radius: 3px;
  padding: 2px;
  li {
    line-height: 28px;
    padding: 0 10px;
    border-radius: 3px;
    cursor: pointer;
    transition: 0.3s;
    &.cur {
      color: #7c8088;
      background-color: #fff;
    }
  }
}
</style>
