import { merge } from 'lodash-es'
import { BisTypes } from '../../'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './mobile-ad-setting.vue'
import Comp from './mobile-ad.vue'

// 类型
export const type = 'mobile-ad'

// 数据类型约束
export interface IDesignMobileAd extends IDesignLayer {
  type: typeof type
  data: any
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.common,
    type,
    name: '广告',
    showType: ['mobile'],
    showInteractive: [InteractiveEnum.lotteryv3, InteractiveEnum.listlotteryv3, InteractiveEnum.piclotteryv3],
    Comp,
    CompSetting,
    thumbnail: new URL('./mobile-ad.png', import.meta.url).href,
    defaultData(options): IDesignMobileAd {
      return merge({
        uuid: layerUuid(),
        name: '广告',
        type,
        style: {},
        data: {},
      }, options as IDesignMobileAd)
    },
  })
}
