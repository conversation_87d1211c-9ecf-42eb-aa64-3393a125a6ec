<script setup lang="ts">
defineProps<{
  message: string
}>()
</script>

<template>
  <div class="box">
    <div class="wrap">{{ message }}</div>
  </div>
</template>

<style scoped lang="scss">
.box {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}
.wrap {
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 20px 30px;
  border-radius: 10px;
  font-size: 16px;
}
</style>
