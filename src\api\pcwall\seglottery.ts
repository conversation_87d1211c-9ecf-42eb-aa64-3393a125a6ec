import { HiRequest } from '../request'

export default {
  read: (params: any) => HiRequest.post('/pro/hxc/web/proseglottery/read.htm', params),
  start: (params?: any) => HiRequest.post('/pro/hxc/web/proseglottery/start.htm', params),
  list: (params?: any) => HiRequest.post('/pro/hxc/web/proseglottery/list.htm', params),
  stop: (params?: any) => HiRequest.post('/pro/hxc/web/proseglottery/stop.htm', params),
  next: (params?: any) => HiRequest.post('/pro/hxc/web/proseglottery/next.htm', params),
  importList: (params: any) => HiRequest.post('/pro/hxc/web/proseglottery/importList.htm', params),
  switch: (params: any) => HiRequest.post('/pro/hxc/web/proseglottery/switch.htm', params),
  updateState: (params: any) => HiRequest.post('/pro/hxc/web/proseglottery/updateState.htm', params),
  stageList: (params: any) => HiRequest.post('/pro/hxc/web/proseglotterystage/list.htm', params),
  updateLottery: (params: any) => HiRequest.post('/pro/hxc/web/proseglotteryconfig/updateLottery.htm', params),
  importCnt: (params: any) => HiRequest.post('/pro/hxc/web/proseglottery/importCnt.htm', params),
  result: (params: any) => HiRequest.post('/pro/hxc/web/proseglottery/result.htm', params),
  reset: (params: any) => HiRequest.post('/pro/hxc/web/proseglottery/reset.htm', params),
}
