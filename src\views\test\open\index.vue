<script setup lang="ts">
definePage({ meta: { label: '三方接口' } })

interface IThird {
  label: string
  value: string
}
const appId = ref('third_default')
const thirdList = ref<IThird[]>([
  { label: '默认服务商', value: 'third_default' },
  { label: '自定义服务商', value: 'third_self' },
])
const userName = ref('123456')

const href = computed(() => {
  if (!appId.value || !userName.value) {
    return
  }
  return `/pro/hxc/test/prouser/testLogin.htm?appId=${appId.value}&userName=${userName.value}`
})

const iframeSwitch = ref(false)
</script>

<template>
  <div class="open-box">
    <div class="form-box">
      <el-select v-model="appId">
        <el-option
          v-for="item in thirdList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-input v-model.trim="userName" />
      <div>
        <a v-if="href" :href="href" target="_blank">
          <el-button type="primary" :disabled="!userName">登录</el-button>
        </a>
        <el-button type="primary" class="ml-20" @click="iframeSwitch = !iframeSwitch">Iframe打开</el-button>
      </div>
    </div>
    <iframe v-if="iframeSwitch && href" class="iframe-box" :src="href"></iframe>
  </div>
</template>

<style scoped lang="scss">
.open-box {
  padding: 20px;
}
.form-box {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 400px;
}
.iframe-box {
  margin-top: 20px;
  border: 1px solid #ddd;
  width: 80%;
  height: 600px;
}
</style>
