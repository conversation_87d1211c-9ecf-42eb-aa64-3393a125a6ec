import type { IShapeTipProps } from '../components/shake-tip.vue'
import { throttle } from 'lodash-es'
import { render } from 'vue'
import shakeAudio from '~/src/assets/audio/shake_sound.mp3'
import { useSound } from '~/src/hooks/useSound'
import { isLiveAgent } from '~/src/utils/agent'
import { envUtils } from '~/src/utils/env'
import ShakeTip from '../components/shake-tip.vue'

// 提示窗
function openTip(option: IShapeTipProps) {
  return new Promise<string>((resolve) => {
    const vnode = h(ShakeTip, {
      ...option,
      onOk: (select: string) => {
        resolve(select)
        /* 销毁组件重新走组件生命周期  */
        render(null, document.body)
      },
    })
    // @ts-expect-error
    vnode.appContext = window.__bootstrap__?._context
    render(vnode, document.body)
  })
}

interface CustomDeviceMotionEvent extends DeviceMotionEvent {
  accelerationIncludingGravity: DeviceMotionEventAcceleration | null
}

// 开发环境
function devShake() {
  console.warn('模拟Shake开始')
  const random = (min: number, max: number) => Number.parseInt(String(Math.random() * (max - min + 1) + min), 10)
  setInterval(() => {
    const accelerationIncludingGravity = {
      x: random(0, 200),
      y: random(0, 1800),
      z: random(0, 600),
    }
    dispatchDevicemotionEvent(accelerationIncludingGravity)
  }, 1000)
}

// 模拟devicemotion事件，系统内可能存在监听的地方都能正常使用
const devicemotionEvent = new Event('devicemotion') as CustomDeviceMotionEvent
function dispatchDevicemotionEvent(data: DeviceMotionEventAcceleration) {
  devicemotionEvent.accelerationIncludingGravity = {
    ...data,
  }
  window.dispatchEvent(devicemotionEvent)
}

export function useBindShake(
  count: Ref<number>,
  options?: {
    random?: boolean
    immediate: boolean
  },
) {
  const controller = new AbortController()

  // 一秒内随机3次
  const randomShake = throttle(() => {
    count.value += Math.floor(Math.random() * 3)
  }, 1000, { trailing: true })

  const timeout = 400
  const threshold = 8
  let lastTime = new Date()
  let lastX = 0
  let lastY = 0
  let lastZ = 0

  const isOpen = ref(!!options?.immediate) // 是否开启检测

  const sound = useSound(shakeAudio)

  function deviceMotionHandler(e: DeviceMotionEvent) {
    if (!isOpen.value) return
    const current = e.accelerationIncludingGravity
    if (!current) return

    if (lastX === null && lastY === null && lastZ === null) {
      lastX = current.x!
      lastY = current.y!
      lastZ = current.z!
      return
    }
    const deltaX = Math.abs(lastX - current.x!)
    const deltaY = Math.abs(lastY - current.y!)
    const deltaZ = Math.abs(lastZ - current.z!)
    if (
      (deltaX > threshold && deltaY > threshold)
      || (deltaX > threshold && deltaZ > threshold)
      || (deltaY > threshold && deltaZ > threshold)
    ) {
      const currentTime = new Date()
      const timeDifference = currentTime.getTime() - lastTime.getTime()
      if (timeDifference > timeout) {
        lastTime = new Date()
        lastX = current.x!
        lastY = current.y!
        lastZ = current.z!
        if (options?.random) {
          randomShake()
        } else {
          count.value++
        }
        // 播放摇一摇声音
        if (!sound?.isPlaying.value) {
          sound?.play()
        }
      }
    }
  }

  // 申请权限
  async function applyPermission() {
    if (!window.DeviceMotionEvent) {
      await openTip({ title: '提示', tips: '您好，你目前所用的设备不支持摇一摇！' })
    }
    if (envUtils.isDev) {
      // @ts-ignore
      window.devShake = devShake
      devShake()
    } else if ('requestPermission' in window.DeviceMotionEvent && window.DeviceMotionEvent.requestPermission) {
      try {
        // @ts-ignore
        const response = await window.DeviceMotionEvent.requestPermission()
        if (response !== 'granted') {
          await openTip({ title: '提示', tips: '您已拒绝微信“访问动作和方向”，请关闭微信进程，重新进入游戏点击“允许”后参与' })
        }
      } catch (err) {
        console.log('err:', err)
        await openTip({ title: '提示', tips: '请点击确定后进入游戏', btnText: '确定' })
        applyPermission()
      }
    }
  }

  const listenerDevicemotion = () => {
    window.removeEventListener('devicemotion', deviceMotionHandler, true)
    window.addEventListener('devicemotion', deviceMotionHandler, {
      capture: true,
      signal: controller.signal,
    })
  }

  const start = () => {
    isOpen.value = true
  }

  const stop = () => {
    isOpen.value = false
  }

  const iframeOnMessage = (event: any) => {
    if (['PLV_DEVICEMOTION_SWITCH', 'DEVICEMOTION_SWITCH'].includes(event.data.type)) {
      dispatchDevicemotionEvent(event?.data.message?.accelerationIncludingGravity)
    }
  }

  tryOnMounted(async () => {
    if (!isLiveAgent()) {
      applyPermission()
    }
    listenerDevicemotion()
    window.addEventListener('message', iframeOnMessage, { signal: controller.signal })
  })

  tryOnBeforeUnmount(() => {
    controller?.abort()
    sound?.stop()
  })

  return {
    start,
    stop,
  }
}
