{
  // 使用 IntelliSense 了解相关属性。
  // 悬停以查看现有属性的描述。
  // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Vite: Edge",
      "type": "msedge",
      "request": "launch",
      "url": "https://test.hixianchang.com:5173/?isDebug=1",
      "webRoot": "${workspaceFolder}",
      "sourceMaps": true,
      "skipFiles": [
        "${workspaceFolder}/node_modules/**"
      ],
      // 设置进入 debug 环境之前需要执行的任务。
      // 此名称对应项目中 .vscode 目录下 tasks.json 文件中的 label 属性）
      "preLaunchTask": "vuejs: start"
    }
  ]
}