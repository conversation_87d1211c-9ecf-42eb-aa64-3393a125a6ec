<script lang="ts" setup>
import { useDesignMusic } from '@/views/__/design/index.ts'
import { useImData } from '~/src/hooks/useImData'
import { changeMuteAudio } from '~/src/utils/audio'

definePage({ meta: { label: '大屏幕' } })

const { isPlaying } = useDesignMusic()

watch(() => isPlaying.value, (val) => {
  changeMuteAudio(!val)
})
// 数据同步
useImData({
  'im:audioOpenSwitch': isPlaying,
}, false)
</script>

<template>
  <hi-pcwall-layout />
</template>

<style scoped lang="scss">
</style>
