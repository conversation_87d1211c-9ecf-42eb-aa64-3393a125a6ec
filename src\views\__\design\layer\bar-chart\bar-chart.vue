<script setup lang="ts">
import { <PERSON><PERSON><PERSON> } from 'echarts/charts'
import { DatasetComponent, GridComponent, LegendComponent, TitleComponent, TooltipComponent, TransformComponent } from 'echarts/components'
import * as echarts from 'echarts/core'
import { LabelLayout, UniversalTransition } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'

import { defineCustomEmits, injectScale, useDesignState } from '../..'
import { defalutLableLenth, defalutLableRotate, defaultBarColor, defaultBarColorsType, defaultDirection, defaultShowSort, type IDesignBarChart } from './bar-chart'

const layer = defineModel<IDesignBarChart>('layer', { required: true })
const customEmits = defineCustomEmits(layer)

// 注册必须的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  Bar<PERSON><PERSON>,
  LabelLayout,
  UniversalTransition,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  LegendComponent,
])

const domRef = ref<HTMLElement>()
let chart: echarts.EChartsType
const designState = useDesignState()

const domRefSize = useElementSize(domRef, undefined, { box: 'border-box' })
const scale = injectScale()

const direction = computed(() => {
  return layer.value.direction ?? defaultDirection
})
const showSort = computed(() => {
  return layer.value.showSort ?? defaultShowSort
})
const barColorType = computed(() => {
  return layer.value.barColorsType ?? defaultBarColorsType
})
const lableRotate = computed(() => {
  return layer.value.lableRotate ?? defalutLableRotate
})
const lableLenth = computed(() => {
  return layer.value.lableLenth ?? defalutLableLenth
})

const blindData = computed(() => {
  const defaultData = { title: [], data: [] }
  if (!layer.value.dataSource) {
    return defaultData
  }
  const data = designState.getLayerData('allTableStatistics') || {}
  return data[layer.value.dataSource]
})

const firstTextkey = computed(() => {
  const arr = blindData.value?.title || []
  let firstText = ''
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i]
    if (['TEXT', 'GROUP'].includes(item.type)) {
      firstText = item.key
      break
    }
  }
  return firstText
})
const labelData = computed(() => {
  const key = firstTextkey.value
  if (!key) {
    return []
  }
  let list: any = blindData.value?.data || []
  if (direction.value === 'horizontal') {
    list = list.slice().reverse()
  }
  if (showSort.value) {
    return list.map((item: { key: string | number }, index: number) => {
      return `${index + 1}_cut_${item[key as keyof typeof item]}`
    }) || []
  }
  return list.map((item: { key: string | number }) => {
    return item[key as keyof typeof item]
  }) || []
})

// 获取所有值为数字的key
const valueNumberKeys = computed(() => {
  const title = blindData.value?.title || []
  const _arr: any = []
  const nameArr: string[] = []
  title.forEach((item: { key: string, type: string, name: string }) => {
    if (item.type === 'NUM' || item.type === 'CUSTOM') {
      _arr.push(item.key)
      nameArr.push(item.name)
    }
  })
  return {
    keys: _arr,
    nameArr,
  }
})

const allAndMore = computed(() => {
  return (valueNumberKeys.value.keys.length > 1) && barColorType.value === 'all'
})
const seriesData = computed(() => {
  let list: any = blindData.value?.data || []
  if (direction.value === 'horizontal') {
    list = list.slice().reverse()
  }
  const arr: any = []
  valueNumberKeys.value.keys.forEach((key: string, index: number) => {
    const data: any = []
    let isPercentType = false
    list.forEach((item: any, i: number) => {
      if (typeof item[key] === 'string' && item[key].includes('%')) {
        isPercentType = true
      }
      data.push({
        value: isPercentType ? Number(item[key].replace('%', '')) : Number(item[key]) || 0,
        itemStyle: !allAndMore.value
          ? {
              borderRadius: layer.value.barRadius,
              color: getBarColor(i),
            }
          : { borderRadius: layer.value.barRadius },
      })
    })
    arr.push({
      data,
      type: 'bar',
      barWidth: layer.value.barWidth * scale.value,
      name: valueNumberKeys.value.nameArr[index],
      itemStyle: allAndMore.value
        ? {
            color: getBarColor(index),
            borderRadius: layer.value.barRadius * scale.value,
          }
        : '',
      label: {
        show: true, // 显示标签
        position: direction.value === 'vertical' ? 'top' : 'right', // 标签位置
        color: layer.value.valueFontColor,
        fontSize: layer.value.valueFontSize * scale.value,
        valueAnimation: true,
        formatter: isPercentType ? '{c}%' : '{c}',
      },
    })
  })
  return arr
})

const xAxis = computed(() => {
  if (direction.value === 'vertical') {
    return {
      type: 'category',
      axisLine: { show: false }, // 隐藏轴线
      axisTick: { show: false }, // 隐藏刻度线
      data: labelData.value,
      axisLabel: {
        color: layer.value.labelFontColor,
        fontSize: layer.value.labelFontSize * scale.value,
        interval: 0, // 强制显示所有标签
        rotate: lableRotate.value,
        formatter(value: string) {
          return value.length > lableLenth.value ? `${value.slice(0, lableLenth.value)}…` : value
        },
      },
    }
  }
  return {
    type: 'value',
    show: false, // 隐藏轴
  }
})

const yAxis = computed(() => {
  if (direction.value === 'vertical') {
    return {
      type: 'value',
      show: false, // 隐藏Y轴
    }
  }
  return {
    type: 'category',
    axisLine: { show: false }, // 隐藏轴线
    axisTick: { show: false }, // 隐藏刻度线
    data: labelData.value,
    axisLabel: {
      color: layer.value.labelFontColor,
      fontSize: layer.value.labelFontSize,
      interval: 0, // 强制显示所有标签
      rotate: lableRotate.value,
      formatter(value: string) {
        return value.length > lableLenth.value ? `${value.slice(0, lableLenth.value)}…` : value
      },
    },
  }
})

const option = computed(() => {
  return {
    grid: {
      left: direction.value === 'vertical' ? '' : `${30 * scale.value}px`,
      right: `${50 * scale.value}px`,
      bottom: '3%',
      containLabel: true,
    },
    xAxis: xAxis.value,
    yAxis: yAxis.value,
    series: seriesData.value,
    legend: {
      show: allAndMore.value,
      data: valueNumberKeys.value.nameArr,
      left: 'center',
      textStyle: {
        color: layer.value.labelFontColor,
      },
    },
    responsive: true,
  }
})

function getBarColor(index: number) {
  if (barColorType.value === 'all') {
    const l = layer.value.barColorsAll.length
    return layer.value.barColorsAll[index % l] ?? defaultBarColor
  }
  if (barColorType.value === 'ones') {
    const l = layer.value.barColorsOnes.length
    // console.log('layer.value.barColorsOnes', index, index % l)
    return layer.value.barColorsOnes[index % l] ?? defaultBarColor
  }
}

watch(
  () => layer.value.dataSource,
  (c) => {
    chart?.clear()
    customEmits('dataSourceBlind', c)
  },
  { immediate: true },
)
watch([xAxis, yAxis, lableLenth], () => {
  // chart.clear()
  chart.setOption(option.value)
}, { deep: true })

watch(() => seriesData.value, async () => {
  try {
    chart.setOption(option.value)
  } catch (err) {
    console.log(err)
  }
}, { deep: true })
watch(
  () => [domRefSize.width.value, domRefSize.height.value],
  () => {
    chart.resize()
  },
)
onMounted(() => {
  chart = echarts.init(domRef.value as HTMLDivElement)
  chart.setOption(option.value)
})
</script>

<template>
  <div class="bar-chart-box">
    <div ref="domRef" class="wrap"></div>
  </div>
</template>

<style scoped lang="scss">
.bar-chart-box {
  width: 100%;
  height: 100%;
  .wrap {
    width: 100%;
    height: 100%;
    color: var(--color);
  }
}
</style>
