import { BisTypes, useDesignState } from '../..'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './rankings3-setting.vue'
import Comp from './rankings3.vue'

// 类型
export const type = 'rankings3'

export interface IDesignRankings3 extends IDesignLayer {
  type: typeof type
  default: string
  data: { name: string, avatar: string }[]
  startRanking?: number
  decoration: string
  itemWidth?: number
  gap?: number
}

export function setup(app: IDesignSetup) {
  let status: string[]
  switch (app.interactive) {
    case InteractiveEnum.shakev3:
      status = ['finish']
      break
    default:
      status = ['finish']
      break
  }

  app.registry({
    dev: true,
    showType: ['pcwall'],
    bisType: BisTypes.rankingList,
    thumbnail: new URL('./rankings3.png', import.meta.url).toString(),
    type,
    name: '排行榜3',
    status,
    Comp,
    showInteractive: [InteractiveEnum.shakev3, InteractiveEnum.moneyv3, InteractiveEnum.goldcoinv3, InteractiveEnum.diglettv3, InteractiveEnum.firev3],
    mountedCondition: computed(() => {
      const designState = useDesignState()
      const rankings = designState.getLayerData('endRankings')
      return !!rankings
    }),
    CompSetting,
    defaultData() {
      return {
        uuid: layerUuid(),
        name: '排行榜3',
        type,
        default: new URL(`./assets/default.png`, import.meta.url).href,
        data: [],
        decoration: new URL(`./assets/decoration.png`, import.meta.url).href,
        style: {
          width: '960px',
          height: '170px',
          top: '160px',
          left: '130px',
        },
      }
    },
  })
}
