<script setup lang="ts">
import type { IDesignRankings } from './rankings'
import { cloneDeep } from 'lodash-es'
import { defineCustomEmits, useDesignState } from '../..'

const designState = useDesignState()
const layer = defineModel<IDesignRankings>('layer', { required: true })
const customEmits = defineCustomEmits(layer)
const rankingsRef = ref<HTMLElement>()

const count = 3

const rankings = computed(() => {
  const list = cloneDeep(designState.getLayerData('endRankings') || [])
  if (list.length < count) {
    for (let i = 0, size = count - list.length; i < size; i++) {
      list.push({ name: '', avatar: layer.value.default, score: 0 })
    }
  }
  return list.slice(0, count)
})

const starStyle = computed(() => {
  return {
    background: `url(${layer.value.star}) no-repeat 50%`,
  }
})

onMounted(() => {
  customEmits('finishRankingCount', count)
})
</script>

<template>
  <div ref="rankingsRef" v-objectfit="[823, 452]" class="rankings-box">
    <img class="light" :src="layer.light" />
    <img class="place-bg" :src="layer.podium" />
    <template v-for="(item, index) in rankings" :key="`${item.name}_${item.avatar}`">
      <img :src="item.avatar" class="avatar-item" :class="[`item-${index}`]">
      <div class="name-item" :class="[`item-${index}`]"><span>{{ item.name }}</span></div>
    </template>
    <div class="star-box">
      <span v-for="item in 7" :key="item" :style="starStyle"></span>
    </div>
  </div>
</template>

<style scoped lang="scss">
@use 'sass:math';

.light {
  position: absolute;
  width: 80%;
  aspect-ratio: 1;
  top: -40%;
  left: 10%;
  animation: light-rotate 3s linear infinite;
}
@keyframes light-rotate {
  0% {
    transform: rotate(0);
  }
  to {
    transform: rotate(1turn);
  }
}

.place-bg,
.star-box {
  position: absolute;
  width: 100%;
  height: 100%;
}
.place-bg {
  top: 0;
  left: 0;
  z-index: 5;
}
.avatar-item {
  position: absolute;
  z-index: 2;
  aspect-ratio: 1;
  border-radius: 50%;
  overflow: hidden;

  &.item-0 {
    width: math.percentage(math.div(208, 823));
    top: math.percentage(math.div(40, 452));
    left: math.percentage(math.div(308, 823));
  }
  &.item-1 {
    width: math.percentage(math.div(135, 823));
    top: math.percentage(math.div(159, 452));
    left: math.percentage(math.div(100, 823));
  }
  &.item-2 {
    width: math.percentage(math.div(135, 823));
    top: math.percentage(math.div(174, 452));
    left: math.percentage(math.div(594, 823));
  }
}
.name-item {
  position: absolute;
  z-index: 6;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #ffee7a;

  span {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  &.item-0 {
    width: math.percentage(math.div(204, 823));
    height: math.percentage(math.div(50, 452));
    top: math.percentage(math.div(213, 452));
    left: math.percentage(math.div(310, 823));
    font-size: calc(var(--width) * 0.04);
  }
  &.item-1 {
    width: math.percentage(math.div(168, 823));
    height: math.percentage(math.div(40, 452));
    top: math.percentage(math.div(266, 452));
    left: math.percentage(math.div(83, 823));
    font-size: calc(var(--width) * 0.03);
  }
  &.item-2 {
    width: math.percentage(math.div(165, 823));
    height: math.percentage(math.div(40, 452));
    top: math.percentage(math.div(280, 452));
    left: math.percentage(math.div(578, 823));
    font-size: calc(var(--width) * 0.03);
  }
}
.star-box {
  z-index: 10;
  span {
    position: absolute;
    width: math.percentage(math.div(34, 823));
    height: math.percentage(math.div(34, 452));
    background-size: contain;
    animation: xing-data 2s ease-in-out 1.5s infinite backwards;

    &:nth-child(1) {
      top: math.percentage(math.div(320, 452));
      left: math.percentage(math.div(0, 823));
      animation-delay: -2287ms;
    }
    &:nth-child(2) {
      top: math.percentage(math.div(230, 452));
      left: math.percentage(math.div(50, 823));
      animation-delay: -2074ms;
    }
    &:nth-child(3) {
      top: math.percentage(math.div(180, 452));
      left: math.percentage(math.div(230, 823));
      animation-delay: 1648ms;
    }
    &:nth-child(4) {
      top: math.percentage(math.div(70, 452));
      left: math.percentage(math.div(280, 823));
      animation-delay: -2435ms;
    }
    &:nth-child(5) {
      top: math.percentage(math.div(200, 452));
      left: math.percentage(math.div(550, 823));
      animation-delay: -2222ms;
    }
    &:nth-child(6) {
      top: math.percentage(math.div(250, 452));
      left: math.percentage(math.div(770, 823));
      animation-delay: 1861ms;
    }
    &:nth-child(7) {
      top: math.percentage(math.div(100, 452));
      left: math.percentage(math.div(740, 823));
      animation-delay: 181ms;
    }
  }
}
@keyframes xing-data {
  0% {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}
</style>
