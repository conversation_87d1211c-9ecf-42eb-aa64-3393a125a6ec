<script setup lang="ts">
import SignaturePad from 'signature_pad'
import { useI18n } from 'vue-i18n'
import { envUtils } from '~/src/utils/env'
import { Toast } from '~/src/utils/toast'
import { defaultConfig } from '~/src/views/admin/wall/padsign'
import dealScroll from './dealScroll'
import 'vant/lib/index.css'

const { t } = useI18n()

definePage({ meta: { label: 'iPad 签字', ignoreLola: true } })

const boxRef = ref<HTMLDivElement>()
const boxSize = useElementSize(boxRef)
// 是否旋转
const isRotate = computed(() => {
  return boxSize.width.value < boxSize.height.value
})
const canvasProps = computed(() => {
  return {
    width: `${boxSize.width.value}px`,
    height: `${boxSize.height.value}px`,
  }
})
const rotateStyle = computed(() => {
  if (isRotate.value) {
    return {
      transform: 'rotate(90deg) translateX(-100vw)',
      transformOrigin: 'bottom left',
      width: `${boxSize.height.value}px`,
      height: `${boxSize.width.value}px`,
    }
  }
  return {}
})

const isLoading = ref(false)
// 配置数据
const padsignConfig = ref({
  bgVideo: '', // 背景视频
  bgImage: '', // 背景图片
  fontColor: '',
  fontSize: [0, 0],
})

// 查配置
async function fetchConfig() {
  let config = await api.pcwall.padsign.configRead({})
  if (config) {
    config = Object.assign({}, defaultConfig, config)
  } else {
    config = defaultConfig
  }

  const resource = config.bgMobile || defaultConfig.bgMobile
  const endFlag = `${resource.split('.').reverse()[0]}`.toLocaleLowerCase()
  if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'bpm'].includes(endFlag)) {
    padsignConfig.value.bgImage = resource
  } else if (['mp4', 'mov', 'avi', 'mpg', 'mpeg', 'webm'].includes(endFlag)) {
    padsignConfig.value.bgVideo = resource
  } else {
    Toast.message(t('padsign.unknownBgType', [endFlag]))
  }
  padsignConfig.value.fontColor = config.fontColor
  padsignConfig.value.fontSize = JSON.parse(config.fontSize)
}

const canvasRef = ref<HTMLCanvasElement>()
let signaturePad: SignaturePad | null = null

const canvasBoxBackgroundImage = computed(() => {
  return `url(${padsignConfig.value.bgImage})`
})

function onReset() {
  signaturePad?.clear()
}

async function onSubmit() {
  if (!canvasRef.value) return

  if (signaturePad?.isEmpty()) {
    return
  }

  let _canvas = trimCanvas(canvasRef.value)
  if (isRotate.value) {
    // canvas 旋转 90 度
    _canvas = rotateCanvas(_canvas)
    _canvas = rotateCanvas(_canvas)
    _canvas = rotateCanvas(_canvas)
  }

  isLoading.value = true
  // 转成文件进行上传
  const blob = base64ToBlob(_canvas.toDataURL('image/png'))
  const file = new File([blob], 'test3d.png', { type: 'image/png' })
  const formData = new FormData()
  formData.append('file', file)
  // const res = await Upload(`/pro/hxc/web/procommonfile/uploadAnonymous.htm`, formData)
  const res = await axios.post(`/pro/hxc/web/procommonfile/uploadAnonymous.htm`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
  const img = `${envUtils.resdomain}/${res.data.data.url}`

  await api.pcwall.padsign.dataInsert({ img })

  canvasOutUp()
  onReset()
  isLoading.value = false
}

function canvasOutUp() {
  try {
    const rCanvas = canvasRef.value!
    const { width, height } = rCanvas
    const ctx = rCanvas!.getContext('2d')
    const cloneCanvas = canvasRef.value!.cloneNode() as HTMLCanvasElement

    const cloneCanvasCtx = cloneCanvas!.getContext('2d')

    cloneCanvasCtx?.putImageData(ctx!.getImageData(0, 0, width, height), 0, 0)

    Object.assign(cloneCanvas.style, {
      position: 'absolute',
      top: `${0}px`,
      left: `${0}px`,
      zIndex: 999,
      borderColor: 'transparent',
    })
    cloneCanvas.addEventListener('animationend', () => {
      boxRef.value!.removeChild(cloneCanvas)
      boxRef.value!.style.position = 'static'
    })

    cloneCanvas.classList.add(isRotate.value ? 'backOutLeft' : 'backOutUp')
    boxRef.value!.appendChild(cloneCanvas)
  } catch (e: any) {
    console.log(e.message)
  }
}

function trimCanvas(originalCanvas: HTMLCanvasElement) {
  const canvas = document.createElement('canvas')
  canvas.width = originalCanvas.width
  canvas.height = originalCanvas.height

  const ctx = canvas.getContext('2d')
  ctx!.drawImage(originalCanvas, 0, 0, canvas.width, canvas.height, 0, 0, canvas.width, canvas.height)

  // 裁剪，只提交有效像素区域，四边空白剪掉
  const padding = 10
  const imgData = ctx!.getImageData(0, 0, canvas.width, canvas.height).data
  let top = canvas.height
  let right = 0
  let bottom = 0
  let left = canvas.width
  for (let h = 0; h < originalCanvas.height; h++) {
    for (let w = 0; w < originalCanvas.width; w++) {
      const i = (h * originalCanvas.width + w) * 4
      const aPixel = imgData[i + 3]
      if (aPixel) {
        if (h < top) {
          top = h
        }
        if (h > bottom) {
          bottom = h
        }
        if (w > right) {
          right = w
        }
        if (w < left) {
          left = w
        }
      }
    }
  }
  top = Math.max(0, top - padding)
  right = Math.min(originalCanvas.width, right + padding)
  bottom = Math.min(originalCanvas.height, bottom + padding)
  left = Math.max(0, left - padding)

  canvas.width = originalCanvas.width - left - (originalCanvas.width - right)
  canvas.height = originalCanvas.height - (originalCanvas.height - bottom) - top
  ctx!.drawImage(originalCanvas, left, top, canvas.width, canvas.height, 0, 0, canvas.width, canvas.height)

  return canvas
}

function rotateCanvas(canvas: HTMLCanvasElement) {
  const rotateCanvas = document.createElement('canvas')
  rotateCanvas.width = canvas.height
  rotateCanvas.height = canvas.width
  const rotateCtx = rotateCanvas.getContext('2d')
  rotateCtx!.translate(canvas.height, 0)

  rotateCtx!.rotate(90 * (Math.PI / 180))
  rotateCtx!.drawImage(canvas, 0, 0)
  rotateCtx!.setTransform(1, 0, 0, 1, 0, 0)

  return rotateCanvas
}

function base64ToBlob(code: string) {
  const parts = code.split(';base64,')
  const contentType = parts[0].split(':')[1]
  const raw = window.atob(parts[1])
  const rawLength = raw.length
  const uInt8Array = new Uint8Array(rawLength)
  for (let i = 0; i < rawLength; ++i) {
    uInt8Array[i] = raw.charCodeAt(i)
  }
  return new Blob([uInt8Array], { type: contentType })
}

onMounted(async () => {
  dealScroll()
  await fetchConfig()
  await nextTick()
  signaturePad = new SignaturePad(canvasRef.value!, {
    minWidth: padsignConfig.value.fontSize[0],
    maxWidth: padsignConfig.value.fontSize[1],
    penColor: padsignConfig.value.fontColor,
  })
})
</script>

<template>
  <div ref="boxRef" class="sign-container">
    <canvas ref="canvasRef" v-bind="canvasProps"></canvas>
    <div class="bg-box" :style="rotateStyle"></div>
    <div class="footer" :style="rotateStyle">
      <div class="footer-wrap">
        <van-button class="w-20rem" @click="onReset">
          {{ t('padsign.resign') }}
        </van-button>
        <van-button type="primary" class="w-20rem" style="margin-left: 2rem" :loading="isLoading" @click="onSubmit">
          {{ t('padsign.submit') }}
        </van-button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.sign-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;

  > * {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}
canvas {
  z-index: 1;
}
.bg-box {
  background: v-bind(canvasBoxBackgroundImage) no-repeat center center;
  background-size: 100% 100%;
  position: relative;
  width: 100%;
  height: 100%;
  user-select: none;
}

.footer {
  pointer-events: none;
  z-index: 2;
  .footer-wrap {
    pointer-events: auto;
    position: absolute;
    right: 6%;
    bottom: 8%;
  }
}

.backOutUp {
  border-color: transparent;
  animation: backOutUp 3s ease 1 forwards;
}
@keyframes backOutUp {
  0% {
    transform: translateY(0);
  }

  to {
    transform: translateY(-100vw);
  }
}

.backOutLeft {
  border-color: transparent;
  animation: backOutLeft 3s ease 1 forwards;
}
@keyframes backOutLeft {
  0% {
    transform: translateY(0);
  }

  to {
    transform: translateX(100vw);
  }
}
</style>
