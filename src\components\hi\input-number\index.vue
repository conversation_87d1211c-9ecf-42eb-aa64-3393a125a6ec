<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    modelValue?: number
    disabled?: boolean
    readonly?: boolean
    min?: number
    max?: number
    prepend?: string
    append?: string
    precision?: number // 最大保留小数
  }>(),
  {
    min: 0,
    disabled: false,
    precision: 0,
  },
)
const emits = defineEmits<{
  (e: 'update:modelValue', v: number): void
}>()

function normalValue(v: string | number) {
  const x = Number(v)
  if (Number.isNaN(x)) {
    return `${props.min ?? 0}`
  }
  if (props.min !== undefined && x < props.min) {
    return `${props.min}`
  }
  if (props.max !== undefined && x > props.max) {
    return `${props.max}`
  }
  return `${v}`
}
const data = ref<string>(`${props.modelValue}`)
watch(() => props.modelValue, (v) => {
  if (Number(data.value) !== v) {
    data.value = `${v}`
  }
}, { immediate: true })
watch(data, (v) => {
  emits('update:modelValue', Number(normalValue(v)))
}, { immediate: true })

function inputFn(val: string | number) {
  let tmp = `${val}`
  // 判断是否负数
  const negative = tmp.startsWith('-')
  if (negative) {
    tmp = tmp.slice(1)
  }
  // 中文输入法下的小数点转换
  tmp = tmp.replace(/。/g, '.')
  if (tmp.length === 1 && tmp === '.') {
    tmp = '0.'
  } else {
    // 不能输入除数字和小数点外的字符
    tmp = tmp.replace(/[^\d.]/g, '')
    if (tmp.length > 1) {
      // 移除开头的0，除非是0.
      if (tmp.startsWith('0') && tmp[1] !== '.') {
        tmp = tmp.slice(1)
      }
      // 不能输入多个小数点
      const arr = tmp.split('.')
      if (arr.length > 2) {
        tmp = `${arr[0]}.${arr[1]}`
      }
      // 保留小数位数
      if (props.precision) {
        const index = tmp.indexOf('.')
        if (index !== -1) {
          tmp = `${tmp.slice(0, index + 1)}${tmp.slice(index + 1, index + 1 + props.precision)}`
        }
      }
    }
  }
  data.value = (negative ? '-' : '') + tmp
}
function blurFn() {
  data.value = `${Number(props.modelValue)}`
}

// 拖拽调整数值大小
let oldValue = 0
let startX = 0
function mousemoveFn(e: MouseEvent) {
  const step = 1 / (10 ** props.precision)
  const diff = (e.clientX - startX) * step
  data.value = normalValue(`${(oldValue + diff).toFixed(props.precision)}`)
}
function mouseupFn() {
  document.removeEventListener('mousemove', mousemoveFn)
  document.removeEventListener('mouseup', mouseupFn)
}
function mousedownFn(e: MouseEvent) {
  oldValue = Number(data.value)
  startX = e.clientX
  document.addEventListener('mousemove', mousemoveFn)
  document.addEventListener('mouseup', mouseupFn)
}
</script>

<template>
  <el-input
    v-model.trim="data"
    :disabled="disabled"
    :readonly="readonly"
    @input="inputFn"
    @blur="blurFn"
    @mousedown="mousedownFn"
  >
    <template v-if="prepend" #prepend>{{ prepend }}</template>
    <template v-if="append" #append>{{ append }}</template>
  </el-input>
</template>

<style scoped lang="scss">
.el-input {
  :deep() {
    .el-input-group__prepend,
    .el-input-group__append {
      padding: 0 6px;
    }
  }
}
</style>
