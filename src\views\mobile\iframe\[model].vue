<script lang="ts" setup>
import { cloneDeep } from 'lodash-es'
import { timer } from '~/src/utils'

definePage({ meta: { label: 'iframe嵌套' } })
type Model = 'shakev3' | 'goldcoinv3' | 'diglettv3'
const iframeRef = ref<HTMLIFrameElement>()
const route = useRoute()
const model = computed(() => (route.params as { model: Model }).model || 'shakev3')

const { mobileFlag } = route.query

const query: string[] = []
if (mobileFlag) {
  query.push(`mobileFlag=${mobileFlag}`)
}

const iframeUrl = computed(() => {
  return `${location.href.split('/iframe/')[0]}/wall/${model.value}?${query.join('&')}`
})

///////////////////
const allConfig = ref<any>()
const wall = ref<any>()
const wxuser = ref<any>()
const featureConfig = ref<any>()
const config = ref<any>()
const round = ref<any>()
// 对接现有的长链接服务
const connectTime = ref(0)
const maxId: Record<string, number> = {}

async function fetchAllConfig() {
  const _allConfig = await api.mobile.common.allConfig({
    where: { },
    except: ['deleteTag', 'createDate', 'updateDate'],
  })
  allConfig.value = _allConfig
  Object.keys(_allConfig).forEach((key) => {
    if (key === 'timeStamp') {
      connectTime.value = _allConfig[key]
    } else if (key === `${model.value}Config`) {
      config.value = _allConfig[key]
    } else if (key === 'featureConfig') {
      featureConfig.value = _allConfig[key]
    }
  })
}
async function fetchRound(id: number) {
  round.value = await api.mobile[model.value].read({ where: { id } })
}
watch(
  () => config.value?.[`${model.value}Id`],
  (v) => {
    fetchRound(v)
  },
)
async function goConnect() {
  await fetchAllConfig()
  // 启动长链接
  let valid = 1
  const next = async () => {
    try {
      const post: any = {
        timeStamp: connectTime.value,
        moduleArr: [
          { module: `${model.value}`, id: maxId[`${model.value}`] || -1, pageSize: 100 },
        ],
      }
      if (valid === 1) post.valid = 1
      const result = await api.mobile.common.connect(post)
      if (!result) return
      if (valid === 1) valid = 0
      Object.keys(result).forEach((key) => {
        if (key === 'config') {
          fetchAllConfig()
        } else if (key === `${model.value}`) {
          if (result[key].id) {
            maxId[key] = Math.max(maxId[key] || -1, result[key].id)
          }
          const dataList = result[key].dataList
          for (const item of dataList) {
            if (item.cmd === 'notice') {
              const data = item.data
              if (data.cmd === `pro_${model.value}_update`) {
                fetchRound(data.bizData.id)
              }
            }
            maxId[key] = Math.max(maxId[key] || -1, item.id)
          }
        }
      })
    } catch (e) {
      await timer(300)
      console.error(e)
    } finally {
      next()
    }
  }
  if (!['microsite'].includes(model.value)) {
    next()
  }
}
goConnect()

async function featchWall() {
  wall.value = await api.mobile.wall.read({ where: { mobileFlag: route.query.mobileFlag } })
}
async function featchWxuser() {
  wxuser.value = await api.mobile.wxuser.read({ })
}

// 微站专用 /////////////////
const activitys = [
  {
    actName: '签到',
    actType: 'applysign',
  },
  {
    actName: '提问',
    actType: 'question',
    color: '#41A1FF',
  },
  {
    actName: '许愿树',
    actType: 'wish',
    color: '#41A1FF',
  },
  {
    actName: '签到簿',
    actType: 'signbook',
    color: '#41A1FF',
  },
  {
    actName: '签名墙',
    actType: 'signature',
    color: '#41A1FF',
  },
  {
    actName: '图片墙',
    actType: 'pic',
    color: '#41A1FF',
  },
  {
    actName: '启动仪式',
    actType: 'launchingceremony',
    color: '#2ABE5B',
  },
  {
    actName: '订货会',
    actType: 'placeorder',
    color: '#2ABE5B',
  },
  {
    actName: '地图签到',
    actType: 'mapsign',
    color: '#00a2ff',
  },
  {
    actName: '业绩目标会',
    actType: 'performance',
    color: '#27bf5b',
  },
  {
    actName: '业绩目标会',
    actType: 'performancev3',
    color: '#27bf5b',
  },
  {
    actName: '对对碰',
    actType: 'supperzzle',
    color: '#fbba1e',
  },
  {
    actName: '滚动抽奖',
    actType: 'lottery',
    color: '#FF5B52',
  },
  {
    actName: '名单抽奖',
    actType: 'listlottery',
    color: '#FF5B52',
  },
  {
    actName: '红包雨',
    actType: 'redpack',
    color: '#FF5B52',
  },
  {
    actName: '图片抽奖',
    actType: 'piclottery',
    color: '#FF5B52',
  },
  {
    actName: '摇号抽奖',
    actType: 'lotlottery',
    color: '#FF5B52',
  },
  {
    actName: '弹幕抽奖',
    actType: 'danmulottery',
    color: '#FF5B52',
  },
  {
    actName: '拍照抽奖',
    actType: 'photolottery',
    color: '#FF5B52',
  },
  {
    actName: '套红包',
    actType: 'ropepack',
    color: '#FF5B52',
  },
  {
    actName: '语音红包',
    actType: 'voicepack',
    color: '#FF5B52',
  },
  {
    actName: '摇一摇(旧版)',
    actType: 'shake', // 需要根据不同主题变换图标
    color: '#FBBA1E',
  },
  {
    actName: '摇一摇',
    actType: 'shakev3',
    color: '#FBBA1E',
  },
  {
    actName: '敲敲乐',
    actType: 'diglett', // 需要根据不同主题变换图标
    color: '#FBBA1E',
  },
  {
    actName: '敲敲乐',
    actType: 'diglettv3',
    color: '#FBBA1E',
  },
  {
    actName: '数钱',
    actType: 'money',
    color: '#FBBA1E',
  },
  {
    actName: '数钱',
    actType: 'moneyv3',
    color: '#FBBA1E',
  },
  {
    actName: '拔河',
    actType: 'tugwar',
    color: '#FBBA1E',
  },
  {
    actName: '最佳射手',
    actType: 'shoot',
    color: '#FBBA1E',
  },
  {
    actName: '射击游戏',
    actType: 'fire', // 需要根据不同主题变换名称
    color: '#30eecd',
  },
  {
    actName: '射击游戏',
    actType: 'firev3',
    color: '#30eecd',
  },
  {
    actName: '接金币',
    actType: 'goldcoin',
    color: '#30eecd',
  },
  {
    actName: '接金币',
    actType: 'goldcoinv3',
    color: '#30eecd',
  },
  {
    actName: '答题',
    actType: 'answerrace',
    color: '#FBBA1E',
  },
  {
    actName: '投票',
    actType: 'vote',
    color: '#775FFF',
  },
  {
    actName: '打赏',
    actType: 'reward',
    color: '#775FFF',
  },
  {
    actName: '评分',
    actType: 'mark',
    color: '#775FFF',
  },
]
function isOpen(type: string) {
  const _allConfig = allConfig.value
  if (!_allConfig) return false
  const arr = [
    'threed',
    'countdown',
    'tugwar',
    'question',
    'mark',
    'answerrace',
    'playguess',
    'guest',
    'diglett',
    'ropepack',
    'topic',
    'mapsign',
    'wish',
    'signbook',
    'listlottery',
    'shakev3',
    'moneyv3',
    'firev3',
    'diglettv3',
    'goldcoinv3',
  ]
  const config = arr.includes(type) ? _allConfig[`${type}Config`] : _allConfig[`wall${type}Config`]
  if (type === 'launchingceremony') {
    return config && config.openState === 'Y' && (_allConfig.walllaunchingceremony || {}).mobileShowSwitch === 'Y'
  }
  if (type === 'applysign') {
    return config && config.signSuccessSwitch === 'Y'
  }
  return config && config.openState === 'Y'
}
const openActArr = computed(() => {
  const result: any[] = []
  activitys.forEach((item) => {
    if (isOpen(item.actType)) {
      result.push(item.actType)
    }
  })
  return result
})

///////////////////
function postMessage(type: string, message: any) {
  const contentWindow = iframeRef.value?.contentWindow
  if (contentWindow) {
    contentWindow.postMessage({ type, message: cloneDeep(message) }, '*')
  }
}
watch(wall, () => postMessage(`im:wall`, wall.value), { deep: true })
watch(wxuser, () => postMessage(`im:wxuser`, wxuser.value), { deep: true })
watch(allConfig, () => postMessage(`im:allConfig`, allConfig.value), { deep: true })
watch(featureConfig, () => postMessage(`im:feature:config`, featureConfig.value), { deep: true })
watch(config, () => postMessage(`im:${model.value}:config`, config.value), { deep: true })
watch(round, () => postMessage(`im:${model.value}`, round.value), { deep: true })
watch(openActArr, () => postMessage(`im:openActArr`, openActArr.value), { deep: true })

function onMessage(
  { data }:
  { data: { type: string, message?: any } },
) {
  if (data.type === 'iframe:ready') {
    const contentWindow = iframeRef.value?.contentWindow
    if (contentWindow) {
      if (wall.value) {
        postMessage(`im:wall`, wall.value)
      }
      if (wxuser.value) {
        postMessage(`im:wxuser`, wxuser.value)
      }
      if (allConfig.value) {
        postMessage(`im:allConfig`, allConfig.value)
      }
      if (featureConfig.value) {
        postMessage(`im:feature:config`, featureConfig.value)
      }
      if (config.value) {
        postMessage(`im:${model.value}:config`, config.value)
      }
      if (round.value) {
        postMessage(`im:${model.value}`, round.value)
      }
      if (openActArr.value) {
        postMessage(`im:openActArr`, openActArr.value)
      }
    }
  }
}

onMounted(() => {
  featchWxuser()
  featchWall()

  window.addEventListener('message', onMessage)
})
onBeforeMount(() => {
  window.removeEventListener('message', onMessage)
})
</script>

<template>
  <div class="iframe-box">
    <iframe
      ref="iframeRef"
      frameborder="0"
      scrolling="no"
      :src="iframeUrl"
    ></iframe>
  </div>
</template>

<style scoped lang="scss">
.iframe-box {
  width: 100%;
  height: 100%;
  font-size: 0px;
  overflow: hidden;
}
iframe {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
