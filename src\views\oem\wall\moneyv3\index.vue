<script setup lang="ts">
import HiDesign from 'design/index.vue'
import { useDesignTemp } from '~/src/views/__/design'
import { useMobileMoneyv3 } from '~/src/views/mobile/wall/moneyv3'
import { usePcwallMoneyv3 } from '~/src/views/pcwall/moneyv3'

definePage({ meta: { label: '数钱游戏' } })

const interactive = 'moneyv3'
const designTemp = useDesignTemp()

watch(
  () => designTemp.showType,
  (v) => {
    if (v === 'mobile') {
      useMobileMoneyv3()
    } else {
      usePcwallMoneyv3()
    }
  },
  { immediate: true },
)
</script>

<template>
  <HiDesign :interactive="interactive" />
</template>

<style scoped lang="scss">
</style>
