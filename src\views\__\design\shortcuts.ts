import type { IDesignGroup } from './layer/group/group'
//  快捷键实现
import type { ILayerTypes, InteractiveEnum } from './types'
import { designDataHistory, useDesignData, useDesignSetup, useDesignTemp } from '.'
import { createComponent } from './edit/createLayer'
import { canMergeToGroup, canSplitGroup, mergeToGroup, splitGroup } from './edit/mergeSplit'
import { fileUpload, getInteractive, layerUuid } from './utils'

export function initShortcuts() {
  const designData = useDesignData()
  const designTemp = useDesignTemp()

  function moveFn(event: KeyboardEvent) {
    let xOffset = 0
    let yOffset = 0

    switch (event.key) {
      case 'ArrowUp':
        yOffset = event.shiftKey ? -10 : -1
        break
      case 'ArrowDown':
        yOffset = event.shiftKey ? 10 : 1
        break
      case 'ArrowLeft':
        xOffset = event.shiftKey ? -10 : -1
        break
      case 'ArrowRight':
        xOffset = event.shiftKey ? 10 : 1
        break
      default:
        break
    }

    const moveLayers = designData.getLayerByUuids(designTemp.activeList)

    for (const layer of moveLayers) {
      const left = Number.parseFloat(String(layer.style.left) || '0')
      const right = Number.parseFloat(String(layer.style.right) || '0')
      const top = Number.parseFloat(String(layer.style.top) || '0')
      const bottom = Number.parseFloat(String(layer.style.bottom) || '0')

      const { style } = layer
      // 判定x方向
      if (style.right && !style.left) {
        style.right = `${right - xOffset}px`
      } else {
        style.left = `${left + xOffset}px`
      }
      // 判定y方向
      if (style.bottom && !style.top) {
        style.bottom = `${bottom - yOffset}px`
      } else {
        style.top = `${top + yOffset}px`
      }
    }

    // 批量更新，替代forEach中的单个更新
    designData.layers = designData.layers.map(
      (layer) => {
        const moveLayer = moveLayers.find(l => l.uuid === layer.uuid) || layer
        if (moveLayer.type === 'group') {
          moveLayer.layers = moveLayer.layers.map(
            (child) => {
              const moveLayerChild = moveLayers.find(l => l.uuid === child.uuid)
              return moveLayerChild || child
            },
          )
        }
        return moveLayer
      },
    )
  }

  const deleteRelationEvenets = (uuid: string, layers: ILayerTypes[]) => {
    layers.forEach((layer) => {
      if (layer.events) {
        layer.events.forEach((event) => {
          if (event.type === 'show-hide') {
            event.value = event.value?.filter(v => v.uuid !== uuid) || null
          }
        })
      }
      if (layer.type === 'group') {
        deleteRelationEvenets(uuid, layer.layers)
      }
    })
  }

  function trashLayers(layers: ILayerTypes[]) {
    for (let i = layers.length - 1; i >= 0; i--) {
      const layer = layers[i]
      if (designTemp.activeList.includes(layer.uuid)) {
        layers.splice(i, 1)
        // 判断有没有图层事件绑定了这个图层,如果存在则删除
        deleteRelationEvenets(layer.uuid, designData.layers)
        designTemp.activeList = designTemp.activeList.filter((uuid: string) => uuid !== layer.uuid)
      }
      if (layer.type === 'group') {
        trashLayers(layer.layers)
      }
    }
  }

  function trashFn() {
    if (designTemp.activeList.length === 0)
      return
    trashLayers(designData.layers)
  }

  let isClipboardWriteWebSupported = true

  const copyFn = () => {
    const interactive = getInteractive()
    // TODO 获取当前用户id
    // TODO 获取当前用户角色
    const designClipboard = JSON.stringify({
      copyItems: designData.getLayerByUuids(designTemp.activeList),
      interactive,
      userId: '',
      role: '',
      themeId: designTemp.theme?.id,
      time: Date.now(),
    } as HxcDeisgn, (key, value) => key.startsWith('$') ? undefined : value)

    localStorage.setItem('designClipboard', designClipboard)
    if (isClipboardWriteWebSupported) {
      navigator?.clipboard?.write([
        new ClipboardItem({
          'web hxc/design': new Blob([designClipboard], { type: 'web hxc/design' }),
        }),
      ]).catch((err) => {
        isClipboardWriteWebSupported = false
        console.error('clipboard write', err)
      })
    }
  }

  const getPasteFiles = (event: ClipboardEvent) => {
    const files: File[] = []
    const items = event.clipboardData?.items || []
    for (const item of items) {
      if (item.kind === 'file') {
        const file = item.getAsFile()
        if (file) {
          files.push(file)
        }
      }
    }
    return files
  }

  interface HxcDeisgn {
    copyItems: ILayerTypes[]
    interactive: string
    userId: string
    role: string
    themeId: string | number
    time: string | number
  }

  const TEXT_PLAIN = 'text/plain'
  const WEB_HXC_DESIGN = 'web hxc/design'
  async function getClipboardContent(event: ClipboardEvent | null): Promise<{ type: string, value: string | HxcDeisgn } | undefined> {
    // 如果不带event表示用户按钮触发,可能是复制粘贴选中图层,这里不获取剪贴板内容,避免错误粘贴
    if (!event) return

    try {
      if (!navigator.clipboard?.read) {
        // 浏览器不支持直接read，通过event获取文本
        const value = event.clipboardData?.getData('text') as string
        return { type: TEXT_PLAIN, value }
      }

      const clipboardItems = await navigator.clipboard.read()
      const clipobardData: { type: string, value: string | HxcDeisgn }[] = []
      for (const clipboardItem of clipboardItems) {
        // 读取一条文本或者一条自定义格式的内容
        const allowedTypes = [TEXT_PLAIN, WEB_HXC_DESIGN]
        const types = clipboardItem.types.filter(type => allowedTypes.includes(type))
        if (types.length !== clipboardItem.types.length) {
          console.log('clipboardItems', clipboardItems)
        }
        for (const type of types) {
          const blob = await clipboardItem.getType(type)
          const text = await blob.text()
          if (type === WEB_HXC_DESIGN) {
            clipobardData.push({
              type,
              value: JSON.parse(await blob.text()),
            })
          }
          clipobardData.push({
            type,
            value: text,
          })
        }
      }
      return clipobardData.filter(it => it.type === WEB_HXC_DESIGN)[0] || clipobardData[0]
    } catch (err) {
      console.error(err)
    }
  }

  const uuidMapNewUuid = new Map()
  const pasteWarningMap = new Map()

  // 过滤不符合当前互动的图层
  const filterLayer = (data: ILayerTypes[], interactive?: InteractiveEnum): ILayerTypes[] => {
    const designSetup = useDesignSetup()
    return data.filter((layer) => {
      const setupOption = designSetup.getComponentSetupOption(layer.type)

      if (setupOption?.showInteractive && interactive && !setupOption?.showInteractive.includes(interactive)) {
        pasteWarningMap.set(layer, ['图层不符合当前互动，不能被粘贴到当前互动'])
        return false
      }
      return true
    })
  }

  // 替换图层的UUID
  const replaceLayerUuid = (data: ILayerTypes[]): ILayerTypes[] => {
    if (!Array.isArray(data)) return []
    // 收集UUID，如果events包含相同的UUID也需要替换
    data.forEach((layer) => {
      const newUuid = uuidMapNewUuid.get(layer.uuid) || layerUuid()
      if (!uuidMapNewUuid.has(layer.uuid)) {
        uuidMapNewUuid.set(layer.uuid, newUuid)
      }
      layer.uuid = newUuid
      // 替换事件的图层绑定
      layer?.events?.forEach((event) => {
        if (event.type === 'show-hide' && Array.isArray(event.value)) {
          event.value?.forEach((item) => {
            const eventUuid = item.uuid && uuidMapNewUuid.get(item.uuid)
            if (eventUuid) {
              item.uuid = eventUuid
            } else {
              // 可能绑定的图层不存在，提示用户自己做核查
              const error = pasteWarningMap.get(layer) || []
              error.push('绑定的事件图层不存在，可能影响使用，请检查')
              pasteWarningMap.set(layer, error)
            }
          })
        }
      })
      // 替换分组的布局绑定
      if (layer.type === 'group' && layer.layout) {
        const items: Record<string, any> = {}
        Object.keys(layer.layout.items)?.forEach((uuid) => {
          const newUuid = uuidMapNewUuid.get(uuid) || layerUuid()
          if (!uuidMapNewUuid.has(uuid)) {
            uuidMapNewUuid.set(uuid, newUuid)
          }
          items[newUuid] = layer.layout?.items[uuid]
        })
        layer.layout.items = items
      }

      if (layer.type === 'group') {
        replaceLayerUuid(layer.layers)
      }
    })
    return data
  }

  // 位置偏移20px
  const offsetPosition = (data: ILayerTypes[]): ILayerTypes[] => {
    const offset = 0
    return data.map((layer) => {
      if (layer.style?.left && String(layer.style?.left)?.endsWith('px')) {
        layer.style.left = `${Number.parseInt(String(layer.style.left)) + offset}px`
      }
      if (layer.style.top) {
        layer.style.top = `${Number.parseInt(String(layer.style.top)) + offset}px`
      }
      return layer
    })
  }

  // 查询粘贴图层首个的位置
  const findFirstIndex = (data: ILayerTypes[]): number[] => {
    const uuidSet = new Set(data.map(item => item.uuid))

    const searchRecursive = (layers: ILayerTypes[], path: number[]): number[] | null => {
      for (let i = 0; i < layers.length; i++) {
        // 检查当前层是否匹配
        if (uuidSet.has(layers[i].uuid)) {
          return [...path, i]
        }

        // 递归搜索子层级
        if (layers[i].type === 'group') {
          const group = layers[i] as IDesignGroup
          const childPath = searchRecursive(group.layers, [...path, i])
          if (childPath) return childPath
        }
      }
      return null
    }

    return searchRecursive(designData.layers, []) || []
  }

  const pasteEventFn = async (event: ClipboardEvent | null = null) => {
    if (event) {
      const files = getPasteFiles(event)
      if (files.length) {
        // TODO: 需要修改上传接口才能使用，暂不放开
        return
      }
      // NOTE: 暂时只处理image，不处理视频
      const imageFiles = files.filter(file => file.type.startsWith('image'))
      if (files.length) {
        const timer = setTimeout(() => {
          ElLoading.service({
            lock: true,
            text: '正在上传图片...',
          })
        }, 1000)
        const urls: string[] = []
        const errors: [unknown, File][] = []
        for (const file of imageFiles) {
          try {
            const url = await fileUpload(file, {
              type: 'image',
              size: 1024 * 2,
            })
            urls.push(url)
          } catch (e) {
            errors.push([e, file])
          }
        }
        clearTimeout(timer)
        ElLoading.service().close()
        urls.forEach(url => createComponent('image', url))
        if (urls.length < imageFiles.length) {
          console.log('部分图片上传失败', errors)
          ElMessageBox.alert(errors.map(it => `${it[1].name}：${it[0]}`).join('\n'), '部分图片上传失败')
        } else {
          ElMessage.success('图片创建成功')
        }
        return
      }
    }

    let content = await getClipboardContent(event)
    if (!content) {
      // NOTE: 老旧浏览器不支持自定义写入，此时内容在当前浏览器的缓存中
      const designClipboard = localStorage.getItem('designClipboard')
      if (designClipboard) {
        const design: HxcDeisgn = JSON.parse(designClipboard)
        if (Number(design.time) > Date.now() - 60000) {
          content = {
            type: WEB_HXC_DESIGN,
            value: design,
          }
        } else {
          localStorage.removeItem('designClipboard')
        }
      }
    }
    if (!content) return
    if (content.type === 'text/plain' || typeof content.value === 'string') {
      createComponent('text-rich', content.value)
    } else if (content.type === 'web hxc/design') {
      const curInteractive = getInteractive()
      // userId, role,
      const { copyItems, interactive, themeId } = content.value
      let _copyItems = copyItems || []
      // 不同互动时过滤图层
      if (interactive !== curInteractive) {
        _copyItems = filterLayer(_copyItems, curInteractive)
      }
      // 计算原始图层首个位置
      const itemIndex = findFirstIndex(_copyItems)
      // 替换图层UUID
      const updatedLayers = replaceLayerUuid(_copyItems)
      // 位置偏移
      if (Number(themeId) === designTemp.theme?.id) {
        _copyItems = offsetPosition(_copyItems)
      }
      // 添加图层到当前设计
      if (updatedLayers) {
        if (itemIndex.length <= 1) {
          designData.layers.splice(itemIndex?.[0] || 0, 0, ...updatedLayers)
        } else {
          let parentLayers = designData.layers
          const lastIndex = itemIndex.pop()!
          while (itemIndex.length) {
            parentLayers = (parentLayers[itemIndex.shift()!] as IDesignGroup).layers
          }
          parentLayers.splice(lastIndex + 1, 0, ...updatedLayers)
        }
      }
      // 上述操作存在可能出现的问题时，汇总告诉用户
      if (pasteWarningMap.size) {
        let text = ''
        Array.from(pasteWarningMap?.entries()).forEach(([layer, tip]) => {
          text += `<span style="margin-right:4px;color:red">${layer.name}</span> ${tip} <br\>`
        })
        ElMessageBox.alert(text, '部分图层粘贴错误', {
          dangerouslyUseHTMLString: true,
        })
      } else if (Number(themeId) !== designTemp.theme?.id) {
        ElMessage.success('图层粘贴成功')
      }
      uuidMapNewUuid.clear()
      pasteWarningMap.clear()
    } else {
      console.warn('不支持的剪切板内容', content)
    }
  }

  const pasteFn = () => {
    pasteEventFn()
  }

  let lastPasteTime = 0
  useEventListener('paste', async (event) => {
    // 減少频繁粘贴
    if (Date.now() - lastPasteTime < 1000) return
    // 如果当前是输入框，或者在某个contentEditable元素内，或者是文本组件，不执行自定义逻辑
    if (
      ['input', 'textarea'].includes((event.target as HTMLElement)?.tagName?.toLowerCase())
      || (event.target as HTMLElement).closest('[contenteditable]')
    ) {
      return
    }
    lastPasteTime = Date.now()
    pasteEventFn(event)
  }, { capture: true })

  onKeyStroke(true, (event) => {
    if (designTemp.activeList.length) {
      //  如果当前是输入框，不执行自定义逻辑
      if (
        (event.target as HTMLElement).contentEditable === 'true'
        || ['input', 'textarea'].includes((event.target as HTMLElement)?.tagName?.toLowerCase())
      ) {
        return
      }

      // 上下左右移动
      if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
        moveFn(event)
        event.preventDefault()
      }
      // 复制
      if (event.key === 'c' && (event.ctrlKey || event.metaKey)) {
        copyFn()
      }

      // 删除
      if (event.key === 'Delete' || event.key === 'Backspace') {
        trashFn()
        event.preventDefault()
      }
    }

    // 创建分组
    if (event.key === 'g' && (event.ctrlKey || event.metaKey)) {
      if (canMergeToGroup.value) {
        mergeToGroup()
      } else {
        createComponent('group')
      }
      event.stopPropagation()
      event.preventDefault()
    }

    // 拆分分组
    if (event.key.toLowerCase() === 'g' && (event.ctrlKey || event.metaKey) && event.shiftKey) {
      if (canSplitGroup.value) {
        splitGroup()
      }
      event.stopPropagation()
      event.preventDefault()
    }

    // 撤销
    if (designDataHistory.value.canUndo && event.key === 'z' && (event.ctrlKey || event.metaKey)) {
      designDataHistory.value.undo()
      event.preventDefault()
    }
    // 恢复
    if (designDataHistory.value.canRedo && event.key === 'y' && (event.ctrlKey || event.metaKey)) {
      designDataHistory.value.redo()
      event.preventDefault()
    }
  })

  return {
    copyFn,
    pasteFn,
    trashFn,
  }
}
