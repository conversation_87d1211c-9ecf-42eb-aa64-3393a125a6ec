import { defineStore } from 'pinia'

// 用户信息，理论上同一时间/路径只可能存在一种类型的用户，这里只用一个user来存，不做区分

interface SpoUser {
  cashAgreementStatus: string
  headPic: string
  hxcVatDiscount: string
  id: number
  industryId: number
  industryName: string
  nickName: string
  pId: number
  passwd: string
  phone: string
  registerSource: string
  role: string
  schoolFlag: string
  userName: string
}
export const useUserStore = defineStore('user', () => {
  const user = ref<SpoUser | null>(null)
  const route = useRoute()
  const isAdmin = computed(() => route.path.startsWith('/admin/'))

  const fetchUser = async () => {
    if (!isAdmin.value) {
      console.error('未适配的用户类型')
      return
    }
    const res = await api.admin.user.read()
    user.value = res
  }

  watch(isAdmin, async (val) => {
    if (val) {
      await fetchUser()
    } else {
      user.value = null
    }
  }, { immediate: true })

  const userRoleType = computed(() => {
    if (isAdmin.value) {
      if (user.value?.role) {
        return user.value?.role
      }
      return 'COMMON'
    }
    return ''
  })

  return {
    user,
    fetchUser,
    userRoleType,
  }
})
