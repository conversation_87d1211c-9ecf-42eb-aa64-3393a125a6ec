<script setup lang="ts">
import type { CSSProperties } from 'vue'
import type { BehitItem } from '../diglett-ing'
import BezierEasing from 'bezier-easing'
import gsap from 'gsap'
import { playAudio } from '~/src/utils/audio'

const props = defineProps<{
  size: number
  beHitList: BehitItem[]
  behitBtomPos?: number
  holeImg?: string
  hitImg?: string
  scoreColor?: string
  bownImg?: string
  successAudio?: string
  failAudio?: string
  isMobile?: boolean
}>()
const emits = defineEmits<{
  (event: 'updateScore', score: number): void
}>()

const objectRef = ref<HTMLElement>()
const shouRef = ref<HTMLElement>()
const beHited = ref<boolean>(false)

const hitItemIndex = ref<number>(0)
const hitItem = computed(() => props.beHitList[hitItemIndex.value] || {
  img_normal: '',
  img_yun: '',
  score: 0,
})

gsap.config({ force3D: true })
// 晃动
const inOutTimeline = gsap.timeline({ paused: true })
const pullTimeline = gsap.timeline({ paused: true })
const shakeTimeline = gsap.timeline({ paused: true })

// 有效
const invalid = ref(false)
const pulling = ref(false)
const showScore = ref(false)
// 拔
function init() {
  const shouDom = shouRef.value
  const objectDom = objectRef.value
  if (!shouDom || !objectDom) return
  const ease = BezierEasing(0.63, 0.46, 0.21, 1)
  const onUpdate = () => {
    const transform = objectDom.style.transform
    if (!transform) {
      invalid.value = false
      return
    }
    const arr = transform.split(/[(, ]/).filter(Boolean)
    const y = Number.parseFloat(arr[arr.indexOf('translate3d') + 2])
    const isTmp = y < 25
    if (invalid.value !== isTmp) {
      invalid.value = isTmp
    }
  }

  // 自由进出
  inOutTimeline //
    .repeat(-1)
    // 重置数据
    .to(objectDom, { x: 0, y: '100%', rotateZ: 0, scale: 0.5, opacity: 1, delay: Math.random() * 2 })
    .to(objectDom, { x: 0, y: '22%', ease, scale: 1, delay: 0.6, duration: 0.7, onUpdate })
    .to(objectDom, { x: 0, y: '100%', ease, scale: 0.5, delay: 1, duration: 0.5, onUpdate, onComplete() {
      setItem()
    } })

  // 拔
  pullTimeline
    // 初始
    .call(() => {
      pulling.value = true
    })
    .to(shouDom, { x: '-150%', y: '-50%', opacity: 1, duration: 0 })
    // 伸出
    .to(shouDom, {
      x: '0',
      y: '0',
      duration: 0.3,
      onComplete() {
        if (invalid.value) {
          beHited.value = true
          inOutTimeline.pause()
        }
      },
    })
    // 隐藏手
    .to(shouDom, {
      opacity: 0,
      onComplete() {
        if (!beHited.value) {
          // 没有拔到
          props.failAudio && playAudioFn(props.failAudio)
          pullTimeline.pause()
          invalid.value = false
          pulling.value = false
          return
        }
        shakeTimeline.restart()
      },
    })
    // 萝卜位移
    .to(objectDom, { x: '-50%', y: '-10%', duration: 0.3, delay: 0.6, onComplete() {
      showScore.value = true
      hitSuccess()
    } })
    // 消失
    .to(objectDom, { opacity: 0, duration: 0.3, delay: 0.3 })
    // 重置数据
    .to(objectDom, {
      x: 0,
      y: '100%',
      rotateZ: 0,
      scale: 0.5,
      opacity: 1,
      duration: 0,
      onComplete() {
        setItem()
        beHited.value = false
        invalid.value = false
        shakeTimeline.pause()
        inOutTimeline.restart()
        pulling.value = false
      },
    })

  shakeTimeline // 萝卜晃动
    .to(objectDom, {
      keyframes: {
        '0%': { rotateZ: 0 },
        '25%': { rotateZ: 5 },
        '50%': { rotateZ: 0 },
        '75%': { rotateZ: -5 },
        '100%': { rotateZ: 0 },
      },
      ease: 'none',
      repeat: -1,
    })
}
function go() {
  // 判断是否已经在动画
  if (pulling.value) return
  pullTimeline.restart()
}

function hitSuccess() {
  props.successAudio && playAudioFn(props.successAudio)
  emits('updateScore', hitItem.value!.score)
}
function playAudioFn(audiourl: string) {
  if (props.isMobile) {
    playAudio(audiourl).play()
  }
}

defineExpose({
  hitIt: go,
})

const objectStyle = computed(() => {
  const img = beHited.value ? hitItem.value.img_yun : hitItem.value.img_normal
  const style: CSSProperties = {
    backgroundImage: `url(${img})`,
  }
  return style
})

const wrapStyle = computed(() => {
  const style: CSSProperties = {
    '--width': `${props.size}px`,
    '--object-wrap-bottom': `${props.behitBtomPos}%`,
    '--color': props.scoreColor,
    '--score-font-size': `${Math.min(40, Math.ceil(props.size / 3.5))}px`,
  }
  return style
})

const dongStyle = computed(() => {
  const img = props.holeImg
  const style: CSSProperties = {
    backgroundImage: `url(${img})`,
  }
  return style
})
const chuiziStyle = computed(() => {
  const img = props.hitImg
  const style: CSSProperties = {
    backgroundImage: `url(${img})`,
  }
  return style
})

function setItem() {
  hitItemIndex.value = Math.floor(Math.random() * props.beHitList.length)
}

onMounted(() => {
  setItem()
  init()
  inOutTimeline.play()
})
</script>

<template>
  <div class="item-box" :style="wrapStyle">
    <div class="dong" :style="dongStyle"></div>
    <div class="object-wrap">
      <div ref="objectRef" class="object" :style="objectStyle"></div>
    </div>
    <div v-if="showScore" class="score" @animationend="showScore = false">{{ `+${hitItem.score}` }}</div>
    <div ref="shouRef" class="shou" :style="chuiziStyle" @click="go"></div>
  </div>
</template>

<style scoped lang="scss">
.item-box {
  --width: 100px;
  --height: calc(var(--width) * 1);
  --object-wrap-bottom: 25%;
  --object-offset: 22%;

  width: var(--width);
  height: var(--height);
  position: relative;
  display: flex;
  justify-content: center;
  align-items: flex-end;

  > div {
    position: absolute;
  }

  .dong {
    width: var(--width);
    aspect-ratio: 3 / 2;
    background-repeat: no-repeat;
    background-position: bottom center;
    background-size: contain;
    z-index: 1;
  }
  .object-wrap {
    bottom: var(--object-wrap-bottom);
    display: flex;
    justify-content: center;
    align-items: flex-end;
    width: calc(var(--width) * 2);
    height: calc(var(--height) * 1.5);
    border-radius: 0 0 100% 100%;
    z-index: 2;
    overflow: hidden;
    .object {
      background-repeat: no-repeat;
      background-position: center bottom;
      background-size: contain;
      width: var(--width);
      aspect-ratio: 2 / 3;
      transform: translate3d(0, 100%, 0) scale(0.5);
    }
  }
  .shou {
    width: var(--width);
    aspect-ratio: 2 / 3;
    background-repeat: no-repeat;
    background-position: bottom center;
    background-size: contain;
    z-index: 3;
    opacity: 0;
  }
  .score {
    position: absolute;
    top: 0;
    left: 50%;
    font-size: var(--score-font-size, 30px);
    z-index: 9;
    color: var(--color);
    animation-name: score-ani;
    animation-duration: 0.5s;
    animation-iteration-count: 1;
  }
  @keyframes score-ani {
    0% {
      transform: translateY(50%) scale(0.8) rotate(0deg);
    }
    50% {
      transform: translateY(0) scale(1) rotate(-20deg);
    }
    100% {
      transform: translateY(-100%) scale(0.8) rotate(-40deg);
      opacity: 0.5;
    }
  }
}
</style>
