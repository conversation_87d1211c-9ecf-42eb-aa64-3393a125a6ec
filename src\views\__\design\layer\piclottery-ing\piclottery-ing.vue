<script setup lang="ts">
import { debounce, sample } from 'lodash-es'
import * as THREE from 'three'
import { computed, nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue'
import { injectScale, useDesignState } from '../..'
import { processStyle } from '../../utils'
import { DEFAULT_DATA, type IDesignPiclotteryIng } from './piclottery-ing'

const threeRoot = ref<HTMLDivElement | null>(null)
const threeRootSize = useElementSize(threeRoot)

const layer = defineModel<IDesignPiclotteryIng>('layer', { required: true })
const scale = injectScale()

function getCurrentWH() {
  const w = threeRootSize.width.value || 640
  const h = threeRootSize.height.value || 480
  return [w, h]
}

const data = computed(() => {
  return { ...DEFAULT_DATA, ...layer.value.data }
})

const designState = useDesignState()
// 参与人奖池
const regeditList = computed(() => {
  return designState.getLayerData('regeditList') || []
})

/** 设计参数（可根据需求调整） */
const targetCellSize = computed(() => Number.parseInt(processStyle(`${data.value.targetCellSize}px`, scale.value))) // 每个网格单元的期望最大尺寸
const minGap = computed(() => Number.parseInt(processStyle(`12px`, scale.value))) // 单元格之间的最小间隙
const batchSize = 10 // 每批次动画显示的格子数
const batchDelay = computed(() => (data.value.animationSpeed || 1) * 10) // 每批次之间的间隔（ms）
const repeatDelay = 500 // 动画重复间隔

/** 动画排列模式类型 */
type Pattern =
  | 'left-to-right'
  | 'right-to-left'
  | 'top-to-bottom'
  | 'bottom-to-top'
  | 'center-out'
  | 'diagonal'
  | 'spiral'

const currentPattern = computed<Pattern>(() => {
  return data.value.animationMode as Pattern || 'center-out'
})

// 螺旋顺序构建
let buildOrderNumber = 0 // 用于控制螺旋顺序的奇偶切换
function buildSpiralOrder(rows: number, cols: number, reverse = false): number[] {
  const order: number[] = []
  let t = 0; let b = rows - 1; let l = 0; let r = cols - 1
  while (t <= b && l <= r) {
    for (let c = l; c <= r; c++) order.push(t * cols + c)
    t++
    for (let r_ = t; r_ <= b; r_++) order.push(r_ * cols + r)
    r--
    if (t <= b) {
      for (let c = r; c >= l; c--) order.push(b * cols + c)
    }
    b--
    if (l <= r) {
      for (let r_ = b; r_ >= t; r_--) order.push(r_ * cols + l)
    }
    l++
  }
  return reverse ? order.reverse() : order
}

function buildOrder(rows: number, cols: number, pattern: Pattern = 'center-out'): number[] {
  const arr = Array.from({ length: rows * cols }, (_, i) => i)
  switch (pattern) {
    case 'left-to-right':
    case 'right-to-left': {
      // 横向排序，控制列方向
      const dir = pattern === 'left-to-right' ? 1 : -1
      return arr.sort((a, b) => {
        const colA = a % cols; const colB = b % cols
        const rowA = Math.floor(a / cols); const rowB = Math.floor(b / cols)
        return dir * (colA - colB) || rowA - rowB
      })
    }
    case 'top-to-bottom':
    case 'bottom-to-top': {
      // 纵向排序，控制行方向
      const dir = pattern === 'top-to-bottom' ? 1 : -1
      return arr.sort((a, b) => {
        const rowA = Math.floor(a / cols); const rowB = Math.floor(b / cols)
        const colA = a % cols; const colB = b % cols
        return dir * (rowA - rowB) || colA - colB
      })
    }
    case 'center-out': {
      const cx = (cols - 1) / 2; const cy = (rows - 1) / 2
      return arr.sort((a, b) => {
        const ra = Math.floor(a / cols); const ca = a % cols
        const rb = Math.floor(b / cols); const cb = b % cols
        return Math.hypot(ca - cx, ra - cy) - Math.hypot(cb - cx, rb - cy)
      })
    }
    case 'diagonal':
      return arr.sort((a, b) => {
        const ra = Math.floor(a / cols); const ca = a % cols
        const rb = Math.floor(b / cols); const cb = b % cols
        return (ra + ca) - (rb + cb) || ra - rb
      })
    case 'spiral':
      buildOrderNumber++
      if (buildOrderNumber === 100) {
        buildOrderNumber = 0
      }
      return buildSpiralOrder(rows, cols, buildOrderNumber % 2 === 0)
    default:
      return arr
  }
}

// 网格参数自适应计算
interface GridParams { rows: number, cols: number, gap: number, cellSize: number, total: number }
function calcGridParams(W: number, H: number, targetCellSize = 64, minGap = 4): GridParams {
  let best = { cols: 1, rows: 1, gap: minGap, cellSize: targetCellSize, total: 1 }; let minDelta = Infinity
  for (let cols = 1; cols < 200; cols++) {
    const cellSize = (W - minGap * (cols - 1)) / cols
    if (cellSize < 10) break
    const gap = cols > 1 ? (W - cellSize * cols) / (cols - 1) : 0
    const rows = Math.floor((H + gap) / (cellSize + gap))
    if (rows < 1) continue
    const gapY = rows > 1 ? (H - cellSize * rows) / (rows - 1) : 0
    const delta = Math.abs(cellSize - targetCellSize)
    if (delta < minDelta) {
      minDelta = delta
      best = { cols, rows, gap: Math.min(gap, gapY), cellSize, total: cols * rows }
    }
  }
  return best
}

// Three.js 场景和动画核心变量
let scene: THREE.Scene, camera: THREE.OrthographicCamera, renderer: THREE.WebGLRenderer
let aniId = 0; let isRendering = false; let lastRenderTime = 0
const loader = new THREE.TextureLoader()
loader.crossOrigin = 'anonymous'
let cells: Cell[] = []
let gridParams: GridParams = { rows: 1, cols: 1, gap: 4, cellSize: 60, total: 1 }
interface Cell { base: THREE.Mesh, top: THREE.Mesh | null, target: number, fading: boolean }

/** 初始化并启动动画渲染 */
function initAndRender() {
  cancelAnimationFrame(aniId)
  cancelBatchLoop()
  cancelAllBatchTimers()
  isRendering = false

  const [W, H] = getCurrentWH()
  gridParams = calcGridParams(W, H, targetCellSize.value, minGap.value)
  disposeGrid()
  initScene(W, H)
  buildGrid()
  startBatchCover(loop, currentPattern.value)
  isRendering = true
  lastRenderTime = performance.now()
  renderLoop()
}

/** Three.js场景初始化 */
function initScene(w: number, h: number) {
  scene = new THREE.Scene()
  camera = new THREE.OrthographicCamera(0, w, h, 0, 0.1, 1000)
  camera.position.set(0, 0, 10)
  camera.lookAt(0, 0, 0)
  renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true, preserveDrawingBuffer: true })
  renderer.setSize(w, h)
  renderer.setClearColor(0x000000, 0)
  if (threeRoot.value) {
    threeRoot.value.innerHTML = ''
    threeRoot.value.appendChild(renderer.domElement)
  }
}

/** 构建网格，并居中显示 */
function buildGrid() {
  const { rows, cols, gap, cellSize } = gridParams
  const [W, H] = getCurrentWH()
  const contentWidth = cols * cellSize + (cols - 1) * gap
  const contentHeight = rows * cellSize + (rows - 1) * gap
  const offsetX = (W - contentWidth) / 2; const offsetY = (H - contentHeight) / 2
  const geom = new THREE.PlaneGeometry(cellSize, cellSize)
  const baseMat = new THREE.MeshBasicMaterial({ transparent: true, opacity: 0 })
  cells = []
  for (let r = 0; r < rows; r++) {
    for (let c = 0; c < cols; c++) {
      const mesh = new THREE.Mesh(geom.clone(), baseMat.clone())
      const x = offsetX + c * (cellSize + gap) + cellSize / 2
      const y = offsetY + r * (cellSize + gap) + cellSize / 2
      mesh.position.set(x, y, 0)
      scene.add(mesh)
      cells.push({ base: mesh, top: null, target: cellSize, fading: false })
    }
  }
}

/** 清理所有 Three.js 网格与资源 */
function disposeGrid() {
  cells.forEach((c) => {
    scene?.remove(c.base)
    c.base.geometry.dispose()
    if (c.top) disposeMesh(c.top)
  })
  scene?.clear()
  cells = []
}
function disposeMesh(m: THREE.Mesh) {
  scene?.remove(m)
  m.geometry.dispose()
  if ((m.material as any).dispose) (m.material as any).dispose()
}

/* ====== 动画批次显示逻辑 ====== */
const batchTimers = new Set<ReturnType<typeof setTimeout>>()
function cancelAllBatchTimers() {
  batchTimers.forEach(clearTimeout)
  batchTimers.clear()
}
function startBatchCover(done: () => void, pattern: Pattern = 'center-out') {
  cancelAllBatchTimers()
  const order = buildOrder(gridParams.rows, gridParams.cols, pattern)
  const total = cells.length; const batches = Math.ceil(total / batchSize)
  for (let b = 0; b < batches; b++) {
    const timer = setTimeout(() => {
      for (let j = 0; j < batchSize; j++) {
        const idx = b * batchSize + j
        if (idx < total) addTop(order[idx])
      }
      if (b === batches - 1) done()
    }, b * batchDelay.value)
    batchTimers.add(timer)
  }
}

function addTop(idx: number) {
  const cell = cells[idx]
  if (!cell) return

  const avatarUrl = `${sample(regeditList.value)?.avatar || data.value.placeholderHeadImg}`

  loader.load(
    avatarUrl,
    (tex) => {
      tex.colorSpace = THREE.SRGBColorSpace
      const geom = new THREE.PlaneGeometry(gridParams.cellSize, gridParams.cellSize)
      const mat = new THREE.MeshBasicMaterial({ map: tex, transparent: true, opacity: 1 })
      const mesh = new THREE.Mesh(geom, mat)
      mesh.position.copy(cell.base.position)
      mesh.scale.set(0.001, 0.001, 1)
      tex.generateMipmaps = false
      scene.add(mesh)
      if (cell.top) disposeMesh(cell.top)
      cell.top = mesh
      cell.fading = true
    },
    undefined,
    () => ((cell.base.material as THREE.MeshBasicMaterial).color.set('#888')),
  )
}

/* 动画循环 */
let batchLoopTimer: ReturnType<typeof setTimeout> | null = null
function loop() {
  batchLoopTimer = setTimeout(() => {
    startBatchCover(loop, currentPattern.value)
  }, repeatDelay)
}
function renderLoop() {
  if (!isRendering) return
  const now = performance.now()
  const delta = (now - lastRenderTime) / 1000
  lastRenderTime = now
  const fadeSpeed = 2; const growSpeed = 3
  cells.forEach((c) => {
    if (c.top) {
      const s = c.top.scale.x
      if (s < 1) {
        const ns = Math.min(1, s + growSpeed * delta)
        c.top.scale.set(ns, ns, 1)
      }
    }
    if (c.top && c.fading) {
      const baseMat = c.base.material as THREE.MeshBasicMaterial
      if (baseMat.opacity > 0) {
        baseMat.opacity = Math.max(0, baseMat.opacity - fadeSpeed * delta)
        baseMat.needsUpdate = true
      }
      const isTopReady = c.top.scale.x >= 0.99 && (c.top.material as THREE.MeshBasicMaterial).map?.image
      const isBaseGone = baseMat.opacity <= 0.01
      if (isTopReady && isBaseGone) {
        disposeMesh(c.base)
        c.base = c.top
        c.top = null
        c.fading = false
      }
    }
  })
  renderer.render(scene, camera)
  aniId = requestAnimationFrame(renderLoop)
}
function cancelBatchLoop() {
  if (batchLoopTimer) {
    clearTimeout(batchLoopTimer)
    batchLoopTimer = null
  }
}

/* 响应式自适应窗口/容器变化 */
function onResize() {
  isRendering = false
  cancelAnimationFrame(aniId)
  cancelBatchLoop()
  cancelAllBatchTimers()
  nextTick(() => {
    disposeGrid()
    renderer?.dispose()
    initAndRender()
  })
}
const debouncedResize = debounce(onResize, 300)

watch(() => [threeRootSize.width.value, threeRootSize.height.value, targetCellSize.value, currentPattern.value, batchDelay.value, data.value.placeholderHeadImg], debouncedResize)

/* 生命周期与初始化 */
onMounted(() => {
  nextTick().then(() => {
    initAndRender()
    window.addEventListener('resize', debouncedResize)
  })
})
onBeforeUnmount(() => {
  cancelAnimationFrame(aniId)
  window.removeEventListener('resize', debouncedResize)
  renderer?.dispose()
  cancelBatchLoop()
  cancelAllBatchTimers()
  disposeGrid()
})
</script>

<template>
  <div ref="threeRoot" class="piclottery-ing-box h-full w-full overflow-hidden"></div>
</template>
