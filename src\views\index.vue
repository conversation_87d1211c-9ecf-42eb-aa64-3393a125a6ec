<script lang="ts" setup>
import {
  debugState,
  toggleCacheIgnore,
  toggleDebugger,
  toggleLocalDebug,
  togglePolicyIgnore,
  toggleVConsole,
} from '@/utils/debug'
import { envUtils } from '@/utils/env'
import 'uno.css'

definePage({
  meta: {
    policyIgnore: true,
  },
})
const isDebug = location.href.includes('isDebug')

const designer = useLocalStorage('designer', 'answerracev3')

const modules = [
  {
    label: '摇一摇',
    value: 'shakev3',
  },
  {
    label: '接金币',
    value: 'goldcoinv3',
  },
  {
    label: '数钱',
    value: 'moneyv3',
  },
  {
    label: '射击游戏',
    value: 'firev3',
  },
  {
    label: '敲敲乐',
    value: 'diglettv3',
  },
  {
    label: '业绩目标会',
    value: 'performancev3',
  },
  {
    label: '微站',
    value: 'microsite',
  },
  {
    label: '滚动抽奖',
    value: 'lotteryv3',
  },
  {
    label: '名单抽奖',
    value: 'listlotteryv3',
  },
  {
    label: '图片抽奖',
    value: 'piclotteryv3',
  },
  {
    label: '排座抽奖',
    value: 'seglottery',
  },
  {
    label: '答题',
    value: 'answerracev3',
  },
]

/// // 工具 /////
</script>

<template>
  <div v-if="isDebug || envUtils.isDev" class="box">
    <h2 class="title">项目入口</h2>
    <div class="inl block">
      <router-link to="/admin/wall/padsign" target="_blank">
        <button>后台 - 签名</button>
      </router-link>
      <router-link to="/pcwall/padsign?wallFlag=ylfqW1JZ" target="_blank">
        <button>大屏幕 - 签名</button>
      </router-link>
      <router-link to="/pcwall/padsign/pad?wallFlag=ylfqW1JZ" target="_blank">
        <button>iPad - 签名</button>
      </router-link>
    </div>
    <div class="flex items-center">
      <h2 class="title">设计器</h2>
      <select v-model="designer" class="mx-10 p-5">
        <option v-for="item in modules" :key="item.value" :value="item.value">{{ item.label }}</option>
      </select>
    </div>
    <div class="inl block">
      <router-link to="/test/design" target="_blank">
        <button>调试</button>
      </router-link>
      <router-link :to="`/manage/wall/${designer}`" target="_blank">
        <button>管理端</button>
      </router-link>
      <router-link v-if="envUtils.isOem" :to="`/oem/wall/${designer}`" target="_blank">
        <button>服务商</button>
      </router-link>
      <router-link :to="`/admin/wall/${designer}`" target="_blank">
        <button>主办方</button>
      </router-link>
      <router-link :to="`/pcwall/iframe/${designer}/?wallFlag=nmKpyMPJ`" target="_blank">
        <button>大屏幕</button>
      </router-link>
      <router-link :to="`/mobile/iframe/${designer}/?mobileFlag=Xxz5lNbD`" target="_blank">
        <button>移动端</button>
      </router-link>
    </div>
    <h2 class="title">工具</h2>
    <div class="inl block">
      <button @click="toggleDebugger()">
        开启调试({{ debugState?.debug }})
      </button>
      <button @click="toggleCacheIgnore()">
        CacheIgnore({{ debugState?.cacheIgnore }})
      </button>
      <button @click="togglePolicyIgnore()">
        PolicyIgnore({{ debugState?.policyIgnore }})
      </button>
      <button @click="toggleLocalDebug()">
        接口本地调试({{ debugState?.localDebug }})
      </button>
      <button @click="toggleVConsole()">
        VConsole({{ debugState?.vConsole }})
      </button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.box {
  margin: 20px;
  h2 {
    display: inline-block;
    background-image: linear-gradient(109deg, #ec6f66, #f3a183);
    background-clip: text;
    color: transparent;
  }
  .pre {
    word-break: break-all;
    white-space: break-spaces;
    display: block !important;
  }
}
.block {
  padding: 10px 20px;
  border: 1px dashed transparent;
  transition: 0.3s;
  &:hover {
    border-color: #ccc;
    background-color: #f5f7fa;
  }
  > * {
    display: block;
    margin-left: 0px;
    margin-top: 10px;
    margin-bottom: 10px;
    margin-right: 20px;
    &:last-child {
      margin-right: 0;
    }
  }
  &.inl {
    > * {
      display: inline-block;
      text-decoration: none;
    }
  }
}
.relative {
  position: relative;
}

// Transition
.track {
  background: rgba(125, 125, 125, 0.3);
  border-radius: 50px;
  width: 100%;
  height: 50px;
  margin: 25px 0;
  padding: 0 25px;
}

.sled {
  background: #44bd87;
  border-radius: 50%;
  height: 50px;
  position: absolute;
  width: 50px;
  transform: translateX(-50%);
}

button {
  padding: 6px 16px;
  font-size: 14px;
  color: #fff;
  border: 0;
  background-color: #079992;
  border-radius: 4px;
  height: 36px;
  line-height: 1;
  transition: opacity 0.2s;
  &:hover {
    opacity: 0.8;
  }
  &:active {
    opacity: 0.6;
  }
}
</style>
