import { randomAvatar, timer } from '@/utils'
import { useDesignData, useDesignState, useDesignTemp } from '@/views/__/design'
import { faker } from '@faker-js/faker/locale/zh_CN'
import { useImData } from '~/src/hooks/useImData'
import { envUtils } from '~/src/utils/env'
import { Toast } from '~/src/utils/toast'
import { useParticipantStore, usePcwallStore } from '..'

// 类型
interface IPage<T> {
  count: number
  dataList: T[]
}
interface IConfig {
  lotteryv3Id: number
  lotteryThemeLimit: 'Y' | 'N'
  lotteryAdvancedLimit: 'N' | 'Y'
}
interface IRound {
  id: number
  themeId: number
  title: string
  startTime?: number
  endTime?: number
  batchNo?: string
  sort?: number
  onceNum?: number // Added onceNum property
}
interface IRegedit {
  name: string
  avatar: string
}
type Status = 'NOT_STARTED' | 'WIN_LIST' | 'STARTING' | 'IN' | 'FINISH'
interface IRound {
  id: number
  themeId: number
  title: string
  startTime?: number
  state?: Status
}
interface IAward {
  id: number
  type: 'KIND' | 'CASH'
  name: string
  img: string
  mode: 'lotteryv3'
  count: number
}
interface QrcodeOption {
  actQrcodeUrl: string
  isShowDesignQrcode: boolean
  wallConfigQrCode: string
  miniprogramLogo: string
}
export function usePcwallLotteryv3() {
  // const timestamp = useTimestamp()

  const designTemp = useDesignTemp()
  const designData = useDesignData()
  const designState = useDesignState()

  const pcwallStore = usePcwallStore()
  const participantStore = useParticipantStore()

  const isDesignEdit = designTemp.isEdit

  const config = ref<IConfig>()
  const round = ref<IRound>()
  const roundList = ref<IRound[]>([])
  // 参与人
  let regeditTimer: NodeJS.Timeout | undefined
  const regeditPage = ref<IPage<IRegedit>>()
  const regeditCount = ref(10)

  // 奖品
  const awards = ref<IAward[]>()
  const awardLeftCount = ref<Record<number, number>>({})
  const totleWinCount = ref(0) // 总中奖人数
  // 一次抽奖人数
  const onceNum = ref(1)
  const qrcodeOptions = ref<QrcodeOption>()
  // 名字
  const getName = (wxUserId: number) => {
    if (pcwallStore.isSignName) {
      return participantStore.getSignName(wxUserId) || participantStore.getWxName(wxUserId)
    }
    return participantStore.getWxName(wxUserId)
  }

  // 状态
  const status = computed(() => {
    const statusObj = {
      NOT_STARTED: 'ready',
      WIN_LIST: 'winlist',
      STARTING: 'staring',
      IN: 'ing',
      FINISH: 'finish',
    }
    if (!round.value) return ''

    if (isDesignEdit) {
      return designState.status
    }
    return round.value?.state ? statusObj[round.value.state] : ''
  })

  // 获取配置
  async function fetchConfig() {
    if (isDesignEdit) {
      await timer(200)
      config.value = { lotteryv3Id: 1, lotteryThemeLimit: 'Y', lotteryAdvancedLimit: 'Y' }
    }
  }

  // 轮次
  async function fetchRound() {
    round.value = undefined
    if (isDesignEdit) {
      await timer(200)
      round.value = {
        id: 1,
        themeId: 1,
        title: '滚动抽奖',
      }
    }
  }
  async function fetchRoundList() {
    roundList.value = []
    if (isDesignEdit) {
      await timer(200)
      roundList.value = [{
        id: 1,
        themeId: 1,
        title: '滚动抽奖',
        sort: 1,
      }]
    } else {
      const data = await api.pcwall.lotteryv3.list({ sort: { sort: 'asc' } })
      roundList.value = data
    }
  }

  // 查询主题
  async function fetchTheme() {
    if (isDesignEdit) {
      designTemp.fetchTheme()
    } else {
      const { themeId } = round.value || {}
      if (!themeId) return
      const theme = await api.pcwall.theme.readSimple({ where: { id: themeId } })
      designTemp.theme = theme
      if (theme) {
        designData.setState(JSON.parse(theme.webContent))
      }
    }
  }

  // 查询奖池列表
  async function fetchRegedit() {
    if (isDesignEdit) {
      if ((regeditPage.value?.count || 0) > 200) return
      const count = 200
      // 生成列表
      const dataList = []
      if (envUtils.isPlayWright) {
        faker.seed(1)
      }
      for (let i = 0; i < count; i++) {
        dataList.push({ name: faker.person.fullName(), avatar: randomAvatar(i), id: i })
      }
      regeditPage.value = {
        count,
        dataList,
      }
    } else {
      // 查询参与人
      const lotteryv3Id = round.value?.id
      if (!lotteryv3Id) return
      const page = await api.pcwall.lotteryv3.lotteryList({
        where: { lotteryv3Id, showNum: 200 },
      })

      await participantStore.relationParticipant(page.dataList)

      const list: any = []
      page.dataList.forEach((wxUserId: number) => {
        const name = getName(wxUserId)
        const avatar = participantStore.getAvatar(wxUserId)
        list.push({ name, avatar })
      })
      page.dataList = list
      regeditPage.value = page
    }
  }

  // 查询奖品
  async function fetchAward() {
    if (isDesignEdit) {
      awards.value = [{
        type: 'KIND',
        name: '实物奖品',
        img: '',
        mode: 'lotteryv3',
        count: 1,
        id: 0,
      }]
    } else {
      const moduleId = round.value?.id
      if (!moduleId) return
      const data = await api.pcwall.awards.list({
        where: {
          moduleId,
          module: 'lotteryv3',
        },
      })
      awards.value = data
    }
  }

  // 开始
  async function startFn() {
    try {
      if (!round.value) return
      if ((regeditPage.value?.count ?? 0) < onceNum.value) {
        Toast.message('参与人数不足')
        return
      }
      // 开始
      if (isDesignEdit) {

      } else {
        await api.pcwall.lotteryv3.start({ lotteryv3Id: round.value.id, onceNum: onceNum.value })
      }
    } catch (error: any) {
      Toast.message(error?.message || error?.data?.msg || '操作失败')
      console.error(error)
    }
  }
  // 结束
  async function stopFn() {
    try {
      if (!round.value) return
      // 开始
      if (isDesignEdit) {

      } else {
        await api.pcwall.lotteryv3.stop({ lotteryv3Id: round.value.id })
      }
    } catch (error: any) {
      Toast.message(error?.message || error?.data?.msg || '操作失败')
      console.error(error)
    }
  }

  const winningList = ref<any[]>([])
  // const winingList2 = ref<any[]>([]) // 用于名单组件2的数据
  async function fetchWinningList(status: string, batchNo?: string) {
    if (status !== 'ready') {
      winningList.value = []
    }
    // 中奖结果
    if (isDesignEdit) {
      const list = []
      if (envUtils.isPlayWright) {
        faker.seed(1)
      }
      for (let i = 0; i < 10; i++) {
        list.push({ avatar: randomAvatar(i), name: faker.person.fullName(), id: i })
      }
      winningList.value = [
        {
          prizeName: '奖品1',
          awardList: list,
        },
      ]
      totleWinCount.value = 10
    } else {
      if (!round.value) return
      const where: any = {
        moduleId: round.value.id,
        module: 'lotteryv3',
      }
      if (batchNo) {
        where.uuid = round.value.batchNo
      }
      const page = await api.pcwall.awards.recordpage({ where, pageIndex: 1, pageSize: 200, sort: { id: 'desc' } })
      totleWinCount.value = page.total
      await participantStore.relationParticipant(page.dataList)

      const list: any = []
      page.dataList.forEach((item: { wxUserId: number, id: number }) => {
        const { wxUserId } = item
        const name = getName(wxUserId)
        const avatar = participantStore.getAvatar(wxUserId)
        list.push({ name, avatar, id: item.id })
      })
      winningList.value = [
        {
          prizeName: awards.value?.[0]?.name || '奖品名称',
          awardList: list,
        },
      ]
    }
  }
  function showWinlistFn() {
    if (isDesignEdit) {
      designState.setStatus('winlist')
    } else {
      updateRemoteState('WIN_LIST')
    }
  }
  function hideWinFn() {
    if (isDesignEdit) {
      designState.setStatus('ready')
    } else {
      updateRemoteState('NOT_STARTED')
    }
  }
  async function deleteRemoteWinning(awardId: number) {
    if (isDesignEdit) return
    await api.pcwall.awards.recorddelete({
      where: { moduleId: round.value?.id, module: 'lotteryv3', id: awardId },
    })
    winningList.value.forEach((item) => {
      item.awardList = item.awardList.filter((i: { id: number }) => i.id !== awardId)
    })
  }
  // 更新状态
  async function updateRemoteState(state: Status) {
    await api.pcwall.lotteryv3.updateState({ lotteryv3Id: round.value?.id, state })
  }
  function onceNumChange(num: number) {
    return () => {
      if (onceNum.value + num < 1) return
      onceNum.value += num
    }
  }

  const selRound = computed({
    get: () => round.value?.id,
    set: (v) => {
      v && updateLottery(v)
    },
  })
  async function updateLottery(lotteryv3Id: number) {
    if (isDesignEdit) return
    await api.pcwall.lotteryv3.updateLottery({ lotteryv3Id })
  }
  async function fetchAwardsleft() {
    if (isDesignEdit) return
    const data = await api.pcwall.awards.leftCnt({ where: { moduleId: round.value?.id, module: 'lotteryv3' } })
    return awardLeftCount.value = data
  }
  async function switchRound(type: 'down' | 'up') {
    if (isDesignEdit) return
    if (['staring', 'in'].includes(status.value)) {
      Toast.message('当前状态不允许切换轮次')
      return
    }
    await api.pcwall.lotteryv3.switch({ type })
  }
  // 主要作用是将数据暴露给图层组件
  designState
    .setStatusList([
      { label: '准备页', value: 'ready' },
      { label: '全部中奖名单', value: 'winlist' },
      { label: '启动中', value: 'staring' },
      { label: '进行中', value: 'ing' },
      { label: '单次中奖名单', value: 'finish' },
    ])
    .setLayerData({
      '#轮次标题#': computed(() => {
        return round.value?.title || ''
      }),
      '#参与人数#': computed(() => {
        return regeditPage.value?.count || 0
      }),
      'regeditList': computed(() => {
        return regeditPage.value?.dataList || []
      }),
      // 单次抽取数量
      '#每次抽取人数#': onceNum,
      'selectDataSources': computed(() => [
        {
          label: '轮次列表',
          // value是LayerData的key，需要符合[{label,value}]格式
          value: 'roundList',
        },
      ]),
      '%轮次%': selRound,
      // 轮次列表
      'roundList': computed(() => {
        return roundList.value.map(item => ({ label: item.title, value: item.id }))
      }),
      // 中奖名单
      'winningList': computed(() => {
        return winningList.value
      }),
      'winningList2': computed(() => {
        return winningList.value[0]?.awardList || []
      }),
      // 奖品列表
      'awards': computed(() => {
        return awards.value?.map(item => (Object.assign({}, item, { count: awardLeftCount.value[item.id] ?? item.count }))) || []
      }),
      // 总中奖人数
      '#总中奖人数#': computed(() => {
        return totleWinCount.value
      }),
      '#奖品名称#': computed(() => {
        return awards.value?.[0]?.name || ''
      }),
      '#奖品数量#': computed(() => {
        const award = awards.value?.[0]
        if (!award) return 0
        return awardLeftCount.value[award.id] || award.count
      }),
      // 大屏幕二维码配置
      'qrcodeOptions': computed(() => {
        return qrcodeOptions.value
      }),
    })
    // 图层组件事件
    .setLayerEventList([
      // 事件
      {
        eventId: 'start',
        name: '开始',
        value: startFn,
        status: ['ready'],
      },
      {
        eventId: 'stop',
        name: '停止',
        value: stopFn,
        status: ['ing'],
      },
      {
        eventId: 'showWinlist',
        name: '打开中奖名单',
        value: showWinlistFn,
        status: ['ready'],
      },
      {
        eventId: 'hideWinlist',
        name: '关闭中奖名单',
        value: hideWinFn,
        status: ['winlist', 'finish'],
      },
      {
        eventId: 'refreshLotteryList',
        name: '刷新参与人',
        value: fetchRegedit,
        status: ['ready'],
      },
      {
        eventId: 'addOnceNum',
        name: '单次抽取数量+1',
        value: onceNumChange(1),
        status: ['ready'],
      },
      {
        eventId: 'minOnceNum',
        name: '单次抽取数量-1',
        value: onceNumChange(-1),
        status: ['ready'],
      },
      {
        eventId: 'upRound',
        name: '上一轮',
        value: () => switchRound('up'),
        status: ['ready'],
      },
      {
        eventId: 'downRound',
        name: '下一轮',
        value: () => switchRound('down'),
        status: ['ready'],
      },
      {
        // 中奖名单组件，取消中奖
        eventId: 'winning-delete',
        value: async (awardId) => {
          await deleteRemoteWinning(awardId)
          if (status.value === 'ready') {
            fetchAwardsleft()
          }
        },
      },
    ])

  // 变化监控
  watch(
    () => round.value?.id,
    async (n, o) => {
      if (n === o) return
      onceNum.value = round.value?.onceNum || 1
      // fetchRegedit()
    },
    { immediate: true },
  )
  watch(() => config.value?.lotteryv3Id, () => {
    fetchRound()
    fetchRoundList()
  })
  watch(() => round.value?.id, async () => {
    fetchAward()
    if (isDesignEdit) return
    // 重置数据
    regeditPage.value = undefined
    regeditCount.value = 3
  })
  watch(() => round.value?.themeId, fetchTheme)
  const starTime = ref(0)
  const t = 2000
  // 变化监控
  watch(
    () => [status.value, round.value?.id] as const,
    async (current, previous) => {
      if (!round.value) return
      const [v, newId] = current || []
      const [o, oldId] = previous || []
      if (v === o && newId === oldId) return
      if (v === 'ready') {
        await Promise.all([fetchAwardsleft(), fetchRegedit()])
        fetchWinningList(v)
        clearInterval(regeditTimer)
        regeditTimer = setInterval(fetchRegedit, 2000)
      } else {
        clearInterval(regeditTimer)
        regeditTimer = undefined
      }
      if (v === 'winlist') {
        fetchWinningList(v)
      }
      if (v === 'staring') {
        if (!isDesignEdit) {
          starTime.value = Date.now()
        }
      }
      if (v === 'ing') {
        if (!o) {
          // 如果刷新页面过来，先获取一次参与人
          await fetchRegedit()
        }
        if (!isDesignEdit) {
          // 如果是从启动中过来的，那么保证启动中至少进行了t毫秒
          if (o === 'staring') {
            const time = Date.now() - starTime.value // 计算时间差
            if (time < t) {
              await timer(t - time)
            }
          }
          // 如果是从ready过来的，那么先设置到启动中,先注释掉，产品不想要
          // if (o === 'ready') {
          //   designState.setStatus('staring')
          //   await timer(t)
          // }
        }
      }
      if (v === 'finish') {
        await fetchWinningList(v, round.value?.batchNo)
      }
      designState.setStatus(status.value)
    },
    { immediate: true },
  )
  // 数据同步
  useImData({
    'im:lotteryv3:config': config,
    'im:lotteryv3': computed({
      get() { return round.value },
      async set(v) {
        if (v?.id !== round.value?.id) {
          round.value = undefined
          await nextTick()
        }
        round.value = v
      },
    }),
    'im:qrcode': qrcodeOptions,
  })

  tryOnMounted(async () => {
    await pcwallStore.fetchApplySignConfig()
    if (isDesignEdit) {
      fetchConfig()
    }
  })
  tryOnBeforeUnmount(() => {
    clearInterval(regeditTimer)
    regeditTimer = undefined
  })
}
