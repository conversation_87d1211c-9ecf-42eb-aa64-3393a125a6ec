<script setup lang="ts">
import type { PropType } from 'vue'
import { timer } from '@/utils'
import { buttonProps, ElButton } from 'element-plus'

defineOptions({
  name: 'HiElButton',
})

const props = defineProps({
  ...buttonProps,
  onClick: {
    type: Function as PropType<((e: MouseEvent) => Promise<any>) | ((e: MouseEvent) => any)>,
    default: () => {},
  },
})

const loading = ref(false)
const wait = ref(false)

const SYMBOL_TIMEOUT = Symbol('timer')

function timeout(v: number) {
  return timer(v).then(() => {
    return Promise.reject(SYMBOL_TIMEOUT)
  })
}

async function originClick($event: MouseEvent) {
  //  如果200ms内没有执行完毕，设置loading
  // 一旦设置loading，loading至少出现200ms
  if (wait.value)
    return
  wait.value = true
  const fn = props.onClick($event)
  Promise.race([fn, timeout(200)])
    .catch((error) => {
      if (error === SYMBOL_TIMEOUT) {
        loading.value = true
        return Promise.all([fn, timer(200)])
      } else {
        return Promise.reject(error)
      }
    })
    .finally(() => {
      wait.value = false
      loading.value = false
    })
}

const _props = computed(() => {
  return {
    ...props,
    onClick: originClick,
  }
})
</script>

<template>
  <ElButton v-bind="_props" :loading="loading || props.loading">
    <slot />
  </ElButton>
</template>
