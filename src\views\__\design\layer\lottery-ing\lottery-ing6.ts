import type { IDesignLayer, IDesignSetup } from '../../types'
import { BisTypes, useDesignData } from '../..'
import { InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './lottery-ing6-setting.vue'
import Comp from './lottery-ing6.vue'
// 类型
export const type = 'lottery-ing6'
export const defaultanimateSameSwitch = true // 是否开启动画相同开关
export const defaultHeadSize = 180 // 调整默认尺寸以适应更多数量
export const defaultPlaceHolderHeadImg = new URL('./assets/placehoder.png', import.meta.url).href
// 数据类型约束
export interface IDesignLotteryIng6 extends IDesignLayer {
  type: typeof type
  headSize?: number
  animateSameSwitch?: boolean
  maskImg?: string
  placeHolderHeadImg?: string
  data: {
    name: string
    avatar: string
    score: number
  }[]
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.sportsIng,
    showType: ['pcwall'],
    status: ['ing', 'ready'],
    showInteractive: [InteractiveEnum.lotteryv3],
    type,
    name: '抽奖3d效果',
    thumbnail: new URL('./lottery-ing6.png', import.meta.url).href,
    Comp,
    CompSetting,
    defaultData() {
      const designData = useDesignData()
      const [width, height] = designData.option.drafts
      return {
        type,
        uuid: layerUuid(),
        name: '抽奖3d效果',
        data: [],
        style: {
          width: `${width}px`,
          height: `${height}px`,
          top: 0,
          left: 0,
        },
      }
    },
  })
}
