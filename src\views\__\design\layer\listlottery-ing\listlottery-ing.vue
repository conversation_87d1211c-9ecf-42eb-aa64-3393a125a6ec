<script setup lang="ts">
import { sampleSize } from 'lodash-es'
import { injectScale, useDesignState, useDesignTemp } from '../../index'
import { processStyle } from '../../utils'
import { DEFAULT_DATA, type IDesignListlotteryIng } from './listlottery-ing'

const layer = defineModel<IDesignListlotteryIng>('layer', { required: true })

const designState = useDesignState()
const designTemp = useDesignTemp()

const scale = injectScale()

const data = computed(() => {
  return { ...DEFAULT_DATA, ...layer.value.data }
})

// 参与人奖池
const regeditList = computed(() => {
  return designState.getLayerData('regeditList') || []
})

const importformShowOption = computed(() => {
  return designState.getLayerData('importformShowOption') || []
})

const peopleCount = computed(() => {
  return designState.getLayerData('#每次抽取人数#')
})

const ingCount = computed(() => {
  return Math.min(designTemp.isEdit ? 10 : peopleCount.value, regeditList.value.length)
})

const randomSubset = shallowRef<any[]>([])

function updateRandomSubset() {
  randomSubset.value = sampleSize(regeditList.value, ingCount.value)
}

// 自动刷新
useIntervalFn(updateRandomSubset, 50, {
  immediate: true,
})

const contentStyle = computed(() => {
  return data.value.contentStyle.map((item) => {
    return processStyle({
      flex: 1,
      fontSize: `${item.fontSize}px`,
      color: item.fontColor || '#000',
      fontWeight: item.fonBold ? 'bold' : 'normal',
    }, scale.value)
  })
})

const itemGap = computed(() => processStyle(`${data.value.itemGap}px`, scale.value))

const itemStyle = computed(() => {
  return processStyle({
    width: `calc((100% - ${(data.value.itemsPerRow - 1) * data.value.itemGap}px) / ${data.value.itemsPerRow})`,
    background: data.value.bgColor,
    gap: `${data.value.innerGap}px`,
    paddingTop: `${data.value.innerGap}px`,
    paddingBottom: `${data.value.innerGap}px`,
    borderRadius: `${data.value.itemRadius}px`,
  }, scale.value)
})
</script>

<template>
  <ul
    class="max-h-full flex flex-wrap items-center justify-around overflow-hidden"
    :style="{
      gap: itemGap,
    }"
  >
    <li
      v-for="(item, index) in randomSubset"
      :key="index"
      class="line-clamp-1 flex list-none items-center justify-between overflow-hidden rounded-2xl px-6 py-4 text-center"
      :class="{
        'flex-col': data.direction === 'col',
        'flex-row': data.direction === 'row',
      }"
      :style="itemStyle"
    >
      <span v-if="importformShowOption[0]" :style="contentStyle[0]" class="line-clamp-1 line-height-tight"> {{ item.nameD0 }}</span>
      <span v-if="importformShowOption[1]" :style="contentStyle[1]" class="line-clamp-1 line-height-tight"> {{ item.nameD1 }} </span>
      <span v-if="importformShowOption[2]" :style="contentStyle[2]" class="line-clamp-1 line-height-tight">  {{ item.nameD2 }}</span>
    </li>
  </ul>
</template>

<style scoped lang="scss"></style>
