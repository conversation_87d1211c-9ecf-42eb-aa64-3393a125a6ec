<script setup lang="ts">
import { nextTick, ref, watch } from 'vue'

interface Shadow {
  id: number
  offsetX: number
  offsetY: number
  blurRadius: number
  color: string
}

// 文字艺术
export interface TextArtMaterial {
  id: string
  name: string
  type: string
  style?: any
}

const props = defineProps<{
  modelValue: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
}>()

const shadows = ref<Shadow[]>([])
let internalUpdate = false

function parseTextShadow(value: string): Shadow[] {
  if (!value || value === 'none') {
    return []
  }
  const parsedShadows: Shadow[] = []
  let idCounter = 0

  const shadowRegex = /^\s*(-?\d+(?:\.\d+)?(?:px|em)?)\s+(-?\d+(?:\.\d+)?(?:px|em)?)(?:\s+(-?\d+(?:\.\d+)?(?:px|em)?))?\s+(.+)\s*$/

  const parts = value.split(/,(?![^(]*\))/).map(s => s.trim()).filter(Boolean)

  for (const part of parts) {
    const match = part.match(shadowRegex)

    if (match) {
      try {
        const offsetX = Number.parseFloat(match[1])
        const offsetY = Number.parseFloat(match[2])

        const blurRadius = match[3] ? Number.parseFloat(match[3]) : 0
        const color = match[4].trim()

        if (!Number.isNaN(offsetX) && !Number.isNaN(offsetY) && !Number.isNaN(blurRadius) && color) {
          parsedShadows.push({
            id: idCounter++,
            offsetX,
            offsetY,
            blurRadius,
            color,
          })
        } else {
          console.warn(`解析 text-shadow 时遇到无效值: "${part}"`)
        }
      } catch (error: any) {
        console.warn(`解析 text-shadow 部分时出错: "${part}". 错误: ${error.message}`)
      }
    } else {
      console.warn(`无法解析部分 text-shadow: "${part}". 格式不匹配预期格式 'offsetX offsetY [blurRadius] color'.`)
    }
  }

  return parsedShadows
}

function generateTextShadow(shadowsToGenerate: Shadow[]): string {
  if (!shadowsToGenerate || shadowsToGenerate.length === 0) {
    return 'none'
  }
  return shadowsToGenerate
    .map(s => `${s.offsetX}px ${s.offsetY}px ${s.blurRadius}px ${s.color}`)
    .join(', ')
}

function addShadow() {
  shadows.value.push({
    id: Date.now(),
    offsetX: 2,
    offsetY: 2,
    blurRadius: 4,
    color: 'rgba(0, 0, 0, 0.5)',
  })
}

function removeShadow(id: number) {
  shadows.value = shadows.value.filter(s => s.id !== id)
}

watch(() => props.modelValue, (newValue) => {
  if (internalUpdate) {
    internalUpdate = false
    return
  }
  shadows.value = parseTextShadow(newValue)
}, { immediate: true })

watch(shadows, (newShadows) => {
  const newCssString = generateTextShadow(newShadows)
  if (newCssString !== props.modelValue) {
    internalUpdate = true
    emit('update:modelValue', newCssString)
    nextTick(() => {
    })
  }
}, { deep: true })
</script>

<template>
  <div class="text-shadow-editor">
    <template v-if="shadows.length > 0">
      <div v-for="shadow in shadows" :key="shadow.id" class="shadow-item">
        <div class="shadow-controls">
          <div class="control-group">
            <span class="control-label">X</span>
            <el-input-number v-model="shadow.offsetX" v-input-number size="small" controls-position="right" class="control-input" />
          </div>
          <div class="control-group">
            <span class="control-label">Y</span>
            <el-input-number v-model="shadow.offsetY" v-input-number size="small" controls-position="right" class="control-input" />
          </div>
          <div class="control-group">
            <span class="control-label">模糊</span>
            <el-input-number v-model="shadow.blurRadius" v-input-number :min="0" size="small" controls-position="right" class="control-input" />
          </div>
          <div class="control-group">
            <span class="control-label">颜色</span>
            <el-color-picker v-model="shadow.color" class="ml-10" size="small" show-alpha />
          </div>
        </div>
        <el-tooltip content="删除此阴影" placement="top">
          <el-button type="danger" text size="small" class="remove-button" @click="removeShadow(shadow.id)">
            <icon-ph:trash-bold />
          </el-button>
        </el-tooltip>
      </div>
    </template>
    <div v-else class="empty-state">
      暂无阴影，点击下方按钮添加
    </div>
    <el-button type="primary" size="small" text @click="addShadow">
      <icon-ph:plus />
      添加阴影
    </el-button>
  </div>
</template>

<style scoped lang="scss">
.text-shadow-editor {
  font-size: 12px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.shadow-item {
  display: flex;
  margin-bottom: 10px;
  padding: 8px;
  border: 1px solid #eee;
  border-radius: 4px;
  background-color: #fdfdfd;
  position: relative;
  transition: all 0.2s ease-out;
}

.shadow-controls {
  display: grid;
  grid-template-columns: repeat(2, auto);
  gap: 8px 15px;
  flex-grow: 1;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 4px;
}

.control-label {
  white-space: nowrap;
  width: 18px;
  color: #666;
  text-align: right;
}

.remove-button {
  padding: 4px;
  border-radius: 4px;
}

.empty-state {
  color: #999;
  font-size: 12px;
  text-align: center;
  padding: 20px 10px;
  margin-bottom: 10px;
}
</style>
