import parseApng from 'apng-js'
import mitt from 'mitt'
import * as THREE from 'three'

// 暂时实现了apng
// 支持: gif, apng, webp, svga, pag, lottie, 帧动画
// type EventNames = 'ready' | 'play' | 'frame' | 'pause' | 'stop' | 'end'
export class AnimationTexture extends THREE.CanvasTexture {
  sourceType: 'gif' | 'apng' | 'webp' | 'svga' | 'pag' | 'lottie' | 'frame' = 'gif'
  url: string = ''
  autoPlay = true
  loop = 0

  // 播放器
  player?: {
    play: () => void
    pause: () => void
    stop: () => void
  }

  // 基本信息
  info?: {
    width: number
    height: number
    duration: number
    frameCount: number
    frames?: any[]
  }

  // 一些事件
  event = mitt()

  constructor({
    url,
    autoPlay = true,
    loop = 0,
  }: {
    //
    url: string
    autoPlay?: boolean
    loop?: number
  }) {
    const canvas = document.createElement('canvas')
    super(canvas)
    this.colorSpace = THREE.SRGBColorSpace

    this.url = url
    this.autoPlay = autoPlay
    this.loop = loop

    // 截取后缀
    const endFix = url.split('.').pop()!

    // 调用
    const thisFn = (this as any)[`${endFix}Init`]
    if (thisFn) {
      thisFn.call(this)
    } else {
      console.error('未实现', endFix)
    }
  }

  async pngInit() {
    this.sourceType = 'apng'
    const oldCanvas = this.image
    const oldCtx = oldCanvas.getContext('2d')!
    const { width: oldWidth, height: oldHeight } = oldCanvas

    const apng = await fetch(this.url)
      .then(res => res.arrayBuffer())
      .then((buffer) => {
        const res = parseApng(buffer)
        if (res instanceof Error) {
          return Promise.reject(res)
        }
        return res
      })
    apng.numPlays = this.loop

    if (oldWidth !== apng.width || oldHeight !== apng.height) {
      oldCanvas.width = apng.width
      oldCanvas.height = apng.height
      // 销毁后后续会自动创建新的
      this.dispose()
    }
    const player = await apng.getPlayer(oldCtx, this.autoPlay)
    this.player = {
      play: player.play.bind(player),
      pause: player.pause.bind(player),
      stop: player.stop.bind(player),
    }
    player.addListener('frame', () => (this.needsUpdate = true))

    this.info = {
      width: apng.width,
      height: apng.height,
      duration: apng.playTime,
      frameCount: apng.frames.length,
      frames: apng.frames,
    }
    this.event.emit('ready', this.info)
  }

  getCanvas() {
    return this.image
  }
}
