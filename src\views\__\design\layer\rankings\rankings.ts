import { BisTypes, useDesignState } from '../..'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './rankings-setting.vue'
import Comp from './rankings.vue'

// 类型
export const type = 'rankings'

export interface IDesignRankings extends IDesignLayer {
  type: typeof type
  default: string
  podium: string // 奖台
  star: string
  light: string
  data: { name: string, avatar: string }[]
}

export function setup(app: IDesignSetup) {
  let status: string[]
  switch (app.interactive) {
    case InteractiveEnum.shakev3:
      status = ['finish']
      break
    default:
      status = ['finish']
      break
  }

  app.registry({
    bisType: BisTypes.rankingList,
    showType: ['pcwall'],
    showInteractive: [InteractiveEnum.shakev3, InteractiveEnum.moneyv3, InteractiveEnum.goldcoinv3, InteractiveEnum.diglettv3, InteractiveEnum.firev3],
    thumbnail: new URL('./rankings.png', import.meta.url).href,
    type,
    name: '排行榜',
    status,
    Comp,
    // 渲染前置条件
    mountedCondition: computed(() => {
      const designState = useDesignState()
      const rankings = designState.getLayerData('endRankings')
      return !!rankings
    }),
    CompSetting,
    defaultData() {
      return {
        uuid: layerUuid(),
        name: '排行榜',
        type,
        default: new URL(`./assets/default.png`, import.meta.url).href,
        podium: new URL(`./assets/podium.png`, import.meta.url).href,
        star: new URL(`./assets/star.png`, import.meta.url).href,
        light: new URL(`./assets/light.png`, import.meta.url).href,
        data: [],
        style: {
          width: '823px',
          height: '452px',
          top: '10px',
          left: '228px',
        },
      }
    },
  })
}
