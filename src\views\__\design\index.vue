<script setup lang="ts">
import { useDesignData, useDesignSetup, useDesignTemp } from '.'
import HiDesignEdit from './edit.vue'
import HiDesignPreview from './preview.vue'

const props = defineProps<{
  // 互动
  interactive: string
}>()

provide('interactive', props.interactive)

const designSetup = useDesignSetup()
const designTemp = useDesignTemp()
const designData = useDesignData()
const rootRef = ref<HTMLElement>()

const isReady = computed(() => {
  return designSetup.isReady.value && designData.version
})

const whiteList = ['http://', 'https://', '//']
function clickFn(e: MouseEvent) {
  const target = e.target as HTMLElement
  if (target.tagName === 'A') {
    const href = target.getAttribute('href') || ''
    for (const item of whiteList) {
      // 不合法的阻止
      if (href.startsWith(item) && !href.startsWith(`${item}${location.host}`)) {
        e.preventDefault()
        break
      }
    }
  }
}

watchOnce(
  () => rootRef.value,
  () => {
    // 防止a标签外链跳转
    if (rootRef.value) {
      rootRef.value.addEventListener('click', clickFn)
    }
  },
)

onMounted(() => {
  // 图层组件的注册
  designSetup.init(props.interactive)
})
onBeforeUnmount(() => {
  if (rootRef.value) {
    rootRef.value.removeEventListener('click', clickFn)
  }
})
</script>

<template>
  <div v-if="isReady" ref="rootRef" class="design-root">
    <HiDesignEdit v-if="designTemp.isEdit" />
    <HiDesignPreview v-else-if="designTemp.isPreview" />
  </div>
</template>

<style scoped lang="scss">
.design-root {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  outline: 0px dashed red;
  user-select: none;

  :deep() {
    // 样式需要重置，增加缩放比
    h1 {
      font-size: calc(var(--design-scale) * 32px);
      margin-top: calc(var(--design-scale) * 12px);
      margin-bottom: calc(var(--design-scale) * 12px);
    }

    h2 {
      font-size: calc(var(--design-scale) * 24px);
      margin-top: calc(var(--design-scale) * 10px);
      margin-bottom: calc(var(--design-scale) * 10px);
    }

    h3 {
      font-size: calc(var(--design-scale) * 18px);
      margin-top: calc(var(--design-scale) * 8px);
      margin-bottom: calc(var(--design-scale) * 8px);
    }

    h4 {
      font-size: calc(var(--design-scale) * 16px);
      margin-top: calc(var(--design-scale) * 6px);
      margin-bottom: calc(var(--design-scale) * 6px);
    }

    h5 {
      font-size: calc(var(--design-scale) * 14px);
      margin-top: calc(var(--design-scale) * 4px);
      margin-bottom: calc(var(--design-scale) * 4px);
    }

    h6 {
      font-size: calc(var(--design-scale) * 12px);
      margin-top: calc(var(--design-scale) * 2px);
      margin-bottom: calc(var(--design-scale) * 2px);
    }
    .design-page {
      line-height: 1.1;

      &::-webkit-scrollbar {
        display: none;
      }

      // 默认样式
      [data-type='text'] {
        width: 100%;
      }
    }
    .el-radio:not(:last-child) {
      margin-right: 5px;
    }
  }
}
</style>

<style lang="scss">
.slide-fade-enter-active {
  transition: all 0.2s ease-out;
}
.slide-fade-leave-active {
  transition: all 0.2s cubic-bezier(1, 0.5, 0.8, 1);
}
.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(20px);
  opacity: 0;
}

@keyframes ani_l_r {
  0% {
    transform: none;
  }
  5% {
    transform: translate3d(-25%, 0, 0) rotateZ(-5deg);
  }
  10% {
    transform: translate3d(20%, 0, 0) rotateZ(3deg);
  }
  15% {
    transform: translate3d(-15%, 0, 0) rotateZ(-3deg);
  }
  20% {
    transform: translate3d(10%, 0, 0) rotateZ(2deg);
  }
  25% {
    transform: translate3d(-5%, 0, 0) rotateZ(-1deg);
  }
  30% {
    transform: none;
  }
}
</style>
