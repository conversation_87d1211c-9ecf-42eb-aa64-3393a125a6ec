<script setup lang="ts">
import { getDefaultMaterial, openSelectMaterial, useDataAttr } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'
import { defaultAnimation, defaultAvatarSize, type IDesignRegedit } from './regedit'

const layer = defineModel<IDesignRegedit>('layer', { required: true })

async function updateMaterialFn(name: 'default' | 'decoration', isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, `${name}`)
    : await openSelectMaterial('PIC')
  if (result) {
    layer.value[name] = result
  } else if (isReset) {
    delete layer.value[name]
  }
}

const avatarSizeBind = useDataAttr(layer, 'avatarSize', defaultAvatarSize)
const colorBind = useDataAttr(layer.value.style, 'color', 'rgba(255,255,255,1)')
const animationBind = useDataAttr(layer, 'animation', defaultAnimation)
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>默认头像</h3>
        <div class="bgblank relative h-60 w-60">
          <MaterialThumbnail @select="updateMaterialFn('default')" @reset="updateMaterialFn('default', true)">
            <img :src="layer.default">
          </MaterialThumbnail>
        </div>
      </div>
      <div class="setting-item">
        <h3>头像装饰</h3>
        <div class="bgblank relative h-60 w-60">
          <MaterialThumbnail @select="updateMaterialFn('decoration')" @reset="updateMaterialFn('decoration', true)">
            <img :src="layer.decoration">
          </MaterialThumbnail>
        </div>
      </div>
      <div class="setting-item">
        <span>文本颜色</span>
        <hi-color v-model="colorBind" />
      </div>
      <div class="setting-item">
        <span>尺寸</span>
        <el-slider v-model="layer.itemWidth" :min="60" :max="500" :step="1" class="w-160" />
      </div>
      <div class="setting-item">
        <span>头像大小</span>
        <el-slider v-model="avatarSizeBind" :min="1" :max="100" :step="1" class="w-160" />
      </div>
      <div class="setting-item">
        <span>间隔</span>
        <el-slider v-model="layer.gap" :min="0" :max="50" :step="1" class="w-160" />
      </div>
      <div class="setting-item">
        <span>动画</span>
        <el-switch v-model="animationBind" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
