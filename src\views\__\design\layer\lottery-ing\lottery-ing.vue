<script setup lang="ts">
import type { IDesignLotteryIng } from './lottery-ing'
import useThree from '@/views/__/three/useThree'
import BezierEasing from 'bezier-easing'
import gsap from 'gsap'
import { cloneDeep, sample } from 'lodash-es'
import * as THREE from 'three'
import { useDesignState } from '../..'
import { defaultHeadCount, defaultHeadSize, defaultIsScaleAnimation, defaultPlaceHolderHeadImg, defaultrotateSpeed } from './lottery-ing'

const layer = defineModel<IDesignLotteryIng>('layer', { required: true })
const designState = useDesignState()
const status = computed(() => designState.status)

// 参与人奖池
const regeditList = computed(() => {
  return designState.getLayerData('regeditList') || []
})
const headCount = computed(() => layer.value.headCount ?? defaultHeadCount)
const headSize = computed(() => layer.value.headSize ?? defaultHeadSize)
const isScaleAnimation = computed(() => layer.value.isScaleAnimation ?? defaultIsScaleAnimation)
const rotateSpeed = computed(() => layer.value.rotateSpeed ?? defaultrotateSpeed)

const threeRef = ref<HTMLElement | null>(null)
const { scene, loadTexture, getViewSizeAtZ, camera } = useThree({
  threeRef,
  hasHelp: false,
  hasControls: false,
})

const cache: Record<string, any> = {}
// 生成纹理
function createTexture(url: string) {
  let texture = cache[url]
  if (texture) {
    return texture
  }
  texture = loadTexture(url)
  cache[url] = texture
  return texture
}

class BaseShape {
  name = 'BaseShape'
  group: THREE.Group
  intervalHandler: NodeJS.Timeout | null = null
  constructor() {
    this.group = new THREE.Group()
    this.group.visible = false
    scene.add(this.group)
  }

  disposeItem(item: THREE.Object3D<THREE.Object3DEventMap>) {
    if (item instanceof THREE.Mesh || item instanceof THREE.Sprite) {
      item.material?.dispose()
      item.geometry?.dispose()
    } else if (item instanceof THREE.Group) {
      item.children.forEach((child) => {
        this.disposeItem(child)
      })
    }
  }

  init() {
    this.intervalHandler && clearInterval(this.intervalHandler)
    this.group.traverse((item) => {
      this.disposeItem(item)
    })
    this.group.clear()
  }

  play() {
  }

  getEase(value = [0.66, 0.2, 0.45, 0.79] as [number, number, number, number]) {
    return BezierEasing(...value)
  }

  destory() {
    this.intervalHandler && clearInterval(this.intervalHandler)
    this.group.traverse((item) => {
      this.disposeItem(item)
    })
    this.group.clear()
    this.group.visible = false
  }
}

// 球体效果
class ShapeBall extends BaseShape {
  constructor() {
    super()
    this.name = 'ShapeBall'
  }

  async init() {
    super.init()
    // 相机
    camera.fov = 70
    camera.position.set(0, 0, 300)
    camera.updateProjectionMatrix()
    camera.lookAt(0, 0, 0)
    // 可视区大小
    const size = getViewSizeAtZ(0)
    const count = headCount.value

    const vector = new THREE.Vector3()
    for (let i = 0; i < count; i++) {
      const phi = Math.acos(-1 + (2 * i) / count)
      const theta = Math.sqrt(count * Math.PI) * phi

      const object = new THREE.Object3D()

      const itemData = getItem()
      if (!itemData) continue

      const threeObject = new THREE.Mesh(
        new THREE.CircleGeometry(headSize.value, 32),
        new THREE.MeshBasicMaterial({
          side: THREE.DoubleSide,
          map: createTexture(itemData.avatar),
          transparent: true,
        }),
      )

      threeObject.position.setFromSphericalCoords(Math.min(size.width, size.height) * 0.35, phi, theta)
      vector.copy(object.position).multiplyScalar(2)
      threeObject.lookAt(vector)
      this.group.add(threeObject)
    }
  }

  animite() {
    gsap.to(this.group.rotation, { y: Math.PI * 2, repeat: Infinity, duration: 100 / rotateSpeed.value, ease: 'none' })
    const t1 = gsap.timeline()
    t1.addLabel('start')
    if (isScaleAnimation.value) {
      t1.to(
        this.group.scale,
        { x: 1.5, y: 1.5, z: 1.5, ease: this.getEase([0.46, 0.4, 0.66, 0.8]), duration: 1.5 },
        'start+=2',
      )
      t1.addLabel('big')
      t1.to(
        this.group.scale,
        { x: 1, y: 1, z: 1, ease: this.getEase([0.46, 0.4, 0.66, 0.8]), duration: 1.5 },
        'big+=2',
      )
    }
    return t1
  }

  play() {
    const timeline = this.animite()
    timeline.restart().repeat(-1)
    this.intervalHandler = setInterval(() => {
      this.group.children.forEach((object) => {
        // if (Math.random() > 0.2) return
        const newItem = getItem()
        if (!newItem) return
        if (object instanceof THREE.Mesh || object instanceof THREE.Sprite) {
          object.material.map = createTexture(newItem.avatar)
        }
      })
    }, 100)
  }
}

const shapeObj = ref<BaseShape | null>(null)
async function runAnimit() {
  shapeObj.value = new ShapeBall()
  shapeObj.value.group.visible = true
  shapeObj.value.init()
  shapeObj.value.play()
}
const defaultHeadImg = computed(() => {
  return layer.value.placeHolderHeadImg ?? defaultPlaceHolderHeadImg
})
function getItem() {
  if (regeditList.value.length === 0) {
    return {
      avatar: defaultHeadImg.value,
    }
  }
  const tem = cloneDeep(sample(regeditList.value))
  if (!tem.avatar) tem.avatar = defaultHeadImg.value
  const itemData = Object.assign({}, tem)
  return itemData
}

watch(
  () => [status.value, headCount.value, headSize.value, isScaleAnimation.value, rotateSpeed.value],
  () => {
    shapeObj.value?.destory()
    runAnimit()
  },
  { deep: true, immediate: true },
)

onUnmounted(() => {
  shapeObj.value?.destory()
})
</script>

<template>
  <div class="lottery-ing-box">
    <div ref="threeRef" class="three-box"></div>
  </div>
</template>

<style scoped lang="scss">
.lottery-ing-box {
  width: 100%;
  height: 100%;
  .three-box {
    width: 100%;
    height: 100%;
  }
}
</style>
