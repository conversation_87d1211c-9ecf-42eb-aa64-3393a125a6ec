import type { IDesignLayer, IDesignSetup } from '../../types'
import { merge } from 'lodash-es'
import { BisTypes, openSelectMaterial, ResourceUtil } from '../..'
import { layerUuid } from '../../utils'

import CompSetting from './image-roll-setting.vue'
import Comp from './image-roll.vue'

export const type = 'image-roll'

export const defaultDirection = 'x'
export const defaultDuration = 5
export const defaultBSize = 'contain'

export type IDrirection = 'x' | 'y'
export interface IDesignImageRoll extends IDesignLayer {
  type: typeof type
  data: string
  direction?: IDrirection
  reverse?: boolean
  duration?: number
  playStates?: string[]
  forceStop?: boolean
  bSize?: number | string // background size 百分比或者contain
}

export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.common,
    type,
    base: true,
    name: '图片滚动动画',
    thumbnail: new URL('./image-roll.png', import.meta.url).href,
    Comp,
    CompSetting,
    async defaultData(options) {
      const data = (options as IDesignImageRoll)?.data || await openSelectMaterial()
      if (!data) return
      const { width, height, top, left } = await ResourceUtil.loadImage(data)

      return merge({
        uuid: layerUuid(),
        name: '图片滚动动画',
        type,
        data,
        style: {
          width: `${width}px`,
          height: `${height}px`,
          top: `${top}px`,
          left: `${left}px`,
        },
      }, options as IDesignImageRoll)
    },
  })
}
