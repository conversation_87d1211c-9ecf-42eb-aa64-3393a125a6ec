<script setup lang="ts">

</script>

<template>
  <div>
    <div class="limit-box">
      <img
        class="face"
        src="../../assets/face.png"
      >
      <h3>抱歉，您未获得参与资格</h3>
      <p>请联系现场管理员</p>
    </div>
  </div>
</template>

<style scoped lang="scss">
.limit-box {
  width: 100vw;
  height: 100vh;
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  background-color: #263456;
}

.limit-box p {
  font-size: 14px;
  margin-top: 20px;
  margin-bottom: 30vh;
}

.limit-box .face {
  width: 160px;
}

.limit-box h3 {
  margin-top: 30px;
  font-size: 16px;
}
</style>
