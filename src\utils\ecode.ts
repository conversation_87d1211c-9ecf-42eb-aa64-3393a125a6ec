import type { App } from 'vue'
import { Toast } from './toast'

export interface IEcode {
  name?: string
  code: number
  message: string
  time?: number
}

export interface IDealParam {
  redirect?: string
}

// 从后台接口获取所有类型
const allError: IEcode[] = [
  { name: 'SUCCESS', code: 200, message: '成功' },
  { code: 401, message: '未授权' },
  { code: 404, message: '资源不存在' },
  { name: 'ERROR', code: 500, message: '服务器错误' },
  { code: 502, message: '网关错误' },
  { code: 504, message: '网关超时' },
  { name: 'LOGIN_TIMEOUT', code: 10004, message: '登录超时' },
  { name: 'NO_AUTH', code: 10007, message: '没有权限' },
]

export enum NotifiedType {
  warning = 'warning',
  error = 'error',
  success = 'success',
  info = 'info',
}

export class Ecode {
  name: string
  code: number
  message: string
  time: number

  private static cache: { [key: string]: Ecode } = {}
  private static element?: typeof import('element-plus')

  constructor(ecode: IEcode) {
    const { name = '', code, message } = ecode
    this.name = name
    this.code = code
    this.message = message
    this.time = 0
  }

  private async loadElementPlus() {
    if (Ecode.element) {
      return Ecode.element
    }
    await import('element-plus/dist/index.css')
    const element = await import('element-plus')
    Ecode.element = element
    return element
  }

  static forCode(code: number): IEcode | undefined {
    return allError.find(item => item.code === code)
  }

  static forName(name: string): IEcode | undefined {
    return allError.find(item => item.name === name)
  }

  static build(param: IEcode | ErrorEvent | Error): Ecode {
    if (param === null)
      return Ecode.build({ code: 500, message: '未知错误' })
    if (param instanceof ErrorEvent) {
      param = {
        name: 'error',
        code: 500,
        message: param.message || param.toString(),
      }
    }
    const config: IEcode = {
      name: param.name,
      code: (param as IEcode).code || 500,
      message: param.message || param.toString(),
    }
    // 根据ecode做缓存
    const key = JSON.stringify(config)
    const cache = Ecode.cache[key]
    if (cache) {
      return cache
    }
    const ecode = new Ecode(config)
    Ecode.cache[key] = ecode
    return ecode
  }

  notified({
    title,
    message,
    duration,
    type,
  }: { title?: string, message?: string, duration?: number, type?: NotifiedType } = {}): Ecode {
    // 方便notified的链式调用，弹窗可以异步执行
    const fn = async () => {
      const config = {
        title: title || '提示',
        message: message || this.message,
        duration: duration || 3000,
        type: type || NotifiedType.error,
      }
      if (Date.now() - this.time < config.duration) {
        return
      }
      this.time = Date.now()
      // 定义不同ecode的提示信息
      switch (this.code) {
        case 200:
          break
        default:
          break
      }
      const path = window.location.pathname
      if (path.startsWith('/next/mobile')) {
        Toast.message(config.message)
      } else {
        const element = await this.loadElementPlus()
        element.ElNotification(config)
      }
    }
    fn()
    return this
  }

  /**
   *
   * @param _options 配置
   * @returns Ecode
   */
  deal(_options?: IDealParam): Ecode {
    switch (this.code) {
      case 10004:
        break
      default:
        break
    }
    // 登录超时异常
    return this
  }
}

export function setupEcode(app: App) {
  window.addEventListener('error', (err) => {
    if (err.message === 'ResizeObserver loop limit exceeded')
      return
    console.error(err)
    Ecode.build(err.error || err)
      .notified()
      .deal()
  })
  window.addEventListener('unhandledrejection', (err) => {
    if (!err.reason)
      return
    if (err.reason === 'cancel' || err.reason?.message === 'cancel')
      return
    // 把网络引起的错误提示改成友好的提示语
    if (err.reason?.message?.includes('Failed to fetch dynamically imported module')) {
      err.reason.message = '当前网络不稳定，建议刷新页面'
    }
    console.error(err)
    Ecode.build(err.reason?.data?.msg ?? err.reason).notified().deal()
  })
  app.config.errorHandler = (err: any, _instance: any, _info: any) => {
    if (err.message === 'cancel')
      return
    console.error(err)
    Ecode.build(err?.data?.msg || err?.data?._msg || err?.data?.state || err).notified().deal()
  }
}
