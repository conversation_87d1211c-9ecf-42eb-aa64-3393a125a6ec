import { HiRequest } from '../request'

export default {
  page: (params: any) => HiRequest.post('/manage/admin/promaterial/page.htm', params),
  batchAdd: (params: any) => HiRequest.post('/manage/admin/promaterial/batchAdd.htm', params),
  delete: (params: any) => HiRequest.post('/manage/admin/promaterial/delete.htm', params),
  update: (params: any) => HiRequest.post('/manage/admin/promaterial/update.htm', params),
}
