import HiToast from '@/components/hi/toast/index.vue'
import { render } from 'vue'

export const Toast = {
  message(str: string, time = 3000) {
    const vnode = h(HiToast, {
      message: str,
    })
    // @ts-expect-error
    vnode.appContext = window.__bootstrap__?._context
    render(vnode, document.body)

    return new Promise<void>((resolve) => {
      setTimeout(() => {
        render(null, document.body)
        resolve()
      }, time)
    })
  },
  messageSync(str: string) {
    const vnode = h(HiToast, {
      message: str,
    })
    // @ts-expect-error
    vnode.appContext = window.__bootstrap__?._context
    render(vnode, document.body)

    return () => {
      render(null, document.body)
    }
  },
}
