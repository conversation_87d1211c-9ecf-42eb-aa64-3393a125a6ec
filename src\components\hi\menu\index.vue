<script setup lang="ts">
import type { IMenu } from './types.ts'

const props = defineProps<{
  logoText: string
  menuTree?: IMenu[]
  policyIgnore?: boolean
  theme?: number
}>()

const route = useRoute()

const flatMenu = computed(() => {
  const arr: IMenu[] = []
  const flat = (menu: IMenu[]) => {
    menu.forEach((item) => {
      arr.push(item)
      if (item.children) {
        flat(item.children)
      }
    })
  }
  flat(props.menuTree ?? [])
  return arr
})
const currentMenu = computed(() =>
  flatMenu.value
    .toSorted((a, b) => (b.to?.length || 0) - (a.to?.length || 0))
    .find(item => route.path.startsWith(item.to ?? '')),
)
const parentMenu = computed(() => {
  if (!props.menuTree)
    return null
  if (!currentMenu.value)
    return null
  const title = currentMenu.value.title
  return props.menuTree.find((item) => {
    let contain = false
    item.children?.forEach((child) => {
      if (child.title === title) {
        contain = true
      }
    })
    return contain
  })
})

const isFold = ref(true)
</script>

<template>
  <div class="menu-box flex" :class="[`theme${theme || ''}`]">
    <div class="fold-box" @click="isFold = !isFold">{{ isFold ? '>' : '<' }}</div>
    <template v-if="!isFold">
      <div class="logo">
        {{ logoText }}
      </div>
      <div class="left-box">
        <template v-for="item in props.menuTree" :key="item">
          <router-link
            v-if="item?.children?.length && item.children[0].to"
            class="item"
            :class="[{ cur: parentMenu?.title === item.title }]"
            :to="item.children[0].to"
          >
            {{ item.title }}
          </router-link>
        </template>
      </div>
      <div v-if="(parentMenu?.children?.length || 0) > 1" class="right-box w-150">
        <strong class="right-title">{{ parentMenu?.title }}</strong>
        <template v-for="item in parentMenu?.children" :key="item.to">
          <router-link
            v-if="item.to"
            class="item"
            :class="[{ cur: currentMenu === item }]"
            :to="item.to"
            :title="item.title"
          >
            {{ item.title }}
          </router-link>
        </template>
      </div>
    </template>
  </div>
</template>

<style scoped lang="scss">
@use 'sass:color';

.theme {
  $color: #f8697d;

  --hi-menu-bg-color: #{$color};
  --hi-menu-bg-image: linear-gradient(135deg, #f97794, #623aa2);
  --hi-menu-logo-bg-color: #{rgba($color, 0.1)};

  --hi-menu-bg-color_hover: #{color.adjust($color, $lightness: 2%)};
  --hi-menu-bg-color_cur: #{color.adjust($color, $lightness: 6%)};
  --hi-menu-font-color: #{rgba(#fff, 0.8)};
  --hi-menu-font-color_cur: #fff;

  --hi-menu1-bg-color_hover: #{color.adjust($color, $lightness: 28%)};
  --hi-menu1-bg-color_cur: #{color.adjust($color, $lightness: 25%)};
  --hi-menu1-font-color: #{rgba(#000, 0.8)};
  --hi-menu1-font-color_cur: #{color.adjust($color, $lightness: 1%)};
}
.theme1 {
  $color: #030852;

  --hi-menu-bg-color: #{$color};
  --hi-menu-bg-image: linear-gradient(135deg, #2afadf, #4c83ff);
  --hi-menu-logo-bg-color: #{rgba($color, 0.1)};

  --hi-menu-bg-color_hover: #{rgba(#fff, 0.2)};
  --hi-menu-bg-color_cur: #{color.adjust($color, $lightness: 45%)};
  --hi-menu-font-color: #{rgba(#fff, 0.8)};
  --hi-menu-font-color_cur: #fff;

  --hi-menu1-bg-color_hover: #{rgba(#000, 0.05)};
  --hi-menu1-bg-color_cur: #{rgba(#000, 0.1)};
  --hi-menu1-font-color: #{rgba(#000, 0.8)};
  --hi-menu1-font-color_cur: #{color.adjust($color, $lightness: 45%)};
}
.theme2 {
  $color: #8126d1;

  --hi-menu-bg-color: #{$color};
  --hi-menu-bg-image: linear-gradient(45deg, #{$color}, #18c0de);
  --hi-menu-logo-bg-color: #{rgba($color, 0.1)};

  --hi-menu-bg-color_hover: #{rgba($color, 0.3)};
  --hi-menu-bg-color_cur: #{rgba($color, 0.5)};
  --hi-menu-font-color: #{rgba(#92f8f5, 0.8)};
  --hi-menu-font-color_cur: #92f8f5;

  --hi-menu1-bg-color_hover: #{rgba($color, 0.05)};
  --hi-menu1-bg-color_cur: #{rgba($color, 0.1)};
  --hi-menu1-font-color: #{rgba(#000, 0.8)};
  --hi-menu1-font-color_cur: #{$color};
}

.menu-box {
  position: relative;

  .logo {
    width: 160px;
    height: 66px;
    position: absolute;
    top: 0;
    left: 0;
    line-height: 66px;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    background-color: var(--hi-menu-logo-bg-color);
    backdrop-filter: blur(1px);
  }
}
.fold-box {
  position: absolute;
  bottom: 10px;
  right: -16px;
  z-index: 100;
  width: 16px;
  height: 16px;
  line-height: 16px;
  background-color: #fff;
  border: 1px solid #ccc;
  text-align: center;
  color: #666;
  border-top-right-radius: 6px;
  cursor: pointer;
  opacity: 0.5;
}
.left-box {
  width: 160px;
  height: 100%;
  padding: 66px 0;
  background-color: var(--hi-menu-bg-color);
  background-image: var(--hi-menu-bg-image);
  overflow: auto;
  .item {
    display: inline-block;
    width: 100%;
    height: 40px;
    line-height: 40px;
    padding: 0 25px;
    cursor: pointer;
    color: var(--hi-menu-font-color);
    transition:
      background-color 0.3s,
      color 0.3s;
    white-space: nowrap;
    &:hover {
      background-color: var(--hi-menu-bg-color_hover);
    }
    &.cur {
      background-color: var(--hi-menu-bg-color_cur);
      color: var(--hi-menu-font-color_cur);
    }
  }
}
.right-box {
  overflow: auto;
  padding-bottom: 30px;
  background-color: #fff;
  .right-title {
    height: 66px;
    font-size: large;
    font-weight: 700;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .item {
    display: inline-block;
    width: calc(100% - 32px);
    height: 36px;
    line-height: 36px;
    margin: 0 8px;
    padding: 0 8px;
    border-radius: 2px;
    cursor: pointer;
    background-color: var(--hi-menu1-bg-color);
    color: var(--hi-menu1-font-color);
    transition:
      background-color 0.3s,
      color 0.3s;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    &:hover {
      background-color: var(--hi-menu1-bg-color_hover);
    }
    &.cur {
      background-color: var(--hi-menu1-bg-color_cur);
      color: var(--hi-menu1-font-color_cur);
    }
  }
}
</style>
