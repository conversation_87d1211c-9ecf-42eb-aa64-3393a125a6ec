<script setup lang="ts">
import type { IDesignRankings1 } from './rankings1'
import { cloneDeep } from 'lodash-es'
import { defineCustomEmits, injectScale, useAdaptionQuantity, useDesignState } from '../..'
import { processStyle } from '../../utils'

const layer = defineModel<IDesignRankings1>('layer', { required: true })
const customEmits = defineCustomEmits(layer)
const scale = injectScale()
const designState = useDesignState()

const boxRef = ref<HTMLElement>()
const displaySize = useElementSize(boxRef)

const adaptionQuantity = useAdaptionQuantity({
  displaySize: computed(() => {
    return {
      width: displaySize.width.value / scale.value,
      height: displaySize.height.value / scale.value,
    }
  }),
  itemWidth: computed(() => layer.value.itemWidth),
  aspectRatio: 0.9,
  gap: computed(() => layer.value.gap),
})

const ulStyle = computed(() => {
  return processStyle({
    gap: `${adaptionQuantity.gap}px`,
    fontSize: `${adaptionQuantity.width * 0.15}px`,
  }, scale)
})
const liStyle = computed(() => {
  return processStyle({
    width: `${adaptionQuantity.width}px`,
    height: `${adaptionQuantity.height}px`,
  }, scale)
})

const startRanking = computed(() => layer.value.startRanking || 0)

const rankings = computed(() => {
  const dataList = cloneDeep(designState.getLayerData('endRankings') || [])
  // 除去前几位
  dataList.splice(0, startRanking.value)

  // 看数量是否够用
  if (dataList.length < adaptionQuantity.count) {
    const count = adaptionQuantity.count - dataList.length
    for (let i = 0; i < count; i++) {
      dataList.push({ name: '', avatar: layer.value.default, score: 0 })
    }
  }
  // 超出删掉
  if (dataList.length > adaptionQuantity.count) {
    dataList.splice(adaptionQuantity.count)
  }
  return dataList
})

const count = computed(() => {
  return startRanking.value + adaptionQuantity.count
})

watch(
  () => count.value,
  (c, o) => {
    if (c === o) return
    customEmits('finishRankingCount', c)
  },
)
</script>

<template>
  <ul ref="boxRef" :style="ulStyle">
    <li v-for="(item, index) in rankings" :key="index" :style="liStyle">
      <img :src="item.avatar" class="img1" alt="">
      <div class="name">
        <span>{{ item.name }}</span>
      </div>
      <span class="num" :style="{ fontSize: `${adaptionQuantity.width * scale * 0.2}px` }">{{ startRanking + index + 1 }}</span>
    </li>
  </ul>
</template>

<style scoped lang="scss">
ul {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  align-content: center;
  li {
    position: relative;
    img,
    .name,
    .num {
      position: absolute;
      left: 0;
    }
    .img1 {
      width: 66%;
      aspect-ratio: 1;
      border-radius: 50%;
      top: 8%;
      left: 17%;

      border: 2px solid #eecd62;
      outline: 2px dashed #eecd62;
      outline-offset: 4px;
    }
    .name {
      width: 100%;
      height: 26%;
      bottom: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #fff000;
      span {
        width: 100%;
        white-space: nowrap;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .num {
      color: #eecd62;
      font-weight: bold;
      left: 5%;
    }
  }
}
</style>
