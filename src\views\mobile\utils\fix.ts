export function fixiOSInput(): void {
  // 修复 iPhone 软键盘收起后，页面回不来，下方出现空白和事件不可点击的 bug
  let blurHandler: number | NodeJS.Timeout | null = null
  let blurDom: HTMLElement | null = null

  window.addEventListener(
    'focus',
    (e: Event) => {
      const target = e.target as HTMLElement
      if (target && ['INPUT', 'TEXTAREA', 'SELECT'].includes(target.nodeName)) {
        if (blurDom !== target) {
          if (blurHandler) {
            clearTimeout(blurHandler)
            blurHandler = null
          }
        }
      }
    },
    true,
  )

  window.addEventListener(
    'blur',
    (e: Event) => {
      const target = e.target as HTMLElement
      if (target && ['INPUT', 'TEXTAREA', 'SELECT'].includes(target.nodeName)) {
        blurDom = target
        setTimeout(() => {
          blurDom = null
        }, 100)

        if (blurHandler) {
          clearTimeout(blurHandler)
        }

        blurHandler = setTimeout(() => {
          window.requestAnimationFrame(() => {
            const scrollHeight
              = document.documentElement.scrollTop || document.body.scrollTop || 0
            window.scrollTo(0, scrollHeight + 1)
          })
        }, 100)
      }
    },
    true,
  )
}
