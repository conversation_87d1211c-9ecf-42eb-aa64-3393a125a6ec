import type { App } from 'vue'

// 驼峰转横线
function humpToHyphen(s: string | undefined) {
  if (!s)
    return ''
  return s.replace(/([A-Z])/g, '-$1').toLowerCase()
}

export function setupDirective(app: App) {
  const files = import.meta.glob('./*.ts', { eager: true }) as any
  Object.keys(files).forEach((key: string) => {
    Object.keys(files[key]).forEach((k) => {
      app.directive(k === 'default' ? key.slice(2, -3) : humpToHyphen(k), files[key][k])
    })
  })
}
