<script setup lang="ts">
import { useUserStore } from '~/src/store/user'
import { useWallStore } from '~/src/store/wall'
import { hasAuth } from '~/src/utils/auth'

const isReady = ref(false)
const { fetchWallConfig } = useWallStore()
const { fetchUser } = useUserStore()

const show10thBanner = ref(false)

onMounted(async () => {
  await fetchWallConfig()
  await fetchUser()

  show10thBanner.value = !hasAuth(['man', 'oem', 'spo.hi.vip', 'spo.ft'])
  isReady.value = true
})
</script>

<template>
  <header v-if="show10thBanner" class="relative z-999 h-60 w-full">
    <a href="/special/10th-anniversary.html?from=nav" target="_blank" class="relative h-full w-full">
      <img class="h-full w-full object-cover" src="@/assets/layout/editor-top-banner.png" alt="10th" />
    </a>
    <img class="absolute right-20 top-15 h-30 w-30 cursor-pointer" src="@/assets/layout/editor-top-banner-close-button.png" alt="10th" @click.stop="show10thBanner = false" />
  </header>
  <router-view v-if="isReady" />
</template>

<style scoped lang="scss">
</style>
