function disableChildMouse(el: HTMLElement) {
  el.style.cursor = 'e-resize'
  for (const item of el.children) {
    if (item instanceof HTMLElement) {
      item.style.pointerEvents = 'none'
    }
  }
}
function enableChildMouse(el: HTMLElement) {
  el.style.cursor = ''
  for (const item of el.children) {
    if (item instanceof HTMLElement) {
      item.style.cssText = ''
    }
  }
}

function mousedownFn(e: MouseEvent) {
  const target = e.target as HTMLElement
  if (!target) return
  const inputDom = target.querySelector('input') as HTMLInputElement
  if (!inputDom) return

  if (document.activeElement === inputDom) return

  const step = Number.parseFloat(inputDom.getAttribute('step') || '1')

  const oldValue = Number.parseFloat(inputDom.value || '0')

  // let pointerLock = false
  let mouseDom: HTMLElement | null = null
  let dx = 0
  let dy = 0
  const move = async (e: MouseEvent) => {
    e.stopPropagation()
    e.preventDefault()

    // 特殊情况会出现闪动
    const deltaX = e.movementX
    dx += Math.abs(deltaX) > 100 ? 0 : deltaX
    const deltaY = e.movementY
    dy += Math.abs(deltaY) > 100 ? 0 : deltaY

    if (!mouseDom && (Math.abs(dx) || Math.abs(dy))) {
      target.requestPointerLock()
      mouseDom = document.createElement('div')
      mouseDom.classList.add('pointer-lock')
      document.body.appendChild(mouseDom)
      mouseDom.getBoundingClientRect()
    }

    if (mouseDom) {
      let x = (e.clientX + dx) % window.innerWidth
      if (x < 0) {
        x = window.innerWidth + x
      }
      let y = (e.clientY + dy) % window.innerHeight
      if (y < 0) {
        y = window.innerHeight + y
      }
      mouseDom.style.transform = `translate3d(${x}px, ${y}px, 0)`
    }

    // 滑动距离转换为数字
    let res = oldValue + Math.round(dx / 3) * (step || 1)
    // 精度处理
    if (step !== undefined) {
      // 识别小数点后几位
      const stepStr = step.toString().split('.')[1] || ''
      if (stepStr.length > 0) {
        const fix = 10 ** stepStr.length
        res = Math.round(res * fix) / fix
      }
    }

    inputDom.value = `${res}`
    const inputEvent = new Event('input', { bubbles: true })
    inputDom.dispatchEvent(inputEvent)
    const changeEvent = new Event('change', { bubbles: true })
    inputDom.dispatchEvent(changeEvent)
  }
  const up = () => {
    document.removeEventListener('mousemove', move)
    document.removeEventListener('mouseup', up)
    if (mouseDom) {
      mouseDom.remove()
      mouseDom = null
      document.exitPointerLock()
    }
    if (!dx) {
      enableChildMouse(target)
      inputDom.focus()
      inputDom.addEventListener('blur', () => {
        disableChildMouse(target)
      }, { once: true })
    }
  }

  document.addEventListener('mousemove', move)
  document.addEventListener('mouseup', up)
}

export default {
  mounted(el: HTMLElement) {
    const inputBox = el.querySelector('.el-input') as HTMLElement
    const inputDom = el.querySelector('input') as HTMLInputElement
    if (!inputBox || !inputDom) {
      console.warn('指令仅适用于 el-input 组件')
      return
    }
    // 先禁用 el-input 中所有图层的操作，然后监听鼠标事件
    disableChildMouse(inputBox)

    inputBox.addEventListener('mousedown', mousedownFn)
  },
  unmounted(el: HTMLElement) {
    const inputBox = el.querySelector('.el-input') as HTMLElement
    const inputDom = el.querySelector('input') as HTMLInputElement
    if (!inputBox || !inputDom) return

    enableChildMouse(inputBox)

    inputBox.removeEventListener('mousedown', mousedownFn)
  },
}
