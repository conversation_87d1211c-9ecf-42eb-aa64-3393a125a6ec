import { merge } from 'lodash-es'
import { BisTypes } from '../..'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './winning-list2-setting.vue'
import Comp from './winning-list2.vue'

// 类型
export const type = 'winning-list2'

// 数据类型约束
export interface IDesignWinningList2 extends IDesignLayer {
  type: typeof type
  data: {
    avatarSize?: number
    defaultAvatar?: string
    avatarDecoration?: string
    nameColor?: string
    nameFontSize?: number
    yGap?: number
  }
}

export const DEFAULT_DATA = {
  avatarSize: 30,
  defaultAvatar: new URL(`./assets/default.png`, import.meta.url).href,
  avatarDecoration: new URL(`./assets/headborder.png`, import.meta.url).href,
  nameColor: '#fff',
  nameFontSize: 14,
  yGap: 10,
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.result,
    showType: ['pcwall'],
    showInteractive: [InteractiveEnum.lotteryv3],
    type,
    name: '中奖名单',
    Comp,
    CompSetting,
    thumbnail: new URL('./winning-list2.png', import.meta.url).href,
    defaultData(options): IDesignWinningList2 {
      return merge({
        uuid: layerUuid(),
        name: '中奖名单',
        type,
        data: {},
        style: {
          width: '260px',
          height: '600px',
          top: '0px',
          right: '30px',
        },
      }, options as IDesignWinningList2)
    },
  })
}
