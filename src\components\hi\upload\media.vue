<script lang="ts" setup>
import type { UploadFile, UploadFiles, UploadProps } from 'element-plus'
import { ElMessage } from 'element-plus'
import { isVideo } from '~/src/utils'

const props = defineProps({
  moduleValue: {
    type: String,
    default: '',
  },
  maxSize: {
    type: [String, Number],
    default: 5,
  },
  maxWidth: {
    type: [String],
    default: '',
  },
  accept: {
    type: String,
    default: 'image/*,video/*',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  // 是否需要显示loding状态
  showLoading: {
    type: Boolean,
    default: false,
  },
  use: {
    type: String,
    default: 'image',
  },
})
const emits = defineEmits<{
  (e: 'update:modelValue', v: string): void
  (event: 'beforeUpload'): void
  (event: 'success', url: string, uploadFile: UploadFile, uploadFiles: UploadFiles): void
  (event: 'error'): void
}>()

const acceptType = ['image/*', 'video/*']
const attrs = useAttrs()
const upload = ref<any>()
const loading = ref(false)

const verifyImgSize: UploadProps['beforeUpload'] = (file) => {
  console.log(attrs, '----attrs', props, '----props')

  const maxSize = isVideo(file) ? 20 : Number(props.maxSize)
  const maximum = file.size / 1024 / 1024 > maxSize

  const pattern = file.type

  emits('beforeUpload')

  if (!/image|video/.test(pattern)) {
    ElMessage.error({ message: `只支持图片或视频` })
    return false
  }

  if (maximum) {
    const name = isVideo(file) ? '视频' : '图片'
    let message = `上传${name}大小不能超过${maxSize}M`
    if (maxSize < 1) {
      message = `上传${name}大小不能超过${Math.trunc((maxSize * 1024) / 100) * 100}k`
    }
    ElMessage.error({ message })
    return false
  }

  if (props.showLoading) {
    loading.value = true
  }
  if (!props.maxWidth) {
    return true
  }
  // 图片尺寸检查
  return new Promise((resolve, reject) => {
    if (isVideo(file)) {
      resolve(true)
    } else {
      const img = new Image()
      img.src = window.URL.createObjectURL(file)
      img.onload = function () {
        const width = img.width
        const height = img.height
        const [maxWidth, maxHeight] = props.maxWidth.split('x')
        if (width > Number(maxWidth) || height > Number(maxHeight)) {
          ElMessage.error({ message: `上传的图片尺寸不能超过${maxWidth}x${maxHeight}` })
          reject(new Error(`上传的图片尺寸不能超过${maxWidth}x${maxHeight}`))
        } else {
          resolve(true)
        }
      }
    }
  })
}

const uploadSuccess: UploadProps['onSuccess'] = async (res, file: any, fileList) => {
  emits('success', res.data.url, file, fileList)
  emits('update:modelValue', res.data.url)
}
</script>

<template>
  <el-upload
    ref="upload"
    v-bind="$attrs"
    action="/pro/hxc/procommonfile/uploadImgOrVideo.htm"
    :before-upload="verifyImgSize"
    :accept-type="acceptType"
    :accept="accept"
    :disabled="disabled"
    :on-success="uploadSuccess"
  >
    <slot></slot>
  </el-upload>
</template>

<style scope lang="scss">
:deep(.el-upload--picture-card) {
  width: 100%;
  height: 100%;
}
</style>
