import type { AxiosRequestConfig, InternalAxiosRequestConfig, Method } from 'axios'
import { decryptResponse, encryptRequest } from '@/utils/secrecy'
import { setupToken } from '@/utils/token'
import { wordsFilter } from '@/utils/words-filter'
import axios from 'axios'
import { useInstanceRouter } from '../hooks/useInstanceRouter'
import { timer, uuid } from '../utils'
import { envUtils } from '../utils/env'
import { mobileOnFulfilledResInterceptors, mobileOnRejectedResInterceptors } from './mobile/interceptors'

interface RequestResponse<D = any> {
  code: number
  data: D
  msg: string
}

const instance = axios.create({
  withCredentials: true,
  baseURL: location.origin,
})

let tabId = window.sessionStorage.getItem('tabId')
if (!tabId) {
  tabId = `${new Date().getTime()}${Math.floor(Math.random() * 1000)}`
  window.sessionStorage.setItem('tabId', tabId)
}
let browserId = window.localStorage.getItem('browserId')
if (!browserId) {
  browserId = uuid()
  window.localStorage.setItem('browserId', browserId)
}

// 添加请求拦截器
instance.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    setupToken(config)

    // 添加浏览器和窗口标识，便于分析日志
    config.headers['browser-tab'] = `${browserId}-${tabId}`

    // 只有请求为post和put才进行过滤
    if (['POST', 'PUT'].includes(config.method?.toUpperCase() || '')) {
      if (!config.url?.includes('proanswerracev3')) {
        wordsFilter(config.data)
      }
      encryptRequest(config)
    }

    // 扫描当前 localStorage 中所有_cookie_开头的key和value放入请求头中
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith('x-cookie-')) {
        config.headers[key] = localStorage.getItem(key)
      }
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

// 返回拦截器
instance.interceptors.response.use(
  async (res) => {
    decryptResponse(res)
    await mobileOnFulfilledResInterceptors(res)
    return res
  },
  async (error) => {
    if (error?.response?.data) {
      const res = error.response
      decryptResponse(res)
      const { state } = res.data || {}
      const url = error.config.url

      if (url.startsWith('/pro/hxc/mobile/')) {
        // 手机端专有异常逻辑
        await mobileOnRejectedResInterceptors(res)
      }

      if (state === 'LOGIN_TIME_OUT' || state === 'WX_LOGIN_TIME_OUT') {
        ElMessage.error('登录超时，请重新登录')
        // 跳登录页面
        if (envUtils.isDev) {
          const router = useInstanceRouter()
          const route = router.currentRoute.value
          const query = route.query
          const queryStr = Object.keys(query).map(key => `${key}=${query[key]}`).join('&')
          const redirect = encodeURIComponent(queryStr ? `${route.path}?${queryStr}` : route.path)
          if (url.startsWith('/pro/hxc/mobile/')) {
            router.push({
              path: '/mobile/wxuser/login',
              query: { redirect },
            })
          } else if (url.startsWith('/pro')) {
            router.push({
              path: '/admin/user/login',
              query: { redirect },
            })
          } else if (url.startsWith('/manage')) {
            router.push({
              path: '/manage/user/login',
              query: { redirect },
            })
          } else if (url.startsWith('/padsign')) {
            router.push({
              path: '/manage/user/login',
              query: { redirect },
            })
          } else if (url.startsWith('/oem')) {
            router.push({
              path: '/oem/user/login',
              query: { redirect },
            })
          }
        } else {
          // todo 未做iframe嵌套处理
          const redirect = encodeURIComponent(location.href)
          if (url.startsWith('/pro/hxc/mobile')) {
            location.href = `/pro/hxc/wx/auth/go.htm?url=${redirect}`
          } else if (url.startsWith('/pro')) {
            location.href = `/pro/admin/user/login.html?redirect=${redirect}`
          } else if (url.startsWith('/manage')) {
            location.href = `/act/manage/user/login.html?redirect=${redirect}`
          } else if (url.startsWith('/oem')) {
            location.href = `/pro/oem/user/login.html?redirect=${redirect}`
          }
        }
        return new Promise(() => { })
      }
      return Promise.reject(res)
    }
    return Promise.reject(error)
  },
)

async function request<D = any>(url: string, method: Method = 'GET', config: AxiosRequestConfig = {}) {
  let defultCount = 10
  let outError: Error | null = null
  for (let n = 0; n < defultCount; n++) {
    try {
      const res = await instance.request<RequestResponse<D>>({ ...config, url, method })
      return Promise.resolve(res.data.data)
    } catch (error: any) {
      outError = error
      if (error?.data?.state === 'RETRY' && n < defultCount - 1) {
        const { count, time } = error.data.data || {}
        if (count) {
          defultCount = count
        }
        await timer(time || 500)
      } else {
        return Promise.reject(error)
      }
    }
  }
  return Promise.reject(outError)
}

const headers = { 'Content-Type': 'application/text' }

type Request =
  | (<D = any>(url: string, _params: any, config?: AxiosRequestConfig) => Promise<D>)
  | (<D = any>(url: string, data: any, config?: AxiosRequestConfig) => Promise<D>)

type ById = <D = any>(url: string, id: string, config?: AxiosRequestConfig) => Promise<D>

export const Get: Request = (url, _params, config = {}) => {
  // 数组处理
  const params: { [key: string]: string } = {}
  for (const key in _params) {
    let value: any = _params[key]
    if (Array.isArray(value)) {
      value = value.join(',')
    }
    params[key] = value
  }
  return request(url, 'GET', {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    ...config,
    params,
  })
}
export const ReadById: ById = (url, id, config = {}) => {
  return request(url, 'GET', {
    headers,
    ...config,
    params: { id },
  })
}
export const Post: Request = (url, data, config = {}) => {
  return request(url, 'POST', {
    headers,
    ...config,
    data,
  })
}
export const DeleteById: ById = (url, id, config = {}) => {
  return request(url, 'DELETE', {
    headers,
    ...config,
    params: { id },
  })
}
export const Delete: Request = (url, data, config = {}) => {
  return request(url, 'DELETE', {
    headers,
    ...config,
    data,
  })
}
export const Patch: Request = (url, data, config = {}) => {
  return request(url, 'PATCH', {
    headers,
    ...config,
    data,
  })
}
export const Put: Request = (url, data, config = {}) => {
  return request(url, 'PUT', {
    headers,
    ...config,
    data,
  })
}

// 文件上传
export const Upload: Request = (url, data, config = {}) => {
  return request(url, 'POST', {
    headers: { 'Content-Type': 'multipart/form-data' },
    ...config,
    data,
  })
}

export const HiRequest = {
  get: Get,
  post: Post,
  put: Put,
  delete: Delete,
  patch: Patch,
  deleteById: DeleteById,
  readById: ReadById,
  upload: Upload,
}
