<script setup lang="ts">
import type { TextArtMaterial } from '../hooks/useFontLoader'

interface Props {
  textArtList: TextArtMaterial[]
}
defineProps<Props>()

const emit = defineEmits<{
  (e: 'select', item: TextArtMaterial): void
}>()

function onItemClick(item: TextArtMaterial) {
  emit('select', item)
}
</script>

<template>
  <div class="text-art-box">
    <div class="text-art-content">
      <div
        v-for="item in textArtList"
        :key="item.id"
        class="text-art-item"
        @click="onItemClick(item)"
      >
        <div class="preview-text" :style="item.style">文字</div>
        <div class="item-name">{{ item.name }}</div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.text-art-box {
  width: 100%;
  background: #fff;
  border-radius: 4px;
  padding: 16px;
  max-height: 300px;
  overflow-y: auto;

  .text-art-content {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
  }

  .text-art-item {
    position: relative;
    height: 100px;
    width: 100px;
    background: #f5f7fa;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;

    &:hover {
      background: #e6e8ea;
    }

    &.is-selected {
      background: #e6f7ff;
      border: 1px solid #1890ff;
    }

    &.is-playing {
      .preview-text {
        animation: textPulse 2s infinite;
      }
    }

    .preview-text {
      font-size: 24px;
      margin-bottom: 8px;
      transition: transform 0.3s;
    }

    .item-name {
      font-size: 12px;
      color: #666;
    }
  }
}
</style>
