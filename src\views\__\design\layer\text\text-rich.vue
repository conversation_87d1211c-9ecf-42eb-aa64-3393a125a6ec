<script setup lang="ts">
import type { CSSProperties } from 'vue'
import Quill from 'quill'
import { hasAuth } from '~/src/utils/auth'
import { useDesignTemp } from '../..'
import { defaultData, type IDesignTextRich } from './text-rich'
import CompAttachment from './text-rich-attachment.vue'
import { useQuillManager } from './text-rich-utils'
import 'quill/dist/quill.snow.css'

const layer = defineModel<IDesignTextRich>('layer', { required: true })
const designTemp = useDesignTemp()

const quillRef = ref<HTMLElement>()

// 附件
const teleportStyle = ref<CSSProperties>({
  position: 'absolute',
  pointerEvents: 'none',
  zIndex: 999,
})

const quillManager = useQuillManager(layer)

const { isEnabled } = quillManager

watch(
  () => [
    isEnabled.value,
    layer.value.style.left,
    layer.value.style.top,
    layer.value.style.right,
    layer.value.style.bottom,
  ],
  ([v]) => {
    if (v) {
      const rect = layer.value.$dom?.getBoundingClientRect()
      if (!rect) return
      const offsetX = rect.width < 80 ? 80 - rect.width : 0
      const offsetY = rect.height < 80 ? 80 - rect.height : 0

      Object.assign(teleportStyle.value, {
        width: `${rect.width + offsetX}px`,
        height: `${rect.height + offsetY}px`,
        left: `${rect.left - offsetX / 2}px`,
        top: `${rect.top - offsetY / 2}px`,
      })
    }
  },
)

onClickOutside(quillRef, (e) => {
  const target = e.target as HTMLElement
  if (!target) return
  if (target.closest('.moveable-ignore')) return
  if (target.closest('.el-select')) return
  if (target.closest('.el-popper')) return
  if (target.closest(`section[data-uuid="${layer.value.uuid}"]`)) return
  if (isEnabled.value) {
    quillManager.disable()
  }
})

// 设置默认样式
const richStyle = computed(() => {
  const style: CSSProperties = {}
  const { color, writingMode, fontFamily, textShadow, '-webkit-text-stroke': textStroke, backgroundImage } = layer.value.style
  if (!color) {
    style.color = defaultData.color
  }
  if (writingMode) {
    style.width = 'auto'
    style.height = '100%'
  } else {
    style.width = '100%'
    style.height = 'auto'
  }
  if (fontFamily) {
    style.fontFamily = fontFamily
  }
  if (textShadow) {
    style.textShadow = textShadow
  }
  if (textStroke) {
    style['-webkit-text-stroke'] = textStroke
  }
  if (backgroundImage) {
    style.backgroundImage = backgroundImage
  }
  return style
})

async function dblclickFn() {
  if (!designTemp.isEdit) return
  if (!hasAuth(['man', 'oem', 'spo.hi.vip', 'spo.ft']) && !layer.value.spoVisible) return

  quillManager.enable()
}

watchOnce(
  () => !!layer.value.$dom,
  () => {
    layer.value.$dom?.addEventListener('dblclick', dblclickFn)
  },
)

if (!designTemp.isEdit) {
  watch(
    () => [quillRef.value, quillManager.renderHtml.value] as const,
    ([dom, v]) => {
      if (!dom) return
      dom.innerHTML = `<div class="ql-editor ql-blank">${v}</div>`
    },
    { immediate: true },
  )
}

onMounted(async () => {
  if (!quillRef.value) return

  if (!designTemp.isEdit) return
  const quill = new Quill(quillRef.value, { theme: 'snow', modules: { toolbar: null } })
  quillManager.init(quill)
})
onBeforeUnmount(() => {
  layer.value.$dom?.removeEventListener('dblclick', dblclickFn)
  quillManager.destroy()
})
</script>

<template>
  <div
    ref="quillRef"
    class="rich-box"
    :style="richStyle"
  ></div>
  <Teleport v-if="isEnabled" to="body">
    <div class="attachment-box" :style="teleportStyle">
      <CompAttachment v-model:layer="layer" />
    </div>
  </Teleport>
</template>

<style scoped lang="scss">
.rich-box {
  user-select: text;
  position: relative;
  font-size: 14px;
  word-break: break-all;
  max-height: 100%;
  max-width: 100%;
  overflow: auto;

  &.ql-container {
    border: none;
  }

  img {
    display: inline-block;
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    vertical-align: middle;
    margin: 0 auto;
  }

  :deep() {
    .ql-editor {
      padding: 0;
      line-height: inherit;
      scrollbar-width: none; /* firefox */
      -ms-overflow-style: none; /* IE 10+ */
      &::-webkit-scrollbar {
        display: none; /* Chrome Safari */
      }

      // fix: span的高度会比p标签高度高，选中后会出滚动条
      > p {
        overflow: hidden;
      }
    }
  }
}
</style>
