import type { IDesignLayer, IDesignSetup } from '../../types'
import { BisTypes } from '../..'
import { layerUuid } from '../../utils'
import CompSetting from './mobile-game-rule-setting.vue'
import Comp from './mobile-game-rule.vue'

export const type = 'mobile-game-rule'

export const defaultData: IDesignMobileGameRule['data'] = {
  text: '',
  textColor: '#000000',
  closeImg: new URL('./assets/close.png', import.meta.url).href,
  titleImg: new URL('./assets/title.png', import.meta.url).href,
  bgImg: new URL('./assets/bg.png', import.meta.url).href,
}

export interface IDesignMobileGameRule extends IDesignLayer {
  type: typeof type
  version?: number
  data: {
    text?: string
    textColor?: string
    // v2专有的字段，2024.11.5新增，后续废弃v1的字段（需要先修改所有旧模板）
    closeImg?: string
    titleImg?: string
    bgImg?: string
    // v1提供的字段
    title?: string
    closeColor?: string
    titleColor?: string
    titleBgColor?: string
    textBgColor?: string
    textBorderColor?: string
  }
}

export function setup(app: IDesignSetup) {
  app.registry({
    type,
    bisType: BisTypes.rule,
    showType: ['mobile'],
    name: '游戏规则',
    thumbnail: new URL('./mobile-game-rule.png', import.meta.url).href,
    Comp,
    CompSetting,
    defaultData() {
      return {
        uuid: layerUuid(),
        name: '游戏规则',
        type,
        style: {
          top: '0px',
          left: '0px',
          width: `100%`,
          height: `100%`,
          backgroundColor: 'rgba(0,0,0,0.4)',
        },
        version: 2,
        data: {},
      }
    },
  })
}
