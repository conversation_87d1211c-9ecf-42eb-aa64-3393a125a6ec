<script setup lang="ts">
import type { IPadsignConfig } from '~/src/api/pcwall/padsign'
import { ElTable } from 'element-plus'

const props = defineProps<{
  config: IPadsignConfig
  wallId: number
}>()

definePage({ meta: { label: 'iPad 签字' } })

const data = ref<any[]>([])
const selectList = ref([])

async function fetchData() {
  const res = await api.admin.padsign.listData({
    where: {
      wallId: props.wallId,
    },
  })
  data.value = res
}

async function onDel(row: any) {
  await ElMessageBox.confirm('是否删除该签名', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
  await api.admin.padsign.deleteData({
    where: {
      idList: [row.id!],
    },
  })
  await fetchData()
}

function handleSelectionChange(val: any) {
  selectList.value = val
}

async function onBatchDel() {
  const idList = selectList.value.map((item: any) => item.id)
  if (!idList?.length) {
    ElMessage.warning('请选择要删除的签名')
    return
  }
  await ElMessageBox.confirm('是否删除该签名', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
  await api.admin.padsign.deleteData({ where: { idList } })
  await fetchData()
}

onMounted(async () => {
  await fetchData()
})
</script>

<template>
  <div class="pb-66 pt-10">
    <div class="mb-20 flex">
      <el-button type="danger" class="ml-10" @click="onBatchDel">批量删除</el-button>
    </div>
    <ElTable :data="data" border @selection-change="handleSelectionChange">
      <el-table-column type="selection"></el-table-column>
      <el-table-column prop="value" label="签名图" align="center">
        <template #default="{ row }">
          <div class="bg-#ccc">
            <el-image :src="row.img" loading="lazy" class="h-100" fit="contain" />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="{ row }">
          <el-button type="danger" @click="onDel(row)"> 删除 </el-button>
        </template>
      </el-table-column>
    </ElTable>
  </div>
</template>
