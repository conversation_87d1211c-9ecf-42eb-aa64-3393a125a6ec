<script setup lang="ts">
import { useDataAttr } from '../..'
import { defaultColor, defaultShadowColor, type IDesignDown } from './down'

const layer = defineModel<IDesignDown>('layer', { required: true })

const colorBind = useDataAttr(layer.value, 'color', defaultColor)
const shadowColorBind = useDataAttr(layer.value, 'shadowColor', defaultShadowColor)
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <span>颜色</span>
        <hi-color v-model="colorBind" />
      </div>

      <div class="setting-item">
        <span>阴影</span>
        <hi-color v-model="shadowColorBind" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
