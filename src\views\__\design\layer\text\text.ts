import type { IDesignLayer, IDesignSetup } from '../../types'
import { merge } from 'lodash-es'
import { layerUuid } from '../../utils'
import CompSetting from './text-setting.vue'
import Comp from './text.vue'

// 类型
export const type = 'text'

export const defaultStyle: {
  fontSize: number
  color: string
  lineHeight: number
  padding: string
  textAlign: 'left' | 'center' | 'right'
} = {
  fontSize: 16,
  color: '#000',
  lineHeight: 1.5,
  padding: '6px',
  textAlign: 'center',
}

// 数据类型约束
export interface IDesignText extends IDesignLayer {
  type: typeof type
  data: string
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    type,
    base: true,
    name: '文字',
    Comp,
    CompSetting,
    defaultData(options) {
      return merge({
        uuid: layerUuid(),
        name: '新文本',
        type,
        data: '双击编辑文本',
        style: {},
      }, options as IDesignText)
    },
  })
}
