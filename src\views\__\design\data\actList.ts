export const actList = [
  {
    actName: '签到',
    actType: 'applysign',
    color: '#41A1FF',
  },
  {
    actName: '上墙',
    actType: 'msg',
    color: '#41A1FF',
  },
  {
    actName: '提问',
    actType: 'question',
    color: '#41A1FF',
  },
  {
    actName: '许愿树',
    actType: 'wish',
    color: '#41A1FF',
  },
  {
    actName: '签到簿',
    actType: 'signbook',
    color: '#41A1FF',
  },
  {
    actName: '签名墙',
    actType: 'signature',
    color: '#41A1FF',
  },
  {
    actName: '图片墙',
    actType: 'pic',
    color: '#41A1FF',
  },
  {
    actName: '启动仪式',
    actType: 'launchingceremony',
    color: '#2ABE5B',
  },
  {
    actName: '订货会',
    actType: 'placeorder',
    color: '#2ABE5B',
  },
  {
    actName: '地图签到',
    actType: 'mapsign',
    color: '#00a2ff',
  },
  {
    actName: '业绩目标会',
    actType: 'performancev3',
    color: '#27bf5b',
  },
  {
    actName: '对对碰',
    actType: 'supperzzle',
    color: '#fbba1e',
  },
  {
    actName: '滚动抽奖(旧)',
    actType: 'lottery',
    color: '#FF5B52',
  },
  {
    actName: '滚动抽奖',
    actType: 'lotteryv3',
    color: '#FF5B52',
  },
  {
    actName: '名单抽奖(旧)',
    actType: 'listlottery',
    color: '#FF5B52',
  },
  {
    actName: '名单抽奖',
    actType: 'listlotteryv3',
    color: '#FF5B52',
  },
  {
    actName: '排座抽奖',
    actType: 'seglottery',
    color: '#FF5B52',
  },
  {
    actName: '红包雨',
    actType: 'redpack',
    color: '#FF5B52',
  },
  {
    actName: '图片抽奖(旧)',
    actType: 'piclottery',
    color: '#FF5B52',
  },
  {
    actName: '图片抽奖',
    actType: 'piclotteryv3',
    color: '#FF5B52',
  },
  {
    actName: '摇号抽奖',
    actType: 'lotlottery',
    color: '#FF5B52',
  },
  {
    actName: '弹幕抽奖',
    actType: 'danmulottery',
    color: '#FF5B52',
  },
  {
    actName: '拍照抽奖',
    actType: 'photolottery',
    color: '#FF5B52',
  },
  {
    actName: '套红包',
    actType: 'ropepack',
    color: '#FF5B52',
  },
  {
    actName: '语音红包',
    actType: 'voicepack',
    color: '#FF5B52',
  },
  {
    actName: '摇一摇',
    actType: 'shakev3',
    color: '#FBBA1E',
  },
  {
    actName: '敲敲乐',
    actType: 'diglettv3',
    color: '#FBBA1E',
  },
  {
    actName: '数钱',
    actType: 'moneyv3',
    color: '#FBBA1E',
  },
  {
    actName: '拔河',
    actType: 'tugwar',
    color: '#FBBA1E',
  },
  {
    actName: '最佳射手',
    actType: 'shoot',
    color: '#FBBA1E',
  },
  {
    actName: '射击游戏',
    actType: 'firev3',
    color: '#30eecd',
  },
  {
    actName: '接金币',
    actType: 'goldcoinv3',
    color: '#30eecd',
  },
  {
    actName: '答题(旧)',
    actType: 'answerrace',
    color: '#FBBA1E',
  },
  {
    actName: '答题',
    actType: 'answerracev3',
    color: '#FBBA1E',
  },
  {
    actName: '投票',
    actType: 'vote',
    color: '#775FFF',
  },
  {
    actName: '打赏',
    actType: 'reward',
    color: '#775FFF',
  },
  {
    actName: '评分',
    actType: 'mark',
    color: '#775FFF',
  },
  {
    actName: '大转盘',
    actType: 'wheelsurf',
    color: '#FF5B52',
  },
  {
    actName: '九宫格',
    actType: 'ninegrids',
    color: '#30eecd',
  },
  {
    actName: '拆盲盒',
    actType: 'mysterybox',
    color: '#FBBA1E',
  },
  {
    actName: '红包墙',
    actType: 'packetwall',
    color: '#775FFF',

  },
  {
    actName: '个人中心',
    actType: 'wxuser-center',
  },
  {
    actName: '中奖记录',
    actType: 'wxuser-awards',
  },
]
