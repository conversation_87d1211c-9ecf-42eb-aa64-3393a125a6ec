import { merge } from 'lodash-es'
import { BisTypes } from '../../'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './winning-list5-setting.vue'
import Comp from './winning-list5.vue'

// 类型
export const type = 'winning-list5'

// 数据类型约束
export interface IDesignWinningList5 extends IDesignLayer {
  type: typeof type
  data: {
    gap: number

    backgroundColor: string
    borderRadius: number
    cardWidth: number
    cardHeight: number

    numColor: string
    numFontSize: number
    numFontWeight: boolean

    cardBorderShow: boolean
    cardBorderColor: string
    cardBorderWidth: number
  }
}

export const DEFAULT_DATA: IDesignWinningList5['data'] = {

  cardHeight: 70,
  cardWidth: 199,
  cardBorderShow: true,
  borderRadius: 12,
  cardBorderColor: 'rgba(168, 168, 168, 1)',
  numFontSize: 26,
  gap: 33,

  numFontWeight: true,
  backgroundColor: 'rgba(0, 0, 0, 0.5)',

  numColor: 'rgba(0, 255, 246, 1)',
  cardBorderWidth: 1,
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.result,
    showInteractive: [InteractiveEnum.seglottery],
    showType: ['pcwall'],
    type,
    name: '排座抽奖-中奖名单',
    Comp,
    CompSetting,
    thumbnail: new URL('./winning-list5.png', import.meta.url).href,
    defaultData(options): IDesignWinningList5 {
      return merge({
        uuid: layerUuid(),
        name: '排座抽奖-中奖名单',
        type,
        style: {
          left: '0px',
          top: '0px',
          width: '1280px',
          height: '800px',
        },
        data: {},
      }, options as IDesignWinningList5)
    },
  })
}
