<script setup lang="ts">
import type { IDesignVideo } from './video'
import { defaultControls } from './video'

const layer = defineModel<IDesignVideo>('layer', { required: true })

const videoRef = ref<HTMLVideoElement | null>(null)

const isPlaying = ref(false)
const controller = new AbortController()
const controls = computed(() => {
  return layer.value.controls ?? defaultControls
})
// 尝试播放
function addEventTryPlay() {
  ['touchstart', 'click', 'WeixinJSBridgeReady'].forEach((event) => {
    document.addEventListener(event, () => {
      videoRef.value?.load()
      videoRef.value?.play().catch(() => {})
    }, { signal: controller.signal })
  })
  // setTimeout(() => {
  //   if (videoRef.value && !isPlaying.value) {
  //     videoRef.value.poster = new URL('./assets/poster.png', import.meta.url).href
  //   }
  // }, 1000)
}

async function tryPlay() {
  if (isPlaying.value) return
  try {
    videoRef.value?.load()
    await videoRef.value?.play()
    const paused = videoRef.value?.paused
    if (paused) {
      addEventTryPlay()
    }
  } catch {
    addEventTryPlay()
  }
}

function playSuccess() {
  isPlaying.value = true
  controller.abort()
  if (videoRef.value) {
    videoRef.value.removeAttribute('poster')
  }
}

function togglePlay() {
  if (layer.value.autoplay) return
  if (isPlaying.value) {
    videoRef.value?.pause()
  } else {
    videoRef.value?.play()
  }
  isPlaying.value = !isPlaying.value
}
// const videoRefSize = useElementSize(videoRef, undefined, { box: 'border-box' })
// function getFirstImg(): Promise<string> {
//   return new Promise<string>((resolve, reject) => {
//     // 创建video和canvas元素
//     const video = document.createElement('video')
//     const canvas = document.createElement('canvas')
//     const context = canvas.getContext('2d')

//     video.src = layer.value.data
//     video.width = videoRefSize.width.value
//     video.height = videoRefSize.height.value
//     video.setAttribute('display', 'none')
//     video.preload = 'auto'
//     video.muted = true // 静音播放
//     video.crossOrigin = 'anonymous'

//     video.addEventListener('canplay', () => {
//       canvas.width = video.videoWidth
//       canvas.height = video.videoHeight
//       if (!context) {
//         reject(new Error('Canvas上下文获取失败'))
//         return
//       }
//       context.drawImage(video, 0, 0, canvas.width, canvas.height)
//       const imageDataURL = canvas.toDataURL('image/png')
//       resolve(imageDataURL)
//     })
//   })
// }
// async function setPoster() {
//   try {
//     const imageDataURL = await getFirstImg()
//     if (videoRef.value) {
//       videoRef.value.poster = imageDataURL
//     }
//   } catch (error) {
//     if (videoRef.value) {
//       videoRef.value.poster = new URL('./assets/poster.png', import.meta.url).href
//     }
//     console.error('获取视频第一帧失败:', error)
//   }
// }
onMounted(async () => {
  // setPoster() //不设置封面图即可自动获取第一帧，先保留获取第一帧的代码
  if (layer.value.autoplay) {
    await nextTick()
    tryPlay()
  }
})
</script>

<template>
  <video
    :id="layer.uuid"
    ref="videoRef"
    :controls="controls"
    :autoplay="layer.autoplay"
    :loop="layer.loop"
    :muted="layer.muted"
    :src="layer.data"
    :style="layer.style"
    class="design-video"
    crossOrigin="anonymous"
    disablepictureinpicture
    playsinline
    preload="auto"
    webkit-playsinline
    x-webkit-airplay
    x5-playsinline
    x5-video-player-type="h5-page"
    @click="togglePlay"
    @play="playSuccess"
  />
</template>

<style scoped lang="scss">
.design-video {
  width: 100% !important;
  height: 100% !important;
  object-fit: fill;
}
</style>
