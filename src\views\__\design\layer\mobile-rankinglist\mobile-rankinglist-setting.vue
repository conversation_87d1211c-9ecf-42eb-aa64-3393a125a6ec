<script setup lang="ts">
import MaterialThumbnail from '../../components/material-thumbnail.vue'
import { getDefaultMaterial, openSelectMaterial, useDataAttr } from '../../index'
import { defaultData, type IDesignMobileRankinglist } from './mobile-rankinglist'

const layer = defineModel<IDesignMobileRankinglist>('layer', { required: true })

const unitBind = useDataAttr(layer.value.data, 'unit', defaultData.unit)
const topOneIconImgBind = useDataAttr(layer.value.data, 'topOneIconImg', defaultData.topOneIconImg)
const topTwoIconImgBind = useDataAttr(layer.value.data, 'topTwoIconImg', defaultData.topTwoIconImg)
const topThreeIconImgBind = useDataAttr(layer.value.data, 'topThreeIconImg', defaultData.topThreeIconImg)
const topOtherIconImgBind = useDataAttr(layer.value.data, 'topOtherIconImg', defaultData.topOtherIconImg)
const titleImgBind = useDataAttr(layer.value.data, 'titleImg', defaultData.titleImg)
const bgImgBind = useDataAttr(layer.value.data, 'bgImg', defaultData.bgImg)
const closeImgBind = useDataAttr(layer.value.data, 'closeImg', defaultData.closeImg)
const rankingColorBind = useDataAttr(layer.value.data, 'rankingColor', defaultData.rankingColor || '')
const itemRankingColorBind = useDataAttr(layer.value.data, 'itemRankingColor', defaultData.itemRankingColor || '')
const borderColorBind = useDataAttr(layer.value.data, 'borderColor', defaultData.borderColor || '')
const nameColorBind = useDataAttr(layer.value.data, 'nameColor', defaultData.nameColor || '')
const currentNameColorBind = useDataAttr(layer.value.data, 'currentNameColor', defaultData.currentNameColor || '')
const avatarSizeBind = useDataAttr(layer.value.data, 'avatarSize', defaultData.avatarSize)
const avatarRadiusBind = useDataAttr(layer.value.data, 'avatarRadius', defaultData.avatarRadius)

async function updateMaterialFn(dataKey: Exclude<keyof typeof layer.value.data, 'avatarSize' | 'avatarRadius'>, isReset: boolean = false) {
  if (!dataKey) {
    return
  }
  const result = isReset
    ? await getDefaultMaterial(layer.value, `data.${dataKey}`)
    : await openSelectMaterial('PIC')
  if (result) {
    layer.value.data[dataKey] = result
  } else if (isReset) {
    delete layer.value.data[dataKey]
  }
}
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <div class="setting-item-label">成绩单位</div>
        <div class="setting-item-content">
          <el-input
            v-model="unitBind"
            class="w-124px"
            maxlenght="2"
            :input-style="{
              textAlign: 'right',
              paddingRight: '10px',
            }"
            type="text"
          />
        </div>
      </div>
      <div class="setting-item">
        <div class="setting-item-label">第一名图标</div>
        <div class="setting-item-content">
          <div class="thumbnail-box bgblank">
            <MaterialThumbnail @select="updateMaterialFn('topOneIconImg')" @reset="updateMaterialFn('topOneIconImg', true)">
              <img v-if="topOneIconImgBind" :src="topOneIconImgBind" />
            </MaterialThumbnail>
          </div>
        </div>
      </div>
      <div class="setting-item">
        <div cla1ss="setting-item-label">第二名图标</div>
        <div class="setting-item-content">
          <div class="thumbnail-box bgblank">
            <div class="bg">
              <MaterialThumbnail @select="updateMaterialFn('topTwoIconImg')" @reset="updateMaterialFn('topTwoIconImg', true)">
                <img v-if="topTwoIconImgBind" :src="topTwoIconImgBind" />
              </MaterialThumbnail>
            </div>
          </div>
        </div>
      </div>
      <div class="setting-item">
        <div class="setting-item-label">第三名图标</div>
        <div class="setting-item-content">
          <div class="thumbnail-box bgblank">
            <MaterialThumbnail @select="updateMaterialFn('topThreeIconImg')" @reset="updateMaterialFn('topThreeIconImg', true)">
              <img v-if="topThreeIconImgBind" :src="topThreeIconImgBind" />
            </MaterialThumbnail>
          </div>
        </div>
      </div>
      <div class="setting-item">
        <div class="setting-item-label">后续背景图标</div>
        <div class="setting-item-content">
          <div class="thumbnail-box bgblank">
            <MaterialThumbnail @select="updateMaterialFn('topOtherIconImg')" @reset="updateMaterialFn('topOtherIconImg', true)">
              <img v-if="topOtherIconImgBind" :src="topOtherIconImgBind" />
            </MaterialThumbnail>
          </div>
        </div>
      </div>
      <div class="setting-item">
        <div class="setting-item-label">标题图标</div>
        <div class="setting-item-content">
          <div class="thumbnail-box bgblank">
            <MaterialThumbnail @select="updateMaterialFn('titleImg')" @reset="updateMaterialFn('titleImg', true)">
              <img v-if="titleImgBind" :src="titleImgBind" />
            </MaterialThumbnail>
          </div>
        </div>
      </div>
      <div class="setting-item">
        <div class="setting-item-label">背景图</div>
        <div class="setting-item-content">
          <div class="thumbnail-box bgblank">
            <MaterialThumbnail @select="updateMaterialFn('bgImg')" @reset="updateMaterialFn('bgImg', true)">
              <img v-if="bgImgBind" :src="bgImgBind" />
            </MaterialThumbnail>
          </div>
        </div>
      </div>
      <div class="setting-item">
        <div class="setting-item-label">关闭图标</div>
        <div class="setting-item-content">
          <div class="thumbnail-box bgblank">
            <MaterialThumbnail @select="updateMaterialFn('closeImg')" @reset="updateMaterialFn('closeImg', true)">
              <img v-if="closeImgBind" :src="closeImgBind" />
            </MaterialThumbnail>
          </div>
        </div>
      </div>
      <div class="setting-item">
        <div class="setting-item-label">自己名次颜色</div>
        <div class="setting-item-content">
          <hi-color v-model="rankingColorBind" />
        </div>
      </div>
      <div class="setting-item">
        <div class="setting-item-label">列表名次颜色</div>
        <div class="setting-item-content">
          <hi-color v-model="itemRankingColorBind" />
        </div>
      </div>
      <div class="setting-item">
        <div class="setting-item-label">分割线颜色</div>
        <div class="setting-item-content">
          <hi-color v-model="borderColorBind" />
        </div>
      </div>
      <div class="setting-item">
        <div class="setting-item-label">排行颜色</div>
        <div class="setting-item-content">
          <hi-color v-model="nameColorBind" />
        </div>
      </div>
      <div class="setting-item">
        <div class="setting-item-label">自己排行颜色</div>
        <div class="setting-item-content">
          <hi-color v-model="currentNameColorBind" />
        </div>
      </div>
      <div class="setting-item">
        <span>头像大小</span>
        <el-slider v-model="avatarSizeBind" :min="20" :max="40" :step="1" class="mx-20 flex-1" />
        <span class="w-30 text-right">{{ avatarSizeBind }}</span>
      </div>
      <div class="setting-item">
        <span>头像圆角</span>
        <el-slider v-model="avatarRadiusBind" :min="0" :max="50" :step="1" class="mx-20 flex-1" />
        <span class="w-30 text-right">{{ avatarRadiusBind }}</span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.setting-wrap {
  padding: 10px 0;
}
.thumbnail-box {
  width: 100px;
  height: 100px;
  outline: 1px solid #e6ebed;
  border-radius: 4px;
  position: relative;

  img {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    object-fit: contain;
  }
}
</style>
