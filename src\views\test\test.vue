<script setup lang="ts">
definePage({ meta: { label: '测试' } })

class SVGColorParser {
  private svgElement: HTMLElement

  constructor(svgString: string) {
    const parser = new DOMParser()
    const doc = parser.parseFromString(svgString, 'image/svg+xml')
    if (doc.documentElement.nodeName === 'parsererror') {
      throw new Error('Failed to parse SVG string')
    }
    this.svgElement = doc.documentElement
  }

  // 判断是否为有效的颜色值
  private isValidColor(value: string): boolean {
    return !value.startsWith('url(')
  }

  // 获取所有表示颜色的属性值
  getColorAttributes(): { [key: string]: string } {
    const colorAttributes: { [key: string]: string } = {}
    const allElements = this.svgElement.getElementsByTagName('*')

    for (let i = 0; i < allElements.length; i++) {
      const element = allElements[i]

      // 颜色种类
      // #fff  #ffffff  #fffa #ffffffaa
      // rgb()
      // rgba()
      // 颜色名( red blue green ...)
      // hsl
      // hsla
      // hsv
      // hsva

      // 内联样式匹配目前只能通过正则表达式进行，全局临时存储原svg dom, 每次颜色替换都是在原svg dom上进行

      // 查看节点的属性值
      for (const attr of ['fill', 'stroke', 'stop-color']) {
        if (element.hasAttribute(attr)) {
          const value = element.getAttribute(attr)
          if (value && this.isValidColor(value)) {
            const key = `${element.tagName}-${attr}-${i}`
            // 如果有内联，内联应该优先级高
            colorAttributes[key] = value
          }
        }
      }

      // 查看节点属性内联样式

      // 查询到所有的内联样式

      // colorProps.forEach((prop) => {
      //   if (element.hasAttribute(prop)) {
      //     const value = element.getAttribute(prop)
      //     if (value && this.isValidColor(value)) {
      //       const key = `${element.tagName}-${prop}-${i}`
      //       colorAttributes[key] = value
      //     }
      //   }
      // })

      // 处理渐变色
      if (element.tagName === 'linearGradient' || element.tagName === 'radialGradient') {
        const stops = element.getElementsByTagName('stop')
        for (let j = 0; j < stops.length; j++) {
          const stop = stops[j]
          if (stop.hasAttribute('stop-color')) {
            const value = stop.getAttribute('stop-color')
            if (value && this.isValidColor(value)) {
              const key = `${element.tagName}-stop-${j}-stop-color-${i}`
              colorAttributes[key] = value
            }
          }
        }
      }
    }

    return colorAttributes
  }

  // 修改指定属性的颜色值
  modifyColor(key: string, newColor: string): boolean {
    const parts = key.split('-')
    const tagName = parts[0]
    const prop = parts[parts.length - 2]
    const index = Number.parseInt(parts[parts.length - 1], 10)

    if (tagName === 'linearGradient' || tagName === 'radialGradient') {
      const gradientIndex = Number.parseInt(parts[parts.length - 3], 10)
      const gradients = this.svgElement.getElementsByTagName(tagName)
      if (gradientIndex < gradients.length) {
        const gradient = gradients[gradientIndex]
        const stops = gradient.getElementsByTagName('stop')
        const stopIndex = Number.parseInt(parts[1], 10)
        if (stopIndex < stops.length) {
          const stop = stops[stopIndex]
          if (prop === 'stop-color') {
            stop.setAttribute('stop-color', newColor)
            return true
          }
        }
      }
    } else {
      const allElements = this.svgElement.getElementsByTagName(tagName)
      if (index < allElements.length) {
        const element = allElements[index]
        if (element.hasAttribute(prop)) {
          element.setAttribute(prop, newColor)
          return true
        }
      }
    }

    return false
  }

  // 获取修改后的 SVG 字符串
  getModifiedSVG(): string {
    const serializer = new XMLSerializer()
    return serializer.serializeToString(this.svgElement)
  }
}

// 使用示例
const svgString = `
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
  viewBox="0 0 340 340">
  <defs>
    <linearGradient>
      <stop offset="0" stop-color="#4b717b" />
      <stop offset="1" stop-color="#3a586e" />
    </linearGradient>
  </defs>

   <!-- 定义线性渐变 -->
  <linearGradient id="myLinearGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:red;stop-opacity:1" />
      <stop offset="100%" style="stop-color:blue;stop-opacity:1" />
  </linearGradient>
  <!-- 使用线性渐变填充矩形 -->
  <rect x="10" y="10" width="180" height="180" fill="url(#myLinearGradient)" />

  <!-- 定义滤镜 -->
  <filter id="lightingFilter">
      <feDiffuseLighting in="SourceGraphic" lighting-color="pink">
          <fePointLight x="50" y="50" z="100" />
      </feDiffuseLighting>
  </filter>
  <!-- 应用滤镜到圆形 -->
  <circle cx="100" cy="100" r="90" filter="url(#lightingFilter)" />

  <circle cx="175.496" cy="92.49" r="3.813" fill="#d57370" />
  <rect style="fill:blue;stroke:pink;stroke-width:5;"/>
  <filter id="shadow_filter">
    <feDropShadow dx="5" dy="5" stdDeviation="5" flood-color="gray" flood-opacity="0.5" />
  </filter>
  <rect x="50" y="50" width="100" height="80" fill="red" filter="url(#shadow_filter)" />

  <style>
    .cls-1 {
      --color: #39394c;
      --color1: red;
      --color2: rgb(244,2,3);
      --color3: rgba(244,2,3,0.5);
      fill: #4b717b;
      stroke: #3a586e;
    }
  </style>
</svg>
`

const parser = new SVGColorParser(svgString)

// 获取所有颜色属性
const colors = parser.getColorAttributes()
console.log('Original colors:', colors, Object.keys(colors).length)

// 修改颜色
const success = parser.modifyColor('linearGradient-stop-1-stop-color-21', 'yellow') // "#39394c"

if (success) {
  console.log('Color modified successfully')
} else {
  console.log('Failed to modify color')
}

// 获取修改后的 SVG 字符串
const modifiedSVG = parser.getModifiedSVG()
console.log('Modified SVG:', modifiedSVG.includes('yellow'))
</script>

<template>
  <div></div>
</template>

<style scoped lang="scss">
</style>
