<script setup lang="ts">
import { defineCustomEmits, injectScale, useDesignState } from '../..'
import { defaultColor, defaultPadding, defaultShowBorder, defaultShowTh, type IDesignIngTablerank } from './tablerank'

const layer = defineModel<IDesignIngTablerank>('layer', { required: true })
const customEmits = defineCustomEmits(layer)

const designState = useDesignState()
const scale = injectScale()

const domRef = ref<HTMLElement>()
const domTableBoxRef = ref<HTMLElement>()

const showTh = computed(() => layer.value.showTh ?? defaultShowTh)
const showBorder = computed(() => layer.value.showBorder ?? defaultShowBorder)

const blindData = computed(() => {
  const defaultData = { title: [], data: [] }
  if (!layer.value.dataSource) {
    return defaultData
  }
  const data = designState.getLayerData('allTableStatistics') || {}
  return data[layer.value.dataSource]
})

const titleData = computed(() => {
  return blindData.value?.title || []
})
const tableKeys = computed(() => {
  // console.log('tableKeys', titleData.value.map((i: { key: string }) => i.key))
  return titleData.value.map((i: { key: string }) => i.key)
})
const tableData = computed(() => {
  return blindData.value?.data || []
})
const contentStyle = computed(() => {
  return layer.value.contentStyle
})

const defaultAvatar = new URL('./img/head-default.png', import.meta.url).href
watch(
  () => layer.value.dataSource,
  (c) => {
    customEmits('dataSourceBlind', c)
  },
  { immediate: true },
)

const tebleRankStyle = computed(() => {
  const paddingLeft = layer.value.paddingLeft ?? defaultPadding[1]
  const paddingRight = layer.value.paddingRight ?? defaultPadding[3]
  const paddingTop = layer.value.paddingTop ?? defaultPadding[2]
  const paddingBottom = layer.value.paddingBottom ?? defaultPadding[0]
  const style = {
    padding: `${paddingTop}px ${paddingRight}px ${paddingBottom}px ${paddingLeft}px`,
  }
  return style
})
const wapStyle = computed(() => {
  const style = {
    '--color': layer.value.style.color || defaultColor,
    '--title-color': layer.value.titleFontColor,
    '--title-font-size': `${layer.value.titleFontSize * scale.value}px`,
  }
  return style
})

const avatarStyle = computed(() => {
  if (layer.value.avatarSize) {
    return {
      width: `${layer.value.avatarSize * scale.value}px`,
      height: `${layer.value.avatarSize * scale.value}px`,
    }
  }
  return {}
})

const clomStyle = computed(() => {
  return (index: number) => {
    const item = contentStyle.value[index]
    if (!item) {
      return {}
    }
    const style = {
      fontSize: `${item.fontSize * scale.value}px`,
      color: item.fontColor,
      fontWeight: item.fontBold ? 'bold' : 'normal',
      textAlign: item.align,
      width: `${item.width}%`,
    }
    return style
  }
})

const titleClomStyle = computed(() => {
  return (index: number) => {
    const item = contentStyle.value[index]
    if (!item) {
      return {}
    }
    const style = {
      textAlign: item.align,
      width: `${item.width}%`,
    }
    return style
  }
})
</script>

<template>
  <div ref="domRef" class="table-rank-box" :style="tebleRankStyle">
    <div class="wrap" :style="wapStyle" :class="[{ border: showBorder }]">
      <!-- <img v-if="layer.bgTop" class="bgimg bgtop" :src="layer.bgTop" alt=""> -->
      <table ref="domTableBoxRef" class="table-box" align="center">
        <thead class="fixed-header">
          <tr v-if="showTh" class="tr-box title-cell">
            <th v-for="(i, index) in titleData" :key="i" :style="titleClomStyle(index)">
              <div class="title-item">
                {{ i.unit ? `${i.name}${`（${i.unit}）`} ` : i.name }}
              </div>
            </th>
          </tr>
        </thead>
        <tbody name="show" align="center">
          <tr v-for="(item, i) in tableData" :key="item.id" tag="tr" class="tr-box content-cell">
            <td v-for="(key, index) in tableKeys" :key="key" :style="clomStyle(index)">
              <div v-if="key === 'wx_user_id'" class="header">
                <img :style="avatarStyle" :src="item?.avatar || defaultAvatar" class="avatar-item" :class="[`item-${index}`]">
              </div>
              <div v-else class="text">{{ key === 'id' ? i + 1 : item[key] }}</div>
            </td>
          </tr>
        </tbody>
      </table>
      <!-- <img v-if="layer.bgBottom" class="bgimg bgtottom" :src="layer.bgBottom" alt=""> -->
    </div>
  </div>
</template>

<style scoped lang="scss">
.table-rank-box {
  width: 100%;
  height: 100%;
  background-position:
    top center,
    bottom center;
  background-repeat: no-repeat;
  background-size: contain, contain;
  .wrap {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: auto;
  }
  .table-box {
    background-repeat: repeat-y;
    background-size: 100%;
    width: 100%;
    min-height: 100%;
    color: var(--color);
  }
  .title-cell {
    font-size: var(--title-font-size);
    color: var(--title-color);
    .title-item {
      padding: 8px 0;
    }
  }
  .content-cell {
    font-size: 14px;
    color: #000;
  }
  .tr-box {
    width: 100%;
  }
  .num {
    width: calc(var(--content-font-size) * 1.5);
    height: calc(var(--content-font-size) * 1.5);
    background: #ecbe7b;
    border-radius: 50%;
    text-align: center;
    line-height: calc(var(--content-font-size) * 1.5);
    font-size: var(--content-font-size);
    color: #fff;
  }
  .header {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .avatar-item {
      width: 35px;
      height: 35px;
      border-radius: 50%;
    }
  }

  .nickname {
    width: 40%;
    flex: 1;
    font-size: 12px;
    flex-shrink: 0;
  }

  .bgimg {
    width: 100%;
    object-fit: contain;
  }
  .bgtottom {
    object-position: top center;
  }

  .border {
    table,
    th,
    td {
      border: 1px solid black;
      border-collapse: collapse; /* 移除单元格之间的间隔 */
    }
  }
  .fixed-header {
    height: min-content;
    line-height: 1.5;
  }
}
</style>
