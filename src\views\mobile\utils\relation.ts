import { useMobileStore } from '../stores/useMobileStore'
import { useParticipantStore } from '../stores/useParticipantStore'

// 获取用户昵称
const mobileStore = useMobileStore()
const participantStore = useParticipantStore()
export function getWxuserName(wxUserId: number) {
  if (mobileStore.isSignName) {
    return participantStore.getSignName(wxUserId)
  }
  return participantStore.getWxName(wxUserId)
}

// 获取用户头像
export function getWxUserAvatar(wxUserId: number) {
  return participantStore.getAvatar(wxUserId)
}

// 关联参与者
export async function relationParticipant(list: { wxUserId: number }[]) {
  await participantStore.relationParticipant(list)
}
