<script setup lang="ts">
import type { CSSProperties } from 'vue'
import { useDesignData } from '.'

const designData = useDesignData()

const backgroundColorStyle = computed<CSSProperties>(() => {
  return {
    background: designData.background?.color,
  }
})

const backgrundImageStyle = computed<CSSProperties>(() => {
  const style = {}
  if (designData.background?.image) {
    const type = designData.background?.type || 'background'
    const size = designData.background?.size || 0.15
    Object.assign(style, {
      [`${type}Image`]: `url(${designData.background.image})`,
      [`${type}Size`]: size === 1 ? 'cover' : `${(size * 100).toFixed()}%`,
      [`${type}Position`]: 'center',
      backgroundColor: designData.background.bgColor || 'rgba(0,0,0,1)',
    })
    if (type === 'background') {
      const showType = designData.background?.showType || 'repeat'
      if (showType !== 'repeat') {
        if (showType === 'fill') {
          Object.assign(style, { [`${type}Size`]: '100% 100%' })
        } else {
          Object.assign(style, { [`${type}Size`]: showType })
        }
      }
    }
  }
  return style
})

const backgrundBlurStyle = computed<CSSProperties>(() => {
  const { blur } = designData.background || {}
  return {
    position: 'absolute',
    top: 0,
    left: 0,
    zIndex: 100,
    width: '100%',
    height: '100%',
    backdropFilter: `blur(${blur}px)`,
  }
})
</script>

<template>
  <div class="bg-box">
    <div v-if="designData.background?.color" :style="backgroundColorStyle"></div>
    <div v-if="designData.background?.image" :style="backgrundImageStyle"></div>
    <div v-if="designData.background?.blur" :style="backgrundBlurStyle"></div>
  </div>
</template>

<style scoped lang="scss">
.bg-box {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
  > div {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }
}
</style>
