<script setup lang="ts">
import { getDefaultMaterial, openSelectMaterial, useDataAttr } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'
import { DEFAULT_DATA, type IDesignMobileAnswerraceIng } from './mobile-answerrace-ing'
import { ingMockConfig } from './mock'

const layer = defineModel<IDesignMobileAnswerraceIng>('layer', { required: true })

async function updateMaterialFn(name: 'submitButtonImage' | 'progressDecorationImage', isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, `${name}`)
    : await openSelectMaterial('PIC')
  if (result) {
    layer.value.data[name] = result
  } else if (isReset) {
    delete layer.value.data[name]
  }
}

// 进度
const progressQuestionColorBind = useDataAttr(layer.value.data, 'progressQuestionColor', DEFAULT_DATA.progressQuestionColor)
const progressBarDefaultColorBind = useDataAttr(layer.value.data, 'progressBarDefaultColor', DEFAULT_DATA.progressBarDefaultColor)
const progressBarFilledColorBind = useDataAttr(layer.value.data, 'progressBarFilledColor', DEFAULT_DATA.progressBarFilledColor)
const progressDecorationImageBind = useDataAttr(layer.value.data, 'progressDecorationImage', DEFAULT_DATA.progressDecorationImage)
const progressTextColorBind = useDataAttr(layer.value.data, 'progressTextColor', DEFAULT_DATA.progressTextColor)

// 题目
const questionFontSizeBind = useDataAttr(layer.value.data, 'questionFontSize', DEFAULT_DATA.questionFontSize)
const questionColorBind = useDataAttr(layer.value.data, 'questionColor', DEFAULT_DATA.questionColor)

// 选项
const optionSelectedColorBind = useDataAttr(layer.value.data, 'optionSelectedColor', DEFAULT_DATA.optionSelectedColor)
const optionSelectedBackgroundBind = useDataAttr(layer.value.data, 'optionSelectedBackground', DEFAULT_DATA.optionSelectedBackground)
const optionSelectedBorderColorBind = useDataAttr(layer.value.data, 'optionSelectedBorderColor', DEFAULT_DATA.optionSelectedBorderColor)

// 回答错误
const optionErrorColorBind = useDataAttr(layer.value.data, 'optionErrorColor', DEFAULT_DATA.optionErrorColor)
const optionErrorBackgroundBind = useDataAttr(layer.value.data, 'optionErrorBackground', DEFAULT_DATA.optionErrorBackground)
const optionErrorBorderColorBind = useDataAttr(layer.value.data, 'optionErrorBorderColor', DEFAULT_DATA.optionErrorBorderColor)

const optionUnselectedColorBind = useDataAttr(layer.value.data, 'optionUnselectedColor', DEFAULT_DATA.optionUnselectedColor)
const optionUnselectedBackgroundBind = useDataAttr(layer.value.data, 'optionUnselectedBackground', DEFAULT_DATA.optionUnselectedBackground)
const optionUnselectedBorderColorBind = useDataAttr(layer.value.data, 'optionUnselectedBorderColor', DEFAULT_DATA.optionUnselectedBorderColor)

// 其他
const answerStatusColorBind = useDataAttr(layer.value.data, 'answerStatusColor', DEFAULT_DATA.answerStatusColor)
const eliminatedTextBind = useDataAttr(layer.value.data, 'eliminatedText', DEFAULT_DATA.eliminatedText)
const answerCorrectTextBind = useDataAttr(layer.value.data, 'answerCorrectText', DEFAULT_DATA.answerCorrectText)
const annotationColorBind = useDataAttr(layer.value.data, 'annotationColor', DEFAULT_DATA.annotationColor)
const submitButtonImageBind = useDataAttr(layer.value.data, 'submitButtonImage', DEFAULT_DATA.submitButtonImage)

// mock
const USER_ANSWER_STATUS = [
  { label: '回答正确', value: 'correct' },
  { label: '回答错误', value: 'incorrect' },
  { label: '被淘汰', value: 'out' },
]

const QUESTION_FLOW_STATUS = [
  { label: '321 倒计时', value: '321' },
  { label: '看题', value: 'preview' },
  { label: '答题', value: 'answer' },
  { label: '答案展示', value: 'result' },
]

const QUESTION_TYPE = [
  { label: '文字', value: 'TEXT' },
  { label: '图文', value: 'IMAGE' },
  { label: '视频', value: 'VIDEO' },
  { label: '音频', value: 'AUDIO' },
]

const OPTION_TYPE = [
  { label: '文字', value: 'TEXT' },
  { label: '混合', value: 'IMAGE_TEXT' },
]
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <h2>预览切换</h2>

      <!-- 答题流程状态 -->
      <div class="setting-item">
        <h3>流程状态</h3>
        <el-select v-model="ingMockConfig.questionFlowStatus" placeholder="请选择流程状态" style="width: 140px">
          <el-option v-for="opt in QUESTION_FLOW_STATUS" :key="opt.value" v-bind="opt" />
        </el-select>
      </div>

      <!-- 用户答题状态 -->
      <div v-if="ingMockConfig.questionFlowStatus === 'result'" class="setting-item">
        <h3>用户答题状态</h3>
        <el-select v-model="ingMockConfig.userAnswerStatus" placeholder="请选择答题状态" style="width: 140px">
          <el-option v-for="opt in USER_ANSWER_STATUS" :key="opt.value" v-bind="opt" />
        </el-select>
      </div>

      <!-- 题目类型 -->
      <div class="setting-item">
        <h3>题目类型</h3>
        <el-select v-model="ingMockConfig.questionType" placeholder="请选择题目类型" style="width: 140px">
          <el-option v-for="opt in QUESTION_TYPE" :key="opt.value" v-bind="opt" />
        </el-select>
      </div>

      <div class="setting-item">
        <h3>答案类型</h3>
        <el-select v-model="ingMockConfig.optionType" placeholder="请选择答案类型" style="width: 140px">
          <el-option v-for="opt in OPTION_TYPE" :key="opt.value" v-bind="opt" />
        </el-select>
      </div>

      <el-divider />

      <h2>答题进度</h2>

      <div class="setting-item">
        <h3>第x题颜色</h3>
        <hi-color v-model="progressQuestionColorBind" />
      </div>

      <div class="setting-item">
        <h3>进度默认颜色</h3>
        <hi-color v-model="progressBarDefaultColorBind" />
      </div>

      <div class="setting-item">
        <h3>进度填充颜色</h3>
        <hi-color v-model="progressBarFilledColorBind" />
      </div>

      <div class="setting-item">
        <h3>进度装饰图</h3>
        <div class="flex items-end">
          <div class="bgblank relative h-60 w-60">
            <MaterialThumbnail @select="updateMaterialFn('progressDecorationImage')" @reset="updateMaterialFn('progressDecorationImage', true)">
              <img v-if="progressDecorationImageBind" :src="progressDecorationImageBind" alt="" class="h-40 w-40 object-cover" />
            </MaterialThumbnail>
          </div>
        </div>
      </div>

      <div class="setting-item">
        <h3>答题进度颜色</h3>
        <hi-color v-model="progressTextColorBind" />
      </div>

      <el-divider />

      <h2>题目效果</h2>

      <div class="setting-item">
        <h3>题目字号</h3>
        <el-input-number v-model="questionFontSizeBind" v-input-number size="small" controls-position="right" class="control-input" :max="30" :min="10" />
      </div>

      <div class="setting-item">
        <h3>题目颜色</h3>
        <hi-color v-model="questionColorBind" />
      </div>

      <el-divider />

      <h2>选项效果</h2>

      <div class="setting-item">
        <h3>选中/正确颜色</h3>
        <hi-color v-model="optionSelectedColorBind" />
      </div>

      <div class="setting-item">
        <h3>选中/正确背景色</h3>
        <hi-color v-model="optionSelectedBackgroundBind" />
      </div>

      <div class="setting-item">
        <h3>选中/正确边框色</h3>
        <hi-color v-model="optionSelectedBorderColorBind" />
      </div>

      <div class="setting-item">
        <h3>回答错误颜色</h3>
        <hi-color v-model="optionErrorColorBind" />
      </div>

      <div class="setting-item">
        <h3>回答错误背景色</h3>
        <hi-color v-model="optionErrorBackgroundBind" />
      </div>

      <div class="setting-item">
        <h3>回答错误边框色</h3>
        <hi-color v-model="optionErrorBorderColorBind" />
      </div>

      <div class="setting-item">
        <h3>没选中颜色</h3>
        <hi-color v-model="optionUnselectedColorBind" />
      </div>

      <div class="setting-item">
        <h3>没选中背景色</h3>
        <hi-color v-model="optionUnselectedBackgroundBind" />
      </div>

      <div class="setting-item">
        <h3>没选中边框色</h3>
        <hi-color v-model="optionUnselectedBorderColorBind" />
      </div>

      <el-divider />

      <h2>其他配置</h2>

      <div class="setting-item">
        <h3>回答状态颜色</h3>
        <hi-color v-model="answerStatusColorBind" />
      </div>

      <div class="setting-item">
        <h3>被淘汰提示语</h3>
        <el-input v-model="eliminatedTextBind" type="textarea" class="w-120" />
      </div>

      <div class="setting-item">
        <h3>回答正确提示语</h3>
        <el-input v-model="answerCorrectTextBind" type="textarea" class="w-120" />
      </div>

      <div class="setting-item">
        <h3>注释颜色</h3>
        <hi-color v-model="annotationColorBind" />
      </div>

      <div class="setting-item">
        <h3>提交按钮</h3>
        <div class="flex items-end">
          <div class="bgblank relative h-50 w-120">
            <MaterialThumbnail @select="updateMaterialFn('submitButtonImage')" @reset="updateMaterialFn('submitButtonImage', true)">
              <img v-if="submitButtonImageBind" :src="submitButtonImageBind" alt="" class="h-full w-full object-cover" />
            </MaterialThumbnail>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
</style>
