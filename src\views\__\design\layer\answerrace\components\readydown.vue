<script setup lang="ts">
import { envUtils } from '~/src/utils/env'
import { useDesignTemp } from '../../..'

const props = defineProps<{
  color?: string
  shadowColor?: string
  bgImg?: string
  downTime?: number | string
}>()
const timestamp = useTimestamp()
const designTemp = useDesignTemp()

const downRef = ref<HTMLElement>()
const boxSize = useElementSize(downRef)

const boxStyle = computed(() => {
  const color = props.color
  const shadowColor = props.shadowColor || 'rgba(0, 0, 0, 0.3)'

  const fontSize = Math.min(boxSize.width.value, boxSize.height.value) * 0.5

  let textShadow = ''
  for (let i = 1; i <= Math.max(fontSize / 50, 6); i++) {
    textShadow += `,${i}px ${i}px ${shadowColor}`
  }

  return {
    fontSize: `${fontSize}px`,
    color,
    textShadow: textShadow.slice(1),
    backgroundImage: props.bgImg ? `url(${props.bgImg})` : 'none',
  }
})

const data = computed(() => {
  const downFlagTime = Number(props.downTime)
  if (!downFlagTime) return '0'
  // 计算距离的秒数
  const x = downFlagTime - (envUtils.isPlayWright ? Date.now() : timestamp.value)
  if (x < 0) {
    return designTemp.isEdit ? '0' : ''
  }
  if (x > 3000) {
    return '3'
  }
  return Math.ceil(x / 1000)
})
</script>

<template>
  <div ref="downRef" class="design-down-box" :style="boxStyle">
    <Transition>
      <span :key="data">{{ data }}</span>
    </Transition>
  </div>
</template>

<style scoped lang="scss">
.design-down-box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

// 组件内动画
.v-enter-active,
.v-leave-active {
  transition: opacity 0.8s ease;
  position: absolute;
}
.v-enter-from,
.v-leave-to {
  opacity: 0;
}
</style>
