import type { IDesignLayer, IDesignSetup } from '../../types'
import { merge } from 'lodash-es'
import { openSelectMaterial, ResourceUtil } from '../..'

import { layerUuid } from '../../utils'
import CompSetting from './image-setting.vue'
import Comp from './image.vue'

export const type = 'image'

export type IMode = 'none' | 'contain' | 'fill' | 'cover' | 'auto'
export type IRepeat = 'no-repeat' | 'repeat-x' | 'repeat-y' | 'repeat'

export const defaultMode = 'contain'
export const defaultRepeat = 'no-repeat'
export const defaultSize = 100
export const defaultPosition = 50

export interface IDesignImage extends IDesignLayer {
  type: typeof type
  data: string
  mode?: IMode
  repeat?: IRepeat
  sizeX?: number // 百分比, 0 为 auto
  sizeY?: number // 百分比, 0 为 auto
  posX?: number // x 位置百分数
  posY?: number // y 位置百分数
  color?: string // 背景颜色，如果设置了颜色渲染时转为mask的方式，部分属性会失效
  hasQrcode?: boolean // 是否有二维码
  clickPreviewSwitch?: boolean // 点击放大预览
}

export function setup(app: IDesignSetup) {
  app.registry({
    type,
    base: true,
    name: '图片',
    Comp,
    CompSetting,
    async defaultData(options) {
      const data = (options as IDesignImage)?.data || await openSelectMaterial()
      if (!data) return
      const { width, height } = await ResourceUtil.loadImage(data, { draftScale: 0.2 })

      return merge({
        uuid: layerUuid(),
        name: '新图片',
        data,
        type,
        style: {
          width: `${width}px`,
          height: `${height}px`,
        },
        ratio: Number.parseFloat((width / height).toFixed(2)),
      }, options as IDesignImage)
    },
  })
}
