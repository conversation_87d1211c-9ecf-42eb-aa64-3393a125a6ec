import { BisTypes } from '../../'
import { useDesignData, useDesignTemp } from '../../index'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './diglett-ing-setting.vue'
import Comp from './diglett-ing.vue'
// 类型
export const type = 'diglett-ing'

// 数据类型约束
export interface BehitItem {
  score: number
  img_normal: string
  img_yun: string
}
export interface DiglettIngDefaultData {
  holeImg?: string
  hitImg?: string
  behitBtomPos?: number
  scoreColor?: string
  gameMode?: 'hit' | 'pull'
  bownImg?: string
  behitHide?: boolean
  successAudio?: string
  failAudio?: string
}
export interface IDesignDiglettIng extends DiglettIngDefaultData, IDesignLayer {
  type: typeof type
  beHitList: BehitItem[]
  hitShake: boolean
  data: any
}
export const defaultData: DiglettIngDefaultData = {
  holeImg: new URL('./assets/dong.png', import.meta.url).href,
  hitImg: new URL('./assets/cuizi.png', import.meta.url).href,
  behitBtomPos: 30,
  scoreColor: '#FFFF00',
  gameMode: 'hit',
  bownImg: new URL('./assets/bown.png', import.meta.url).href,
  behitHide: false,
  successAudio: new URL('./assets/success.mp3', import.meta.url).href,
  failAudio: new URL('./assets/fail.mp3', import.meta.url).href,
}
// 注册组件
export function setup(app: IDesignSetup) {
  const designTemp = useDesignTemp()
  const isMobile = computed(() => designTemp.showType === 'mobile')

  const designData = useDesignData()
  app.registry({
    bisType: BisTypes.sportsIng,
    showType: ['pcwall', 'mobile'],
    type,
    status: ['ready', 'ing'],
    name: '敲敲乐进行中',
    thumbnail: new URL('./diglett-ing.png', import.meta.url).href,
    showInteractive: [InteractiveEnum.diglettv3],
    Comp,
    CompSetting,
    defaultData() {
      return {
        uuid: layerUuid(),
        name: '敲敲乐进行中',
        type,
        hitShake: true,
        beHitList: [
          {
            score: 1,
            img_normal: new URL('./assets/item2.png', import.meta.url).href,
            img_yun: new URL('./assets/item2_yun.png', import.meta.url).href,
          },
          {
            score: 3,
            img_normal: new URL('./assets/item1.png', import.meta.url).href,
            img_yun: new URL('./assets/item1_yun.png', import.meta.url).href,
          },
        ],
        data: [],
        style: {
          width: isMobile.value ? '100%' : `${designData.option.drafts[0] * 0.8}px`,
          height: isMobile.value ? '80%' : `${designData.option.drafts[1] * 0.8}px`,
          left: isMobile.value ? '0' : `${designData.option.drafts[0] * 0.1}px`,
          top: isMobile.value ? '10%' : `${designData.option.drafts[1] * 0.1}px`,
        },
      }
    },
  })
}
