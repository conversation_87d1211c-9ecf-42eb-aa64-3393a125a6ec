import type { IDesignLayer, IDesignSetup } from '../../types'
import { merge } from 'lodash-es'
import { layerUuid } from '../../utils'
import CompSetting from './text-rich-setting.vue'
import Comp from './text-rich.vue'

// 类型
export const type = 'text-rich'

// 数据类型约束
export interface IDesignTextRich extends IDesignLayer {
  type: typeof type
  data: string
}

export const defaultData = {
  color: '#000',
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    type,
    base: true,
    name: '富文本',
    Comp,
    CompSetting,
    defaultData(options) {
      const text = (options as IDesignTextRich)?.data
      const data = text || `<p class="ql-align-center"><span style="font-size: 16px;color:black;">双击编辑文本</span></p>`
      return merge({
        uuid: layerUuid(),
        name: '富文本',
        type,
        data,
        style: {
          width: '375px',
          height: '50px',
          display: 'flex',
          alignItems: 'center',
        },
      }, options as IDesignTextRich)
    },
  })
}
