import { expect, test } from '@playwright/test'
import { freezePageForSnapshot } from './freezePageForSnapshot'

function getThemeUrl(id: number): string {
  // 1-33 shakev3,3和6不是我们维护的?
  // 34-41 diglettv3
  // 42-47 goldcoinv3
  // 48-62 moneyv3
  // 63-70 firev3
  // 71 performancev3
  // 72-104 microsite
  // 105-112 lotteryv3
  if (id <= 33) {
    return `/shakev3?themeId=${id}`
  }
  if (id <= 41) {
    return `/diglettv3?themeId=${id}`
  }
  if (id <= 47) {
    return `/goldcoinv3?themeId=${id}`
  }
  if (id <= 62) {
    return `/moneyv3?themeId=${id}`
  }
  if (id <= 70) {
    return `/firev3?themeId=${id}`
  }
  if (id <= 71) {
    return `/performancev3?themeId=${id}`
  }
  if (id <= 104) {
    return `/microsite?themeId=${id}`
  }
  if (id <= 112) {
    return `/lotteryv3?themeId=${id}`
  }
  return ''
}

let page: any // 全局 page 变量
let context: any

test.beforeAll(async ({ browser }) => {
  context = await browser.newContext()
  page = await context.newPage()

  await page.goto('https://test.hixianchang.com:5173/next/manage/user/login')
  await page.fill('#username', '18339821272')
  await page.fill('#password', 'test123456')
  await page.click('button:has-text("登录")')
  await page.waitForNavigation()
})

test.afterAll(async () => {
  await context.close()
})

const 起始主题ID = 1
const 结束主题ID = 112

for (let id = 起始主题ID; id <= 结束主题ID; id++) {
  test(`截图主题 ${id}`, async () => {
    const url = `https://test.hixianchang.com:5173/next/manage/wall${getThemeUrl(id)}`
    console.log(`访问主题 ${id}: ${url}`)

    // 添加全局变量，用于判断是否是 Playwright 浏览器
    await page.addInitScript(() => {
      // @ts-ignore
      window.__IS_PLAYWRIGHT__ = true
      // @ts-ignore
      console.log('[Playwright Flag]', window.__IS_PLAYWRIGHT__)
    })

    await page.goto(url)
    await page.waitForSelector('.design-page', { timeout: 5000 }) // 渲染区域出现在页面上
    await page.waitForLoadState('networkidle')

    // 获取 viewModes
    const viewModes = await page.$$eval('.toggle-name', els =>
      els.map(el => ({
        text: el.textContent?.trim() || '',
        selector: `[data-view="${el.getAttribute('data-view')}"]`,
      })))

    const effectiveViewModes = viewModes.length > 0
      ? viewModes
      : [{ text: '默认视图', selector: null }] // 没有视图按钮，直接处理当前状态

    for (const view of effectiveViewModes) {
      if (view.selector) {
        console.log(`切换视图：${view.text}`)
        await page.click(view.selector)
        await page.waitForLoadState('networkidle')
      }

      // 获取状态按钮
      const states = await page.$$eval('.status-btn', els =>
        els.map(el => ({
          text: el.textContent?.trim() || '',
          selector: `[data-status="${el.getAttribute('data-status')}"]`,
        })))

      // TODO 可能还需要切换不同模式（竞技？），截图数量又翻倍

      for (const state of states) {
        console.log(`状态：${state.text}`)
        await page.click(state.selector)
        await page.waitForLoadState('networkidle')
        await freezePageForSnapshot(page)

        await page.waitForTimeout(1500)

        // 截图文件名
        const name = `theme-${id}-${view.text}-${state.text}.png`
        expect(await page.screenshot({
          mask: [
            page.locator('[data-test-mask]'),
          ],
          maskColor: '#ccc',
          animations: 'disabled',
        })).toMatchSnapshot(name, {
          threshold: 0.2, // 容忍像素有轻微差异
        })
      }
    }
  })
}
