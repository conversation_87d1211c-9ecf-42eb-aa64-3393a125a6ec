<script setup lang="ts">
import type { IDesignLotteryIng6 } from './lottery-ing6'
import useThree from '@/views/__/three/useThree'
import BezierEasing from 'bezier-easing'
import gsap from 'gsap'
import { sample, throttle } from 'lodash-es'
import * as THREE from 'three'
import { useDesignState } from '../..'
import { defaultanimateSameSwitch, defaultHeadSize, defaultPlaceHolderHeadImg } from './lottery-ing6'

const layer = defineModel<IDesignLotteryIng6>('layer', { required: true })

const designState = useDesignState()
const status = computed(() => designState.status)
// 参与人奖池
const regeditList = computed(() => {
  return designState.getLayerData('regeditList') || []
})

const wallHeadSize = computed(() => layer.value.headSize ?? defaultHeadSize)
const animateSwitch = computed(() => layer.value.animateSameSwitch ?? defaultanimateSameSwitch)
const maskImg = computed(() => layer.value.maskImg ?? '')

const threeRef = ref<HTMLElement | null>(null)
const { scene, loadTexture, camera } = useThree({
  threeRef,
  hasHelp: false,
  hasControls: false,
})

const cache: Record<string, any> = {}
// 生成纹理
function createTexture(url: string) {
  let texture = cache[url]
  if (texture) {
    return texture
  }
  texture = loadTexture(url)
  cache[url] = texture
  return texture
}

class BaseShape {
  name = 'BaseShape'
  group: THREE.Group
  intervalHandler: NodeJS.Timeout | null = null
  constructor() {
    this.group = new THREE.Group()
    this.group.visible = false
    scene.add(this.group)
  }

  disposeItem(item: THREE.Object3D<THREE.Object3DEventMap>) {
    if (item instanceof THREE.Mesh || item instanceof THREE.Sprite) {
      gsap.killTweensOf(item.scale)
      item.material?.dispose()
      item.geometry?.dispose()
    } else if (item instanceof THREE.Group) {
      item.children.forEach((child) => {
        this.disposeItem(child)
      })
    }
  }

  init() {
    this.intervalHandler && clearInterval(this.intervalHandler)
    this.group.traverse((item) => {
      this.disposeItem(item)
    })
    this.group.clear()
  }

  play(person?: number) {
    console.log('play', person)
  }

  getEase(value = [0.66, 0.2, 0.45, 0.79] as [number, number, number, number]) {
    return BezierEasing(...value)
  }

  destory() {
    this.intervalHandler && clearInterval(this.intervalHandler)
    this.group.children.forEach(child => this.disposeItem(child))
    this.group.clear()
    this.group.visible = false
    if (this instanceof ShapeObj) {
      this.activeTweens.forEach(tween => tween.kill())
      this.activeTweens = []
    }
  }
}

class ShapeObj extends BaseShape {
  initialScale = 1
  minScaleFactor = 0.4
  breathingDurationBase = 3
  maskTexture: THREE.Texture | null = null // 用于存储加载的遮罩纹理
  activeTweens: gsap.core.Tween[] = []

  constructor() {
    super()
    this.name = 'ShapeGalaxy'
  }

  async init() {
    super.init()
    await this.loadMask(maskImg.value)
    // 相机
    camera.fov = 45
    camera.position.y = 0
    camera.position.z = 1000
    camera.updateProjectionMatrix()
    camera.lookAt(scene.position)
    scene.rotation.set(0, 0, 0)
    this.initShapeWall()
  }

  async initShapeWall() {
    const viewSizeAtOrigin = this.getDisplayArea(0)
    // 半径可以基于视口宽度或高度，或者一个固定值，这里用视口宽度
    const n = 1
    const radius = viewSizeAtOrigin.width * 1.6 * n // 调整系数以控制半径
    const targetWallHeight = viewSizeAtOrigin.height * 2 * n// 目标墙体总高度

    const headSize = wallHeadSize.value
    const startAngle = Math.PI / 4// 起始角度
    const endAngle = Math.PI * 3 / 4 // 结束角度
    const rangeAngle = endAngle - startAngle
    // 根据目标高度和头像大小计算行数
    const rowCount = Math.max(1, Math.floor(targetWallHeight / headSize))

    const colCount = Math.floor(rangeAngle * radius / headSize) // 计算列数
    const itemR = rangeAngle / colCount
    const geometry = new THREE.PlaneGeometry(headSize - 10, headSize - 10) // 稍微缩小以留出间隙

    for (let i = 0; i < rowCount; i++) {
      // 调整 Y 坐标计算，使其基于实际墙高居中
      const y = (i - (rowCount - 1) / 2) * headSize
      const centerVector = new THREE.Vector3(0, y, 0)
      for (let j = 0; j < colCount; j++) {
        const z = -radius * Math.sin((j + 0.5) * itemR + startAngle)
        const x = radius * Math.cos((j + 0.5) * itemR + startAngle)
        const itemData = getItem()
        if (!itemData) continue // 使用 continue 而不是 return
        const material = new THREE.MeshBasicMaterial({
          map: createTexture(itemData.avatar),
          fog: false,
          transparent: true,
          alphaMap: this.maskTexture,
        })
        material.onBeforeCompile = (shader) => {
          shader.fragmentShader = shader.fragmentShader.replace(
            '#include <alphamap_fragment>',
            `
          #ifdef USE_ALPHAMAP
              float alpha = texture2D( alphaMap, vAlphaMapUv ).a;
              diffuseColor.a = alpha;
          #endif
        `,
          )
        }
        const threeMesh = new THREE.Mesh(geometry, material)
        threeMesh.position.set(x, y, z)
        threeMesh.lookAt(centerVector)
        this.group.add(threeMesh)
      }
    }
  }

  getDisplayArea(z = 0) {
    const radian = Math.PI / 180
    const distance = Math.abs(camera.position.z - z)
    const height = 2 * Math.tan((camera.fov / 2) * radian) * distance
    const width = camera.aspect * height
    return {
      width,
      height,
    }
  }

  async loadMask(url: string) {
    if (url) {
      try {
        this.maskTexture = await loadTexture(url)
        this.maskTexture.wrapS = THREE.ClampToEdgeWrapping // 根据需要设置纹理环绕方式
        this.maskTexture.wrapT = THREE.ClampToEdgeWrapping
      } catch (error) {
        console.error('Failed to load mask texture:', error)
        this.maskTexture = null // 加载失败则不使用遮罩
      }
    } else {
      this.maskTexture = null
    }
  }

  play() {
    this.intervalHandler && clearInterval(this.intervalHandler)
    if (status.value === 'ready') {
      this.intervalHandler = setInterval(() => {
        this.updateItem()
      }, 100)
    }
    this.activeTweens.forEach(tween => tween.kill())
    this.activeTweens = []

    const breathDur = this.breathingDurationBase
    this.group.children.forEach((object) => {
      if (object instanceof THREE.Mesh) {
        const randomDelay = Math.random() * breathDur * 0.5
        const delay = animateSwitch.value ? 0.5 : randomDelay
        const breathTween = gsap.to(object.scale, {
          x: this.minScaleFactor,
          y: this.minScaleFactor,
          duration: breathDur / 2,
          delay,
          repeat: -1,
          yoyo: true,
          ease: 'sine.inOut',
        })
        this.activeTweens.push(breathTween)
      } else {
        console.log(`Skipping non-mesh object: ${object.type}`)
      }
    })
  }

  updateItem() {
    try {
      const arr = this.group.children.filter((item) => {
        return (item instanceof THREE.Mesh)
      })
      const object = arr[(Math.random() * arr.length) | 0]
      const itemData = getItem()
      if (!itemData) return
      object.material.map = createTexture(itemData.avatar)
    } catch (e) {
      console.log('Error in updateItem:', e)
    }
  }
}

const shapeObj = ref<BaseShape | null>(null)
async function runAnimit() {
  shapeObj.value = new ShapeObj()
  shapeObj.value.group.visible = true
  await shapeObj.value.init()
  shapeObj.value.play()
}

const defaultHeadImg = computed(() => {
  return layer.value.placeHolderHeadImg ?? defaultPlaceHolderHeadImg
})
function getItem() {
  if (regeditList.value.length === 0) {
    return {
      avatar: defaultHeadImg.value,
    }
  }
  const tem = sample(regeditList.value)
  const itemData = Object.assign({}, tem)
  return itemData
}

watch(
  () => [status.value, layer.value.headSize, animateSwitch.value, layer.value.maskImg],
  () => {
    throttle(() => {
      shapeObj.value?.destory()
      runAnimit()
    }, 300, { leading: false })()
  },
  { deep: true, immediate: true },
)

onUnmounted(() => {
  shapeObj.value?.destory()
})
</script>

<template>
  <div class="lottery-ing2-box">
    <div ref="threeRef" class="three-box"></div>
  </div>
</template>

<style scoped lang="scss">
.lottery-ing2-box {
  width: 100%;
  height: 100%;
  .three-box {
    width: 100%;
    height: 100%;
  }
}
</style>
