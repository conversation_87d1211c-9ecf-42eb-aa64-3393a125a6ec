<script setup lang="ts">
import type { IDesignSelect } from './select'
import { useDesignState } from '../../index'

const layer = defineModel<IDesignSelect>('layer', { required: true })

const designState = useDesignState()
</script>

<template>
  <el-select
    v-model="designState.layerData[layer.data.bindKey]"
    placeholder="请选择"
    class="select-layer-box"
    :style="{
      '--el-color-primary': layer.data.textColor,
      '--el-text-color-primary': layer.data.textColor,
      '--el-text-color-regular': layer.data.textColor,
      '--el-text-color-placeholder': layer.data.placeholderColor,
      '--el-input-placeholder-color': layer.data.placeholderColor,
    }"
  >
    <el-option
      v-for="item in designState.layerData?.[layer.data.dataSource]"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    >
    </el-option>
  </el-select>
</template>

<style scoped lang="scss">
.select-layer-box {
  width: 100%;
  height: 100%;
  :deep() {
    .el-select__wrapper {
      height: 100%;
      box-shadow: none;
      background: transparent;
    }
  }
}
</style>
