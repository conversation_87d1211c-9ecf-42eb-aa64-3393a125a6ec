import { HiRequest } from '../request'

interface AwardsModuleParams {
  module: 'listlotteryv3' | 'piclotteryv3' | 'seglottery'
  moduleId: number
}

interface Page {
  pageSize?: number
  pageIndex?: number
}

export default {

  read: (params: any) => HiRequest.post('/pro/hxc/mobile/proawards/read.htm', params),
  count: (params: any) => HiRequest.post('/pro/hxc/mobile/proawards/count.htm', params),
  list: (params: { where: AwardsModuleParams }) => HiRequest.post('/pro/hxc/mobile/proawards/list.htm', params),

  recordRead: (params: any) => HiRequest.post('/pro/hxc/mobile/proawardsrecord/read.htm', params),
  recordList: (params: any) => HiRequest.post('/pro/hxc/mobile/proawardsrecord/list.htm', params),
  recordLotteryPage: (params: { where: AwardsModuleParams & { likeRemarkByValue?: string, likeImportKey?: string } } & Page) => HiRequest.post('/pro/hxc/mobile/proawardsrecord/lotteryPage.htm', params),
}
