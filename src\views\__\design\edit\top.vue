<script setup lang="ts">
import type { IDesignStateStatus } from '../types'
import hxcLogo from '@/assets/design/layout/logo.png'
import { Close, Edit, Plus } from '@element-plus/icons-vue'
import { remove } from 'lodash-es'
import { envUtils } from '~/src/utils/env'
import { useDesignData, useDesignState, useDesignTemp } from '..'
import { useShortcutKey } from '../hooks/useShortcutKey'
import { randomStr } from '../utils'

const isReady = ref(false)
const interactive = inject<string>('interactive', '')
const designState = useDesignState()
const designTemp = useDesignTemp()
const designData = useDesignData()

const router = useRouter()

// const isManage = ref(router.currentRoute.value.path.startsWith('/manage'))
// const isAdmin = ref(router.currentRoute.value.path.startsWith('/admin'))
const isOem = ref(router.currentRoute.value.path.startsWith('/oem'))

useShortcutKey({
  'ctrl+s': {
    checkIsWork: () => true,
    callback: async () => {
      if (designTemp.themeChanged) {
        designTemp.reset()
        await designTemp.saveTheme()
      }
    },
  },
})
// 重置 //////////////////
const nowParentData = computed(() => {
  const { webContent, mobContent } = designTemp.parentTheme || {}
  return designTemp.showType === 'mobile' ? mobContent : webContent
})
const canReset = computed(() => {
  if (!nowParentData.value) return false
  return nowParentData.value !== designData.toString()
})
function resetFn() {
  if (!nowParentData.value) return
  designData.setState(JSON.parse(nowParentData.value))
}

/////////////////////////
const agentExtend = ref()

const logo = computed(() => {
  if (!isReady.value) return ''
  return agentExtend.value?.logo || hxcLogo
})

const gameMode = computed(() => {
  return Object
    .keys(designState.layerData)
    .filter(key => key.startsWith('$游戏模式-') && key.endsWith('$'))
    .map((name) => {
      return {
        label: name.slice(1, -1),
        value: name,
      }
    })
})

watch(
  () => gameMode.value,
  (v) => {
    designState.setGameMode(v[0]?.value)
  },
  { immediate: true },
)

const isMicrosite = ref(interactive === 'microsite')

if (isMicrosite.value) {
  designTemp.showType = 'mobile'
}

const hideStatus = computed(() => {
  if (isMicrosite.value) {
    return false
  }
  return designState.statusList.length < 2
})

const addStatus = ref<string | null>(null)
function addStatusFn() {
  if (addStatus.value) {
    designState.addStatus({ label: addStatus.value, value: randomStr() })
  }
  addStatus.value = null
}
function removeStatusFn(val: string) {
  if (designState.statusList.length <= 1) {
    return
  }
  // 遍历图层，将图层中 show 字段中含有的该图层删除
  designData.walk((layer) => {
    if (layer.show?.includes(val)) {
      remove(layer.show, v => v === val)
    }
  })
  designState.removeStatus(val)
}

async function editStatusFn(val: IDesignStateStatus) {
  if (!val) {
    return
  }
  ElMessageBox.prompt('请输入页面名称', '编辑名称', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /^[\u4E00-\u9FA5a-z0-9]+$/i,
    inputErrorMessage: '请输入中文、英文或数字',
    inputPlaceholder: val.label,
  }).then(({ value }) => {
    if (value) {
      const index = designState.statusList.findIndex(v => v.value === val.value)
      designState.statusList[index].label = value
      designState.setStatusList(designState.statusList)
    }
  }).catch(() => {})
}

if (isMicrosite.value) {
  // 微站时需要给状态列表同步保存到数据中
  watch(
    () => designState.statusList,
    v => designData.setStatus(v),
    { deep: true },
  )
}

onMounted(async () => {
  if (isOem.value) {
    // 服务商自己
    agentExtend.value = await api.oem.oemagent.extendRead({})
  } else if (envUtils.isOem) {
    // 服务商主办方
    agentExtend.value = await api.admin.oemagent.extendRead({
      where: {
        agentId: new URLSearchParams(location.search).get('agentId') || '',
      },
    })
  }
  isReady.value = true
})
</script>

<template>
  <div class="edit-top">
    <div class="btns-left">
      <img class="max-h-54px max-w-100px" :src="logo" alt="">
      <div v-if="designTemp.theme?.id && !isMicrosite" class="toggle-wrapper">
        <div
          v-for="item of designTemp.typeList"
          :key="item.value"
          class="toggle-name"
          :data-view="item.value"
          :class="{ active: designTemp.showType === item.value }"
          @click="designTemp.showType = item.value"
        >
          {{ item.label }}
        </div>
        <div
          v-if="designTemp.showType"
          class="toggle-block"
          :data-show-slide="designTemp.showType"
          :style="{
            width: `calc(${1 / designTemp.typeList.length * 100}% - 8px)`,
          }"
        ></div>
      </div>
      <template v-if="gameMode.length">
        <el-select
          v-model="designState.gameMode"
          class="ml-10 mr-4 w-140"
          placeholder="游戏模式"
        >
          <el-option v-for="item of gameMode" :key="item.value" :label="item.label" :value="item.value">
            {{ item.label }}
          </el-option>
        </el-select>
        <el-tooltip
          effect="dark"
          placement="top-start"
        >
          <template #content>
            当前互动存在多种游戏模式，
            <br>
            可以通过 图层设置->触发->显示/隐藏->逻辑中控制
            <br>
            指定哪个模式显示内容，然后在左侧下拉切换模式进行预览。
          </template>
          <icon-ph:question-fill class="cursor-pointer" />
        </el-tooltip>
      </template>
    </div>
    <div v-if="!hideStatus" class="status-list mx-30px h-full flex items-center justify-center flex-place-content-start overflow-x-auto">
      <template v-for="item in designState.statusList" :key="item.value">
        <el-button
          class="status-btn"
          :data-status="item.value"
          :color="designState.status === item.value ? '#3C55FF' : '#EEF2F8'"
          @click="designState.setStatus(item.value)"
        >
          {{ item.label }}
          <div v-if="isMicrosite" class="action">
            <el-icon v-if="designState.statusList.length > 1" class="close" @click.stop="removeStatusFn(item.value)"><Close /></el-icon>
            <el-icon class="edit" @click.stop="editStatusFn(item)"><Edit /></el-icon>
          </div>
        </el-button>
      </template>
      <template v-if="isMicrosite">
        <el-button v-if="addStatus === null" :icon="Plus" @click="addStatus = ''">空白页</el-button>
        <el-input
          v-else
          v-model.trim="addStatus"
          v-autofocus
          class="ml-10 w-94 flex-shrink-0"
          @blur="addStatusFn"
          @keyup.enter="addStatusFn"
        />
      </template>
    </div>
    <div v-if="designTemp.showType" class="btns-right">
      <el-popconfirm
        v-if="canReset"
        title="该操作会将所有修改重置为主题原有设置，是否继续?"
        cancel-button-text="取消"
        confirm-button-text="继续"
        :width="260"
        @confirm="resetFn"
      >
        <template #reference>
          <el-button type="danger">重置</el-button>
        </template>
      </el-popconfirm>
      <el-button type="primary" :disabled="!designTemp.themeChanged" @click="designTemp.saveTheme">保 存</el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.edit-top {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  width: 100%;
  height: var(--top-height);
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.3);

  .btns-left {
    display: flex;
    align-items: center;
    flex: 1;
    .toggle-wrapper {
      position: relative;
      padding: 3px;
      display: flex;
      border-radius: 4px;
      background: #f0f0f0;
      margin-left: 30px;
      .toggle-name {
        padding: 5px 10px;
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1;
        cursor: pointer;
        transition: color 0.3s ease;
        &.active {
          color: #7c8088; /* 激活状态颜色 */
        }
      }
      .toggle-block {
        position: absolute;
        margin: 2px;
        width: calc(50% - 8px);
        height: 26px;
        background: #fff;
        transition: transform 0.3s ease;
        transform: translateX(0);
        border-radius: 3px;
        &[data-show-slide='mobile'] {
          transform: translateX(60px);
        }
      }
    }
  }

  .status-list {
    --sb-track-color: #ffffff;
    --sb-thumb-color: #a1a1a1;
    --sb-size: 2px;

    &::-webkit-scrollbar {
      height: var(--sb-size);
    }

    &::-webkit-scrollbar-track {
      background: var(--sb-track-color);
      border-radius: 1px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--sb-thumb-color);
      border-radius: 1px;
    }

    @supports not selector(::-webkit-scrollbar) {
      & {
        scrollbar-color: var(--sb-thumb-color) var(--sb-track-color);
      }
    }
  }
  .status-btn {
    position: relative;
    .action {
      position: absolute;
      top: 0;
      right: -2px;
      opacity: 0;
      transition: 0.3s;
      display: flex;
      align-items: center;
      gap: 6px;
    }
    .close,
    .edit {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      transform: translate(50%, -50%);
      background: #666;
      color: #fff;
    }
    &:hover {
      .action {
        opacity: 1;
      }
    }
  }

  .btns-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex: 1;
  }
}
</style>
