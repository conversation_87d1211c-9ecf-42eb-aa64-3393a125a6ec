export function useVisitType() {
  const route = useRoute()
  const type = computed(() => {
    if (route.path.startsWith('/manage/')) {
      return 'Man'
    }
    if (route.path.startsWith('/oem/')) {
      return 'Oem'
    }
    if (route.path.startsWith('/admin/')) {
      return 'Spo'
    }
    if (route.path.startsWith('/mobile/')) {
      return 'Mobile'
    }
  })
  const isMobileUse = computed(() => type.value === 'Mobile')
  return {
    visitType: type,
    isMobileUse,
  }
}
