<script setup lang="ts">
import type { IDesignListlotteryIng3 } from './listlottery-ing3'
import { useDataAttr, useDesignState } from '../..'
import { defaultAnimateSpeed, defaultHeadSizeH, defaultHeadSizeW } from './listlottery-ing3'

const layer = defineModel<IDesignListlotteryIng3>('layer', { required: true })

const headSizeWBlind = useDataAttr(layer.value, 'headSizeW', defaultHeadSizeW)
const headSizeHBlind = useDataAttr(layer.value, 'headSizeH', defaultHeadSizeH)
const animiteSpeedBlind = useDataAttr(layer.value, 'animiteSpeed', defaultAnimateSpeed)

const designState = useDesignState()
const importformShowOption = computed<Record<number, boolean>>(() => {
  return designState.getLayerData('importformShowOption') || []
})

function removeItemBg(index: number) {
  const arr = layer.value.itemBgColors as string[]
  arr.splice(index, 1)
}
function addItemBg(index: number, defaultValue: string) {
  const arr = layer.value.itemBgColors as string[]
  arr.splice(index + 1, 0, defaultValue)
}

onMounted(() => {
})
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>元素尺寸</h3>
      </div>
      <div class="setting-item">
        <h3></h3>
        <div class="flex flex-a-c">
          <p>宽：</p>
          <el-input-number v-model="headSizeWBlind" v-input-number :max="1000" :min="100" controls-position="right" />
          <p class="ml-10">高：</p>
          <el-input-number v-model="headSizeHBlind" v-input-number :max="1000" :min="100" controls-position="right" />
        </div>
      </div>
      <div class="setting-item flex-items-start!">
        <h3>元素背景</h3>
        <div>
          <div
            v-for="(item, index) in layer.itemBgColors"
            :key="index"
            class="relative mb-5 h-50 w-140 flex"
          >
            <hi-color v-model="layer.itemBgColors[index]" />
            <div class="ml-6 w-20 pt-5">
              <icon-ph:minus-bold v-if="layer.itemBgColors.length > 1" @click.stop="removeItemBg(index)" />
              <icon-ph:plus-bold @click.stop="addItemBg(index, item)" />
            </div>
          </div>
        </div>
      </div>
      <div class="setting-item">
        <h3>动画速度</h3>
        <el-input-number v-model="animiteSpeedBlind" v-input-number :max="30" :min="0" controls-position="right" />
      </div>
      <div v-for="(item, index) in layer.contentStyle" :key="index" class="setting-item flex-a-start">
        <template v-if="importformShowOption[index]">
          <h3>文字{{ index + 1 }}</h3>
          <div>
            <div class="text-item">
              <h3>大小</h3>
              <el-input-number v-model="item.fontSize" v-input-number :max="100" :min="12" controls-position="right" />
            </div>
            <div class="text-item">
              <h3>颜色</h3>
              <hi-color v-model="item.fontColor" />
            </div>
            <div class="text-item">
              <h3>加粗</h3>
              <el-switch v-model="item.fonBold" />
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.text-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.flex-a-start {
  align-items: flex-start !important;
}
</style>
