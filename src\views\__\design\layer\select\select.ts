import { merge } from 'lodash-es'
import { BisTypes } from '../../'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './select-setting.vue'
import Comp from './select.vue'

// 类型
export const type = 'select'

// 数据类型约束
export interface IDesignSelect extends IDesignLayer {
  type: typeof type
  data: {
    dataSource: string
    bindKey: string
    placeholderColor: string
    textColor: string
  }
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.common,
    showInteractive: [InteractiveEnum.lotteryv3],
    showType: ['pcwall'],
    type,
    name: '选择器',
    Comp,
    CompSetting,
    thumbnail: new URL('./select.png', import.meta.url).href,
    defaultData(options): IDesignSelect {
      return merge({
        uuid: layerUuid(),
        name: '选择器',
        type,
        style: {
          width: '300px',
          height: '60px',
          background: '#fff',
        },
        data: {},
      }, options as IDesignSelect)
    },
  })
}
