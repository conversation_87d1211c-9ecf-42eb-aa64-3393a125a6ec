import { HiRequest } from '../request'

export interface IPadsignConfig {
  id?: number
  signStyle: string
  bgPc: string
  bgMobile: string
  specialEffect: string
  fontColor: string
  fontSize: string
  animationEffectSwitch: 'Y' | 'N'
}

export interface IPadsignData {
  id: number
  deleteTag: 'Y' | 'N'
  img: string
  updateTime: number
}

export default {
  configRead: (params: any) => HiRequest.post<IPadsignConfig>('/pro/hxc/web/propadsignconfig/read.htm', params),
  dataList: (params: any) => HiRequest.post<IPadsignData[]>('/pro/hxc/web/propadsigndata/list.htm', params),
  dataInsert: (params: any) => HiRequest.post<IPadsignData[]>('/pro/hxc/web/propadsigndata/insert.htm', params),
}
