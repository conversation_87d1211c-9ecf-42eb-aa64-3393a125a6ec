<script setup lang="ts">
import type { IDesignGoldcoinIng } from './goldcoin-ing'
import { injectComputed, useDesignState } from '../..'
import { defaultAvatarSize, defaultAvatarX, defaultAvatarY, defaultAvatarZIndex, defaultBackground, defaultBgImgHeight, defaultBgImgWidth, defaultBgImgX, defaultBgImgY, defaultBorderColor, defaultColor, defaultHeightPadding } from './goldcoin-ing'

const layer = defineModel<IDesignGoldcoinIng>('layer', { required: true })
const scale = injectComputed('scale', 1)

const designState = useDesignState()
const boxRef = ref<HTMLElement>()
const shakeIngRef = ref<HTMLElement>()
const topBlockImgRef = ref<HTMLElement>()

const { height: topBlockImgHeight } = useElementSize(topBlockImgRef)

const displayData = computed(() => {
  const dataList = designState.getLayerData('ingRankings') || []
  const maxScore = dataList.reduce((max: number, item: any) => Math.max(max, item.score), 0)
  const currentMaxNum = Math.ceil(maxScore / 50) * 50

  return dataList
    .map((item: any) => ({
      id: item.id,
      name: item.name,
      avatar: item.avatar,
      score: item.score,
      progress: (90 * item.score / currentMaxNum),
    }))
})

const shakeIngStyle = computed(() => {
  return {
    '--scale': scale.value,
    '--avatar-size': `${scale.value * 70}px`,
    'fontSize': `${scale.value * 18}px`,
    '--bubble-text-color': layer.value.color ?? defaultColor,
    '--bubble-border-color': layer.value.borderColor ?? defaultBorderColor,
    '--bubble-background-color': layer.value.background ?? defaultBackground,
  }
})

const heightPadding = computed(() => {
  return layer.value.heightPadding ?? defaultHeightPadding
})

function columnWrapStyle(item: any) {
  return {
    height: `calc(${item.progress}% - ${(topBlockImgHeight.value)}px - ${heightPadding.value * scale.value}px)`,
    transition: 'height 1s linear',
  }
}

const avatarBoxStyle = computed(() => {
  return {
    left: `${layer.value.avatarX ?? defaultAvatarX}%`,
    top: `${layer.value.avatarY ?? defaultAvatarY}%`,
    width: `${layer.value.avatarSize ?? defaultAvatarSize}%`,
    height: `${layer.value.avatarSize ?? defaultAvatarSize}%`,
    zIndex: layer.value.avatarZIndex ?? defaultAvatarZIndex,
  }
})

const randoms = Array.from({ length: 20 }, () => 1 + Math.random() * 4)

const bgBoxStyle = computed(() => {
  return {
    left: `${layer.value.bgImgX ?? defaultBgImgX}%`,
    top: `${layer.value.bgImgY ?? defaultBgImgY}%`,
    width: `${layer.value.bgImgWidth ?? defaultBgImgWidth}%`,
    height: `${layer.value.bgImgHeight ?? defaultBgImgHeight}%`,
  }
})
</script>

<template>
  <div ref="boxRef" class="design-shake-ing3">
    <ul ref="shakeIngRef" v-auto-animate :style="shakeIngStyle">
      <li v-for="(item, index) in displayData" :key="item.id">
        <div class="wrap">
          <img v-if="layer.bgImg" class="bg-img" :src="layer.bgImg" :style="bgBoxStyle" />
          <img v-if="layer.drops" class="coin" :src="layer.drops" :style="{ animationDelay: `${randoms[index]}s` }" />
          <div class="column-wrap" :style="columnWrapStyle(item)">
            <div ref="topBlockImgRef" class="top block-img">
              <div class="scoring">
                <div class="bubble">{{ item.score }}</div>
              </div>
              <img v-if="layer.trackTop" :src="layer.trackTop" />
            </div>
            <div class="bar" :style="{ backgroundImage: `url(${layer.track})` }"></div>
            <img class="block-img bottom" :src="layer.trackBottom" />
          </div>
          <div class="top">
            <div class="avatar-box">
              <img class="decoration" :src="layer.decoration" alt="">
              <img :src="item.avatar" class="avatar" :style="avatarBoxStyle" alt="">
            </div>
            <div class="name-box">
              <div class="name">{{ item.name }}</div>
            </div>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>

<style scoped lang="scss">
.design-shake-ing3 {
  width: 100%;
  height: 100%;
  position: relative;
  --avatar-size: 90px;
  --bubble-text-color: #fff;
  --bubble-border-color: #ff6b6b;
  --bubble-background-color: #fff5e6;

  ul {
    width: 100%;
    height: 100%;
    display: flex;
    padding: 30px 30px 50px 30px;
    justify-content: space-around;
  }
  li {
    width: 55px;
    height: 100%;
    position: relative;
  }

  .wrap {
    width: 100%;
    height: 100%;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    position: relative;
    .bg-img {
      position: absolute;
      width: 100%;
      height: 90%;
      left: 0;
      z-index: 1;
      background-repeat: no-repeat;
      background-size: 100%;
      background-position: bottom center;
    }
  }
  .scoring {
    width: 100%;
    height: 50px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    .num {
      font-size: 18px;
      width: 100%;
      line-height: 28px;
      padding-bottom: 10px;
      background: url('./assets/num.svg');
      background-repeat: no-repeat;
      background-position: center top;
      background-size: 100%;
      margin: 0 auto 25px;
      text-align: center;
      color: #fff;
      white-space: nowrap;
      text-shadow: 1px 1px 2px #000;
    }
    .bubble {
      position: relative;
      background-color: var(--bubble-background-color);
      border: 3px solid var(--bubble-border-color);
      border-radius: 20px;
      width: 100%;
      font-size: 17px;
      line-height: 28px;
      margin: 0 auto 15px;
      text-align: center;
      white-space: nowrap;
      color: var(--bubble-text-color);
    }

    .bubble::after {
      content: '';
      position: absolute;
      bottom: -8px;
      left: 50%;
      transform: translateX(-50%) rotate(45deg);
      width: 10px;
      height: 10px;
      background-color: var(--bubble-background-color);
      border-right: 3px solid var(--bubble-border-color);
      border-bottom: 3px solid var(--bubble-border-color);
    }
  }
  .column-wrap {
    width: 100%;
    position: relative;
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
    .block-img {
      position: absolute;
      content: '';
      width: 100%;
      left: 0;
      z-index: 11;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .top {
      top: 0;
      transform: translateY(-98%);
    }
    .bar {
      position: absolute;
      width: 100%;
      background-repeat: repeat-y;
      background-position: center bottom;
      background-size: 100%;
      position: relative;
      z-index: 10;
      flex: 1;
    }
    .bottom {
      bottom: 0;
      transform: translateY(95%);
    }
  }

  .top {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    position: relative;
    .name-box {
      font-weight: 400;
      margin-top: 5%;
      margin-right: 3%;
      display: flex;
      flex-direction: column;
      align-items: center;
      height: 20px;
      width: 200%;

      .count {
        color: #f4ff48;
        font-size: 1.2em;
        text-shadow: 1px 1px 2px #000;
      }

      .name {
        width: 100%;
        color: #fff;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        text-shadow: 1px 1px 2px #000;
        text-align: center;
      }
    }

    .avatar-box {
      position: relative;
      margin-top: 15%;
      width: var(--avatar-size);
      height: var(--avatar-size);
      overflow: hidden;
      background-repeat: no-repeat;
      background-size: 100%;
      background-position: center;

      .avatar {
        position: absolute;
        width: 100%;
        height: 100%;
        aspect-ratio: 1;
        border-radius: 50%;
        transform: translate(-50%, -50%);
      }
      .decoration {
        position: absolute;
        width: 100%;
        height: 100%;
        object-fit: contain;
        z-index: 10;
      }
    }
    .flag-box {
      position: absolute;
      top: 0;
      z-index: 11;
      img {
        max-height: 40px;
        object-fit: contain;
      }
    }
  }
}

.coin {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 95%;
  aspect-ratio: 1 / 1;
  object-fit: contain;
  z-index: 10;
  will-change: transform, opacity;
  animation: coinFall linear infinite;
  animation-play-state: running; // 确保动画正在运行
  animation-duration: 6s;
  opacity: 0;
}

@keyframes coinFall {
  0% {
    top: 0;
    opacity: 1;
    transform: translateX(-50%);
  }
  45% {
    // 下落动画占总时长的一半（3秒）
    top: 80%;
    opacity: 1;
    transform: translate(-50%, -50%);
  }
  45.5% {
    opacity: 0;
  }
  100% {
    // 后半段时间（3秒）保持隐藏状态
    top: 80%;
    opacity: 0;
    transform: translate(-50%, -50%);
  }
}
</style>
