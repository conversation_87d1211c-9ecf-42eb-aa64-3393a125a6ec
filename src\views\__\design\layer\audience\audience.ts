import type { IDesignLayer, IDesignSetup } from '../../types'
import { BisTypes } from '../..'
import { layerUuid } from '../../utils'

import CompSetting from './audience-setting.vue'
import Comp from './audience.vue'

// 观众
export const type = 'audience'
export const defaultColor = '#222829'

export interface IDesignAudience extends IDesignLayer {
  type: typeof type
  data: string
  repeat?: '' | 'x' | 'y'
  color?: string
}

export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.common,
    type,
    name: '观众',
    thumbnail: new URL('./audience.png', import.meta.url).toString(),
    Comp,
    CompSetting,
    async defaultData() {
      return {
        uuid: layerUuid(),
        type,
        name: '观众',
        data: new URL(`./assets/sense.png`, import.meta.url).href,
        style: {
          width: '100%',
          height: `80px`,
          top: `0px`,
          left: 0,
        },
      }
    },
  })
}
