<script setup lang="ts">
import { openSelectMaterial, useDataAttr, useDesignState } from '../../index'
import { defaultDirection, defaultDuration, type IDesignImageRoll } from './image-roll'

const layer = defineModel<IDesignImageRoll>('layer', { required: true })
const designState = useDesignState()

async function updateMaterialFn() {
  const result = await openSelectMaterial('PIC')
  if (result) {
    layer.value.data = result
  }
}

const durationBind = useDataAttr(layer.value, 'duration', defaultDuration)
const playStatesBind = useDataAttr(layer.value, 'playStates', [])
const reverseBind = useDataAttr(layer.value, 'reverse', false)
const forceStopBind = useDataAttr(layer.value, 'forceStop', false)
const directionBind = useDataAttr(layer.value, 'direction', defaultDirection)

const mOrATabs = ['自动', '手动'].map(item => ({ label: item, value: item }))
const mOrA = ref(mOrATabs[layer.value.style.backgroundSize ? 1 : 0].value)
watch(
  () => mOrA.value,
  (v) => {
    if (v === '自动') {
      delete layer.value.style.backgroundSize
    } else {
      if (!layer.value.style.backgroundSize) {
        layer.value.style.backgroundSize = '100% 100%'
      }
    }
  },
  { immediate: true },
)

const bSizeXBind = computed({
  get() {
    return Number.parseFloat(`${layer.value.style.backgroundSize}`.split(' ')[0] || '100')
  },
  set(v) {
    const { backgroundSize = '100% 100%' } = layer.value.style
    const arr = `${backgroundSize}`.split(' ')
    layer.value.style.backgroundSize = `${v}% ${arr[1]}`
  },
})
const bSizeYBind = computed({
  get() {
    return Number.parseFloat(`${layer.value.style.backgroundSize}`.split(' ')[1] || '100')
  },
  set(v) {
    const { backgroundSize = '100% 100%' } = layer.value.style
    const arr = `${backgroundSize}`.split(' ')
    layer.value.style.backgroundSize = `${arr[0]} ${v}%`
  },
})
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <div class="thumbnail-box bgblank" @click="updateMaterialFn">
          <img :src="layer.data">
        </div>
      </div>
      <div class="setting-item">
        <h3>运动方向</h3>
        <el-radio-group v-model="directionBind">
          <el-radio value="x">横向</el-radio>
          <el-radio value="y">纵向</el-radio>
        </el-radio-group>
      </div>
      <div class="setting-item">
        <h3>反向</h3>
        <el-switch v-model="reverseBind"></el-switch>
      </div>
      <div class="flex items-center justify-between">
        <label class="flex items-center">
          尺寸
          <el-tooltip effect="dark" content="位置为百分比时图层不在组中时相对的浏览器整个窗口居中显示。">
            <icon-ph-question-bold class="ml-5" />
          </el-tooltip>
        </label>
        <hi-tabs v-model="mOrA" :tabs="mOrATabs" />
      </div>
      <div class="setting-item justify-end!">
        <template v-if="mOrA === '手动'">
          <h3 class="ml-10">宽</h3>
          <el-input-number v-model="bSizeXBind" v-input-number controls-position="right" />
          <span class="ml-10">%</span>
          <h3 class="ml-10">高</h3>
          <el-input-number v-model="bSizeYBind" v-input-number controls-position="right" />
          <span class="ml-10">%</span>
        </template>
      </div>
      <div class="setting-item">
        <h3>动画时长</h3>
        <hi-input-number v-model="durationBind" class="w-100" :min="0.1" :precision="1" append="秒" />
      </div>
      <div class="setting-item">
        <h3>播放条件</h3>
        <el-select v-model="playStatesBind" class="w-120" multiple placeholder="播放的活动状态">
          <el-option v-for="{ label, value } in designState.statusList" :key="value" :label="label" :value="value"></el-option>
        </el-select>
      </div>
      <div class="setting-item">
        <h3>动画立即暂停</h3>
        <el-switch v-model="forceStopBind"></el-switch>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.thumbnail-box {
  width: 120px;
  height: 120px;
  outline: 1px solid #e6ebed;
  border-radius: 4px;
  position: relative;

  img {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }

  > img {
    object-fit: contain;
  }
}
</style>
