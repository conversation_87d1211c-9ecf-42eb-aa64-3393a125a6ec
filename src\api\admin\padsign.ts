import { HiRequest } from '../request'

export default {
  readConfig: (params: any) => HiRequest.post('/pro/hxc/propadsignconfig/read.htm', params),
  addConfig: (params: any) => HiRequest.post('/pro/hxc/propadsignconfig/add.htm', params),
  updateConfig: (params: any) => HiRequest.post('/pro/hxc/propadsignconfig/update.htm', params),

  deleteData: (params: any) => HiRequest.post('/pro/hxc/propadsigndata/delete.htm', params),
  listData: (params: any) => HiRequest.post('/pro/hxc/propadsigndata/list.htm', params),
  pageData: (params: any) => HiRequest.post('/pro/hxc/propadsigndata/page.htm', params),
}
