import { ref } from 'vue'
import { envUtils } from '~/src/utils/env'

export interface FontConfig {
  name: string // 字体名称
  url: string // 字体文件URL
  value: string // 字体值
  weight?: string // 字重
  style?: string // 样式
  text?: string // 文字
}

// 文字艺术
export interface TextArtMaterial {
  id: string
  name: string
  type: string
  style: any
}

const fontList = [
  { name: '默认', value: 'initial' },
  {
    name: '阿里妈妈刀隶体',
    value: 'AlimamaDaoLiTi',
    url: `${envUtils.resdomain}/qn/lib/fonts/AlimamaDaoLiTi.woff2`,
  },
  {
    name: '标小智龙珠体',
    value: 'LongZhuTi-Regular',
    url: `${envUtils.resdomain}/qn/lib/fonts/LongZhuTi-Regular.woff2`,
  },
  {
    name: '钉钉进步体',
    value: 'DingTalk-JinBuTi',
    url: `${envUtils.resdomain}/qn/lib/fonts/DingTalk-JinBuTi.woff2`,
  },
  {
    name: '江城律动黑',
    value: 'jiangchenglvdonghei',
    url: `${envUtils.resdomain}/qn/lib/fonts/jiangchenglvdonghei.woff2`,
  },
  {
    name: '金山云技术体',
    value: 'Kingsoft_Cloud_Font',
    url: `${envUtils.resdomain}/qn/lib/fonts/Kingsoft_Cloud_Font.woff2`,
  },
  {
    name: '庞门正道标题体',
    value: 'Pangmenzhengdao',
    url: `${envUtils.resdomain}/qn/lib/fonts/Pangmenzhengdao.woff2`,
  },
  {
    name: '字魂扁桃体',
    value: 'ZiHunBianTaoTi',
    url: `${envUtils.resdomain}/qn/lib/fonts/ZiHunBianTaoTi.woff2`,
  },
  {
    name: '江湖古风体',
    value: 'ZiKuJiangHuGuFengTi',
    url: `${envUtils.resdomain}/qn/lib/fonts/ZiKuJiangHuGuFengTi.woff2`,
  },
  {
    name: '云峰飞云体',
    value: 'YunFengFeiYunTi',
    url: `${envUtils.resdomain}/qn/lib/fonts/YunFengFeiYunTi.woff2`,
  },
  {
    name: '三极泼墨',
    value: 'SanJiPoMoTi',
    url: `${envUtils.resdomain}/qn/lib/fonts/SanJiPoMoTi.woff2`,
  },
]

const textArtList: TextArtMaterial[] = [
  {
    id: '1',
    name: '阴影',
    type: 'shadow',
    style: {
      color: 'rgb(255, 255, 255)',
      fontWeight: 'bold',
      textShadow: '0px 0px 10px #6b7d99',
    },
  },
  {
    id: '17',
    name: '彩虹渐变',
    type: 'gradient',
    style: {
      fontWeight: 'bold',
      backgroundImage: 'linear-gradient(90deg, #ff0000 0%, #ff7f00 20%, #ffff00 40%, #00ff00 60%, #0000ff 80%, #800080 100%)',
      backgroundClip: 'text',
      color: 'transparent',
    },
  },
  {
    id: '3',
    name: '贴图',
    type: 'image',
    style: {
      fontWeight: 'bold',
      backgroundClip: 'text',
      color: 'transparent',
      backgroundImage: 'url(https://res3.hixianchang.com/qn/up/ceb3cfc1c905cc43b0a05dad98db053a.gif)',
    },
  },
  {
    id: '4',
    name: '立体',
    type: '',
    style: {
      fontWeight: 'bold',
      color: 'rgb(113, 113, 239)',
      textShadow: '1px 1px 0px rgb(0, 0, 0), 2px 2px 0px rgb(0, 0, 0), 3px 3px 0px rgb(0, 0, 0), 3px 4px 0.2em rgb(0, 0, 0)',
    },
  },
  {
    id: '5',
    name: '多重',
    type: '',
    style: {
      fontWeight: 'bold',
      color: 'rgb(255, 255, 255)',
      textShadow: '1px 1px 0px rgb(37, 64, 128), 3px 3px 0px rgb(52, 219, 119), 5px 5px 0px rgb(42, 71, 172), 7px 7px 0px rgb(51, 57, 73), 10px 10px 10px rgb(125, 144, 175)',
    },
  },
  {
    id: '6',
    name: '描边',
    type: '',
    style: {
      '-webkit-text-stroke': '1px #000',
      'fontWeight': 'bold',
      'color': 'rgb(253, 94, 0)',
      'textShadow': '1px -1px 0px rgb(255, 255, 255), 0px -1px 0px rgb(255, 255, 255), 1px 1px 0px rgb(255, 255, 255), 1px 0px 0px rgb(255, 255, 255), 0px 1px 0px rgb(255, 255, 255), -1px 1px 0px rgb(255, 255, 255), -1px 0px 0px rgb(255, 255, 255), 1px 1px 0px rgb(214, 214, 214), 2px 2px 0px rgb(214, 214, 214), 3px 3px 0px rgb(214, 214, 214), 5px 5px 2px rgb(125, 144, 175)',
    },
  },
  {
    id: '7',
    name: '荧光',
    type: '',
    style: {
      color: '#FFFFFF',
      textShadow: '0 -1px 4px #FFF, 0 -2px 10px #ff0, 0 -10px 20px #ff8000, 0 -18px 40px #F00',
    },
  },
  {
    id: '8',
    name: '雕刻',
    type: '',
    style: {
      color: 'transparent',
      fontWeight: 'bold',
      background: '#666666',
      backgroundClip: 'text',
      textShadow: '0px 3px 3px rgb(255,255,255,0.5)',
    },
  },
  {
    id: '9',
    name: '霓虹蓝光',
    type: 'neon',
    style: {
      fontWeight: 'bold',
      color: '#fff',
      textShadow: '0 0 5px #00bfff, 0 0 10px #00bfff, 0 0 20px #00bfff, 0 0 40px #00bfff, 0 0 80px #00bfff', // 使用天蓝色辉光
    },
  },
  {
    id: '11',
    name: '双色轮廓',
    type: 'stroke',
    style: {
      fontWeight: 'bold',
      color: '#fff', // 文字主体颜色
      textShadow: '-1px -1px 0 #ff69b4, 1px -1px 0 #ff69b4, -1px 1px 0 #ff69b4, 1px 1px 0 #ff69b4, -2px -2px 0 #ffd700, 2px -2px 0 #ffd700, -2px 2px 0 #ffd700, 2px 2px 0 #ffd700', // 内层粉色描边，外层金色描边
    },
  },
  {
    id: '12',
    name: '3D红蓝',
    type: 'shadow',
    style: {
      fontWeight: 'bold',
      color: 'rgba(0, 0, 0, 0)',
      textShadow: '2px 2px 1px rgba(255, 0, 0, 0.7), -2px -2px 1px rgba(0, 255, 255, 0.7)',
    },
  },
  {
    id: '13',
    name: '故障文字',
    type: 'glitch',
    style: {
      fontWeight: 'bold',
      color: '#fff',
      position: 'relative',
      textShadow: '1px 1px 0px #ff00c1, -1px -1px 0px #00fff9',
    },
  },
  {
    id: '14',
    name: '空心描边',
    type: 'stroke',
    style: {
      'fontWeight': 'bold',
      'color': 'transparent',
      '-webkit-text-stroke': '1px #000000',
    },
  },
  {
    id: '16',
    name: '模糊不清',
    type: 'effect',
    style: {
      fontWeight: 'bold',
      color: 'transparent',
      textShadow: '0 0 5px rgba(0, 0, 0, 0.5)',
    },
  },

  {
    id: '24',
    name: '金属铬牌',
    type: 'gradient',
    style: {
      fontWeight: 'bold',
      color: '#ababab',
      backgroundImage: 'linear-gradient(0deg, #777 0%, #ddd 50%, #777 100%)',
      backgroundClip: 'text',
      textShadow: '0px 1px 1px #fff, 0px -1px 1px #333',
    },
  },
  {
    id: '27',
    name: '闪亮金箔',
    type: 'gradient',
    style: {
      fontWeight: 'bold',
      backgroundImage: 'linear-gradient(135deg, #ffd700 0%, #f0c040 50%, #daa520 100%)',
      backgroundClip: 'text',
      color: 'transparent',
      textShadow: '1px 1px 2px rgba(0, 0, 0, 0.3)',
    },
  },
  {
    id: '28',
    name: '复古风格',
    type: 'retro',
    style: {
      fontWeight: 'bold',
      color: '#e5a073',
      textShadow: '2px 2px 0px #5c4033',
    },
  },
  {
    id: '30',
    name: '霓虹粉光',
    type: 'neon',
    style: {
      fontWeight: 'bold',
      color: '#fff',
      textShadow: '0 0 5px #ff00ff, 0 0 10px #ff00ff, 0 0 20px #ff00ff, 0 0 40px #ff00ff, 0 0 80px #ff00ff',
    },
  },
  {
    id: '31',
    name: '粗黑轮廓',
    type: 'stroke',
    style: {
      'fontWeight': 'bold',
      'color': '#ffffff',
      '-webkit-text-stroke': '2px #000000',
    },
  },
  // 来个带动效的
  {
    id: '32',
    name: '闪烁霓虹',
    type: 'neon',
    style: {
      fontWeight: 'bold',
      color: '#fff',
      textShadow: '0 0 5px #00ffff, 0 0 10px #00ffff, 0 0 20px #00ffff',
      animation: 'neonFlicker 1.5s infinite alternate',
    },
  },
]

export function useFontLoader() {
  const loadedFonts = ref<Set<string>>(new Set())
  const loading = ref(false)
  const error = ref<Error | null>(null)

  const loadFont = async (config: FontConfig): Promise<void> => {
    if (loadedFonts.value.has(config.url)) {
      return
    }

    loading.value = true
    error.value = null

    const fontFace = new FontFace(
      config.value,
      `url(${config.url})`,
      {
        weight: config.weight || 'normal',
        style: config.style || 'normal',
      },
    )

    const loadedFont = await fontFace.load()
    document.fonts.add(loadedFont)
    loadedFonts.value.add(config.url)
  }

  const preloadFonts = async (fonts: FontConfig[]): Promise<void> => {
    loading.value = true
    error.value = null

    try {
      const promises = fonts.map(font =>
        loadFont(font).catch((err) => {
          console.warn(`预加载字体 ${font.value} 失败:`, err)
          return null
        }),
      )

      await Promise.allSettled(promises)
    } finally {
      loading.value = false
    }
  }

  const isFontLoaded = (url: string): boolean => {
    return loadedFonts.value.has(url)
  }

  return {
    loadFont,
    preloadFonts,
    isFontLoaded,
    loading,
    error,
    loadedFonts,
    textArtList,
    fontList,
  }
}
