<script setup lang="ts">
import type { IDesignLotteryAreadyAvatar } from './lottery-ready-avatar'
import { injectScale, useDesignState, useDesignTemp } from '../..'
import { processStyle } from '../../utils'
import { defaultDecorateAnimation, defaultGapColumn, defaultGapRow, defaultHeadSize, maxCout } from './lottery-ready-avatar'

const layer = defineModel<IDesignLotteryAreadyAvatar>('layer', { required: true })
const scale = injectScale()
const designState = useDesignState()
const designTemp = useDesignTemp()
const isDesignEdit = designTemp.isEdit

const domRef = ref<HTMLElement>()
const headSize = computed(() => layer.value.headSize ?? defaultHeadSize)
const gapColumn = computed(() => layer.value.gapColumn ?? defaultGapColumn)
const gapRow = computed(() => layer.value.gapRow ?? defaultGapRow)
const decorateAnimation = computed(() => layer.value.decorateAnimation ?? defaultDecorateAnimation)

const domRefSize = useElementSize(domRef, undefined, { box: 'border-box' })
const onceNum = computed(() => {
  return designState.getLayerData('#每次抽取人数#')
})
const headCountObj = computed(() => {
  let headCount = 0
  let gridCols = 0 // 列数
  let gridRows = 0 // 行数

  if (domRefSize.width.value && domRefSize.height.value) {
    const Width = domRefSize.width.value / scale.value
    const Height = domRefSize.height.value / scale.value
    gridCols = Math.floor((Width + gapColumn.value) / (headSize.value + gapColumn.value))
    gridRows = Math.floor((Height + gapRow.value) / (headSize.value + gapRow.value))
    headCount = gridCols * gridRows
  }
  if (!isDesignEdit) {
    headCount = onceNum.value || 0
    // 不能超过headCount
    if (headCount > gridCols * gridRows) {
      headCount = gridCols * gridRows
    } else {
      // 少于最大值，居中排列算出几排几列，
      gridRows = Math.ceil(headCount / gridCols) // 行数
      gridCols = Math.ceil(headCount / gridRows) // 列数
    }
  }
  maxCout[layer.value.uuid] = headCount
  return {
    headCount,
    gridCols,
    gridRows,
  }
})
const decorateImgStyle = computed(() => {
  return {
    backgroundImage: `url(${layer.value.decorateImg})`,
    animation: decorateAnimation.value ? 'rotate 2s infinite linear' : 'none',
  }
})

const domStyle = computed(() => {
  return processStyle({
    '--headSize': `${scale.value * headSize.value}px`,
    '--gapColumn': `${scale.value * gapColumn.value}px`,
    '--gapRow': `${scale.value * gapRow.value}px`,
  })
})
</script>

<template>
  <div ref="domRef" class="lottery-avatar-box" :style="domStyle">
    <div class="wrap">
      <div v-for="j in headCountObj.headCount" :key="j" class="item-box">
        <div class="decorateImg" :style="decorateImgStyle"></div>
        <img class="avatar" :src="layer.avatarImg" alt="">
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.lottery-avatar-box {
  width: 100%;
  height: 100%;
  .wrap {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    align-content: center;
    gap: var(--gapRow) var(--gapColumn);
  }
  .item-box {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    width: var(--headSize);
    height: var(--headSize);
    .decorateImg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      animation: rotate 2s infinite linear;
    }
    img {
      width: 90%;
      height: 90%;
      border-radius: 50%;
    }
  }
}
//旋转动画
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
