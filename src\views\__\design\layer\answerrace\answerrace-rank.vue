<script setup lang="ts">
import type { IDesignAnswerraceRank } from './answerrace-rank'
import { useDesignState } from '../..'
import { defaultData } from './answerrace-rank'
import rankTable from './components/ranktable.vue'

const layer = defineModel<IDesignAnswerraceRank>('layer', { required: true })
// const customEmits = defineCustomEmits(layer)
const designState = useDesignState()
const status = computed(() => designState.status)

interface IDisplayData {
  id: number
  name: string
  avatar: string
  score: number | string // 分数，
  rank?: number // 名次
}

const outerData = computed<IDisplayData[]>(() => {
  // 轮次
  if (status.value === 'finish') {
    return designState.getLayerData('endRankings') || []
  }
  // 全部
  if (status.value === 'winlist') {
    return designState.getLayerData('allRankings') || []
  }
  return []
})

const count = computed(() => {
  return layer.value.showCount ?? defaultData.showCount
})

const showData = computed(() => {
  const list = outerData.value.slice(0, count.value)
  return list
})

const joinType = computed(() => {
  return designState.getLayerData('joinType')
})

const scoreName = computed(() => {
  return joinType.value === 'PERSONAL' ? '分数/用时' : '分数/正确率'
})
const rankConfig = computed(() => {
  return {
    titleFontSize: layer.value.titleFontSize ?? defaultData.titleFontSize,
    titleColor: layer.value.titleColor ?? defaultData.titleColor,
    contentTextAlign: layer.value.contentTextAlign ?? defaultData.contentTextAlign,
    sortColor: layer.value.sortColor ?? defaultData.sortColor,
    sortFontSize: layer.value.sortFontSize ?? defaultData.sortFontSize,
    headerSize: layer.value.headerSize ?? defaultData.headerSize,
    contentColor: layer.value.contentColor ?? defaultData.contentColor,
    contentFontSize: layer.value.contentFontSize ?? defaultData.contentFontSize,
  }
})

const rankLoading = computed(() => {
  if (status.value === 'finish') {
    return designState.getLayerData('finishRankingsLoading') || false
  }
  return false
})
onMounted(() => {
})
</script>

<template>
  <div class="answerrace-rank-box">
    <rank-table :loading="rankLoading" :score-name="scoreName" :rank-data="showData" :config="rankConfig"> </rank-table>
  </div>
</template>

<style scoped lang="scss">
.answerrace-rank-box {
  width: 100%;
  height: 100%;
}
</style>
