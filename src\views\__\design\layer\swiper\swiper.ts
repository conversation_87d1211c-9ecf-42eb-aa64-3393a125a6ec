import type { IDesignLayer, IDesignSetup } from '../../types'
import { BisTypes, openSelectMaterial, useDesignData, useDesignTemp } from '../..'
import { layerUuid } from '../../utils'
import CompSetting from './swiper-setting.vue'
import Comp from './swiper.vue'

// 类型
export const type = 'swiper'
export interface SwiperItem {
  img: string
  linkType: 'big' | 'url' | 'page'
  linkUrl: string
  linkPage: string
  linkPageRoundId?: number | string // 轮播图跳转页面的roundId
}
export const defaultShowPagination = false
export const defaultDirection = 'horizontal'
export const defaultShowArrow = false
export const defaultAutoPlay = true
export const defaultDelay = 3
export type SpecialModule = 'ninegrids' | 'wheelsurf' | 'packetwall' | 'mysterybox'
export const specialModuleList = ['ninegrids', 'wheelsurf', 'packetwall', 'mysterybox']
// 数据类型约束
export interface IDesignSwiper extends IDesignLayer {
  type: typeof type
  swiperData: SwiperItem[]
  showPagination?: boolean
  direction?: string
  showArrow?: boolean
  autoPlay?: boolean
  delay?: number
  effect?: string
  loop?: boolean
}

// 注册组件
export function setup(app: IDesignSetup) {
  const designData = useDesignData()
  const designTemp = useDesignTemp()
  const isMobile = computed(() => designTemp.showType === 'mobile')
  app.registry({
    bisType: BisTypes.common,
    showType: ['pcwall', 'mobile'],
    type,
    name: '轮播图',
    thumbnail: new URL('./swiper.png', import.meta.url).href,
    Comp,
    CompSetting,
    async defaultData() {
      const imgData = await openSelectMaterial()
      if (!imgData) return
      return {
        type,
        uuid: layerUuid(),
        name: '轮播图',
        swiperData: [
          {
            img: imgData,
            linkUrl: '',
            linkType: 'page',
            linkPage: '',
            linkPageRoundId: 0,
          },
        ],
        style: {
          width: isMobile.value ? `${designData.option.drafts[0]}px` : '400px',
          height: '250px',
        },
      }
    },
  })
}
