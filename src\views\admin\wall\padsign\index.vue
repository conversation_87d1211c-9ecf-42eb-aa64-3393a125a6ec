<script setup lang="ts">
import { cloneDeep } from 'lodash-es'
import { difference } from '~/src/utils'
import { envUtils } from '~/src/utils/env'
import { defaultConfig } from './index'
import PadRecord from './record.vue'
import PadSetting from './setting.vue'

definePage({ meta: { label: 'iPad 签字' } })

const router = useRouter()
const route = useRoute()

const loading = ref(true)
const wallId = ref(Number.parseInt(route.query.wallId as string))
const activeName = ref('setting')
const componentList = [
  {
    name: 'setting',
    label: '设置',
    comp: PadSetting,
  },
  {
    name: 'record',
    label: '记录',
    comp: PadRecord,
  },
]

const config = ref()
const originConfig = ref()

const isUpdate = computed(() => {
  const update = difference(config.value, originConfig.value)
  if (!update) return false
  if (update.id) {
    delete update.id
  }
  return Object.keys(update).length > 0
})

async function fetchConfig() {
  if (Number.isNaN(wallId.value) && envUtils.isDev) {
    const inputValue = await ElMessageBox.prompt('', '提示', { inputPlaceholder: '请输入主题id' })
    wallId.value = Number.parseInt(inputValue.value)
    const query = { ...router.currentRoute.value.query, wallId: wallId.value }
    router.replace({ query })
  }
  const data = await api.admin.padsign.readConfig({
    where: { wallId: wallId.value },
  })
  const configData = data ? (data.id ? data : defaultConfig) : defaultConfig

  config.value = cloneDeep(configData)
  originConfig.value = cloneDeep(configData)
}

async function saveConfig() {
  const id = config.value.id
  const data = {
    wallId: wallId.value,
    ...config.value,
  }

  if (id) {
    const update = difference(config.value, originConfig.value)
    if (update) {
      await api.admin.padsign.updateConfig({ where: { id }, update })
    }
  } else {
    await api.admin.padsign.addConfig(data)
  }

  ElMessage.success('保存成功')
  fetchConfig()
}

onMounted(async () => {
  await fetchConfig()
  loading.value = false
})
</script>

<template>
  <div class="h-full w-full">
    <div v-loading="loading" class="padsign-box">
      <el-tabs v-if="!loading" v-model="activeName" type="border-card" class="el-tabs-full">
        <el-tab-pane v-for="item of componentList" :key="item.name" :label="item.label" :name="item.name" class="padsign-tab" lazy>
          <component :is="item.comp" v-if="activeName === item.name" :config="config" :wall-id="wallId" />
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="footer">
      <el-button v-if="activeName === 'setting'" type="primary" :disabled="!isUpdate" @click="saveConfig">保存</el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.padsign-box {
  width: 100%;
  height: 100%;
  padding: 15px;
}
.footer {
  width: 100%;
  height: 66px;
  box-sizing: border-box;
  background-color: #fff;
  border-top: 1px solid #dfe6ec;
  box-shadow: 0 0 5px 5px rgba(0, 0, 0, 0.06);
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}
.padsign-tab {
  overflow-y: auto;
  height: 100%;
}
</style>
