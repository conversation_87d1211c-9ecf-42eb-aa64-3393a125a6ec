<script setup lang="ts">
import type { IDesignIngRank } from './ingrank'
import { getDefaultMaterial, openSelectMaterial, useDataAttr } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'
import { defaultColor, defaultData, defaultShowIndex } from './ingrank'

const layer = defineModel<IDesignIngRank>('layer', { required: true })

const colorBind = useDataAttr(layer.value.style, 'color', defaultColor)
const showIndexBlind = useDataAttr(layer.value, 'showIndex', defaultShowIndex)
const bgTopBind = useDataAttr(layer.value, 'bgTop', defaultData.bgTop)
const bgBodyBind = useDataAttr(layer.value, 'bgBody', defaultData.bgBody)
const bgBottomBind = useDataAttr(layer.value, 'bgBottom', defaultData.bgBottom)

type IType = 'bgTop' | 'bgBody' | 'bgBottom'
async function updateMaterialFn(name: IType, isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, `${name}`)
    : await openSelectMaterial('PIC')
  if (result) {
    layer.value[name] = result
  } else if (isReset) {
    delete layer.value[name]
  }
}
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>字体颜色</h3>
        <hi-color v-model="colorBind" />
      </div>
      <div class="setting-item">
        <h3>序号背景色</h3>
        <hi-color v-model="layer.rankBgColor" />
      </div>
      <div class="setting-item">
        <h3>是否显示序号</h3>
        <el-switch v-model="showIndexBlind" />
      </div>
      <div class="setting-item">
        <h3>左右内边距</h3>
        <el-input-number v-model="layer.paddingLR" v-input-number :max="20" :min="0" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>上下内边距</h3>
        <el-input-number v-model="layer.paddingTB" v-input-number :max="20" :min="0" controls-position="right" />
      </div>
      <div class="setting-item mb-5!">
        <h3>背景图顶部</h3>
        <div class="flex items-end">
          <div class="d bgblank" @click="bgTopBind = ''"></div>
          <div class="bgblank relative h-50 w-64">
            <MaterialThumbnail @select="updateMaterialFn('bgTop')" @reset="updateMaterialFn('bgTop', true)">
              <img v-if="bgTopBind" :src="bgTopBind">
            </MaterialThumbnail>
          </div>
        </div>
      </div>
      <div class="setting-item mt-5!">
        <h3>背景图顶中间</h3>
        <div class="flex items-end">
          <div class="d bgblank" @click="bgBodyBind = ''"></div>
          <div class="bgblank relative h-50 w-64">
            <MaterialThumbnail @select="updateMaterialFn('bgBody')" @reset="updateMaterialFn('bgBody', true)">
              <img v-if="bgBodyBind" :src="bgBodyBind">
            </MaterialThumbnail>
          </div>
        </div>
      </div>
      <div class="setting-item mt-0!">
        <h3>背景图顶底部</h3>
        <div class="flex items-end">
          <div class="d bgblank" @click="bgBottomBind = ''"></div>
          <div class="bgblank relative h-50 w-64">
            <MaterialThumbnail @select="updateMaterialFn('bgBottom')" @reset="updateMaterialFn('bgBottom', true)">
              <img v-if="bgBottomBind" :src="bgBottomBind">
            </MaterialThumbnail>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.d {
  width: 25px;
  height: 25px;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
  &:first-child {
    position: relative;
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 90%;
      height: 90%;
      margin: 5%;
      mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1em' height='1em' viewBox='0 0 256 256'%3E%3Cpath fill='%23000' d='m203.63 62.65l13.25-14.58a12 12 0 0 0-17.76-16.14l-13.24 14.56A100 100 0 0 0 52.37 193.35l-13.25 14.58a12 12 0 1 0 17.76 16.14l13.24-14.56A100 100 0 0 0 203.63 62.65M52 128a75.94 75.94 0 0 1 117.58-63.57l-100.91 111A75.6 75.6 0 0 1 52 128m76 76a75.5 75.5 0 0 1-41.58-12.43l100.91-111A75.94 75.94 0 0 1 128 204'/%3E%3C/svg%3E");
      mask-size: 100%;
      background-color: #999999;
      cursor: pointer;
    }
  }
}
</style>
