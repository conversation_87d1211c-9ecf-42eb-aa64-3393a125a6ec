<script setup lang="ts">
import type { CSSProperties } from 'vue'
import type { IDesignImage2 } from './image2'
import { useImageInfo } from '~/src/hooks/useImageInfo'
import { hasAuth } from '~/src/utils/auth'
import { openSelectMaterial, useDesignState, useDesignTemp } from '../..'
import { defaultDirection, defaultDuration, defaultMode, defaultPosition, defaultRepeat, defaultSize } from './image2'

const layer = defineModel<IDesignImage2>('layer', { required: true })
const designState = useDesignState()
const designTemp = useDesignTemp()
const imageBoxRef = ref<HTMLElement>()
const imageBoxSize = useElementSize(imageBoxRef)

async function updateMaterialFn() {
  if (designTemp.isPreview) return
  if (!hasAuth(['man', 'oem', 'spo.hi.vip', 'spo.ft']) && !layer.value.spoVisible) return
  const result = await openSelectMaterial('PIC')
  if (result) {
    layer.value.data = result
  }
}

let promiseResolve: PromiseWithResolvers<void> | undefined

const aniTempPlay = ref(false)
// 按照活动状态进行播放
const isAllowPlay = computed(() => {
  if (!layer.value.playStates?.length) return true
  return layer.value.playStates.includes(designState.status)
})
watch(
  () => isAllowPlay.value,
  (v) => {
    if (v) {
      aniTempPlay.value = true
      promiseResolve = undefined
    } else {
      if (layer.value.forceStop) {
        aniTempPlay.value = false
        return
      }
      promiseResolve = Promise.withResolvers()
      promiseResolve.promise.then(() => {
        aniTempPlay.value = false
      })
    }
  },
)

const imgPath = computed(() => {
  const url = layer.value.data
  return url
  // const width = layer.value.style.width
  // const height = layer.value.style.height
  // if (!width || !height) return url
  // return `${url}?imageView2/2/w/${Number.parseInt(`${width}`)}/h/${Number.parseInt(`${height}`)}`
})
const imgSize = useImageInfo(imgPath)

const bSize = computed(() => {
  const mode = layer.value.mode ?? defaultMode
  if (mode === 'fill') {
    return '100% 100%'
  }
  if (['contain', 'cover'].includes(mode)) {
    return mode
  }

  const arr = [layer.value.sizeX ?? defaultSize, layer.value.sizeY ?? defaultSize]
  if (arr.length === 1) {
    arr.push(arr[0])
  }
  let sizeX: string | number = arr[0]
  if (Number.isNaN(sizeX)) {
    sizeX = arr[0]
  } else if (sizeX === 0) {
    sizeX = 'auto'
  } else {
    sizeX = sizeX / 100
  }
  let sizeY: string | number = arr[1]
  if (Number.isNaN(sizeY)) {
    sizeY = arr[0]
  } else if (sizeY === 0) {
    sizeY = 'auto'
  } else {
    sizeY = sizeY / 100
  }

  if (sizeX === 'auto' && sizeY === 'auto') {
    return 'auto'
  }
  if (sizeX === 'contain' && sizeY === 'contain') {
    return `contain`
  }

  return {
    x: sizeX,
    y: sizeY,
  }
})
const bSizeStyle = computed(() => {
  if (typeof bSize.value === 'string') {
    return bSize.value
  }
  const x = typeof bSize.value.x === 'number' ? `${bSize.value.x * 100}%` : bSize.value.x
  const y = typeof bSize.value.y === 'number' ? `${bSize.value.y * 100}%` : bSize.value.y
  return `${x} ${y}`
})

const resultSize = computed(() => {
  if (bSize.value === 'contain') {
    const box = { width: imageBoxSize.width.value, height: imageBoxSize.height.value }
    const img = { width: imgSize.width.value, height: imgSize.height.value }
    // 1. 盒子的宽高比
    const boxRatio = box.width / box.height
    // 2. 图片的宽高比
    const imgRatio = img.width / img.height

    // 3. 图片的实际宽高
    let resultWidth = 0
    let resultHeight = 0
    if (boxRatio > imgRatio) {
      // 盒子更宽
      resultWidth = box.height / img.height * img.width
      resultHeight = box.height
    } else {
      // 图片更宽
      resultWidth = box.width
      resultHeight = box.width / img.width * img.height
    }
    return { width: resultWidth, height: resultHeight }
  } else if (bSize.value === 'auto') {
    return { width: imgSize.width.value, height: imgSize.height.value }
  } else {
    if (typeof bSize.value !== 'string') {
    // 第一个是auto，第二个是百分比，返回高度按照百分比计算，宽度按照图片比例计算
      if (bSize.value.x === 'auto') {
        return { width: imageBoxSize.height.value * (imgSize.width.value / imgSize.height.value), height: imageBoxSize.height.value }
      }
      // 第一个是百分比，第二个是auto，返回宽度按照百分比计算，高度按照图片比例计算
      if (bSize.value.y === 'auto') {
        return { width: imageBoxSize.width.value, height: imageBoxSize.width.value * (imgSize.height.value / imgSize.width.value) }
      }
      // 两个都是百分比，分别按照百分比计算
      if (typeof bSize.value.x === 'number' && typeof bSize.value.y === 'number') {
        return { width: imageBoxSize.width.value * bSize.value.x, height: imageBoxSize.height.value * bSize.value.y }
      }
    }
    console.warn('resultSize 错误')
    return { width: 0, height: 0 }
  }
})

const direction = computed(() => layer.value.direction ?? defaultDirection)

const imageBoxStyle = computed(() => {
  const repeat = layer.value.repeat ?? defaultRepeat
  const style: CSSProperties = {
    backgroundImage: `url(${imgPath.value})`,
    backgroundRepeat: repeat,
    backgroundSize: bSizeStyle.value,
    backgroundPosition: `${layer.value.posX ?? defaultPosition}% ${layer.value.posY ?? defaultPosition}%`,
  }

  const mode = layer.value.mode ?? defaultMode
  if (['none', 'contain'].includes(mode) && ['repeat-x', 'repeat-y', 'repeat'].includes(repeat)) {
    /// ----- start 处理动画时的偏移量，实际会根据设置的位置进行纠正
    const posArr = `${style.backgroundPosition}`.split(' ')
    const baseYOffset = posArr[1]
    const baseXOffset = posArr[0]
    /// ----- end
    // 动画
    Object.assign(style, {
      'animationName': `image-box-ani-${direction.value}`,
      'animationDuration': `${layer.value.duration || defaultDuration}s`,
      'animationIterationCount': 'infinite',
      'animationTimingFunction': 'linear',
      'animationPlayState': (isAllowPlay.value || aniTempPlay.value) ? 'running' : 'paused',
      '--base-width': `${layer.value.reverse ? '-' : ''}${resultSize.value.width.toFixed(2)}px`,
      '--base-x-offset': baseXOffset,
      '--base-height': `${layer.value.reverse ? '-' : ''}${resultSize.value.height.toFixed(2)}px`,
      '--base-y-offset': baseYOffset,
    })
  }
  return style
})
function iterationFn() {
  if (promiseResolve) {
    promiseResolve.resolve()
  }
}

onMounted(() => {
  if (isAllowPlay.value) {
    aniTempPlay.value = true
  }
})
</script>

<template>
  <div ref="imageBoxRef" class="image-box" :style="imageBoxStyle" @animationiteration="iterationFn" @dblclick="updateMaterialFn"></div>
</template>

<style scoped lang="scss">
.image-box {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}
</style>

<style lang="scss">
@keyframes image-box-ani-x {
  0% {
    background-position: var(--base-x-offset) var(--base-y-offset);
  }
  100% {
    background-position: calc(var(--base-x-offset) + var(--base-width)) var(--base-y-offset);
  }
}

@keyframes image-box-ani-y {
  0% {
    background-position: var(--base-x-offset) var(--base-y-offset);
  }
  100% {
    background-position: var(--base-x-offset) calc(var(--base-y-offset) + var(--base-height));
  }
}
</style>
