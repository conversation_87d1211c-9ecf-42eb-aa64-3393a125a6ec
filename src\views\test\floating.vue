<script setup lang="ts">
const selectList = ref([
  { label: '哈哈1', value: '1' },
  { label: '哈哈2', value: '2' },
  { label: '哈哈3', value: '3' },
])
const ss = ref('1')
const dialog = ref(false)

function beforeClose(e: MouseEvent) {
  const target = e.target as HTMLElement
  if (target.closest('.el-overlay')) return false
  if (target.closest('.el-dialog')) return false
}
</script>

<template>
  <div class="box">
    <hi-floating placement="bottom-start" :before-close="beforeClose">
      <template #reference>
        <div class="btn">button {{ ss }}</div>
      </template>
      <el-select v-model="ss" class="w-100">
        <el-option
          v-for="item in selectList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
      <el-button @click="dialog = true">打开</el-button>
    </hi-floating>
    <el-dialog v-model="dialog" title="提示" width="30%">
      <div>sfawefafaewf</div>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.box {
  position: relative;
  margin: 100px;
}
.btn {
  width: 100px;
  height: 40px;
  background-color: #409eff;
  color: #fff;
  text-align: center;
  line-height: 40px;
  cursor: pointer;
}
</style>
