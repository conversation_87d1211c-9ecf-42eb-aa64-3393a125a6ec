<script setup lang="ts">
import type { IDesignLotteryIng4 } from './lottery-ing4'
import { getDefaultMaterial, openSelectMaterial, useDataAttr } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'
import { defaultDecorateAnimation, defaultGapColumn, defaultGapRow, defaultHeadSize, maxCout } from './lottery-ing4'

const layer = defineModel<IDesignLotteryIng4>('layer', { required: true })

const headSizeBlind = useDataAttr(layer.value, 'headSize', defaultHeadSize)
const gapColumnBlind = useDataAttr(layer.value, 'gapColumn', defaultGapColumn)
const gapRowBlind = useDataAttr(layer.value, 'gapRow', defaultGapRow)
const decorateAnimationBlind = useDataAttr(layer.value, 'decorateAnimation', defaultDecorateAnimation)

type IType = 'decorateImg'
async function updateMaterialFn(name: IType, isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, `${name}`)
    : await openSelectMaterial('PIC')
  if (result) {
    layer.value[name] = result
  } else if (isReset) {
    delete layer.value[name]
  }
}
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>头像大小</h3>
        <el-input-number v-model="headSizeBlind" v-input-number :max="800" :min="30" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>头像间隔</h3>
      </div>
      <div class="setting-item">
        <p></p>
        <div class="flex">
          <div class="flex">
            <h3>行</h3>
            <el-input-number v-model="gapRowBlind" v-input-number :max="100" :min="5" controls-position="right" />
          </div>
          <div class="ml-10 flex">
            <h3>列</h3>
            <el-input-number v-model="gapColumnBlind" v-input-number :max="100" :min="5" controls-position="right" />
          </div>
        </div>
      </div>
      <div class="setting-item">
        <h3>最多展示头像数量</h3>
        <p class="ml-5">{{ maxCout[layer.uuid] }}</p>
      </div>

      <div class="setting-item mt-5!">
        <h3>头像装饰图</h3>
        <div class="flex items-end">
          <div class="d bgblank" @click="layer.decorateImg = ''"></div>
          <div class="bgblank relative h-50 w-64">
            <MaterialThumbnail @select="updateMaterialFn('decorateImg')" @reset="updateMaterialFn('decorateImg', true)">
              <img v-if="layer.decorateImg" :src="layer.decorateImg" alt="" class="h-full w-full object-cover" />
            </MaterialThumbnail>
          </div>
        </div>
      </div>
      <div class="setting-item">
        <h3>头像装饰图旋转</h3>
        <el-switch v-model="decorateAnimationBlind" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>
