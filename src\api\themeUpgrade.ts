// 错误数据修正, NaN
export function fixNaN(module: string, design: any): any {
  const walk = (layers: any) => {
    for (const layer of layers) {
      for (const key of ['width', 'height', 'left', 'top', 'right', 'bottom']) {
        if (`${layer.style[key]}`.includes('NaN')) {
          delete layer.style[key]
        }
      }

      // 如果 left 和 right 都没有需要补充 left
      if (!layer.style.left && !layer.style.right) {
        layer.style.left = '0px'
      }
      // 如果 top 和 bottom 都没有需要补充 top
      if (!layer.style.top && !layer.style.bottom) {
        layer.style.top = '0px'
      }

      if (layer.type === 'group') {
        walk(layer.layers)
      }
    }
  }
  if (!design.layers) return
  walk(design.layers)
}

// 版本从1 ~ 2
export function upgrade_version2(module: string, design: any): any {
  if (design.version !== 1) return

  const { drafts: [draftsWidth, draftsHeight] } = design.option
  if (!draftsWidth || !draftsHeight) return

  const walk = (layers: any, parentSize: { w: number, h: number }) => {
    let hasPercent = false
    for (const layer of layers) {
      let isPercent = false
      // 处理百分比问题
      let { width, height, top, left } = layer.style
      if (width?.includes('%')) {
        isPercent = true
        width = `${Number.parseFloat(`${width}`) * parentSize.w / 100}px`
        layer.style.width = width
      }
      if (height?.includes('%')) {
        isPercent = true
        height = `${Number.parseFloat(`${height}`) * parentSize.h / 100}px`
        layer.style.height = height
      }
      // left & top 有特殊处理
      if (typeof left === 'string' && left?.includes('%')) {
        isPercent = true
        if (left === '0%') {
          left = '0px'
        } else if (left === '50%') {
          // 父级高度减去自身高度的一半
          left = `${(parentSize.w - Number.parseFloat(`${width}`)) / 2}px`
        } else if (left === '100%') {
          // 父级高度减去自身高度
          left = `${parentSize.w - Number.parseFloat(`${width}`)}px`
        } else {
          console.warn('left 未知的值', left)
        }
        layer.style.left = left
      }
      if (typeof top === 'string' && top?.includes('%')) {
        isPercent = true
        if (top === '0%') {
          top = '0px'
        } else if (top === '50%') {
          // 父级高度减去自身高度的一半
          top = `${(parentSize.h - Number.parseFloat(`${height}`)) / 2}px`
        } else if (top === '100%') {
          // 父级高度减去自身高度
          top = `${parentSize.h - Number.parseFloat(`${height}`)}px`
        } else {
          console.warn('top 未知的值', top)
        }
        layer.style.top = top
      }

      // 如果是group需要重新校准一下group的宽高(之前计算的宽高有误)
      if (layer.type === 'group') {
        let maxW = 0
        let maxH = 0
        for (const child of layer.layers) {
          const { width, height, left, top } = child.style
          if (width && left && !width.includes('%') && !left.includes('%')) {
            maxW = Math.max(maxW, Number.parseFloat(`${width}`) + Number.parseFloat(`${left}`))
          }
          if (height && top && !height.includes('%') && !top.includes('%')) {
            maxH = Math.max(maxH, Number.parseFloat(`${height}`) + Number.parseFloat(`${top}`))
          }
        }
        if (maxW && maxW > Number.parseFloat(`${width}`)) {
          layer.style.width = `${maxW}px`
        }
        if (maxH && maxH > Number.parseFloat(`${height}`)) {
          layer.style.height = `${maxH}px`
        }
      }

      if (layer.type === 'mobile-goldcoin-ing') {
        layer.isPercent = 1
      }

      if (isPercent) {
        layer.isPercent = 1
        hasPercent = true
      }

      if (layer.type === 'group') {
        const groupHasPercent = walk(layer.layers, { w: Number.parseFloat(`${width}`), h: Number.parseFloat(`${height}`) })
        if (!groupHasPercent) {
          for (const child of layer.layers) {
            child.isPercent = 1
          }
        }
      }
    }
    return hasPercent
  }

  if (!design.layers) return
  walk(design.layers, { w: draftsWidth, h: draftsHeight })
  design.version = 2
}

// 版本从2 ~ 3
export function upgrade_version3(module: string, design: any): any {
  if (design.version !== 2) return
  const statusList: string[] = []
  switch (module) {
    case 'microsite':
      statusList.push(...design.status.map((i: any) => i.value))
      break
    case 'performancev3':
      statusList.push('ing')
      break
    default:
      statusList.push('ready', '321', 'ing', 'finish')
  }

  const walk = (layers: any) => {
    for (const layer of layers) {
      // 数钱的钱夹需要维护属性进去
      if (module === 'moneyv3') {
        if (['layer-9bdcyvcdel', 'layer-r6x37mt41n'].includes(layer.uuid)) {
          layer.style.pointerEvents = 'none'
        }
      }

      // 将 hide 转为 show
      if (layer.hide) {
        if (layer.hide.length) {
          // 取反
          layer.show = statusList.filter(i => !layer.hide.includes(i))
        }
        delete layer.hide
      } else {
        // 都显示
      }

      if (layer.type === 'group') {
        walk(layer.layers)
      }
    }
  }
  if (!design.layers) return
  walk(design.layers)
  design.version = 3
}

export function themeUpgrade(data: Promise<any>) {
  return new Promise<any>((resolve) => {
    data.then((theme) => {
      for (const key of ['webContent', 'mobContent']) {
        if (theme?.[key]) {
          const result = JSON.parse(theme[key])
          fixNaN(theme.module, result)
          upgrade_version2(theme.module, result)
          upgrade_version3(theme.module, result)
          if (result) {
            theme[key] = JSON.stringify(result)
          }
        }
      }
      resolve(theme)
    }).catch((error) => {
      console.error('主题升级失败', error)
      resolve(data)
    })
  })
}
