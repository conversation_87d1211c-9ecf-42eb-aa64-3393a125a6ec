import type { ITeamInfo } from '../../__/design/layer/mobile-choose-team/mobile-choose-team.vue'
import type { IRankItem } from '../../__/design/layer/mobile-rankinglist/mobile-rankinglist.vue'
import { getWxUserAvatar, getWxuserName } from './relation'

// 排行榜转换成排行榜组件需要的数据格式，对微信用户、团队进行处理
export function transformRankings({
  rankings = [],
  teamList = [],
  isTeam = false,
}: {
  rankings: { teamId?: number, count?: number, score?: number, ranking?: number, wxUserId?: number }[]
  teamList?: ITeamInfo[]
  isTeam?: boolean
}) {
  if (!rankings) return []
  const list: IRankItem[] = []
  for (const { teamId, count, score, ranking, wxUserId } of (rankings || [])) {
    // 团队模式
    if (isTeam) {
      if (!teamId) continue
      const team = teamList?.find(item => Number(item.id) === teamId)
      if (!team) continue
      const nickName = team.teamName
      const avatarUrl = team.teamHeadImg
      list.push({ nickName, avatarUrl, ranking: count || score || ranking || 0, id: teamId })
    } else {
      if (!wxUserId) continue
      const nickName = getWxuserName(wxUserId)
      const avatarUrl = getWxUserAvatar(wxUserId)
      // 摇一摇是count、接金币是 score
      list.push({ nickName, avatarUrl, ranking: count || score || ranking || 0, id: wxUserId })
    }
  }
  // 排序
  list.sort((a, b) => b.ranking - a.ranking)
  return list
}
