import { HiRequest } from '../request'

export default {
  // 官方标签
  list: (params: any) => HiRequest.post('/pro/hxc/promateriallabel/list.htm', params),
  add: (params: any) => HiRequest.post('/pro/hxc/promateriallabel/add.htm', params),
  update: (params: any) => HiRequest.post('/pro/hxc/promateriallabel/update.htm', params),
  delete: (params: any) => HiRequest.post('/pro/hxc/promateriallabel/delete.htm', params),
  read: (params: any) => HiRequest.post('/pro/hxc/promateriallabel/read.htm', params),
  page: (params: any) => HiRequest.post('/pro/hxc/promateriallabel/page.htm', params),

  // 我的标签
  listOwn: (params: any) => HiRequest.post('/pro/hxc/promateriallabelown/list.htm', params),
  pageOwn: (params: any) => HiRequest.post('/pro/hxc/promateriallabelown/page.htm', params),
  updateOwn: (params: any) => HiRequest.post('/pro/hxc/promateriallabelown/update.htm', params),
  deleteOwn: (params: any) => HiRequest.post('/pro/hxc/promateriallabelown/delete.htm', params),
  readOwn: (params: any) => HiRequest.post('/pro/hxc/promateriallabelown/read.htm', params),
  batchOwn: (params: any) => HiRequest.post('/pro/hxc/promateriallabelown/batch.htm', params),
  addOwn: (params: any) => HiRequest.post('/pro/hxc/promateriallabelown/add.htm', params),
}
