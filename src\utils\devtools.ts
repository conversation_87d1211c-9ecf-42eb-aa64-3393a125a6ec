import { addListener, launch } from 'devtools-detector'
import { debugState } from './debug'

const view = document.createElement('div')
document.body.appendChild(view)

addListener((v) => {
  if (v) {
    try {
      window.open('about:blank', '_self')
    } catch (e) {
      console.error(e)
      const btn = document.createElement('button')
      btn.onclick = function () {
        window.open('about:blank', '_self')
      }
      btn.click()
    }
  }
})
const isDebug = location.href.includes('isDebug')
if (!isDebug && !debugState?.value?.debug) {
  launch()
}
