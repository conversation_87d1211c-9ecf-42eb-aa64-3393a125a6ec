<script setup lang="ts">
import { remove } from 'lodash-es'
import { hasAuth } from '~/src/utils/auth'
import { isLayerDisable, isLockedInHierarchy, useDesignData, useDesignState, useDesignTemp } from '..'
import { createComponent } from './createLayer'
import { canMergeToGroup, canSplitGroup, mergeToGroup, splitGroup } from './mergeSplit'

const designData = useDesignData()
const designTemp = useDesignTemp()
const designState = useDesignState()

const currentLayers = computed(() => {
  return designData.getLayerByUuids(designTemp.activeList)
})

function onGroup() {
  if (canSplitGroup.value) {
    splitGroup()
  } else if (canMergeToGroup.value) {
    mergeToGroup()
  } else {
    createComponent('group')
  }
}

// 全部都是锁定，才显示解锁🔓
const showUnLock = computed(() => {
  let show = true
  currentLayers.value.forEach((layer) => {
    if (!layer.lock) {
      show = false
    }
  })
  return show
})

function onLock() {
  currentLayers.value.forEach((layer) => {
    layer.lock = true
  })
}

function onUnlock() {
  currentLayers.value.forEach((layer) => {
    // 如果父级被锁定，不允许修改子级
    if (!layer.lock && isLockedInHierarchy(layer)) {

    } else {
      layer.lock = false
    }
  })
}

// 显示隐藏
const showStatusHide = computed(() => {
  let show = false
  currentLayers.value.forEach((layer) => {
    if (isLayerDisable(layer))
      return
    if (!layer.show) {
      return
    }
    if (layer.show.includes(designState.status)) {
      show = true
    }
  })
  return show
})

function onStatusHide() {
  currentLayers.value.forEach((layer) => {
    if (isLayerDisable(layer))
      return
    if (!layer.show) {
      layer.show = []
      return
    }
    if (layer.show.includes(designState.status)) {
      remove(layer.show, v => v === designState.status)
    }
  })
}

function onStatusShow() {
  currentLayers.value.forEach((layer) => {
    if (isLayerDisable(layer))
      return
    if (!layer.show) {
      layer.show = []
      return
    }
    if (!layer.show.includes(designState.status)) {
      layer.show.push(designState.status)
    }
  })
}

// 普通用户可见性
const showUnVisible = computed(() => {
  let show = true
  currentLayers.value.forEach((layer) => {
    if (!layer.spoVisible && layer.type !== 'group') {
      show = false
    }
  })
  return show
})

function onVisible() {
  currentLayers.value.forEach((layer) => {
    if (layer.type !== 'group') {
      layer.spoVisible = true
    }
  })
}

function onUnVisible() {
  currentLayers.value.forEach((layer) => {
    if (layer.type !== 'group') {
      layer.spoVisible = false
    }
  })
}

// 是否显示展开
const showUnFold = ref(false)

const showFoldAll = computed(() => {
  return designData.layers.some((layer) => {
    return layer.type === 'group'
  })
})

const foldBus = useEventBus('FOLD_BUS')

function unFoldAll() {
  foldBus.emit('unfold', 'ALL')
  showUnFold.value = !showUnFold.value
}

function foldAll() {
  foldBus.emit('fold', 'ALL')
  showUnFold.value = !showUnFold.value
}
</script>

<template>
  <div>
    <div class="flex flex-row items-center justify-center gap-14 b-b-1px b-b-#ccc b-b-solid bg-white px-10px py-10px text-#555">
      <el-tooltip
        :hide-after="0"
        :content="showStatusHide ? '隐藏' : '显示'"
        placement="top"
        size="small"
      >
        <icon-ph-eye-bold v-if="showStatusHide" @click="onStatusHide" />
        <icon-ph-eye-slash-bold
          v-else
          @click="onStatusShow"
        />
      </el-tooltip>

      <el-tooltip
        :hide-after="0"
        :content="canSplitGroup ? '拆分' : canMergeToGroup ? '组合' : '创建分组'"
        placement="top"
        class="text-12px"
        size="small"
      >
        <icon-ph-folder-bold @click="onGroup" />
      </el-tooltip>

      <el-tooltip
        :hide-after="0"
        :content="showUnLock ? '解锁' : '锁定'"
        placement="top"
        class="text-12px"
        size="small"
      >
        <icon-ph:lock-key-fill v-if="showUnLock" @click="onUnlock" />
        <icon-ph:lock-open-bold v-else @click="onLock" />
      </el-tooltip>

      <!-- 普通用户可见 -->
      <template v-if=" hasAuth('man')">
        <el-tooltip
          :hide-after="0"
          :content="showUnVisible ? '普通用户隐藏' : '普通用户显示'"
          placement="top"
          class="text-12px"
          size="small"
        >
          <icon-ph:user-fill v-if="showUnVisible" @click="onUnVisible" />
          <icon-ph:user v-else @click="onVisible" />>
        </el-tooltip>
      </template>

      <el-tooltip
        v-if="showFoldAll "
        :hide-after="0"
        :content="showUnFold ? '一键展开' : '一键折叠'"
        placement="top"
        class="text-12px"
        size="small"
      >
        <icon-ic:twotone-unfold-less v-if="showUnFold" @click="unFoldAll" />
        <icon-ic:twotone-unfold-more v-else @click="foldAll" />
      </el-tooltip>
    </div>

    <!-- 搜索，可参考masterGo支持基于类型、图层名称搜索 -->
    <div v-if="false" class="flex items-center gap-10">
      <div class="flex items-center">
        <icon-ph:magnifying-glass-light />
        <icon-ph:arrow-down-bold />
      </div>
      <el-input type="text" placeholder="搜索图层" />
    </div>
  </div>
</template>
