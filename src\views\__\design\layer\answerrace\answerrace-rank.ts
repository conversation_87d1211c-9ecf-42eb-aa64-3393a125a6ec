import { BisTypes, useDesignState } from '../..'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './answerrace-rank-setting.vue'
import Comp from './answerrace-rank.vue'

// 类型
export const type = 'answerrace-rank'
export const defaultData = {
  showCount: 10,
  titleFontSize: 18,
  titleColor: '#fec500',
  sortFontSize: 16,
  sortColor: '#fec500',
  headerSize: 50,
  contentTextAlign: 'center',
  contentColor: '#fff',
  contentFontSize: 16,
}
export interface IDesignAnswerraceRank extends IDesignLayer {
  type: typeof type
  showCount?: number
  titleFontSize?: number
  titleColor?: string
  contentTextAlign?: string
  contentFontSize?: number
  contentColor?: string
  sortFontSize?: number
  sortColor?: string
  headerSize?: number
}

export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.rankingList,
    showType: ['pcwall'],
    showInteractive: [InteractiveEnum.answerracev3],
    thumbnail: new URL('./answerrace-rank.png', import.meta.url).href,
    type,
    name: '排行榜',
    status: ['finish', 'winlist'],
    Comp,
    CompSetting,
    defaultData() {
      return {
        uuid: layerUuid(),
        name: '排行榜',
        type,
        data: [],
        style: {
          width: '1000px',
          height: '700px',
          top: '50px',
          left: '140px',
        },
      }
    },
  })
}
