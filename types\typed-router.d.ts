/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'vue-router'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    '/': RouteRecordInfo<'/', '/', Record<never, never>, Record<never, never>>,
    'NotFound': RouteRecordInfo<'NotFound', '/:all(.*)', { all: ParamValue<true> }, { all: ParamValue<false> }>,
    '/admin': RouteRecordInfo<'/admin', '/admin', Record<never, never>, Record<never, never>>,
    '/admin/': RouteRecordInfo<'/admin/', '/admin', Record<never, never>, Record<never, never>>,
    '/admin/user/login': RouteRecordInfo<'/admin/user/login', '/admin/user/login', Record<never, never>, Record<never, never>>,
    '/admin/wall': RouteRecordInfo<'/admin/wall', '/admin/wall', Record<never, never>, Record<never, never>>,
    '/admin/wall/answerracev3/': RouteRecordInfo<'/admin/wall/answerracev3/', '/admin/wall/answerracev3', Record<never, never>, Record<never, never>>,
    '/admin/wall/diglettv3/': RouteRecordInfo<'/admin/wall/diglettv3/', '/admin/wall/diglettv3', Record<never, never>, Record<never, never>>,
    '/admin/wall/firev3/': RouteRecordInfo<'/admin/wall/firev3/', '/admin/wall/firev3', Record<never, never>, Record<never, never>>,
    '/admin/wall/goldcoinv3/': RouteRecordInfo<'/admin/wall/goldcoinv3/', '/admin/wall/goldcoinv3', Record<never, never>, Record<never, never>>,
    '/admin/wall/listlotteryv3/': RouteRecordInfo<'/admin/wall/listlotteryv3/', '/admin/wall/listlotteryv3', Record<never, never>, Record<never, never>>,
    '/admin/wall/lotteryv3/': RouteRecordInfo<'/admin/wall/lotteryv3/', '/admin/wall/lotteryv3', Record<never, never>, Record<never, never>>,
    '/admin/wall/microsite/': RouteRecordInfo<'/admin/wall/microsite/', '/admin/wall/microsite', Record<never, never>, Record<never, never>>,
    '/admin/wall/microsite/preview': RouteRecordInfo<'/admin/wall/microsite/preview', '/admin/wall/microsite/preview', Record<never, never>, Record<never, never>>,
    '/admin/wall/moneyv3/': RouteRecordInfo<'/admin/wall/moneyv3/', '/admin/wall/moneyv3', Record<never, never>, Record<never, never>>,
    '/admin/wall/padsign/': RouteRecordInfo<'/admin/wall/padsign/', '/admin/wall/padsign', Record<never, never>, Record<never, never>>,
    '/admin/wall/padsign/record': RouteRecordInfo<'/admin/wall/padsign/record', '/admin/wall/padsign/record', Record<never, never>, Record<never, never>>,
    '/admin/wall/padsign/setting': RouteRecordInfo<'/admin/wall/padsign/setting', '/admin/wall/padsign/setting', Record<never, never>, Record<never, never>>,
    '/admin/wall/performancev3/': RouteRecordInfo<'/admin/wall/performancev3/', '/admin/wall/performancev3', Record<never, never>, Record<never, never>>,
    '/admin/wall/piclotteryv3/': RouteRecordInfo<'/admin/wall/piclotteryv3/', '/admin/wall/piclotteryv3', Record<never, never>, Record<never, never>>,
    '/admin/wall/seglottery/': RouteRecordInfo<'/admin/wall/seglottery/', '/admin/wall/seglottery', Record<never, never>, Record<never, never>>,
    '/admin/wall/shakev3/': RouteRecordInfo<'/admin/wall/shakev3/', '/admin/wall/shakev3', Record<never, never>, Record<never, never>>,
    '/manage': RouteRecordInfo<'/manage', '/manage', Record<never, never>, Record<never, never>>,
    '/manage/': RouteRecordInfo<'/manage/', '/manage', Record<never, never>, Record<never, never>>,
    '/manage/user/login': RouteRecordInfo<'/manage/user/login', '/manage/user/login', Record<never, never>, Record<never, never>>,
    '/manage/wall': RouteRecordInfo<'/manage/wall', '/manage/wall', Record<never, never>, Record<never, never>>,
    '/manage/wall/answerracev3/': RouteRecordInfo<'/manage/wall/answerracev3/', '/manage/wall/answerracev3', Record<never, never>, Record<never, never>>,
    '/manage/wall/diglettv3/': RouteRecordInfo<'/manage/wall/diglettv3/', '/manage/wall/diglettv3', Record<never, never>, Record<never, never>>,
    '/manage/wall/firev3/': RouteRecordInfo<'/manage/wall/firev3/', '/manage/wall/firev3', Record<never, never>, Record<never, never>>,
    '/manage/wall/goldcoinv3/': RouteRecordInfo<'/manage/wall/goldcoinv3/', '/manage/wall/goldcoinv3', Record<never, never>, Record<never, never>>,
    '/manage/wall/listlotteryv3/': RouteRecordInfo<'/manage/wall/listlotteryv3/', '/manage/wall/listlotteryv3', Record<never, never>, Record<never, never>>,
    '/manage/wall/lotteryv3/': RouteRecordInfo<'/manage/wall/lotteryv3/', '/manage/wall/lotteryv3', Record<never, never>, Record<never, never>>,
    '/manage/wall/microsite/': RouteRecordInfo<'/manage/wall/microsite/', '/manage/wall/microsite', Record<never, never>, Record<never, never>>,
    '/manage/wall/moneyv3/': RouteRecordInfo<'/manage/wall/moneyv3/', '/manage/wall/moneyv3', Record<never, never>, Record<never, never>>,
    '/manage/wall/performancev3/': RouteRecordInfo<'/manage/wall/performancev3/', '/manage/wall/performancev3', Record<never, never>, Record<never, never>>,
    '/manage/wall/piclotteryv3/': RouteRecordInfo<'/manage/wall/piclotteryv3/', '/manage/wall/piclotteryv3', Record<never, never>, Record<never, never>>,
    '/manage/wall/seglottery/': RouteRecordInfo<'/manage/wall/seglottery/', '/manage/wall/seglottery', Record<never, never>, Record<never, never>>,
    '/manage/wall/shakev3/': RouteRecordInfo<'/manage/wall/shakev3/', '/manage/wall/shakev3', Record<never, never>, Record<never, never>>,
    '/mobile': RouteRecordInfo<'/mobile', '/mobile', Record<never, never>, Record<never, never>>,
    '/mobile/': RouteRecordInfo<'/mobile/', '/mobile', Record<never, never>, Record<never, never>>,
    '/mobile/common/debug': RouteRecordInfo<'/mobile/common/debug', '/mobile/common/debug', Record<never, never>, Record<never, never>>,
    '/mobile/common/login': RouteRecordInfo<'/mobile/common/login', '/mobile/common/login', Record<never, never>, Record<never, never>>,
    '/mobile/common/transfer-route': RouteRecordInfo<'/mobile/common/transfer-route', '/mobile/common/transfer-route', Record<never, never>, Record<never, never>>,
    '/mobile/iframe/[model]': RouteRecordInfo<'/mobile/iframe/[model]', '/mobile/iframe/:model', { model: ParamValue<true> }, { model: ParamValue<false> }>,
    '/mobile/wall': RouteRecordInfo<'/mobile/wall', '/mobile/wall', Record<never, never>, Record<never, never>>,
    '/mobile/wall/answerracev3/': RouteRecordInfo<'/mobile/wall/answerracev3/', '/mobile/wall/answerracev3', Record<never, never>, Record<never, never>>,
    '/mobile/wall/applysign/': RouteRecordInfo<'/mobile/wall/applysign/', '/mobile/wall/applysign', Record<never, never>, Record<never, never>>,
    '/mobile/wall/bridge-old': RouteRecordInfo<'/mobile/wall/bridge-old', '/mobile/wall/bridge-old', Record<never, never>, Record<never, never>>,
    '/mobile/wall/common/error': RouteRecordInfo<'/mobile/wall/common/error', '/mobile/wall/common/error', Record<never, never>, Record<never, never>>,
    '/mobile/wall/common/limit': RouteRecordInfo<'/mobile/wall/common/limit', '/mobile/wall/common/limit', Record<never, never>, Record<never, never>>,
    '/mobile/wall/common/location': RouteRecordInfo<'/mobile/wall/common/location', '/mobile/wall/common/location', Record<never, never>, Record<never, never>>,
    '/mobile/wall/copmponents/brand-advertising': RouteRecordInfo<'/mobile/wall/copmponents/brand-advertising', '/mobile/wall/copmponents/brand-advertising', Record<never, never>, Record<never, never>>,
    '/mobile/wall/diglettv3/': RouteRecordInfo<'/mobile/wall/diglettv3/', '/mobile/wall/diglettv3', Record<never, never>, Record<never, never>>,
    '/mobile/wall/firev3/': RouteRecordInfo<'/mobile/wall/firev3/', '/mobile/wall/firev3', Record<never, never>, Record<never, never>>,
    '/mobile/wall/goldcoinv3/': RouteRecordInfo<'/mobile/wall/goldcoinv3/', '/mobile/wall/goldcoinv3', Record<never, never>, Record<never, never>>,
    '/mobile/wall/listlotteryv3/': RouteRecordInfo<'/mobile/wall/listlotteryv3/', '/mobile/wall/listlotteryv3', Record<never, never>, Record<never, never>>,
    '/mobile/wall/lotteryv3/': RouteRecordInfo<'/mobile/wall/lotteryv3/', '/mobile/wall/lotteryv3', Record<never, never>, Record<never, never>>,
    '/mobile/wall/microsite/': RouteRecordInfo<'/mobile/wall/microsite/', '/mobile/wall/microsite', Record<never, never>, Record<never, never>>,
    '/mobile/wall/moneyv3/': RouteRecordInfo<'/mobile/wall/moneyv3/', '/mobile/wall/moneyv3', Record<never, never>, Record<never, never>>,
    '/mobile/wall/msg/': RouteRecordInfo<'/mobile/wall/msg/', '/mobile/wall/msg', Record<never, never>, Record<never, never>>,
    '/mobile/wall/performancev3/': RouteRecordInfo<'/mobile/wall/performancev3/', '/mobile/wall/performancev3', Record<never, never>, Record<never, never>>,
    '/mobile/wall/piclotteryv3/': RouteRecordInfo<'/mobile/wall/piclotteryv3/', '/mobile/wall/piclotteryv3', Record<never, never>, Record<never, never>>,
    '/mobile/wall/seglottery/': RouteRecordInfo<'/mobile/wall/seglottery/', '/mobile/wall/seglottery', Record<never, never>, Record<never, never>>,
    '/mobile/wall/shakev3/': RouteRecordInfo<'/mobile/wall/shakev3/', '/mobile/wall/shakev3', Record<never, never>, Record<never, never>>,
    '/mobile/wall/transfer-route': RouteRecordInfo<'/mobile/wall/transfer-route', '/mobile/wall/transfer-route', Record<never, never>, Record<never, never>>,
    '/mobile/wxuser/login': RouteRecordInfo<'/mobile/wxuser/login', '/mobile/wxuser/login', Record<never, never>, Record<never, never>>,
    '/oem': RouteRecordInfo<'/oem', '/oem', Record<never, never>, Record<never, never>>,
    '/oem/': RouteRecordInfo<'/oem/', '/oem', Record<never, never>, Record<never, never>>,
    '/oem/user/login': RouteRecordInfo<'/oem/user/login', '/oem/user/login', Record<never, never>, Record<never, never>>,
    '/oem/wall': RouteRecordInfo<'/oem/wall', '/oem/wall', Record<never, never>, Record<never, never>>,
    '/oem/wall/answerracev3/': RouteRecordInfo<'/oem/wall/answerracev3/', '/oem/wall/answerracev3', Record<never, never>, Record<never, never>>,
    '/oem/wall/diglettv3/': RouteRecordInfo<'/oem/wall/diglettv3/', '/oem/wall/diglettv3', Record<never, never>, Record<never, never>>,
    '/oem/wall/firev3/': RouteRecordInfo<'/oem/wall/firev3/', '/oem/wall/firev3', Record<never, never>, Record<never, never>>,
    '/oem/wall/goldcoinv3/': RouteRecordInfo<'/oem/wall/goldcoinv3/', '/oem/wall/goldcoinv3', Record<never, never>, Record<never, never>>,
    '/oem/wall/listlotteryv3/': RouteRecordInfo<'/oem/wall/listlotteryv3/', '/oem/wall/listlotteryv3', Record<never, never>, Record<never, never>>,
    '/oem/wall/lotteryv3/': RouteRecordInfo<'/oem/wall/lotteryv3/', '/oem/wall/lotteryv3', Record<never, never>, Record<never, never>>,
    '/oem/wall/microsite/': RouteRecordInfo<'/oem/wall/microsite/', '/oem/wall/microsite', Record<never, never>, Record<never, never>>,
    '/oem/wall/moneyv3/': RouteRecordInfo<'/oem/wall/moneyv3/', '/oem/wall/moneyv3', Record<never, never>, Record<never, never>>,
    '/oem/wall/performancev3/': RouteRecordInfo<'/oem/wall/performancev3/', '/oem/wall/performancev3', Record<never, never>, Record<never, never>>,
    '/oem/wall/piclotteryv3/': RouteRecordInfo<'/oem/wall/piclotteryv3/', '/oem/wall/piclotteryv3', Record<never, never>, Record<never, never>>,
    '/oem/wall/seglottery/': RouteRecordInfo<'/oem/wall/seglottery/', '/oem/wall/seglottery', Record<never, never>, Record<never, never>>,
    '/oem/wall/shakev3/': RouteRecordInfo<'/oem/wall/shakev3/', '/oem/wall/shakev3', Record<never, never>, Record<never, never>>,
    '/pcwall': RouteRecordInfo<'/pcwall', '/pcwall', Record<never, never>, Record<never, never>>,
    '/pcwall/': RouteRecordInfo<'/pcwall/', '/pcwall', Record<never, never>, Record<never, never>>,
    '/pcwall/answerracev3/': RouteRecordInfo<'/pcwall/answerracev3/', '/pcwall/answerracev3', Record<never, never>, Record<never, never>>,
    '/pcwall/diglettv3/': RouteRecordInfo<'/pcwall/diglettv3/', '/pcwall/diglettv3', Record<never, never>, Record<never, never>>,
    '/pcwall/firev3/': RouteRecordInfo<'/pcwall/firev3/', '/pcwall/firev3', Record<never, never>, Record<never, never>>,
    '/pcwall/goldcoinv3/': RouteRecordInfo<'/pcwall/goldcoinv3/', '/pcwall/goldcoinv3', Record<never, never>, Record<never, never>>,
    '/pcwall/iframe/[model]': RouteRecordInfo<'/pcwall/iframe/[model]', '/pcwall/iframe/:model', { model: ParamValue<true> }, { model: ParamValue<false> }>,
    '/pcwall/listlotteryv3/': RouteRecordInfo<'/pcwall/listlotteryv3/', '/pcwall/listlotteryv3', Record<never, never>, Record<never, never>>,
    '/pcwall/lotteryv3/': RouteRecordInfo<'/pcwall/lotteryv3/', '/pcwall/lotteryv3', Record<never, never>, Record<never, never>>,
    '/pcwall/microsite/': RouteRecordInfo<'/pcwall/microsite/', '/pcwall/microsite', Record<never, never>, Record<never, never>>,
    '/pcwall/moneyv3/': RouteRecordInfo<'/pcwall/moneyv3/', '/pcwall/moneyv3', Record<never, never>, Record<never, never>>,
    '/pcwall/padsign/': RouteRecordInfo<'/pcwall/padsign/', '/pcwall/padsign', Record<never, never>, Record<never, never>>,
    '/pcwall/padsign/pad': RouteRecordInfo<'/pcwall/padsign/pad', '/pcwall/padsign/pad', Record<never, never>, Record<never, never>>,
    '/pcwall/performancev3/': RouteRecordInfo<'/pcwall/performancev3/', '/pcwall/performancev3', Record<never, never>, Record<never, never>>,
    '/pcwall/piclotteryv3/': RouteRecordInfo<'/pcwall/piclotteryv3/', '/pcwall/piclotteryv3', Record<never, never>, Record<never, never>>,
    '/pcwall/seglottery/': RouteRecordInfo<'/pcwall/seglottery/', '/pcwall/seglottery', Record<never, never>, Record<never, never>>,
    '/pcwall/shakev3/': RouteRecordInfo<'/pcwall/shakev3/', '/pcwall/shakev3', Record<never, never>, Record<never, never>>,
    '/test': RouteRecordInfo<'/test', '/test', Record<never, never>, Record<never, never>>,
    '/test/': RouteRecordInfo<'/test/', '/test', Record<never, never>, Record<never, never>>,
    '/test/animate': RouteRecordInfo<'/test/animate', '/test/animate', Record<never, never>, Record<never, never>>,
    '/test/color': RouteRecordInfo<'/test/color', '/test/color', Record<never, never>, Record<never, never>>,
    '/test/design': RouteRecordInfo<'/test/design', '/test/design', Record<never, never>, Record<never, never>>,
    '/test/design_syl': RouteRecordInfo<'/test/design_syl', '/test/design_syl', Record<never, never>, Record<never, never>>,
    '/test/floating': RouteRecordInfo<'/test/floating', '/test/floating', Record<never, never>, Record<never, never>>,
    '/test/input-number': RouteRecordInfo<'/test/input-number', '/test/input-number', Record<never, never>, Record<never, never>>,
    '/test/open/': RouteRecordInfo<'/test/open/', '/test/open', Record<never, never>, Record<never, never>>,
    '/test/performancev3': RouteRecordInfo<'/test/performancev3', '/test/performancev3', Record<never, never>, Record<never, never>>,
    '/test/quill': RouteRecordInfo<'/test/quill', '/test/quill', Record<never, never>, Record<never, never>>,
    '/test/swiper': RouteRecordInfo<'/test/swiper', '/test/swiper', Record<never, never>, Record<never, never>>,
    '/test/test': RouteRecordInfo<'/test/test', '/test/test', Record<never, never>, Record<never, never>>,
    '/test/test1': RouteRecordInfo<'/test/test1', '/test/test1', Record<never, never>, Record<never, never>>,
    '/test/time': RouteRecordInfo<'/test/time', '/test/time', Record<never, never>, Record<never, never>>,
    '/test/use-data-attr': RouteRecordInfo<'/test/use-data-attr', '/test/use-data-attr', Record<never, never>, Record<never, never>>,
  }
}
