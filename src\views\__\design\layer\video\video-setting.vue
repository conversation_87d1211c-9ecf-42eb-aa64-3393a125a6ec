<script setup lang="ts">
import { openSelectMaterial, useDataAttr } from '../..'
import { defaultControls, type IDesignVideo } from './video'

const layer = defineModel<IDesignVideo>('layer', { required: true })

async function updateMaterialFn() {
  const result = await openSelectMaterial('VIDEO')
  if (result) {
    layer.value.data = result
  }
}

type IObjectFit = 'none' | 'fill' | 'contain' | 'cover'
const objectFits: { label: string, value: IObjectFit }[] = [
  { label: '无', value: 'none' },
  { label: '拉伸', value: 'fill' },
  { label: '适应', value: 'contain' },
  { label: '裁剪', value: 'cover' },
]

const objectFitBind = useDataAttr(layer.value.style, 'objectFit', 'fill')
const controlsBind = useDataAttr(layer.value, 'controls', defaultControls)
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <div class="thumbnail-box bgblank" @click="updateMaterialFn">
          <video
            :src="layer.data"
            :loop="layer.loop"
            :autoplay="layer.autoplay"
            :muted="layer.muted"
          ></video>
        </div>
      </div>
      <div class="setting-item">
        <h3>显示模式</h3>
        <el-select v-model="objectFitBind" placeholder="选择填充方式" class="w-100">
          <el-option v-for="item in objectFits" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="setting-item">
        <h3>静音</h3>
        <el-switch v-model="layer.muted" active-color="#409EFF" inactive-color="#C0CCDA" />
      </div>
      <div class="setting-item">
        <h3 class="flex items-center">
          <span>自动播放</span>
          <el-tooltip content="自动播放需要浏览器支持" placement="top">
            <icon-ph-question-bold class="ml-5" />
          </el-tooltip>
        </h3>
        <el-switch v-model="layer.autoplay" active-color="#409EFF" inactive-color="#C0CCDA" />
      </div>
      <div class="setting-item">
        <h3>循环播放</h3>
        <el-switch v-model="layer.loop" active-color="#409EFF" inactive-color="#C0CCDA" />
      </div>
      <div class="setting-item">
        <h3>控制栏</h3>
        <el-switch v-model="controlsBind" active-color="#409EFF" inactive-color="#C0CCDA" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.thumbnail-box {
  width: 120px;
  height: 120px;
  outline: 1px solid #e6ebed;
  border-radius: 4px;
  position: relative;

  video {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    object-fit: contain;
  }
}
</style>
