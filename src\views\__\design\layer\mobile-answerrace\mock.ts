import type { ProAnswerracev3 } from '~/types/wall/answerracev3'

// 团队/个人、报名/未报名、
interface ReadyMock {
  mockState: 'roundStarted' | 'waitingToStart'
  mockJoinType: ProAnswerracev3['joinType']
}
export const readyMockConfig = ref<ReadyMock>({
  mockState: 'waitingToStart',
  mockJoinType: 'TEAM',
})

// 流程（321 / 看题 / 答题 / 查看答案）、答题状态(仅查看答案生效)(正确 / 错误 / 淘汰)、题目类型(图 / 文 / 视频 / 音频)、答案类型(图 / 文 / 图 + 文)
interface IngMock {
  userAnswerStatus: 'correct' | 'incorrect' | 'out'
  questionFlowStatus: '321' | 'preview' | 'answer' | 'result'
  questionType: 'IMAGE' | 'TEXT' | 'VIDEO' | 'AUDIO'
  optionType: 'TEXT' | 'IMAGE_TEXT'
}
export const ingMockConfig = ref<IngMock>({
  userAnswerStatus: 'correct',
  questionFlowStatus: 'answer',
  questionType: 'IMAGE',
  optionType: 'TEXT',
})
