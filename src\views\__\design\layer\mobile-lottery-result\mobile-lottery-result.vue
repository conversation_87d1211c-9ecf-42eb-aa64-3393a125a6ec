<script setup lang="ts">
import { defineCustomEmits, useDesignState, useDesignTemp } from '../../index'
import { DEFAULT_DATA, type IDesignMobileLotteryResult } from './mobile-lottery-result'

const layer = defineModel<IDesignMobileLotteryResult>('layer', { required: true })

const designTemp = useDesignTemp()
const designState = useDesignState()
const customEmits = defineCustomEmits(layer)

const data = computed(() => {
  return {
    ...DEFAULT_DATA,
    ...layer.value.data,
  }
})

const rootVars = computed(() => {
  return {
    '--background-color': data.value.backgroundColor,
  }
})

interface DataItem {
  id?: string
  image?: string
  text: string[] | string
}

const roundName = computed(() => {
  if (designTemp.isEdit) {
    return '奖项名称'
  }
  return designState.getLayerData('#轮次标题#')
})
const awardsName = computed(() => {
  if (designTemp.isEdit) {
    return '奖品名称'
  }
  return designState.getLayerData('awardsName')
})
const recordLottery = computed<DataItem[]>(() => designState.getLayerData('recordLottery') || [])

function onSearch() {
  if (designTemp.isEdit) {
    return
  }
  customEmits('loadAwardsRecord')
}
</script>

<template>
  <div class="mobile-lottery-result-box size-full overflow-hidden py-20px text-16px" :style="rootVars">
    <div class="flex items-center justify-between px-6% text-#333 font-bold">
      <p class="m-0 w-50% break-all">{{ roundName }}</p>
      <p class="m-0 max-w-50% break-all">{{ awardsName }}</p>
    </div>
    <div v-if="'%recordLotterySearchVal%' in designState.layerData" class="my-14px flex items-center px-4%">
      <input v-model.trim="designState.layerData['%recordLotterySearchVal%']" type="text" placeholder="请输入信息" class="mr-10px h-full w-full flex-1 rd-20px border-none bg-#F3F3F3 px-20px py-6px text-#333">
      <button class="btn-search" @click="onSearch">查询</button>
    </div>
    <div class="h-full w-full overflow-auto px-4% pb-100px pt-20px">
      <template v-if="!recordLottery.length">
        <p class="py-40px text-center">暂无数据</p>
      </template>
      <div v-else class="flex flex-wrap items-center justify-between">
        <div
          v-for="item in recordLottery"
          :key="item.id"
          class="mb-10px w-46% flex flex-col items-center justify-center"
        >
          <img v-if="item.image" :src="item.image" alt="" class="mb-10px h-140px w-full rd-8px bg-#ccc object-contain">
          <p v-for="t in (Array.isArray(item.text) ? item.text : [item.text])" :key="t" class="whitespace-pre-wrap break-all">
            {{ t }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.btn-search {
  color: white;
  border-radius: 20px;
  outline: none;
  padding: 8px 22px;
  background-color: var(--background-color, #ff0c0c);
}
</style>
