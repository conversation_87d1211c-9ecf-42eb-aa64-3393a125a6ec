<script setup lang="ts">
import HiDesign from 'design/index.vue'
import { randomAvatar } from '~/src/utils'
import { useDesignState, useDesignTemp } from '../__/design'
import { ModeEnum } from '../__/design/types'

definePage({ meta: { label: '调试' } })

const designTemp = useDesignTemp()
const designState = useDesignState()

const interactive = 'performancev3'
designTemp.theme = {
  id: -1,
  name: '调试主题',
  module: interactive,
  themeImg: '',
  webContent: JSON.stringify({ version: 1, option: { drafts: [1280, 800] }, style: {}, layers: [], music: [] }),
  mobContent: JSON.stringify({
    version: 1,
    option: {
      drafts: [375, 820],
    },
    style: {},
    layers: [
    ],
  }),
}

designState
  .setStatusList([
    { label: '进行中', value: 'ing' },
  ])
  .setLayerData({
    ingRankings: computed(() => {
      return [{
        name: '张三',
        avatar: randomAvatar(),
        score: 100,
      }, {
        name: '李四',
        avatar: randomAvatar(),
        score: 90,
      }, {
        name: '王五',
        avatar: randomAvatar(),
        score: 80,
      }, {
        name: '赵六',
        avatar: randomAvatar(),
        score: 70,
      }, {
        name: '孙七',
        avatar: randomAvatar(),
        score: 60,
      }, {
        name: '周八',
        avatar: randomAvatar(),
        score: 50,
      }, {
        name: '吴九',
        avatar: randomAvatar(),
        score: 40,
      }, {
        name: '郑十',
        avatar: randomAvatar(),
        score: 30,
      }, {
        name: '钱十一',
        avatar: randomAvatar(),
        score: 20,
      }, {
        name: '孔十二',
        avatar: randomAvatar(),
        score: 10,
      }]
    }),
  })

designState.status = 'ing'
designTemp.mode = ModeEnum.edit
designTemp.showType = 'pcwall'
</script>

<template>
  <HiDesign :interactive="interactive" />
</template>

<style scoped lang="scss">
</style>
