import type { IDesignLayer, IDesignSetup } from '../../types'
import { BisTypes } from '../..'
import { InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './lottery-prize-setting.vue'
import Comp from './lottery-prize.vue'
// 类型
export const type = 'lottery-prize'
export const defaultPrizeImgWidth = 60
export const defaultPrizeImgHeight = 60
export const defaultNameColor = '#fff'
export const defaultCountColor = '#fff'
export const defaultNameFontSize = 18
export const defaultCoutFontSize = 14
export const defaultShowPrizeName = true
export const defaultShowPrizeCount = true
// 数据类型约束
export interface IDesignLotteryPrize extends IDesignLayer {
  type: typeof type
  prizeImgWidth?: number
  prizeImgHeight?: number
  nameColor?: string
  nameFontSize?: number
  countColor?: string
  coutFontSize?: number
  showPrizeName?: boolean
  showPrizeCount?: boolean
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.sportsIng,
    showType: ['pcwall'],
    status: ['ready', 'ing'],
    showInteractive: [InteractiveEnum.lotteryv3, InteractiveEnum.listlotteryv3, InteractiveEnum.piclotteryv3],
    type,
    name: '奖品',
    thumbnail: new URL('./lottery-prize.png', import.meta.url).href,
    Comp,
    CompSetting,
    defaultData() {
      return {
        type,
        uuid: layerUuid(),
        name: '奖品',
        data: [],
        style: {
          left: '100px',
          top: '10px',
          width: '300px',
          height: '300px',
          background: 'rgba(0,0,0,0.5)',
          borderRadius: '10px',
          padding: '20px',
        },
      }
    },
  })
}
