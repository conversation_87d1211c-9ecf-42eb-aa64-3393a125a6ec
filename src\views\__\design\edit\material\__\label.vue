<script setup lang="ts">
import { cloneDeep } from 'lodash-es'

export interface Label {
  id?: number
  pId?: number
  name: string
}

const props = defineProps<{
  menuType?: string
  labelList: Label[]
}>()
const emits = defineEmits<{
  (e: 'change', val: any): void
  (e: 'upsert', val: any): void
  (e: 'remove', val: any): void
}>()

const route = useRoute()

const isManage = computed(() => route.path.startsWith('/manage'))
const isOem = computed(() => route.path.startsWith('/oem'))
const isSpo = computed(() => route.path.startsWith('/admin'))
const isMyMaterial = computed(() => props.menuType === 'my_material')

const dialogVisible = ref(false)
const selectedParentLabels = ref<number[]>([])
const selectedChildLabels = ref<number[]>([])
const parentLabel = computed(() => props.labelList.filter(item => !item.pId))
const childLabel = computed(() => props.labelList.filter(item => selectedParentLabels.value.includes(item.pId ?? 0)))
const isList = computed(() => [...selectedParentLabels.value, ...selectedChildLabels.value])

watch(() => props.labelList, () => {
  selectedParentLabels.value = []
  selectedChildLabels.value = []
})

function selectLabel(type: string, item: Label) {
  const toggleSelection = (list: Ref<number[]>, id: number) => {
    const index = list.value.indexOf(id)
    if (index === -1) {
      list.value = [id]
    } else {
      list.value.splice(index, 1)
    }
  }
  if (type === 'parent') {
    selectedChildLabels.value = []
    toggleSelection(selectedParentLabels, item.id ?? 0)
  } else {
    toggleSelection(selectedChildLabels, item.id ?? 0)
  }
  emits('change', isList.value)
}

const labelId = ref(0)
const labelName = ref('')
const labelPid = ref(0) // 用于标识是不是二级标签
const dynamicTags = ref<Label[]>([])
const dynamicTagsOld = ref<Label[]>([])
async function addLabel() {
  const data: any = {}
  if (isManage.value || isOem.value) {
    data.name = labelName.value
    data.children = calculateLabelDifferences(dynamicTags.value, dynamicTagsOld.value)
  } else {
    data.name = labelName.value
  }
  if (labelId.value) {
    data.id = labelId.value
  }
  emits('upsert', cloneDeep(data))
  clearAddData()
  dialogVisible.value = false
}
function calculateLabelDifferences(a: Label[], b: Label[]) {
  const delArr: any = []
  const addArr: any = []
  a.forEach((item) => {
    if (!item.id) {
      addArr.push(toRaw(item))
    }
  })
  b.forEach((item) => {
    if (!a.find(i => i.id === item.id)) {
      delArr.push(toRaw(item.id))
    }
  })
  return { delArr, addArr }
}

//  新增标签
const inputVisible = ref(false)
const secondTagName = ref('')
const saveTagInput = ref()
async function showInput() {
  inputVisible.value = true
  await nextTick()
  saveTagInput.value.focus()
}
function handleEdit(item: Label) {
  labelId.value = item.id ?? 0
  labelName.value = item.name
  labelPid.value = item.pId ?? 0
  dynamicTagsOld.value = cloneDeep(props.labelList.filter(i => item.id === i.pId))
  dynamicTags.value = cloneDeep(dynamicTagsOld.value)
  dialogVisible.value = true
}
function handleClose(removedTag: Label) {
  dynamicTags.value = dynamicTags.value.filter(tag => tag !== removedTag)
}
function secondTagAdd() {
  if (secondTagName.value) {
    dynamicTags.value.push({ name: secondTagName.value })
  }
  inputVisible.value = false
  secondTagName.value = ''
}
function clearAddData() {
  dynamicTags.value = []
  dynamicTagsOld.value = []
  labelName.value = ''
  labelId.value = 0
  labelPid.value = 0
}

// refs
function resetLabelRefFn(labelList: number[]) {
  const [p, c] = labelList
  if (p) {
    selectedParentLabels.value = [p]
  }
  if (c) {
    selectedChildLabels.value = [c]
  }
}

// 导出函数
defineExpose({
  resetLabelRefFn,
})
</script>

<template>
  <div class="flex items-center justify-between p-10">
    <div class="header-class">
      <div class="relative h-full w-full">
        <ul class="list classification">
          <span class="absolute left-0 top-2 font-bold">互动</span>
          <li
            v-for="item in parentLabel"
            :key="item.id"
            :class="{ active: selectedParentLabels.includes(item.id ?? 0) }"
            @click="selectLabel('parent', item)"
          >
            {{ item.name }}
            <el-dropdown v-if="isManage || isOem || isSpo && isMyMaterial" class="delete" trigger="click" placement="right-start">
              <icon-ph-dots-three-circle-fill @click.stop />
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleEdit(item)">编辑</el-dropdown-item>
                  <el-dropdown-item @click="emits('remove', item.id)">删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </li>
          <li v-if="isManage || isOem || isSpo && isMyMaterial" class="addLabel" @click="dialogVisible = true;clearAddData()">新增标签+</li>
        </ul>
        <ul v-if="childLabel.length" class="list classification mt-10">
          <span class="absolute left-0 top-42 font-bold">场景</span>
          <li
            v-for="item in childLabel"
            :key="item.id"
            :class="{ active: selectedChildLabels.includes(item.id ?? 0) }"
            @click="selectLabel('child', item)"
          >
            {{ item.name }}
            <el-dropdown v-if="isManage || isOem || isSpo && isMyMaterial" class="delete" trigger="click" placement="right-start">
              <icon-ph-dots-three-circle-fill @click.stop />
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleEdit(item)">编辑</el-dropdown-item>
                  <el-dropdown-item @click="emits('remove', item.id)">删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </li>
        </ul>
      </div>
    </div>
    <el-dialog v-model="dialogVisible" title="分组" append-to-body width="600px" @close="dialogVisible = false">
      <el-form label-width="80px">
        <el-form-item label="标签名称">
          <el-input v-model="labelName" placeholder="请输入标签名称" :max="20"></el-input>
        </el-form-item>
        <el-form-item v-if="(isManage || isOem) && !labelPid" label="二级标签">
          <div class="tag-box">
            <el-tag v-for="tag in dynamicTags" :key="tag.name" closable :disable-transitions="false" @close="handleClose(tag)">
              {{ tag.name }}
            </el-tag>
            <el-input
              v-if="inputVisible"
              ref="saveTagInput"
              v-model="secondTagName"
              class="input-new-tag"
              size="small"
              @keyup.enter="secondTagAdd"
              @blur="secondTagAdd"
            >
            </el-input>
            <el-button v-else class="button-new-tag" size="small" @click="showInput">+ 添加</el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="addLabel">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.header-class {
  padding: 0 12px;
  font-size: 14px;
  line-height: 25px;
  min-height: 30px;
  ul {
    padding-left: 50px;
  }
  li {
    display: inline-block;
    position: relative;
    margin-right: 15px;
    cursor: pointer;
    color: #333;
    transition: all 0.3s;
    height: 30px;
    border-radius: 4px;
    padding: 0 15px;
    line-height: 30px;
    color: #333;

    &:hover {
      background-color: #e6ebed;
    }

    .delete {
      width: 20px;
      height: 20px;
      position: absolute;
      right: -10px;
      top: -5px;
      cursor: pointer;
      color: #333;
      margin-left: 5px;
      opacity: 0;
      transition: all 0.3s;
    }
    &:hover {
      .delete {
        opacity: 1;
      }
    }

    &.active {
      background-color: #1261ff;
      color: #fff;
    }
  }
  .addLabel {
    color: #1261ff;
    cursor: pointer;
  }
}
.tag-box {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  .input-new-tag {
    width: 120px;
  }
}
</style>
