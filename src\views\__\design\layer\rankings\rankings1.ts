import { BisTypes, useDesignState } from '../..'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './rankings1-setting.vue'
import Comp from './rankings1.vue'

// 类型
export const type = 'rankings1'

export interface IDesignRankings1 extends IDesignLayer {
  type: typeof type
  default: string
  data: { name: string, avatar: string }[]
  startRanking?: number
  decoration: string
  itemWidth?: number
  gap?: number
}

export function setup(app: IDesignSetup) {
  let status: string[]
  switch (app.interactive) {
    case InteractiveEnum.shakev3:
      status = ['finish']
      break
    default:
      status = ['finish']
      break
  }

  app.registry({
    showType: ['pcwall'],
    bisType: BisTypes.rankingList,
    type,
    name: '排行榜',
    thumbnail: new URL('./rankings1.png', import.meta.url).toString(),
    showInteractive: [InteractiveEnum.shakev3, InteractiveEnum.moneyv3, InteractiveEnum.goldcoinv3, InteractiveEnum.diglettv3, InteractiveEnum.firev3],
    status,
    Comp,
    mountedCondition: computed(() => {
      const designState = useDesignState()
      const rankings = designState.getLayerData('endRankings')
      return !!rankings
    }),
    CompSetting,
    defaultData() {
      return {
        uuid: layerUuid(),
        name: '排行榜',
        type,
        default: new URL(`./assets/default.png`, import.meta.url).href,
        data: [],
        decoration: new URL(`./assets/1_decoration.png`, import.meta.url).href,
        style: {
          width: '960px',
          height: '170px',
          top: '160px',
          left: '130px',
        },
      }
    },
  })
}
