import type { MaybePromise } from 'rollup'
import { random } from '~/src/utils'

// 快捷键
const inIframe = window !== window.top
// 当前窗口唯一标识
const windowFlag = random(6)
// 是否初始化过
const windowReady = ref(false)

// 缓存当前注册内容
// key: shift+a   使用 + 号分割按键
const bindCache: Record<string, ({
  checkIsWork: () => boolean
  callback: () => MaybePromise<void>
})[]> = {}

const keyDealIng: Record<string, boolean> = {}
let ctrlKey = false
let shiftKey = false
async function dealEvent(type: string, key: string, repeat: boolean) {
  if (!key || repeat) return
  key = key.toLowerCase()

  if (type === 'keydown') {
    if (key === 'control') {
      ctrlKey = true
    } else if (key === 'shift') {
      shiftKey = true
    }
  } else if (type === 'keyup') {
    if (key === 'control') {
      ctrlKey = false
    } else if (key === 'shift') {
      shiftKey = false
    } else {
      // 其他按键
      const resultKey = []
      if (ctrlKey) {
        resultKey.push('ctrl')
      }
      if (shiftKey) {
        resultKey.push('shift')
      }
      resultKey.push(key)
      const result = resultKey.join('+')

      // todo 暂时简单处理，防止重复执行多次
      if (keyDealIng[result]) return
      keyDealIng[result] = true

      const cache = bindCache[result]
      if (cache) {
        const workCache = cache.filter(item => item.checkIsWork())
        for (const { callback } of workCache) {
          // 防止重复执行
          await callback()
        }
      }
      setTimeout(() => {
        keyDealIng[result] = false
      }, 100)
    }
  }
}

function onIframeMessage(e: MessageEvent) {
  const { data } = e
  const { type, key, repeat } = data
  dealEvent(type, key, repeat)
}
function ownEvent(e: KeyboardEvent) {
  const { type, key, repeat } = e
  // ctrl+s 阻止默认事件
  if (type === 'keydown' && key === 's' && 'ctrlKey' in e ? e.ctrlKey : false) {
    e.preventDefault()
  }
  dealEvent(type, key, repeat)
  if (window.parent) {
    window.parent.postMessage({
      type: 'iframe:keyEvent',
      keyEvent: { code: e.code, repeat: e.repeat, type: e.type },
    }, '*')
  }
}

export function initKeybindScript() {
  if (windowReady.value) return
  windowReady.value = true

  // 在iframe
  if (inIframe) {
    // 给当前自己的iframe打上唯一标记，然后将标记传递给父级，父级向子级发送消息时，只发送给这个iframe
    window.parent.document.querySelectorAll('iframe').forEach((iframe) => {
      if (iframe.contentWindow === window) {
        iframe.setAttribute('data-window-flag', windowFlag)
      }
    })
    // 判断父级是否已经存在js
    const scriptDom = window.parent.document.querySelector(`script[data-window-flag="${windowFlag}"]`)
    if (!scriptDom) {
      // 向iframe父级中插入一段js代码
      const script = document.createElement('script')
      script.setAttribute('data-window-flag', windowFlag)
      script.innerHTML = `{
        function ownEvent(e) {
          const { type, key, repeat } = e
          if(key === 'isIframe') return
          if (type === 'keydown' && key === 's' && e.ctrlKey) {
            e.preventDefault()
          }
          const resultIframe = document.querySelector('iframe[data-window-flag="${windowFlag}"]')
          if(resultIframe) {
            resultIframe.contentWindow.postMessage({type, key, repeat}, '*')
          }
        }

        function messageEvent(e) {
          const {type, keyEvent } = e.data
          if (type == 'iframe:keyEvent') {
            const createEvent = new KeyboardEvent(keyEvent.type, {
              code: keyEvent.code,
              repeat: keyEvent.repeat,
              key:'isIframe'
             })
           window.dispatchEvent(createEvent)
          }
        }

        window.addEventListener('keydown', ownEvent)
        window.addEventListener('keyup', ownEvent)
        window.addEventListener('message', messageEvent)
      }
      `
      window.parent.document.body.appendChild(script)
    }

    window.addEventListener('message', onIframeMessage)
  }

  // 不在iframe
  // 直接绑定事件
  window.addEventListener('keydown', ownEvent)
  window.addEventListener('keyup', ownEvent)
}

// 如果存在ctrl+shift时需要保证ctrl在前
export function useShortcutKey(options: {
  [k: string]: {
    checkIsWork: () => boolean
    callback: () => void
  }
}) {
  tryOnBeforeMount(() => {
    // 初始化
    initKeybindScript()
    // 绑定事件
    for (let [key, value] of Object.entries(options)) {
      key = key.toLowerCase()
      let cache = bindCache[key]
      if (!cache) {
        cache = []
        bindCache[key] = cache
      }
      cache.push(value)
    }
  })
  tryOnBeforeUnmount(() => {
    // 取消绑定
    for (let [key, value] of Object.entries(options)) {
      key = key.toLowerCase()
      const cache = bindCache[key]
      if (!cache) return
      const index = cache.indexOf(value)
      if (index !== -1) {
        cache.splice(index, 1)
      }
      if (cache.length === 0) {
        delete bindCache[key]
      }
    }
  })
}
