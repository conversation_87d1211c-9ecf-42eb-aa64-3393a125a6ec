<script setup lang="ts">
import type { IDesignMobileMoneyIng } from './mobile-money-ing'
import { getDefaultMaterial, openSelectMaterial } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'

const layer = defineModel<IDesignMobileMoneyIng>('layer', { required: true })

async function updateMaterialFn(name: 'bottomImg' | 'participants' | 'upImg', index?: number, isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, `data.${name}${index !== undefined ? `.${index}` : ''}`)
    : await openSelectMaterial('PIC')
  if (name === 'participants') {
    if (!result) return
    layer.value.data.participants[index || 0] = result
  } else {
    layer.value.data[name] = result
  }
}

function removeParticipants(index: number) {
  layer.value.data.participants.splice(index, 1)
}

function addParticipants(index: number, defaultValue: any) {
  layer.value.data.participants.splice(index + 1, 0, defaultValue)
}
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>参与物体</h3>
        <div>
          <div
            v-for="(item, index) in layer.data.participants"
            :key="index"
            class="relative mb-10 h-60 flex items-center"
          >
            <MaterialThumbnail class="mr-6px" @select="updateMaterialFn('participants', index)" @reset="updateMaterialFn('participants', index, true)">
              <img :src="item" class="bgblank size-60px object-contain">
            </MaterialThumbnail>
            <div class="ml-6 w-20 pt-10">
              <icon-ph:minus-bold v-if="layer.data.participants.length > 1" @click.stop="removeParticipants(index)" />
              <icon-ph:plus-bold v-if="layer.data.participants.length < 10" @click.stop="addParticipants(index, item)" />
            </div>
          </div>
        </div>
      </div>
      <div class="setting-item">
        <h3>默认宽度</h3>
        <div class="mr-26">
          <el-input-number v-model="layer.data.participantWidth" v-input-number :controls="false" :max="100" :min="0"></el-input-number>
          <span class="ml-4 text-14px text-#333">%</span>
        </div>
      </div>
      <div class="setting-item">
        <h3>滑动缩小</h3>
        <div class="mr-26">
          <el-switch v-model="layer.data.autoScale" />
        </div>
      </div>
      <div class="setting-item">
        <h3>底部图片</h3>
        <div class="mr-26">
          <MaterialThumbnail class="mr-6px" @select="updateMaterialFn('bottomImg')" @reset="updateMaterialFn('bottomImg', undefined, true)">
            <img :src="layer.data.bottomImg" class="bgblank size-60px object-contain">
          </MaterialThumbnail>
        </div>
      </div>
      <div class="setting-item">
        <h3>显示引导</h3>
        <div class="mr-26">
          <el-switch v-model="layer.data.showUp" />
        </div>
      </div>
      <div v-if="layer.data?.showUp" class="setting-item">
        <h3>引导图片</h3>
        <div class="mr-26">
          <MaterialThumbnail class="mr-6px" @select="updateMaterialFn('upImg')" @reset="updateMaterialFn('upImg', undefined, true)">
            <img :src="layer.data.upImg" class="bgblank size-60px object-contain">
          </MaterialThumbnail>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
