import type { Plugin } from 'vite'
import { MagicString } from 'vue/compiler-sfc'

// 由于postcss插件只修改组件内的css，但unocss默认是global默认（输出结果到html中），因此自定义组件模板中使用了unocss类名的不会自动缩放，也不适合通过unocss直接修改返回数据（由于是全局的，非自定义组件实现的地方也可能使用同样类名，但是不需要缩放）。因此写个vite插件提取类名做修改，改成unocss能识别、且支持自动缩放的复杂类名。有更合适方案时再替换。

// 匹配目录，保持和 postcss-px-design.ts 一致
const fileRegex = '/design/layer/'

// 识别class类名，比如 mt-120px h-10 w-180px -px-10px
const remRE = /(?<=^|\s)(-?(?:size|px|py|mx|my|text|mt|mb|ml|mr|pt|pb|pl|pr|max-w|min-w|max-h|min-h|font-size|[whmp]))-(\d+(?:\.\d+)?)(px)?(?=\s|["']|$)/g

function compileFile(src: string) {
  const start = src.indexOf('<template>')
  const end = src.lastIndexOf('</template>')
  const templateCode = src.slice(start, end)

  const code = new MagicString(templateCode)
  code.replaceAll(remRE, (_, prefix, value, unit) => {
    return `${prefix}-[calc(var(--design-scale)*${value}${unit || 'px'})]`
  })

  return `${src.slice(0, start)}${code.toString()}${src.slice(end)}`
}

export function templateUnocssPxDesign(): Plugin {
  return {
    name: 'template-unocss-px-design',
    transform(src, id) {
      if (
        id.includes(fileRegex)
        && !(
          id.includes('-setting.vue')
          || id.includes('-attachment.vue')
          || id.includes('/design/layer/index.vue')
          || id.includes('/design/layer/moveable.vue')
          || !id.endsWith('vue')
        )) {
        return {
          code: compileFile(src),
          map: null,
        }
      }
    },
    enforce: 'pre',
  }
}
