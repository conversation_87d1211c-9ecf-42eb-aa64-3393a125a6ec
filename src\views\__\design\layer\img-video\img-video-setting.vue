<script setup lang="ts">
import type { IDesignImgVideo, IMode, IRepeat } from './img-video.ts'
import { openSelectMaterial, useDataAttr } from '../../index'
import { defaultAutoplay, defaultBgType, defaultControls, defaultLoop, defaultMode, defaultMuted, defaultPosition, defaultRepeat, defaultSize } from './img-video.ts'

const layer = defineModel<IDesignImgVideo>('layer', { required: true })

const bgTypeBlind = useDataAttr(layer.value, 'bgType', defaultBgType)
// 图片类型
const modes: { label: string, value: IMode }[] = [
  { label: '手动', value: 'none' },
  { label: '适应', value: 'contain' },
  { label: '拉伸', value: 'fill' },
  { label: '裁剪', value: 'cover' },
  { label: '自动', value: 'auto' },
]
const repeats: { label: string, value: IRepeat }[] = [
  { label: '无', value: 'no-repeat' },
  { label: '横向', value: 'repeat-x' },
  { label: '纵向', value: 'repeat-y' },
  { label: '平铺', value: 'repeat' },
]

async function selectMaterialFn(type: 'PIC' | 'VIDEO' = 'PIC') {
  const result = await openSelectMaterial(type)
  if (result) {
    if (type === 'VIDEO') {
      layer.value.videoData = result
    } else if (type === 'PIC') {
      layer.value.imgData = result
    }
  }
}

const modeBind = useDataAttr(layer.value, 'mode', defaultMode)
const imgRepeatBlind = useDataAttr(layer.value, 'imgRepeat', defaultRepeat)
const sizeXBind = useDataAttr(layer.value, 'sizeX', defaultSize)
const sizeYBind = useDataAttr(layer.value, 'sizeY', defaultSize)
const positionXBind = useDataAttr(layer.value, 'posX', defaultPosition)
const positionYBind = useDataAttr(layer.value, 'posY', defaultPosition)
const colorBind = useDataAttr(layer.value, 'color', '')

// 视频类型
const objectFitBind = useDataAttr(layer.value, 'videoObjectFit', 'fill')
const controlsBind = useDataAttr(layer.value, 'controls', defaultControls)
const autoplayBind = useDataAttr(layer.value, 'autoplay', defaultAutoplay)
const loopBind = useDataAttr(layer.value, 'loop', defaultLoop)
const mutedBind = useDataAttr(layer.value, 'muted', defaultMuted)
type IObjectFit = 'none' | 'fill' | 'contain' | 'cover'
const objectFits: { label: string, value: IObjectFit }[] = [
  { label: '无', value: 'none' },
  { label: '拉伸', value: 'fill' },
  { label: '适应', value: 'contain' },
  { label: '裁剪', value: 'cover' },
]
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>类型</h3>
        <hi-tabs v-model="bgTypeBlind" :tabs="[{ label: '图片', value: 'img' }, { label: '视频', value: 'video' }]" />
      </div>
      <template v-if="bgTypeBlind === 'img'">
        <div class="setting-item">
          <div class="thumbnail-box bgblank" @click="selectMaterialFn('PIC')">
            <img :src="layer.imgData">
          </div>
        </div>
        <div class="setting-item">
          <h3>显示模式</h3>
          <el-select v-model="modeBind" placeholder="选择填充方式" class="w-100">
            <el-option v-for="item in modes" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div v-if="modeBind === 'none'" class="setting-item">
          <h3>横向大小</h3>
          <el-slider v-model="sizeXBind" :min="0" :max="100" :step="1" :format-tooltip="v => `${v}%`" class="mx-10 flex-1" />
        </div>
        <div v-if="modeBind === 'none'" class="setting-item">
          <h3>纵向大小</h3>
          <el-slider v-model="sizeYBind" :min="0" :max="100" :step="1" :format-tooltip="v => `${v}%`" class="mx-10 flex-1" />
        </div>
        <template v-if="modeBind === 'none' || modeBind === 'contain' || modeBind === 'auto'">
          <div class="setting-item">
            <h3>图片平铺方式</h3>
            <el-select v-model="imgRepeatBlind" placeholder="选择平铺方式" class="w-100">
              <el-option v-for="item in repeats" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
          <div class="setting-item">
            <h3>横向位置</h3>
            <el-slider v-model="positionXBind" :min="0" :max="100" :step="1" :format-tooltip="v => `${v}%`" class="mx-10 flex-1" />
          </div>
          <div class="setting-item">
            <h3>纵向位置</h3>
            <el-slider v-model="positionYBind" :min="0" :max="100" :step="1" :format-tooltip="v => `${v}%`" class="mx-10 flex-1" />
          </div>
        </template>
        <div class="setting-item">
          <div class="flex items-center">
            图片颜色
            <el-tooltip effect="dark">
              <template #content>注意：<br>1. 该设置项只对透明图片有效<br>2. 设置了颜色二维码长按识会失效</template>
              <icon-ph-question-bold class="ml-5" />
            </el-tooltip>
          </div>
          <hi-color v-model="colorBind" type="both" />
        </div>
      </template>

      <template v-if="bgTypeBlind === 'video'">
        <div class="setting-item">
          <div class="thumbnail-box bgblank" @click="selectMaterialFn('VIDEO')">
            <video
              :src="layer.videoData"
              :loop="layer.loop"
              :autoplay="layer.autoplay"
              :muted="layer.muted"
            ></video>
          </div>
        </div>
        <div class="setting-item">
          <h3>显示模式</h3>
          <el-select v-model="objectFitBind" placeholder="选择填充方式" class="w-100">
            <el-option v-for="item in objectFits" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="setting-item">
          <h3>静音</h3>
          <el-switch v-model="mutedBind" />
        </div>
        <div class="setting-item">
          <h3 class="flex items-center">
            <span>自动播放</span>
            <el-tooltip content="自动播放需要浏览器支持" placement="top">
              <icon-ph-question-bold class="ml-5" />
            </el-tooltip>
          </h3>
          <el-switch v-model="autoplayBind" />
        </div>
        <div class="setting-item">
          <h3>循环播放</h3>
          <el-switch v-model="loopBind" />
        </div>
        <div class="setting-item">
          <h3>控制栏</h3>
          <el-switch v-model="controlsBind" />
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped lang="scss">
.thumbnail-box {
  width: 120px;
  height: 120px;
  outline: 1px solid #e6ebed;
  border-radius: 4px;
  position: relative;

  img {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }

  > img {
    object-fit: contain;
  }
  video {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    object-fit: contain;
  }
}
</style>
