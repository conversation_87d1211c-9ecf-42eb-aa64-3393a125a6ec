import type { IDesignLayer, IDesignSetup } from '../../types'
import { BisTypes, useDesignData } from '../..'
import { InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './listlottery-ing4-setting.vue'
import Comp from './listlottery-ing4.vue'
// 类型
export const type = 'listlottery-ing4'
export const defaultAnimateSpeed = 3
export const defaultItemSize = 180

export const defalutTextStyle: textItemStyle = {
  fontSize: 20,
  fontColor: '#fff',
  fonBold: false,
}
// 数据类型约束
export interface IDesignListlotteryIng4 extends IDesignLayer {
  type: typeof type
  itemSize?: number
  animiteSpeed?: number
  contentStyle: textItemStyle[]
  itemBgColors: string[]
}

export interface textItemStyle {
  fontSize: number
  fontColor: string
  fonBold: boolean
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.sportsIng,
    showType: ['pcwall'],
    status: ['ing', 'ready'],
    showInteractive: [InteractiveEnum.listlotteryv3],
    type,
    name: '名单抽奖3d动效',
    thumbnail: new URL('./listlottery-ing4.png', import.meta.url).href,
    Comp,
    CompSetting,
    defaultData() {
      const designData = useDesignData()
      const [width, height] = designData.option.drafts
      return {
        type,
        uuid: layerUuid(),
        name: '名单抽奖3d动效',
        contentStyle: [],
        itemBgColors: ['#ff5448'],
        style: {
          width: `${width}px`,
          height: `${height}px`,
          top: 0,
          left: 0,
        },
      }
    },
  })
}
