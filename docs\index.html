<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
  <style>
    iframe {
      width: 200px;
      height: 200px;
    }
  </style>
</head>

<body>
  <div>哈哈</div>
  <button>按钮1</button>
  <iframe src="/docs/iframe.html?t=1"></iframe>
  <script>
    async function main() {
      console.log('---', window.DeviceMotionEvent)
      const response = await window.DeviceMotionEvent.requestPermission()
      console.log("🚀 ~ main ~ response:", response)
      alert(`1.${response}`)
    }
    document.querySelector('button').addEventListener('click', () => {
      main()
    })
  </script>
</body>

</html>