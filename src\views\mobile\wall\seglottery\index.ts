import { useDesignState, useDesignTemp } from 'design/index'
import { useImData } from '~/src/hooks/useImData'
import { randomAvatar } from '~/src/utils'
import { useMobileStore } from '../../stores/useMobileStore'
import { fetchTheme } from '../../utils'
import { mockConfig, mockRound } from './mock'
import 'vant/lib/toast/style'

// 类型
interface IConfig {
  seglotteryId: number
}
interface IRound {
  id: number
  themeId: number
  title: string
}

export function useMobileSeglottery() {
  const designTemp = useDesignTemp()
  const designState = useDesignState()

  const isDesignEdit = designTemp.isEdit

  designTemp.showType = 'mobile'

  const mobileStore = useMobileStore()
  const { wall, featureConfig, isShowAd, isWedding } = storeToRefs(mobileStore)

  // 互动配置
  const round = ref<IRound>()
  const config = ref<IConfig>()
  // 微信用户信息
  const wxuser = ref()
  // 广告
  const adResponse = ref()

  const awards = ref()
  const awardsName = computed(() => awards?.value?.name)
  const recordLottery = ref()
  const recordLotterySearchVal = ref('')
  const loadAwardsRecord = async () => {
    if (designTemp.isEdit) {
      recordLottery.value = [
        {
          text: '示例数据1',
        },
        {
          text: '示例数据2',
        },
      ]
      return
    }
    if (!round.value?.id) return
    const baseWhere = {
      module: 'seglottery',
      moduleId: round.value?.id,
    } as const
    const [awardRec, recordLotteryRec] = await Promise.all([
      api.mobile.awards.list({
        where: baseWhere,
      }),
      api.mobile.awards.recordLotteryPage({
        where: {
          ...baseWhere,
          likeRemarkByValue: recordLotterySearchVal.value ? recordLotterySearchVal.value : undefined,
        },
        pageIndex: 1,
        pageSize: 100,
      }),
    ])
    awards.value = awardRec[0]
    recordLottery.value = recordLotteryRec.dataList.map((item: any) => {
      const remark = JSON.parse(item.remark)
      return { id: item.id, text: [remark.d1, remark.d2, remark.d3].filter(Boolean).join('-') }
    })
  }
  watch(() => [designState.status, round?.value?.id], (v) => {
    if (v[0] === 'finish' && v[0]) {
      loadAwardsRecord()
    }
  })

  // 主要作用是将数据暴露给图层组件
  designState
    .setStatusList([
      { label: '互动抽奖', value: 'ing' },
      { label: '中奖名单', value: 'finish' },
    ])
    .setLayerData({
      '#主题名称#': computed(() => designTemp.theme?.name),
      '#轮次标题#': computed(() => round.value?.title),
      'isShowAd': isShowAd,
      'isWedding': isWedding,
      'adResponse': computed(() => adResponse.value),
      'wxuser': computed(() => wxuser.value),
      'wall': computed(() => wall.value),

      awardsName,
      'recordLottery': computed(() => recordLottery.value),
      '%recordLotterySearchVal%': recordLotterySearchVal,

    })
    // 图层组件事件
    .setLayerEventList([
      {
        eventId: 'back-home',
        name: '返回首页',
        value() {
          window.parent.postMessage({ type: 'iframe:backHome' }, '*')
        },
      },
      // 广告交互
      {
        eventId: 'clickAd',
        value(data) {
          window.parent.postMessage({ type: 'iframe:clickAd', data }, '*')
        },
      },
      {
        eventId: 'SHOW_AD',
        value(data) {
          window.parent.postMessage({ type: 'iframe:SHOW_AD', data }, '*')
        },
      },
      {
        eventId: 'clickAwards',
        name: '中奖记录',
        value() {
          window.parent.postMessage({ type: 'iframe:clickAwards' }, '*')
        },
      },
      {
        eventId: 'loadAwardsRecord',
        value() {
          loadAwardsRecord()
        },
      },
    ])

  watch(() => round.value?.themeId, themeId => fetchTheme(themeId || ''))

  tryOnMounted(() => {
    if (isDesignEdit) {
      // 设计模式mock数据
      config.value = mockConfig()
      round.value = mockRound()
      wxuser.value = {
        nickName: '测试用户',
        imgpath: randomAvatar(),
      }
    }
    designState.setStatus('ing')
  })

  // 数据同步
  useImData({
    'im:seglottery:config': config,
    'im:seglottery': round,
    'im:wall': wall,
    'im:feature:config': featureConfig,
    'im:wxuser': wxuser,
    'im:adResponse': adResponse,
  })
}
