<script setup lang="ts">
import type { IDesignGroup } from './group'
import HiDesignLayer from '../index.vue'

const layer = defineModel<IDesignGroup>('layer', { required: true })

const sortedLayers = computed(() => {
  const len = layer.value.layers.length
  const layers = Array.from({ length: len }).map((_, i) => i)
  // 非布局模式需要倒序
  if (!layer.value.layout) {
    layers.reverse()
  }
  return layers
})
</script>

<template>
  <HiDesignLayer
    v-for="(val, index) in sortedLayers"
    :key="layer.layers[val].uuid"
    v-model:layer="layer.layers[val]"
    :data-index="index"
  />
</template>

<style scoped lang="scss">
</style>
