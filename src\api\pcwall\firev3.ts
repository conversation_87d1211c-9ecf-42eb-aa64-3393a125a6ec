import { HiRequest } from '../request'

export default {
  configRead: (params: any) => HiRequest.post('/pro/hxc/web/profirev3config/read.htm', params),

  read: (params: any) => HiRequest.post('/pro/hxc/web/profirev3/read.htm', params),
  go: (params?: any) => HiRequest.post('/pro/hxc/web/profirev3/go.htm', params),
  again: (params?: any) => HiRequest.post('/pro/hxc/web/profirev3/again.htm', params),
  ranking: (params: any) => HiRequest.post('/pro/hxc/web/profirev3/ranking.htm', params),

  regeditPage: (params: any) => HiRequest.post('/pro/hxc/web/profirev3regedit/page.htm', params),
  regeditMembersCnt: (params: any) => HiRequest.post('/pro/hxc/web/profirev3regedit/membersCnt.htm', params),

  teamList: (params: any) => HiRequest.post('/pro/hxc/web/profirev3team/list.htm', params),
  switch: (params: any) => HiRequest.post('/pro/hxc/web/profirev3/switch.htm', params),
}
