<script setup lang="ts">
import { deaQiniulUrl } from '@/utils'

export interface ImageMaterial {
  url: string
  id: string
  state: string
  type: string
  name: string
}

const props = defineProps<{
  materialList: ImageMaterial[]
  isMan?: boolean
  menuType?: string
  toUpWallList?: string[]
}>()

const emit = defineEmits<{
  (e: 'selection', val: ImageMaterial): void
  (e: 'delete', val: string): void
  (e: 'upWall', val: string): void
  (e: 'edit', val: ImageMaterial): void
}>()

const route = useRoute()

const isManage = computed(() => route.path.startsWith('/manage'))
const isOem = computed(() => route.path.startsWith('/oem'))
const isSpo = computed(() => route.path.startsWith('/admin'))
const isMyMaterial = computed(() => props.menuType === 'my_material')
const showUpWall = computed(() => props.menuType === 'OFF')

const showViewer = ref(false)
const selectMaterial = ref()
const urlIndex = ref<number>(0)
const urlList = computed(() => props.materialList.map(item => item.url))

function handleSelectMaterial(item: ImageMaterial) {
  selectMaterial.value = item
  emit('selection', item)
}

function handleViewer(index: number) {
  urlIndex.value = index
  showViewer.value = true
}
function edit(item: ImageMaterial) {
  emit('edit', item)
}
function showCur(item: ImageMaterial) {
  if (showUpWall.value) {
    return props.toUpWallList?.includes(item.id)
  } else {
    return selectMaterial.value?.id === item.id
  }
}
</script>

<template>
  <div class="image-box">
    <div
      v-for="(item, index) of materialList"
      :key="item.id"
      class="item-box bgblank"
      :class="{ cur: showCur(item) }"
    >
      <img :src="deaQiniulUrl(item.url)" @click="handleSelectMaterial(item)" />
      <div v-if="isManage || isOem || (isSpo && isMyMaterial)" class="image-btn dots">
        <el-dropdown trigger="click" placement="right-start">
          <icon-ph-dots-three-outline-fill class="color-#fff" />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="edit(item)">编辑</el-dropdown-item>
              <el-dropdown-item @click="handleViewer(index)">查看</el-dropdown-item>
              <el-dropdown-item v-if="showUpWall" @click="emit('upWall', item.id)">上架</el-dropdown-item>
              <el-dropdown-item @click="emit('delete', item.id)">删除</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <div class="image-btn txt">
        <el-tooltip
          class="box-item"
          effect="light"
          :content="item.name"
          placement="top-start"
        >
          详情
        </el-tooltip>
      </div>
      <div class="image-btn eye" @click.stop="handleViewer(index)"><icon-ph-eye-fill /></div>
    </div>
    <el-image-viewer
      v-if="showViewer"
      :url-list="urlList.map(item => deaQiniulUrl(item))"
      :initial-index="urlIndex"
      :hide-on-click-modal="true"
      teleported
      @close="showViewer = false"
    />
  </div>
</template>

<style scoped lang="scss">
.image-box {
  height: inherit;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  gap: 10px;
  padding: 5px 10px;

  .item-box {
    width: 126px;
    height: 126px;
    cursor: pointer;
    position: relative;
    outline: 1px solid #ccc;

    &.cur {
      outline: 2px solid #1261ff;
    }

    &:hover .image-btn {
      opacity: 1;
    }

    .image-btn {
      position: absolute;
      width: 18px;
      height: 18px;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 4px;
      cursor: pointer;
      justify-content: center;
      align-items: center;
      padding: 2px;
      color: #fff;
      display: flex;
      opacity: 0;
      transition: opacity 0.3s;
    }

    .dots {
      top: 3px;
      right: 3px;
    }
    .eye {
      bottom: 3px;
      right: 3px;
    }
    .txt {
      width: auto;
      bottom: 3px;
      right: 26px;
      font-size: 10px;
      padding: 0 5px;
      color: #fff;
    }
  }
}
</style>
