<script setup lang="ts">
import type { CSSProperties } from 'vue'
import type { IDesignGroup } from '../../../layer/group/group'
import type { IDesignLayer } from '../../../types'
import { envUtils } from '~/src/utils/env'
import { fourValueBind, fourValueExpand, useDataAttr, useDesignData, useDesignTemp, useTransformAttr } from '../../..'
import HiBackground from '../../../components/background.vue'
import { canMergeToGroup, canSplitGroup, mergeToGroup, splitGroup } from '../../mergeSplit'

defineOptions({ label: '基础设置' })

const { isDev } = envUtils
const layer = defineModel<IDesignLayer>('layer', { required: true })
const designData = useDesignData()
const designTemp = useDesignTemp()

// 属性绑定
const isPercent = useDataAttr(layer.value, 'isPercent', 0)
const { style } = layer.value
const bgColorBind = useDataAttr(style, 'background', 'rgba(0, 0, 0, 0)')
const opacityBind = useDataAttr(style, 'opacity', 1)
const widthBind = useDataAttr(style, 'width', 0, 'px')
const heightBind = useDataAttr(style, 'height', 0, 'px')

watch(
  () => [widthBind.value, heightBind.value] as const,
  ([w, h], [ow, oh]) => {
    if (!layer.value.ratio) return

    if (ow && w !== ow && h === oh) {
      const rh = Math.round(w / layer.value.ratio)
      layer.value.style.height = `${rh}px`
    }
    if (oh && w === ow && h !== oh) {
      const rw = Math.round(h * layer.value.ratio)
      layer.value.style.width = `${rw}px`
    }
  },
)

const keepRatioBind = computed({
  get() {
    return !!layer.value.ratio
  },
  set(v) {
    if (v) {
      const { width, height } = layer.value.style
      layer.value.ratio = Number.parseFloat((Number.parseFloat(`${width}`) / Number.parseFloat(`${height}`)).toFixed(2))
    } else {
      delete layer.value.ratio
    }
  },
})

// 横向分布
const xDir = computed({
  get() {
    const { style: { left, right } } = layer.value
    return right && !left ? 'right' : 'left'
  },
  set(v) {
    const { style: { left, right } } = layer.value

    delete layer.value.style.left
    delete layer.value.style.right
    layer.value.style[v] = left || right || '0px'
  },
})
const yDir = computed({
  get() {
    const { style: { top, bottom } } = layer.value
    return bottom && !top ? 'bottom' : 'top'
  },
  set(v) {
    const { style: { top, bottom } } = layer.value

    delete layer.value.style.top
    delete layer.value.style.bottom
    layer.value.style[v] = top || bottom || '0px'
  },
})
const leftBind = useDataAttr(style, 'left', 0, 'px')
const rightBind = useDataAttr(style, 'right', 0, 'px')
const topBind = useDataAttr(style, 'top', 0, 'px')
const bottomBind = useDataAttr(style, 'bottom', 0, 'px')

const xBind = computed({
  get() {
    return xDir.value === 'left' ? leftBind.value : rightBind.value
  },
  set(v) {
    if (xDir.value === 'left') {
      leftBind.value = v
    } else {
      rightBind.value = v
    }
  },
})
const yBind = computed({
  get() {
    return yDir.value === 'top' ? topBind.value : bottomBind.value
  },
  set(v) {
    if (yDir.value === 'top') {
      topBind.value = v
    } else {
      bottomBind.value = v
    }
  },
})

const transformBind = [
  { name: '3d', data: useTransformAttr(style, `perspective`, 0) },
  { name: 'X', data: useTransformAttr(style, `rotateX`, 0) },
  { name: 'Y', data: useTransformAttr(style, `rotateY`, 0) },
  { name: 'Z', data: useTransformAttr(style, `rotateZ`, 0) },
]

function getStyleNum(item: IDesignLayer, key: keyof CSSProperties) {
  return Number.parseFloat(`${item.style[key] || 0}`)
}
function alignFn(dir: 'left' | 'right' | 'top' | 'bottom' | 'x' | 'y' | 'horizontal' | 'vertical') {
  // 筛选出同级的图层
  const { activeList } = designTemp
  const layer = designData.getLayerByUuid(activeList[0])
  if (!layer) return
  let layers = layer.$parent ? (layer.$parent as IDesignGroup).layers : designData.layers
  layers = layers.filter(i => activeList.includes(i.uuid))

  const rootWidth = layer.$parent ? getStyleNum(layer.$parent, 'width') : designData.option.drafts[0]
  const rootHeight = layer.$parent ? getStyleNum(layer.$parent, 'height') : designData.option.drafts[1]

  switch (dir) {
    case 'left':
      {
        // 获取最小left
        const minLeft = layers.length === 1 ? 0 : Math.min(...layers.map(item => getStyleNum(item, 'left')))
        layers.forEach((item: IDesignLayer) => {
          item.style.left = `${minLeft}px`
        })
      }
      break
    case 'right':
      {
        const maxRight = layers.length === 1
          ? rootWidth
          : Math.max(...layers.map((item) => {
              return getStyleNum(item, 'left') + getStyleNum(item, 'width')
            }))
        layers.forEach((item: IDesignLayer) => {
          item.style.left = `${maxRight - getStyleNum(item, 'width')}px`
        })
      }
      break
    case 'top':
      {
        const minTop = layers.length === 1 ? 0 : Math.min(...layers.map(item => getStyleNum(item, 'top')))
        layers.forEach((item: IDesignLayer) => {
          item.style.top = `${minTop}px`
        })
      }
      break
    case 'bottom':
      {
        const maxBottom = layers.length === 1
          ? rootHeight
          : Math.max(...layers.map((item) => {
              return getStyleNum(item, 'top') + getStyleNum(item, 'height')
            }))
        layers.forEach((item: IDesignLayer) => {
          item.style.top = `${maxBottom - getStyleNum(item, 'height')}px`
        })
      }
      break
    case 'x':
    case 'y':
      {
        let center = {
          x: rootWidth / 2,
          y: rootHeight / 2,
        }
        // 获取首个中心点位置，其他的都以这个为准
        if (layers.length > 1) {
          const layerFirst = layers[0]
          center = {
            x: getStyleNum(layerFirst, 'left') + getStyleNum(layerFirst, 'width') / 2,
            y: getStyleNum(layerFirst, 'top') + getStyleNum(layerFirst, 'height') / 2,
          }
        }

        layers.forEach((item: IDesignLayer) => {
          if (dir === 'x') {
            item.style.top = `${center.y - getStyleNum(item, 'height') / 2}px`
          } else if (dir === 'y') {
            item.style.left = `${center.x - getStyleNum(item, 'width') / 2}px`
          }
        })
      }
      break
    case 'horizontal':
    case 'vertical':
      {
        const start = dir === 'horizontal' ? 'left' : 'top'
        const size = dir === 'horizontal' ? 'width' : 'height'

        // 水平/垂直分布实现
        const min = Math.min(...layers.map(item => getStyleNum(item, start)))
        const max = Math.max(...layers.map((item) => {
          return getStyleNum(item, start) + getStyleNum(item, size)
        }))
        const total = layers.reduce((prev, item) => prev + getStyleNum(item, size), 0)
        const space = (max - min - total) / (layers.length - 1)

        // 按照left排序
        layers.sort((a, b) => getStyleNum(a, start) - getStyleNum(b, start))

        layers.forEach((item: IDesignLayer, index: number) => {
          if (index === 0) return
          item.style[start] = `${getStyleNum(layers[index - 1], start) + getStyleNum(layers[index - 1], size) + space}px`
        })
      }
      break
  }
}

function moveLayerTo(dir: 'forward' | 'backward' | 'top' | 'bottom') {
  // 仅支持单个图层
  const uuid = designTemp.activeList[0]
  if (!uuid) return
  const layer = designData.getLayerByUuid(uuid)
  if (!layer) return

  const layers = layer.$parent ? (layer.$parent as IDesignGroup).layers : designData.layers
  const index = layers.findIndex(item => item.uuid === uuid)

  if (dir === 'forward') {
    if (index === 0) return
    layers.splice(index - 1, 0, layers.splice(index, 1)[0])
  } else if (dir === 'backward') {
    layers.splice(index + 1, 0, layers.splice(index, 1)[0])
  } else if (dir === 'top') {
    layers.splice(index, 1)
    layers.unshift(layer)
  } else if (dir === 'bottom') {
    layers.splice(index, 1)
    layers.push(layer)
  }
}

const lockSize = computed(() => {
  const { layout } = layer.value.$parent as IDesignGroup || {}
  if (!layout) return false
  return !!layout.items[layer.value.uuid]
})
const lockDragDirection = computed(() => {
  const { layout } = layer.value.$parent as IDesignGroup || {}
  if (!layout) return []
  const { direction } = layout
  if (direction === 'row') {
    return ['left', 'right']
  } else {
    return ['top', 'bottom']
  }
})
const lockDragX = computed(() => lockDragDirection.value.includes('left') && lockDragDirection.value.includes('right'))
const lockDragY = computed(() => lockDragDirection.value.includes('top') && lockDragDirection.value.includes('bottom'))

// 内边距
const paddingExpand = fourValueExpand(layer, 'padding')
const paddingLT = fourValueBind(layer, 'padding', 0)
const paddingRT = fourValueBind(layer, 'padding', 1)
const paddingRB = fourValueBind(layer, 'padding', 2)
const paddingLB = fourValueBind(layer, 'padding', 3)

// 圆角
const borderRadiusExpand = fourValueExpand(layer, 'borderRadius')
const borderRadiusLT = fourValueBind(layer, 'borderRadius', 0)
const borderRadiusRT = fourValueBind(layer, 'borderRadius', 1)
const borderRadiusRB = fourValueBind(layer, 'borderRadius', 2)
const borderRadiusLB = fourValueBind(layer, 'borderRadius', 3)

async function consoleDataFn() {
  const text = JSON.stringify(layer.value, (key, value) => {
    if (key.startsWith('$') || key.startsWith('_')) {
      return undefined
    }
    return value
  })
  console.log(text)
  await navigator.clipboard.writeText(text)
  // 提示
  ElMessage.success('图层数据已复制到剪贴板')
}

// 指针事件
const AllPointerEvents = [
  { label: '默认', value: 'initial' },
  { label: '自动', value: 'auto' },
  { label: '忽略', value: 'none' },
]
const pointerEventsBind = useDataAttr(style, 'pointerEvents', 'initial')

// 定位
const AllPosition = [
  { label: '绝对', value: 'absolute' },
  { label: '固定', value: 'fixed' },
  { label: '相对', value: 'relative' },
]
const positionBind = useDataAttr(style, 'position', 'absolute')
</script>

<template>
  <div class="setting-wrap">
    <div v-if="isDev" class="setting-item flex justify-center!">
      <el-button type="danger" class="w-full" @click="consoleDataFn">输出图层代码</el-button>
    </div>
    <div class="setting-item">
      <el-button v-if="canMergeToGroup" class="w-full" @click="mergeToGroup">组合</el-button>
      <el-button v-if="canSplitGroup" class="w-full" @click="splitGroup">拆分</el-button>
    </div>
    <!-- <h3>对齐</h3> -->
    <div class="setting-item justify-end!">
      <ul class="style-box cursor-pointer">
        <li data-tip="左对齐" @click="alignFn('left')"><icon-ph-align-left-duotone /></li>
        <li data-tip="水平居中" @click="alignFn('y')"><icon-ph-align-center-horizontal-duotone /></li>
        <li data-tip="右对齐" @click="alignFn('right')"><icon-ph-align-right-duotone /></li>
        <li data-tip="靠上对齐" @click="alignFn('top')"><icon-ph-align-top-duotone /></li>
        <li data-tip="垂直居中" @click="alignFn('x')"><icon-ph-align-center-vertical-duotone /></li>
        <li data-tip="靠下对齐" @click="alignFn('bottom')"><icon-ph-align-bottom-duotone /></li>
        <template v-if="designTemp.activeList.length > 2">
          <li data-tip="横向分布" @click="alignFn('horizontal')"><icon-ph-columns-duotone /></li>
          <li data-tip="纵向分布" @click="alignFn('vertical')"><icon-ph-rows-duotone /></li>
        </template>
      </ul>
    </div>
    <template v-if="designTemp.activeList.length === 1">
      <!-- <h3>层级</h3> -->
      <div class="setting-item justify-end!">
        <ul class="style-box cursor-pointer">
          <li data-tip="上移一层" @click="moveLayerTo('forward')"><icon-ph-stack-plus-duotone /></li>
          <li data-tip="下移一层" @click="moveLayerTo('backward')"><icon-ph-stack-minus-duotone /></li>
          <li data-tip="置于顶层" @click="moveLayerTo('top')"><icon-ph-stack-simple-fill /></li>
          <li data-tip="置于底层" @click="moveLayerTo('bottom')"><icon-ph-stack-simple /></li>
        </ul>
      </div>
      <!-- 背景 -->
      <div class="setting-item">
        <h3>背景</h3>
        <HiBackground v-model="bgColorBind" type="both" />
      </div>
      <div class="setting-item">
        <h3>透明度</h3>
        <el-slider v-model="opacityBind" :min="0" :max="1" :step="0.01" class="mx-10 flex-1" />
      </div>
      <!-- 尺寸 -->
      <label class="flex items-center justify-between">
        <div class="flex items-center">
          尺寸 & 位置
          <el-tooltip effect="dark">
            <template #content>固定：宽高固定<br />适应：宽高随比例变换自动适应</template>
            <icon-ph-question-bold class="ml-5" />
          </el-tooltip>
        </div>
        <el-switch
          v-model="isPercent"
          size="default"
          style="--el-switch-on-color: #13ce66; --el-switch-off-color: #409eff"
          inline-prompt
          inactive-text="固定"
          active-text="适应"
          :inactive-value="0"
          :active-value="1"
        />
      </label>
      <div v-if="!lockSize" class="setting-item justify-end!">
        <h3 class="ml-10">宽</h3>
        <el-input-number v-model="widthBind" v-input-number controls-position="right" />
        <template v-if="layer.type !== 'text'">
          <h3 class="ml-10">高</h3>
          <el-input-number v-model="heightBind" v-input-number controls-position="right" />
          <div class="icon-box ml-2" :class="{ active: keepRatioBind }" @click="keepRatioBind = !keepRatioBind">
            <el-tooltip effect="dark">
              <template #content>等比例缩放，开启后宽高会保持在固定比例下同时调整</template>
              <icon-ph-link />
            </el-tooltip>
          </div>
        </template>
      </div>
      <div class="setting-item justify-end!">
        <template v-if="!lockDragX">
          <el-select v-model="xDir" class="w-50">
            <el-option label="左" value="left" />
            <el-option label="右" value="right" />
          </el-select>
          <el-input-number v-model="xBind" v-input-number controls-position="right" />
        </template>
      </div>
      <div class="setting-item justify-end!">
        <template v-if="!lockDragY">
          <el-select v-model="yDir" class="w-50">
            <el-option label="上" value="top" />
            <el-option label="下" value="bottom" />
          </el-select>
          <el-input-number v-model="yBind" v-input-number controls-position="right" />
        </template>
      </div>
      <!-- 内边距 -->
      <div class="setting-item items-center justify-between">
        <h3>内边距</h3>
        <div v-if="!paddingExpand" class="flex-1 text-right">
          <el-input-number v-model="paddingLT" v-input-number :min="0" controls-position="right" />
        </div>
        <div class="icon-box ml-2 cursor-pointer" @click="paddingExpand = !paddingExpand">
          <el-tooltip effect="dark">
            <template #content>展开后按照 上右左下 4 个维度分别配置，否则 1 个值影响所有</template>
            <icon-custom-padding />
          </el-tooltip>
        </div>
      </div>
      <template v-if="paddingExpand">
        <div class="setting-item justify-end!">
          <el-input-number v-model="paddingLT" v-input-number :min="0" controls-position="right" />
          <el-input-number v-model="paddingRT" v-input-number :min="0" controls-position="right" />
        </div>
        <div class="setting-item justify-end!">
          <el-input-number v-model="paddingLB" v-input-number :min="0" controls-position="right" />
          <el-input-number v-model="paddingRB" v-input-number :min="0" controls-position="right" />
        </div>
      </template>
      <!-- 圆角 -->
      <div class="setting-item items-center justify-between">
        <h3>圆角</h3>
        <div v-if="!borderRadiusExpand" class="flex-1 text-right">
          <el-input-number v-model="borderRadiusLT" v-input-number :min="0" controls-position="right" />
        </div>
        <div class="icon-box ml-2 cursor-pointer" @click="borderRadiusExpand = !borderRadiusExpand">
          <el-tooltip effect="dark">
            <template #content>展开后按照 上右左下 4 个维度分别配置，否则 1 个值影响所有</template>
            <icon-custom-border-radius />
          </el-tooltip>
        </div>
      </div>
      <template v-if="borderRadiusExpand">
        <div class="setting-item justify-end!">
          <el-input-number v-model="borderRadiusLT" v-input-number :min="0" controls-position="right" />
          <el-input-number v-model="borderRadiusRT" v-input-number :min="0" controls-position="right" />
        </div>
        <div class="setting-item justify-end!">
          <el-input-number v-model="borderRadiusLB" v-input-number :min="0" controls-position="right" />
          <el-input-number v-model="borderRadiusRB" v-input-number :min="0" controls-position="right" />
        </div>
      </template>
      <h3>旋转</h3>
      <div class="setting-item justify-end!">
        <!-- 景深 -->
        <el-input-number v-model="transformBind[0].data.value" v-input-number :min="0" :max="1000" controls-position="right" />
        <el-input-number v-model="transformBind[1].data.value" v-input-number :min="-360" :max="360" controls-position="right" />
        <el-input-number v-model="transformBind[2].data.value" v-input-number :min="-360" :max="360" controls-position="right" />
        <el-input-number v-model="transformBind[3].data.value" v-input-number :min="-360" :max="360" controls-position="right" />
      </div>
      <div class="setting-item rotate-label justify-end!">
        <div>3D</div> <div>X</div><div>Y</div><div>Z</div>
      </div>
      <div class="setting-item justify-between">
        <div class="flex items-center">
          指针事件
          <el-tooltip effect="dark">
            <template #content>自动：接收鼠标的所有事件<br />忽略：忽略掉鼠标的所有事件</template>
            <icon-ph-question-bold class="ml-5" />
          </el-tooltip>
        </div>
        <el-select v-model="pointerEventsBind" class="w-60">
          <el-option v-for="item in AllPointerEvents" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="setting-item justify-between">
        <h3>定位</h3>
        <el-select v-model="positionBind" class="w-60">
          <el-option v-for="item in AllPosition" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
    </template>
  </div>
</template>

<style scoped lang="scss">
ul.style-box {
  display: flex;
  flex-wrap: wrap;
  font-size: 18px;

  li {
    padding: 4px 6px 0;
    margin: 1px;
    text-align: center;
    background-color: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    background-color: #f5f5f5;
    transition: 0.2s;
    position: relative;
    &:hover {
      background-color: var(--el-color-primary-dark-2);
      color: #fff;
      &::before,
      &::after {
        display: block;
      }
    }
    &::before,
    &::after {
      position: absolute;
      z-index: 100;
      left: 50%;
      transform: translateX(-50%);
    }
    &::before {
      // 三角
      content: '';
      display: none;
      top: -6px;
      border: 6px solid transparent;
      border-top-color: rgba(0, 0, 0, 0.8);
    }
    &::after {
      content: attr(data-tip);
      display: none;
      top: -34px;
      padding: 8px 10px;
      color: #fff;
      font-size: 12px;
      line-height: 12px;
      border-radius: 4px;
      white-space: nowrap;
      background-color: rgba(0, 0, 0, 0.8);
    }
  }
}

.rotate-label {
  div {
    width: 66px;
    text-align: center;
    color: #999;
  }
}

.icon-box {
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 3px;
  &.active {
    background-color: var(--el-color-primary-dark-2);
    color: #fff;
  }
  &:hover {
    border: 1px solid var(--el-color-primary-dark-2);
  }
}
</style>
