<script setup lang="ts">
import HiDesign from 'design/index.vue'
import { useDesignTemp } from '~/src/views/__/design'
import Mobile from './components/mobile.vue'
import Pcwall from './components/pcwall.vue'

definePage({ meta: { label: '排座抽奖' } })

const designTemp = useDesignTemp()

const interactive = 'seglottery'

</script>

<template>
  <HiDesign :interactive="interactive" />
  <Pcwall v-if="designTemp.showType === 'pcwall'" />
  <Mobile v-if="designTemp.showType === 'mobile'" />
</template>
