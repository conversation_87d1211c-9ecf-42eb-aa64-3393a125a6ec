<script setup lang="ts">
import { openSelectMaterial } from '@/views/__/design'
import { Howl } from 'howler/dist/howler.core.min'

const props = defineProps<{
  url?: string
  name?: string
  loop?: boolean
  playCount?: number
  volume?: number
  rate?: number
  trim?: number[] | null
  delay?: number
}>()

const emits = defineEmits<{
  (e: 'selection', val: string): void
  (e: 'loaded', val: Howl): void
}>()

const seek = ref(0) // 当前时间
const duration = ref(0) // 结束时间
const spriteId = ref() // 音乐片段id
const playingMusic = ref<Howl | null>(null) // 当前音乐
const animationFrameId = ref<number>(0) // 动画帧id
const isPlay = computed(() => playingMusic.value?.playing(spriteId.value))
const formatMusicTime = computed(() => {
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`
  }
  return `${formatTime(duration.value)}`
})

watch(() => props.url, initMusic)
watch(() => props.volume, () => playingMusic.value?.volume(props.volume || 0))
watch(() => props.loop, () => playingMusic.value?.loop(props.loop || false))
watch(() => props.rate, () => isPlay.value && playingMusic.value?.rate(props.rate || 1))
watchDebounced(() => props.trim, initMusic, { debounce: 300 })

function initMusic() {
  if (!props.url) {
    return
  }

  let count = 0
  const [str, end] = props.trim || [0, 100]

  if (playingMusic.value) {
    playingMusic.value.stop()
    playingMusic.value.unload()
    playingMusic.value = null
  }

  const _offset = (str / 100 * duration.value) * 1000
  const _duration = duration.value === 0 ? Infinity : (end / 100 * duration.value) * 1000 - _offset

  playingMusic.value = new Howl({
    src: [props.url!],
    preload: 'metadata',
    html5: true,
    volume: props.volume,
    rate: props.rate || 1,
    loop: props.loop || false,
    sprite: {
      actualSound: [_offset, _duration],
    },
    onload() {
      duration.value = playingMusic.value?.duration() as number
      emits('loaded', playingMusic.value as Howl)
    },
    onloaderror(id, error) {
      console.error('加载错误', error)
    },
    onplay: updateProgressBar,
    onend() {
      count++
      if (props.playCount && count < props.playCount) {
        spriteId.value = playingMusic.value?.play('actualSound')
      } else {
        cancelAnimationFrame(animationFrameId.value)
      }
    },
    onpause: () => cancelAnimationFrame(animationFrameId.value),
    onstop: () => cancelAnimationFrame(animationFrameId.value),
  })
}

function _handleMusicPlayback() {
  const music = playingMusic.value
  if (!music) return

  const isPlaying = music.playing(spriteId.value)

  if (isPlaying) {
    music.loop(false) // 确保循环中的音乐不会再次播放
    music.stop(spriteId.value)
  } else {
    music.loop(props.loop || false)
    spriteId.value = music.play('actualSound')
  }
}

async function selectMaterial() {
  const result = await openSelectMaterial('MUSIC')
  if (result) {
    emits('selection', result)
  }
}

const progress = ref(0)
function updateProgressBar() {
  if (playingMusic.value) {
    const sound = playingMusic.value
    seek.value = sound.seek() as number
    progress.value = (sound.seek() as number) / sound.duration()
    animationFrameId.value = requestAnimationFrame(updateProgressBar)
  }
}

onMounted(() => {
  initMusic()
})
</script>

<template>
  <div class="info">
    <!-- <div class="play h-30 w-30 cursor-pointer" @click="handleMusicPlayback">
      <icon-ph-pause-circle v-if="isPlay" class="h-full w-full" />
      <icon-ph-play-circle v-else class="h-full w-full" />
    </div> -->
    <div class="ml-5 h-full flex flex-1 flex-col justify-center">
      <template v-if="playingMusic">
        <div class="flex justify-between">
          <el-popover
            placement="top-start"
            trigger="hover"
            :content="name || url"
            :disabled="!name && !url"
          >
            <template #reference>
              <el-text class="mb-2 w-60" truncated>
                {{ name || url || '暂无音乐' }}
              </el-text>
            </template>
          </el-popover>
          <div class="menu">{{ formatMusicTime }}</div>
        </div>
      </template>
      <span v-else>无音乐</span>
    </div>
    <div class="operate">
      <div @click="selectMaterial">选择</div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.info {
  width: 120px;
  display: flex;
  align-items: center;
  height: 30px;
  padding: 2px 10px;
  box-sizing: border-box;
  border: 1px solid #ebebeb;
  background-color: #fdfdfd;
  position: relative;
  border-radius: 20px;
  position: relative;
  overflow: hidden;
  cursor: pointer;

  .operate {
    position: absolute;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 100%;
    background: #e6ebed;
    border-radius: 50px;
    overflow: hidden;
    transform: translateX(140px);
    transition: all 0.3s ease-in-out;
    &:hover {
      background: #d9e0e7;
    }
    div {
      width: 50%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
  }
}

.info:hover .operate {
  transform: translateX(0);
  transition: all 0.3s ease-in-out;
}

.progress-container {
  --progress-bar-width: 0;

  width: 90%;
  height: 5px;
  background-color: #e6ebed;
  border-radius: 2.5px;
  overflow: hidden;
  margin-top: 5px;

  .progress-bar {
    height: 100%;
    background-color: #1261ff;
    width: var(--progress-bar-width);
  }
}
</style>
