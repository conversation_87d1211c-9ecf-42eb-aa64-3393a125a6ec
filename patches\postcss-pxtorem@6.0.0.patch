diff --git a/index.js b/index.js
index 9b9f8a0ff34cfa85aab69529ee8de217a4bd3692..58443f467098f673347f37c674a73a8d3d0b78a8 100644
--- a/index.js
+++ b/index.js
@@ -116,6 +116,12 @@ function createPropListMatcher(propList) {
   };
 }
 
+const __optsObj = {}
+const __satisfyPropListObj = {}
+const __excludeObj = {}
+const __isExcludeFileObj = {}
+const __pxReplaceObj = {}
+
 module.exports = (options = {}) => {
   convertLegacyOptions(options);
   const opts = Object.assign({}, defaults, options);
@@ -147,10 +153,20 @@ module.exports = (options = {}) => {
         opts.unitPrecision,
         opts.minPixelValue
       );
+
+      __optsObj[filePath] = opts
+      __satisfyPropListObj[filePath] = satisfyPropList
+      __excludeObj[filePath] = exclude
+      __isExcludeFileObj[filePath] = isExcludeFile
+      __pxReplaceObj[filePath] = pxReplace
     },
     Declaration(decl) {
+      isExcludeFile = __isExcludeFileObj[decl.source.input.file]
       if (isExcludeFile) return;
 
+      // opts = __optsObj[decl.source.input.file]
+      // satisfyPropList = __satisfyPropListObj[decl.source.input.file]
+
       if (
         decl.value.indexOf("px") === -1 ||
         !satisfyPropList(decl.prop) ||
@@ -158,6 +174,8 @@ module.exports = (options = {}) => {
       )
         return;
 
+      pxReplace = __pxReplaceObj[decl.source.input.file]
+
       const value = decl.value.replace(pxRegex, pxReplace);
 
       // if rem unit already exists, do not add or replace