import { HiRequest } from '../request'

export default {
  configRead: (params: any) => HiRequest.post('/pro/hxc/mobile/prodiglettv3config/read.htm', params),
  read: (params: any) => HiRequest.post('/pro/hxc/mobile/prodiglettv3/read.htm', params),
  ranking: (params: any) => HiRequest.post('/pro/hxc/mobile/prodiglettv3/ranking.htm', params),

  reportRecord: (params: any) => HiRequest.post('/pro/hxc/mobile/prodiglettv3record/report.htm', params),
  readRecord: (params: any) => HiRequest.post('/pro/hxc/mobile/prodiglettv3record/read.htm', params),
  recordFlushed: (params: any) => HiRequest.post('/pro/hxc/mobile/prodiglettv3record/flushed.htm', params),

  readRegedit: (params: any) => HiRequest.post('/pro/hxc/mobile/prodiglettv3regedit/read.htm', params),
  insertRegedit: (params: any) => HiRequest.post('/pro/hxc/mobile/prodiglettv3regedit/insert.htm', params),
  quitRegedit: (params: any) => HiRequest.post('/pro/hxc/mobile/prodiglettv3regedit/quit.htm', params),

  listTeam: (params: any) => HiRequest.post('/pro/hxc/mobile/prodiglettv3team/list.htm', params),
  readTeam: (params: any) => HiRequest.post('/pro/hxc/mobile/prodiglettv3team/read.htm', params),

}
