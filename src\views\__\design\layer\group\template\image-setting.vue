<script setup lang="ts">
import { openSelectMaterial, useDataAttr } from '../../..'
import { defaultMode, defaultSize, type IDesignImage, type IMode } from '../../image/image'

defineProps<{
  label: string
}>()
const layer = defineModel<IDesignImage>('setting', { required: true })
const modes: { label: string, value: IMode }[] = [
  { label: '手动', value: 'none' },
  { label: '适应', value: 'contain' },
  { label: '拉伸', value: 'fill' },
  { label: '裁剪', value: 'cover' },
]
async function updateMaterialFn() {
  const result = await openSelectMaterial('PIC')
  if (result) {
    layer.value.data = result
  }
}
const modeBind = useDataAttr(layer.value, 'mode', defaultMode)
const sizeXBind = useDataAttr(layer.value, 'sizeX', defaultSize)
const sizeYBind = useDataAttr(layer.value, 'sizeY', defaultSize)
const colorBind = useDataAttr(layer.value, 'color', '')
const bgColorBind = useDataAttr(layer.value.style, 'background', 'rgba(0, 0, 0, 0)')
</script>

<template>
  <div class="setting-wrap">
    <h3>{{ label }}</h3>
    <div class="setting-item justify-end!">
      <div class="thumbnail-box bgblank" @click="updateMaterialFn">
        <img :src="layer.data">
      </div>
    </div>
    <div class="setting-item">
      <h3>显示模式</h3>
      <el-select v-model="modeBind" placeholder="选择填充方式" class="w-100">
        <el-option v-for="item in modes" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </div>
    <div v-if="modeBind === 'none'" class="setting-item">
      <h3>横向大小</h3>
      <el-slider v-model="sizeXBind" :min="0" :max="100" :step="1" :format-tooltip="v => `${v}%`" class="mx-10 flex-1" />
    </div>
    <div v-if="modeBind === 'none'" class="setting-item">
      <h3>纵向大小</h3>
      <el-slider v-model="sizeYBind" :min="0" :max="100" :step="1" :format-tooltip="v => `${v}%`" class="mx-10 flex-1" />
    </div>
    <div class="setting-item">
      <div class="mr-5 flex items-center">
        <el-tooltip effect="dark" content="该设置项只对透明图片有效">
          <icon-ph-question-bold class="ml-5" />
        </el-tooltip>
        图片颜色
      </div>
      <hi-color v-model="colorBind" type="both" />
    </div>
    <div class="setting-item">
      <h3>背景色</h3>
      <hi-color v-model="bgColorBind" type="both" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.setting-wrap {
  padding: 0 !important;
  .setting-item {
    justify-content: flex-end !important;
  }
}
.thumbnail-box {
  width: 100%;
  aspect-ratio: 16 / 9;
  outline: 1px solid #e6ebed;
  position: relative;
  img {
    position: absolute;
    width: 100%;
    height: 100%;
  }

  > img {
    object-fit: contain;
  }
}
</style>
