import { HiRequest } from '../request'

export default {
  configRead: (params: any) => HiRequest.post('/pro/hxc/mobile/promoneyv3config/read.htm', params),
  read: (params: any) => HiRequest.post('/pro/hxc/mobile/promoneyv3/read.htm', params),
  ranking: (params: any) => HiRequest.post('/pro/hxc/mobile/promoneyv3/ranking.htm', params),

  reportRecord: (params: any) => HiRequest.post('/pro/hxc/mobile/promoneyv3record/report.htm', params),
  readRecord: (params: any) => HiRequest.post('/pro/hxc/mobile/promoneyv3record/read.htm', params),

  readRegedit: (params: any) => HiRequest.post('/pro/hxc/mobile/promoneyv3regedit/read.htm', params),
  insertRegedit: (params: any) => HiRequest.post('/pro/hxc/mobile/promoneyv3regedit/insert.htm', params),
  quitRegedit: (params: any) => HiRequest.post('/pro/hxc/mobile/promoneyv3regedit/quit.htm', params),

  listTeam: (params: any) => HiRequest.post('/pro/hxc/mobile/promoneyv3team/list.htm', params),
  readTeam: (params: any) => HiRequest.post('/pro/hxc/mobile/promoneyv3team/read.htm', params),
}
