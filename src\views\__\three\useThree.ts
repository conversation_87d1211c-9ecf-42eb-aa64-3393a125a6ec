import type { BloomPass } from 'three/examples/jsm/postprocessing/BloomPass.js'
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { TrackballControls } from 'three/examples/jsm/controls/TrackballControls.js'
import { GUI } from 'three/examples/jsm/libs/lil-gui.module.min.js'
import Stats from 'three/examples/jsm/libs/stats.module.js'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'
import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer.js'
import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass.js'
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass.js'
import { UnrealBloomPass } from 'three/examples/jsm/postprocessing/UnrealBloomPass.js'
import { FXAAShader } from 'three/examples/jsm/shaders/FXAAShader.js'

interface IParmars {
  threeRef: Ref<HTMLElement | null>
  hasHelp?: boolean
  hasControls?: boolean
  hasAmbientLight?: boolean
  openFxaa?: boolean
  logarithmicDepthBuffer?: boolean
}

type IAnimation = (time: DOMHighResTimeStamp, frame: any) => void | false

const gltfLoader = new GLTFLoader()
const textureLoader = new THREE.TextureLoader()

export interface IUV {
  row: number // 总行数
  col: number // 总列数
  rowIndex: number // 行索引
  colIndex: number // 列索引
  x: number // 物体相对uv的x
  y: number // 物体相对uv的y
  uv: THREE.BufferAttribute // uv
  total: number // 总的像素点
  count: number // 有效点数量
  mapX: number // 使用该uv的纹理x坐标
  mapY: number // 使用该uv的纹理y坐标
  width: number // 单个图片宽度
  height: number // 单个图片高度
}

/**
 * 加载图片
 * @param src 图片路径
 * @returns 图片对象
 */
function loadImg(src: string) {
  return new Promise<HTMLImageElement>((resolve, reject) => {
    const img = new Image()
    img.setAttribute('crossOrigin', '')
    img.src = src
    img.onload = () => resolve(img)
    img.onerror = e => reject(e)
  })
}

/**
 * 将图片四周的透明像素去掉
 * @param img 图片
 * @param xSpace x轴方向的两边留白
 * @param ySpace y轴方向的两边留白
 */
function removeWhiteSpace(img: HTMLImageElement, xSpace = 0, ySpace = 0) {
  const canvas = document.createElement('canvas')
  canvas.width = img.width
  canvas.height = img.height
  const ctx = canvas.getContext('2d')!
  ctx.clearRect(0, 0, img.width, img.height)
  ctx.drawImage(img, 0, 0, img.width, img.height, 0, 0, img.width, img.height)
  const imageData = ctx.getImageData(0, 0, img.width, img.height)
  const data = imageData.data

  // 计算上下左右的边界
  let left = img.width
  let right = 0
  let top = img.height
  let bottom = 0
  for (let i = 0; i < data.length; i += 4) {
    // const r = data[i]
    // const g = data[i + 1]
    // const b = data[i + 2]
    const a = data[i + 3]
    if (a === 0) continue
    const y = Math.floor(i / 4 / img.width)
    const x = i / 4 - y * img.width
    if (x < left) left = x
    if (x > right) right = x
    if (y < top) top = y
    if (y > bottom) bottom = y
  }
  left -= xSpace
  right += xSpace
  top -= ySpace
  bottom += ySpace
  if (left < 0) left = 0
  if (right > img.width) right = img.width
  if (top < 0) top = 0
  if (bottom > img.height) bottom = img.height
  const cutWidth = right - left
  const cutHeight = bottom - top
  const cutCanvas = document.createElement('canvas')
  cutCanvas.width = cutWidth
  cutCanvas.height = cutHeight
  const newCtx = cutCanvas.getContext('2d')!
  newCtx.clearRect(0, 0, cutWidth, cutHeight)
  newCtx.drawImage(
    img, //
    left,
    top,
    cutWidth,
    cutHeight,
    0,
    0,
    cutWidth,
    cutHeight,
  )
  const cutImg = new Image()
  cutImg.src = cutCanvas.toDataURL('image/png')
  return {
    cutCanvas,
    cutImg,
    left,
    right,
    top,
    bottom,
    cutWidth,
    cutHeight,
  }
}

function validCount(imgData: ImageData, firstColor: string) {
  const { data } = imgData
  let count = 0
  for (let i = 0; i < data.length; i += 4) {
    const r = data[i]
    const g = data[i + 1]
    const b = data[i + 2]
    const a = data[i + 3]
    if (a === 0) continue
    const color = `${r},${g},${b}`
    if (color === firstColor) continue
    count++
  }
  return count
}

/**
 * 将图片处理成符合条件的尺寸
 * 1. 单个方格的宽和高必须是偶数
 * 2. 宽高除不尽的部分需要补充透明像素
 */
function standardImage(img: HTMLImageElement, width: number, height: number) {
  // 1. 单个方格的宽和高必须是偶数
  width = Number.parseInt(`${width}`)
  height = Number.parseInt(`${height}`)
  const itemWidth = width % 2 === 0 ? width : width + 1
  const itemHeight = height % 2 === 0 ? height : height + 1
  // 2. 宽高除不尽的部分需要补充透明像素
  const row = Math.ceil(img.height / itemHeight)
  const col = Math.ceil(img.width / itemWidth)
  const newWidth = col * itemWidth
  const newHeight = row * itemHeight
  const canvas = document.createElement('canvas')
  canvas.width = newWidth
  canvas.height = newHeight
  const ctx = canvas.getContext('2d')!
  ctx.clearRect(0, 0, newWidth, newHeight)
  ctx.drawImage(img, 0, 0, img.width, img.height, 0, 0, newWidth, newHeight)
  return new Promise<{
    itemWidth: number
    itemHeight: number
    logoImg: HTMLImageElement
  }>((resolve) => {
    const newImg = new Image()
    newImg.src = canvas.toDataURL('image/png')
    newImg.onload = () => {
      resolve({
        itemWidth,
        itemHeight,
        logoImg: newImg,
      })
    }
  })
}

/**
 * 生成uv切割等信息
 * @param img 图片
 * @param width 显示区域宽度
 * @param height 显示区域高度
 * @param itemWidth 单个图片宽度
 * @param itemHeight 单个图片高度
 * @returns \{uvs, alphaMap}
 */
function generateUVs(img: HTMLImageElement, width: number, height: number, itemWidth: number, itemHeight: number) {
  // TODO uv 单双行切割太整齐，可以考虑做个随机偏移

  const canvas = document.createElement('canvas')
  canvas.width = width
  canvas.height = height
  const ctx = canvas.getContext('2d', { willReadFrequently: true })!
  // 图片整理成符合要求的尺寸
  ctx.drawImage(img, 0, 0, img.width, img.height, 0, 0, width, height)
  const firstData = ctx.getImageData(0, 0, 1, 1)
  const first = firstData.data
  const firstColor = first[3] === 0 ? '255,255,255' : `${first[0]},${first[1]},${first[2]}`
  const total = itemWidth * itemHeight

  const halfItemWidth = itemWidth / 2
  const halfItemHeight = itemHeight / 2

  // TODO 如果Plane设置了Segments, 会影响uv的计算，暂时不支持
  const uvs: IUV[] = []

  const col = Math.floor(width / itemWidth)
  const row = Math.floor(height / itemHeight)

  // 如果宽度和高度不是整数倍，会有一部分被忽略，会影响uv的计算
  const uvX = (col * itemWidth) / width
  const uvY = (row * itemHeight) / height

  // 因为宽度高度有误差，所以需要将误差算进去才能解决位置计算带来的偏差
  const deviationX = (width - col * itemWidth) / 2
  const deviationY = (height - row * itemHeight) / 2

  const stepRow = uvY / row
  const stepCol = uvX / col
  for (let r = 0; r < row; r++) {
    for (let c = 0; c < col; c++) {
      const fx = c * stepCol
      const fy = 1 - r * stepRow

      const p1 = [fx, fy]
      const p2 = [fx + stepCol, fy]
      const p3 = [fx, 1 - (r + 1) * stepRow]
      const p4 = [fx + stepCol, 1 - (r + 1) * stepRow]

      const uv = new THREE.BufferAttribute(new Float32Array(8), 2)
      uv.setXY(0, p1[0], p1[1])
      uv.setXY(1, p2[0], p2[1])
      uv.setXY(2, p3[0], p3[1])
      uv.setXY(3, p4[0], p4[1])

      const x = c * itemWidth - (col * itemWidth) / 2 + halfItemWidth - deviationX
      const y = -(r * itemHeight - (row * itemHeight) / 2 + halfItemHeight - deviationY)

      const imageData = ctx.getImageData(c * itemWidth, r * itemHeight, itemWidth, itemHeight)
      const count = validCount(imageData, firstColor)
      uvs.push({
        row,
        col,
        rowIndex: r,
        colIndex: c,
        x,
        y,
        uv,
        total,
        count,
        mapX: c * itemWidth,
        mapY: r * itemHeight,
        width: itemWidth,
        height: itemHeight,
      })
    }
  }

  //////////////////
  const imageData = ctx.getImageData(0, 0, width, height)
  const data = imageData.data
  for (let i = 0; i < data.length; i += 4) {
    const r = data[i]
    const g = data[i + 1]
    const b = data[i + 2]
    const a = data[i + 3]
    const color = `${r},${g},${b}`
    let num = 0
    if (a !== 0 && color !== firstColor) {
      num = 255
    }
    data[i] = num
    data[i + 1] = num
    data[i + 2] = num
    data[i + 3] = num
  }
  ctx.putImageData(imageData, 0, 0)
  const alphaMap = new THREE.CanvasTexture(canvas)
  alphaMap.colorSpace = THREE.SRGBColorSpace
  alphaMap.needsUpdate = true
  return { uvs, alphaMap }
}

/**
 * 向CanvasTexture中绘制图片，位置根据uv计算
 * @param canvas
 * @param canvasTexture
 * @param img             图片
 * @param item            splitUv对象
 * @param scale           缩放大小（如果绘制的太小，文字可能会很模糊，可以提高该值解决）
 */
function drawImageToCanvasTextureByUV(canvas: HTMLCanvasElement, canvasTexture: THREE.CanvasTexture, img: HTMLImageElement | HTMLCanvasElement, item: IUV, scale: number) {
  const ctx = canvas.getContext('2d')!
  const itemWidth = item.width
  const itemHeight = item.height
  // 保证文字不会超出, 不变型，并且画到中心
  const imgRatio = img.width / img.height
  const itemRatio = itemWidth / itemHeight
  if (imgRatio > itemRatio) {
    // 宽度超出
    const newWidth = itemWidth
    const newHeight = newWidth / imgRatio
    const newY = item.mapY + (itemHeight - newHeight) / 2
    ctx.drawImage(
      img, //
      0,
      0,
      img.width,
      img.height,
      item.mapX * scale,
      newY * scale,
      newWidth * scale,
      newHeight * scale,
    )
  } else {
    // 高度超出
    const newHeight = itemHeight
    const newWidth = newHeight * imgRatio
    const newX = item.mapX + (itemWidth - newWidth) / 2
    ctx.drawImage(
      img, //
      0,
      0,
      img.width,
      img.height,
      newX * scale,
      item.mapY * scale,
      newWidth * scale,
      newHeight * scale,
    )
  }
  canvasTexture.needsUpdate = true
}

/**
 * draw img to canvas (contain, cover)
 * 默认 contain
 */
function drawImageToCanvas(img: HTMLImageElement, canvas: HTMLCanvasElement, type: 'contain' | 'cover' = 'contain') {
  const imageWidth = img.width
  const imageHeight = img.height
  const canvasWidth = canvas.width
  const canvasHeight = canvas.height

  let scale = Math.min(canvasWidth / imageWidth, canvasHeight / imageHeight)
  if (type === 'cover') {
    scale = Math.max(canvasWidth / imageWidth, canvasHeight / imageHeight)
  }

  const srcX = (canvasWidth - imageWidth * scale) / 2
  const srcY = (canvasHeight - imageHeight * scale) / 2

  const ctx = canvas.getContext('2d')!
  ctx.drawImage(img, srcX, srcY, imageWidth * scale, imageHeight * scale)
}

// 通用
function loadTexture(img: string | HTMLImageElement, onLoad?: (data: THREE.Texture) => void) {
  if (img instanceof HTMLImageElement) {
    const texture = new THREE.Texture()
    texture.image = img
    texture.colorSpace = THREE.SRGBColorSpace
    texture.needsUpdate = true
    if (onLoad) onLoad(texture)
    return texture
  }
  const texture = textureLoader.load(img, onLoad)
  texture.colorSpace = THREE.SRGBColorSpace
  return texture
}
function loadMaterial(url: string) {
  return new THREE.MeshStandardMaterial({
    map: loadTexture(url),
    transparent: true,
    side: THREE.DoubleSide,
  })
}

// 等比例的情况下找到最大的宽和高
function arrangeSize(img: HTMLImageElement, width: number, height: number) {
  const ratio1 = img.width / img.height
  const ratio2 = width / height
  if (ratio1 > ratio2) {
    return { width, height: width / ratio1 }
  } else {
    return { width: height * ratio1, height }
  }
}

// 获取mask的shader
function getMaskShader() {
  // --- Shader Code ---
  const vertexShader = `
varying vec2 vUv;
void main() {
  vUv = uv;
  gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
}
`

  const fragmentShader = `
  uniform sampler2D map; // Avatar texture
  uniform sampler2D maskMap; // Mask texture
  uniform float opacity; // Overall opacity controlled by animation
  varying vec2 vUv;

  void main() {
    vec4 mapColor = texture2D(map, vUv);
    vec4 maskColor = texture2D(maskMap, vUv);

    // Use the mask's alpha channel (or red channel if it's grayscale) to control transparency
    // Assuming a mask where white/opaque areas should be visible, black/transparent areas hidden.
    float maskValue = maskColor.a; // Or maskColor.r if using red channel

    // Combine map color with mask and overall opacity
    gl_FragColor = vec4(mapColor.rgb, mapColor.a * maskValue * opacity);
  }
`
  return {
    vertexShader,
    fragmentShader,
  }
}

export default function useThree(
  {
    threeRef, //
    hasHelp = true,
    hasControls = true,
    hasAmbientLight = true,
    openFxaa = false,
    logarithmicDepthBuffer = true,
  }: IParmars,
  debugLevel = 0,
) {
  // 节点
  const { width, height } = useElementSize(threeRef)

  // 渲染器
  const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true, logarithmicDepthBuffer })
  renderer.setPixelRatio(window.devicePixelRatio)
  renderer.setSize(0, 0)
  renderer.shadowMap.enabled = true

  // 场景
  const scene = new THREE.Scene()

  // 辅助线
  if (debugLevel > 2 && hasHelp) {
    const axesHelper = new THREE.AxesHelper(500)
    scene.add(axesHelper)
  }

  // 相机
  const camera = new THREE.PerspectiveCamera(70, window.innerWidth / window.innerHeight, 0.1, 10000)
  camera.position.z = 300

  // 环境光
  if (hasAmbientLight) {
    scene.add(new THREE.AmbientLight(0xFFFFFF, 1))
  }

  // 控制器
  let controls: OrbitControls | TrackballControls | null = null
  if (hasControls) {
    controls = new OrbitControls(camera, renderer.domElement)
    controls.enableDamping = true

    // controls = new TrackballControls(camera, renderer.domElement)
    // controls.rotateSpeed = 6
    // controls.zoomSpeed = 2
    // controls.panSpeed = 0.6
    // controls.keys = ['KeyA', 'KeyS', 'KeyD']
  }

  // gui
  let gui: GUI
  const guiInit = ref<(gui: GUI) => void>()
  // 监控
  let stats: Stats
  if (debugLevel > 1) {
    watch(
      () => {
        const arr = []
        if (guiInit.value) arr.push(1)
        if (threeRef.value) arr.push(2)
        return arr.join(',')
      },
      () => {
        if (gui) gui.destroy()
        if (guiInit.value) {
          gui = new GUI({ container: threeRef.value! })
          guiInit.value(gui)
          gui.open()
        }
      },
    )
    stats = new Stats()
  }

  // 后期 - 通用
  let composer: EffectComposer | null
  const composerInit = ref<(composer: EffectComposer) => void>()
  watchOnce(composerInit, () => {
    if (composer) {
      composer.dispose()
    }
    if (!composerInit.value) return

    composer = new EffectComposer(renderer)
    const renderPass = new RenderPass(scene, camera)
    composerInit.value(composer)
    composer.addPass(renderPass)

    composer.setSize(width.value, height.value)
  })

  // 后期 - 泛光
  const bloomInit = ref<{
    strength: number
    radius: number
    threshold: number
  }>()
  let bloomComposer: EffectComposer | null
  let bloomPass: BloomPass | UnrealBloomPass | null
  let finalComposer: EffectComposer | null
  watch(
    () => [bloomInit.value, width.value, height.value],
    () => {
      const params = bloomInit.value
      if (!params) return
      if (bloomPass) {
        Object.assign(bloomPass, params)
        return
      }
      // 初始bloom环境
      const renderPass = new RenderPass(scene, camera)

      // 第一种简单bloom，类似模糊的实现，效果不如第二种
      // bloomPass = new BloomPass(params.strength)

      // 第二种bloom，更加真实
      bloomPass = new UnrealBloomPass(
        new THREE.Vector2(width.value, height.value), //
        params.strength,
        params.radius,
        params.threshold,
      )

      bloomComposer = new EffectComposer(renderer)
      bloomComposer.renderToScreen = false
      bloomComposer.addPass(renderPass)
      bloomComposer.addPass(bloomPass)
      bloomComposer.setSize(width.value, height.value)

      const finalPass = new ShaderPass(
        new THREE.ShaderMaterial({
          uniforms: {
            baseTexture: { value: null },
            bloomTexture: { value: bloomComposer.renderTarget2.texture },
          },
          vertexShader: `
            varying vec2 vUv;
            void main() {
              vUv = uv;
              gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );
            }
          `,
          // 此处跟官网提供的不一致，为了解决使用了bloom后背景透明失效的问题
          // https://github.com/mrdoob/three.js/issues/14104
          fragmentShader: `
            uniform sampler2D baseTexture;
            uniform sampler2D bloomTexture;
            varying vec2 vUv;
            void main() {
              vec4 base_color = texture2D(baseTexture, vUv);
              vec4 bloom_color = texture2D(bloomTexture, vUv);

              float lum = 0.21 * bloom_color.r + 0.71 * bloom_color.g + 0.07 * bloom_color.b;
              gl_FragColor = vec4(base_color.rgb + bloom_color.rgb, max(base_color.a, lum));
            }
          `,
          defines: {},
        }),
        'baseTexture',
      )
      finalPass.needsSwap = true

      finalComposer = new EffectComposer(renderer)
      finalComposer.addPass(renderPass)
      finalComposer.addPass(finalPass)
      // 加过 outputPass 后模型也会变模糊，不加也能正常辉光
      // finalComposer.addPass(outputPass)
      // 抗锯齿(图片会变模糊)
      if (openFxaa) {
        finalComposer.addPass(new ShaderPass(FXAAShader))
      }
      finalComposer.setSize(width.value, height.value)
    },
    { deep: true },
  )
  const bloomLayer = new THREE.Layers()
  bloomLayer.set(6) // 层级为6的是辉光层
  const bloomMaterials: any = {}
  const meshDarkMaterial = new THREE.MeshBasicMaterial({ color: 'black' })
  const spriteDarkMaterial = new THREE.SpriteMaterial({ color: 'black' })
  const lineBasicMaterial = new THREE.LineBasicMaterial({ color: 'black' })
  const pointsMaterial = new THREE.PointsMaterial({ color: 'black' })
  const darkenNonBloomed = (obj: any) => {
    if (obj.material && bloomLayer.test(obj.layers) === false) {
      bloomMaterials[obj.uuid] = obj.material
      if (obj.isMesh) {
        obj.material = meshDarkMaterial
      } else if (obj.isSprite) {
        obj.material = spriteDarkMaterial
      } else if (obj.isLine) {
        obj.material = lineBasicMaterial
      } else if (obj.isPoints) {
        obj.material = pointsMaterial
      } else {
        console.warn('警告：未知类型', obj)
      }
    }
  }
  const restoreMaterial = (obj: any) => {
    if (bloomMaterials[obj.uuid]) {
      obj.material = bloomMaterials[obj.uuid]
      delete bloomMaterials[obj.uuid]
    }
  }

  // 调整大小
  const resize = ref<(width: number, height: number) => void>()
  watch(resize, () => {
    resize.value?.(width.value, height.value)
  })
  const updateSize = () => {
    camera.aspect = width.value / height.value
    camera.updateProjectionMatrix()
    renderer.setSize(width.value, height.value)
    if (composer) composer.setSize(width.value, height.value)
    if (bloomComposer) bloomComposer.setSize(width.value, height.value)
    if (finalComposer) finalComposer.setSize(width.value, height.value)
    resize.value?.(width.value, height.value)
    if (controls instanceof TrackballControls) {
      controls.handleResize()
    }
  }
  watch(() => [width.value, height.value], updateSize)
  watchOnce(threeRef, () => {
    threeRef.value?.appendChild(renderer.domElement)
    if (stats) {
      stats.dom.style.position = 'absolute'
      threeRef.value?.appendChild(stats.dom)
    }
    updateSize()
  })

  // 动画
  const animation = ref<IAnimation>(() => { })
  watch(
    animation,
    () => {
      renderer.setAnimationLoop((time: DOMHighResTimeStamp, frame: any) => {
        if (stats) {
          stats.update()
        }
        if (controls instanceof OrbitControls && controls.enableDamping) {
          controls.update()
        } else if (controls instanceof TrackballControls) {
          controls.update()
        }
        const res = animation.value?.(time, frame)
        if (res === false) {
          return
        }
        if (bloomComposer?.renderTarget1.width && finalComposer?.renderTarget1.width) {
          scene.traverse(darkenNonBloomed)
          bloomComposer.render()
          scene.traverse(restoreMaterial)
          finalComposer.render()
          return
        }
        if (composer?.renderTarget1.width) {
          composer.render()
        }
        // 后期渲染必须要保证 composer 的size设置后才能执行render，否则会报警告
        renderer.render(scene, camera)
      })
    },
    { immediate: true },
  )

  const videoAutoPlay = () => {
    if (!threeRef.value) return
    const threeDom = threeRef.value as HTMLElement
    const videos = threeDom.querySelectorAll('video')
    for (const video of videos) {
      try {
        if (video.paused && video.autoplay) {
          video.play()
        }
      } catch { }
    }
  }
  onMounted(() => {
    if (threeRef.value) {
      threeRef.value.addEventListener('click', videoAutoPlay, { once: true })
    }
  })
  // 资源销毁
  onUnmounted(() => {
    if (composer) {
      composer.dispose()
      composer = null
    }
    // 销毁后期
    if (bloomComposer) {
      bloomComposer.dispose()
      bloomComposer = null
    }
    if (bloomPass) {
      bloomPass.dispose()
      bloomPass = null
    }
    if (finalComposer) {
      finalComposer.dispose()
      finalComposer = null
    }

    scene.traverse((item: any) => {
      if (item instanceof THREE.Mesh) {
        item.geometry.dispose()
        if (item.material instanceof THREE.Material) {
          item.material.dispose()
        } else if (Array.isArray(item.material)) {
          item.material.forEach((m: THREE.Material) => m.dispose())
        }
      }
    })
    renderer.setAnimationLoop(null)
    if (gui) {
      gui.destroy()
    }
  })

  //////////////////////////////////////////
  // 获取指定z位置的宽高
  const getViewSizeAtZ = (z = 0) => {
    const cameraZ = camera.position.z - z
    const fov = camera.fov * (Math.PI / 180)
    const height = 2 * Math.tan(fov / 2) * Math.abs(cameraZ)
    const width = height * camera.aspect
    return { width, height }
  }

  return {
    renderer,
    scene,
    camera,
    controls,
    animation,
    resize,
    guiInit,
    composerInit,
    bloomInit,
    //
    loadImg,
    removeWhiteSpace,
    standardImage,
    generateUVs,
    arrangeSize,
    drawImageToCanvasTextureByUV,
    drawImageToCanvas,
    loadTexture,
    loadMaterial,
    //
    loadGltf(url: string) {
      return gltfLoader.loadAsync(url)
    },
    //
    getViewSizeAtZ,
    getMaskShader,
  }
}
