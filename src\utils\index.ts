import { isArray, isBoolean, isEqual, isNumber, isObject, isString, transform } from 'lodash-es'
import mitt from 'mitt'
import { isWeiXin } from '../views/mobile/utils/env'
import { envUtils } from './env'
import { sign } from '../views/mobile/utils/jssdk'
import { showImagePreview } from 'vant'

export function randomNumber(min: number, max: number) {
  const range = max - min
  const value = Math.floor(Math.random() * range) + min
  return value
}
// random
export function random(length: number) {
  let str = ''
  for (let i = 0; i < length; i++) {
    str += String.fromCharCode(randomNumber(97, 123))
  }
  return str
}

/**
 * Deep diff between two object, using lodash
 * @param  {object} data Object compared
 * @param  {object} originData   Object to compare with
 * @param  {object} required   Object to compare with
 * @return {object}        Return a new object who represent the diff
 */
export function difference<T = { [key: string]: any }>(data: T, originData: T, required: string[] = ['id', 'version']): Partial<T> | undefined {
  if (isString(originData) || isNumber(originData) || isBoolean(originData)) {
    return isEqual(data, originData) ? undefined : (data as any)
  }
  const changes = (data: any, originData: any) => {
    return transform(data, (result: any, value: T, key: string) => {
      if (isArray(value)) {
        // 数组比对
        const newArr = value || []
        const oldArr = originData[key] || []
        const arr: any[] = []
        const addArr: any[] = []
        const updateArr: any[] = []
        const deleteArr: any[] = []
        const testType = newArr?.[0] || oldArr?.[0]
        if (isNumber(testType) || isString(testType) || isBoolean(testType)) {
          const oldObj = oldArr.reduce((acc: any, cur: any) => {
            acc[cur] = cur
            return acc
          }, {})
          newArr.forEach((item: any) => {
            if (oldObj[item] === undefined) {
              addArr.push(item)
            }
            oldObj[item] = undefined
          })
          deleteArr.push(...Object.values(oldObj).filter((item: any) => !!item))
        } else {
          const oldObj = oldArr.reduce((acc: any, cur: any) => {
            acc[cur.id] = cur
            return acc
          }, {})

          newArr.forEach((item: any) => {
            if (item.id === undefined || oldObj[item.id] === undefined) {
              addArr.push(item)
            } else {
              const result = changes(item, oldObj[item.id])
              // 循环排除数组节点，剩下id和version以外的字段才认为存在修改数据
              const changeKeys = []
              Object.keys(result).forEach((key: any) => {
                if (key !== 'id' && key !== 'version') {
                  changeKeys.push(item.key)
                }
              })
              if (changeKeys.length > 0) {
                // 修改
                updateArr.push(result)
              } else {
                // 无修改
                // arr.push(result)
              }
              delete oldObj[item.id]
            }
          })
          deleteArr.push(
            ...Object.values(oldObj)
              .map((item: any) => item.id)
              .filter(item => !!item),
          )
        }

        // 根据id进行比对，得到新增数据，修改数据，删除id集合
        let tmpKey = key
        // 首字母大写
        tmpKey = tmpKey.replace(/( |^)[a-z]/g, L => L.toUpperCase())
        if (arr.length) {
          result[key] = arr
        }
        if (addArr.length) {
          result[`add${tmpKey}`] = addArr
        }
        if (updateArr.length) {
          result[`update${tmpKey}`] = updateArr
        }
        if (deleteArr.length) {
          result[`delete${tmpKey}`] = deleteArr
        }
      } else if (required.includes(key)) {
        result[key] = value
      } else if (!isEqual(value, originData[key])) {
        result[key] = isObject(value) && isObject(originData[key]) ? changes(value, originData[key]) : value
      }
    })
  }
  return changes(data, originData)
}

/**
 * 基础类型数组比对
 * @param data
 * @param originData
 * @returns '{add: [], del: [], xor: [], union: [], isDiff: boolean}'
 */
export function differenceArray<T extends number | string | boolean>(data: T[], originData: T[]): {
  add: T[]
  del: T[]
  xor: T[]
  union: T[]
  isDiff: boolean
} {
  const add: T[] = []
  const del: T[] = []
  const xor: T[] = []
  const union: T[] = [...data, ...originData]

  data.forEach((item: any) => {
    if (!originData.includes(item as never)) {
      add.push(item)
    }
  })
  originData.forEach((item: any) => {
    if (!data.includes(item as never)) {
      del.push(item)
    }
  })
  xor.push(...add, ...del)
  const isDiff = xor.length > 0
  return { isDiff, add, del, xor, union }
}

/**
 * load img
 */
export function loadImg(url: string, timeout: number = 3000) {
  return new Promise<HTMLImageElement>((resolve, reject) => {
    if (!url) {
      throw new Error('url is required')
    }
    const img = new Image()
    img.crossOrigin = 'anonymous'
    let _timeoutHandler: any = null
    if (timeout) {
      _timeoutHandler = setTimeout(() => reject(new Error('load img timeout')), timeout || 30000)
    }
    img.onload = () => {
      clearTimeout(_timeoutHandler)
      resolve(img)
    }
    img.onerror = () => {
      clearTimeout(_timeoutHandler)
      reject(new Error('load img error'))
    }
    img.src = url
  })
}

/**
 * 等待时间
 * @param v 时间
 * @returns void
 */
export const timer = (v: number): Promise<void> => new Promise(r => setTimeout(r, v))

/**
 * uuid
 * @param division 是否带横杠
 * @returns string
 */
export function uuid(division = false) {
  if (division) {
    'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = (Math.random() * 16) | 0
      const v = c === 'x' ? r : (r & 0x3) | 0x8
      return v.toString(16)
    })
  }
  return 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

/**
 * 是否是手机端
 */
export const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobi/i.test(navigator.userAgent)

/**
 * 图片压缩
 */
export function compress<T = File | File[]>(files: T): Promise<T> {
  return Promise.resolve(files)
}

// 废弃，考虑到素材都是绝对路径，不需要在做转换
export function deaQiniulUrl(url = '', baseDomain = import.meta.env.VITE_APP_RESDOMAIN) {
  if (!url.includes('qn/')) {
    // 非文件服务器代理域名
    return url
  }
  if (
    url.indexOf('http://') === 0
    || url.indexOf('https://') === 0
    || url.indexOf('data:image') === 0
    || url.indexOf('weixin://') === 0
    || url.indexOf('//') === 0
  ) {
    return url
  }

  if (url.charAt(0) === '/') {
    if (baseDomain.charAt(baseDomain.length - 1) === '/') {
      return baseDomain + url.substring(1)
    }
    return baseDomain + url
  } else {
    if (baseDomain.charAt(baseDomain.length - 1) === '/') {
      return baseDomain + url
    }
    return `${baseDomain}/${url}`
  }
}

let avatarIndex = 0
export function randomAvatar(i?: number) {
  if (envUtils.isPlayWright) {
    return new URL(`../assets/avatar/1.jpg`, import.meta.url).href
  }
  if (i !== undefined) {
    return new URL(`../assets/avatar/${i % 50}.jpg`, import.meta.url).href
  }
  avatarIndex = (avatarIndex + 1) % 50
  return new URL(`../assets/avatar/${avatarIndex}.jpg`, import.meta.url).href
}

export const EventBus = mitt()

export function isVideo(val: any) {
  if (!val) return false
  // 判断是file对象么
  if (val instanceof File) {
    const pattern = val.type
    if (/video/.test(pattern)) {
      return true
    }
    return false
  }

  // 如果是url
  if (typeof val === 'string') {
    // 判断后缀名是否是视频
    const pattern = val.split('.').pop()
    if (/mp4|webm/.test(pattern || '')) {
      return true
    }
    return false
  }
  return false
};

// 补全前缀
function normalizeUrl(u: string): string {
  if (!u) return ''
  u = u.trim()

  if (u.startsWith('//')) {
    // 协议相对路径，补全协议
    return `${location.protocol}${u}`
  }

  if (u.startsWith('/')) {
    // 相对路径，补全域名
    return `${location.origin}${u}`
  }

  // 如果是 http/https 开头，直接返回；否则默认认为是完整 URL
  return u
}

let isWxSign = false
export async function previewImage(url: string, urls: string[] = []) {
  const noRepectUrls = Array.from(new Set([url, ...urls])).map(url => normalizeUrl(url))

  if (isWeiXin && window.self === window.parent) {
    if (!isWxSign) {
      await sign()
      isWxSign = true
    }
    window.wx.previewImage({
      current: normalizeUrl(url),
      urls: noRepectUrls,
    })
    return
  }

  if (isMobile) {
    await import('vant/es/image-preview/style')
    showImagePreview({
      images: noRepectUrls,
      startPosition: noRepectUrls.findIndex(u => u === normalizeUrl(url)),
      showIndex: false,
      showIndicators: false,
      teleport: 'body',
      className: 'image-preview',
    })
  }

  // TODO pc端暂不实现
}
