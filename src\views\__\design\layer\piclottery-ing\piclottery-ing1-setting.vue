<script setup lang="ts">
import { getDefaultMaterial, openSelectMaterial, useDataAttr } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'
import { DEFAULT_DATA, type IDesignPiclotteryIng1 } from './piclottery-ing1'

const layer = defineModel<IDesignPiclotteryIng1>('layer', { required: true })

const itemGapBind = useDataAttr(layer.value.data, 'itemGap', DEFAULT_DATA.itemGap)
const innerGapBind = useDataAttr(layer.value.data, 'innerGap', DEFAULT_DATA.innerGap)
const imageWidthBind = useDataAttr(layer.value.data, 'imageWidth', DEFAULT_DATA.imageWidth)
const imageHeightBind = useDataAttr(layer.value.data, 'imageHeight', DEFAULT_DATA.imageHeight)
const imageModeBind = useDataAttr(layer.value.data, 'imageMode', DEFAULT_DATA.imageMode)
const imageRadiusBind = useDataAttr(layer.value.data, 'imageRadius', DEFAULT_DATA.imageRadius)
const borderColorBind = useDataAttr(layer.value.data, 'borderColor', DEFAULT_DATA.borderColor)
const borderWidthBind = useDataAttr(layer.value.data, 'borderWidth', DEFAULT_DATA.borderWidth)
const placeholderHeadImgBind = useDataAttr(layer.value.data, 'placeholderHeadImg', DEFAULT_DATA.placeholderHeadImg)

type IType = 'placeholderHeadImg'
async function updateMaterialFn(name: IType, isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, `${name}`)
    : await openSelectMaterial('PIC')
  if (result) {
    layer.value.data[name] = result
  } else if (isReset) {
    delete layer.value.data[name]
  }
}
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>内容间距</h3>
        <el-input-number
          v-model="itemGapBind"
          v-input-number
          :min="0"
          :max="100"
          controls-position="right"
        />
      </div>
      <div class="setting-item">
        <h3>文字间距</h3>
        <el-input-number
          v-model="innerGapBind"
          v-input-number
          :min="0"
          :max="100"
          controls-position="right"
        />
      </div>
      <div class="setting-item">
        <h3>图片宽度</h3>
        <el-input-number
          v-model="imageWidthBind"
          v-input-number
          :min="0"
          :max="300"
          controls-position="right"
        />
      </div>
      <div class="setting-item">
        <h3>图片高度</h3>
        <el-input-number
          v-model="imageHeightBind"
          v-input-number
          :min="0"
          :max="300"
          controls-position="right"
        />
      </div>
      <div class="setting-item">
        <h3>图片圆角</h3>
        <el-input-number
          v-model="imageRadiusBind"
          v-input-number
          :min="0"
          :max="100"
          controls-position="right"
        />
      </div>
      <div class="setting-item">
        <h3>显示模式</h3>
        <el-select v-model="imageModeBind" placeholder="图片模式" class="w-140px">
          <el-option label="填充" value="fill" />
          <el-option label="适应" value="contain" />
          <el-option label="裁剪" value="cover" />
        </el-select>
      </div>
      <div class="setting-item mt-5!">
        <h3>头像默认图</h3>
        <div class="flex items-end">
          <div class="bgblank relative h-50 w-64">
            <MaterialThumbnail @select="updateMaterialFn('placeholderHeadImg')" @reset="updateMaterialFn('placeholderHeadImg', true)">
              <img v-if="placeholderHeadImgBind" :src="placeholderHeadImgBind" alt="" class="h-full w-full object-cover" />
            </MaterialThumbnail>
          </div>
        </div>
      </div>
      <div class="setting-item">
        <h3>边框颜色</h3>
        <hi-color v-model="borderColorBind" />
      </div>
      <div class="setting-item">
        <h3>边框粗细</h3>
        <el-input-number
          v-model="borderWidthBind"
          v-input-number
          :min="0"
          :max="20"
          controls-position="right"
        />
      </div>
      <div v-for="(item, index) in layer.data.contentStyle || []" :key="index" class="setting-item items-start!">
        <h3>文字{{ index + 1 }}</h3>
        <div>
          <div class="text-item">
            <h3>大小</h3>
            <el-input-number v-model="item.fontSize" v-input-number :max="100" :min="12" controls-position="right" />
          </div>
          <div class="text-item">
            <h3>颜色</h3>
            <hi-color v-model="item.fontColor" />
          </div>
          <div class="text-item">
            <h3>加粗</h3>
            <el-switch v-model="item.fonBold" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.text-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
</style>
