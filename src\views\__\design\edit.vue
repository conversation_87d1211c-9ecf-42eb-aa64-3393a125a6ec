<script setup lang="ts">
import type { ILayerTypes } from './types'
import { envUtils } from '@/utils/env'
import { cloneDeep } from 'lodash-es'
import { hasAuth } from '~/src/utils/auth'
import { useDesignData, useDesignState, useDesignTemp, useDisplayInfo } from '.'
import HiBackground from './background.vue'
import HiEditLeftComponent from './edit/left-component.vue'
import HiEditLeft from './edit/left.vue'
import HiEditLine from './edit/line.vue'
import HiEditRightControl from './edit/right-control.vue'
import HiEditRight from './edit/right.vue'
import HiEditTemporary from './edit/temporary.vue'
import HiEditTop from './edit/top.vue'
import { useMobileDevice } from './hooks/useMobileDevice'
import HiDesignLayer from './layer/index.vue'
import HiMoveable from './layer/moveable.vue'

const route = useRoute()
const router = useRouter()
const designState = useDesignState()
const designData = useDesignData()
const designTemp = useDesignTemp()

const rootRef = ref<HTMLElement>()
const pageOffset = ref({ x: 0, y: 0 })
const designPageRef = ref<HTMLElement>()
const designPageSize = useElementSize(designPageRef)

const isMobileMode = computed(() => designTemp.showType === 'mobile')

watch(
  () => [designPageSize.width.value, designPageSize.height.value] as const,
  ([width, height]) => {
    designTemp.designSize.width = width
    designTemp.designSize.height = height
  },
  { immediate: true },
)

const displayInfo = useDisplayInfo({
  rootRef,
  witchSpace: 100,
})

const scale = computed(() => displayInfo.value.scale)
provide('scale', scale)

const isLoading = computed(() => {
  const { webContent, mobContent } = designTemp.theme || {}
  return !webContent && !mobContent
})

const hasActionAuth = computed(() => hasAuth(['man', 'oem', 'spo.hi.vip', 'spo.ft']))

// TODO 增加区域画选 ///////////////////////////////////////////

// event ///////////////////////////////////////////
// 监听状态变化
watch(
  () => [designState.statusList, designState.status],
  () => {
    if (designTemp.activeList.length) {
      designTemp.reset()
    }
  },
)
if (envUtils.isPro) {
  const focused = useWindowFocus()
  watch(focused, (v) => {
    if (!v) {
      designTemp.reset()
    }
  })
}

const foldBus = useEventBus('FOLD_BUS')

function mousedownFn(e: MouseEvent) {
  const target = e.target as HTMLElement
  if (!target) {
    return
  }
  if (
    target.closest('.moveable-ignore')
    || target.closest('.el-popper')
    || target.closest('.t-popup')
    || target.closest('.setting-control')
    || target.closest('.vc-colorpicker')
  ) {
    return
  }
  // 图层, 拖拽框
  let uuid = target.closest('.moveable-control-box')?.getAttribute('data-uuid')
    || target.closest('.design-layer')?.getAttribute('data-uuid')
    || ''

  // 点击空白
  if (!uuid) {
    // 拖拽
    if (e.ctrlKey && e.button === 0) {
      const mousemoveFn = (event: MouseEvent) => {
        pageOffset.value.x += event.movementX / (designTemp.scale / 100)
        pageOffset.value.y += event.movementY / (designTemp.scale / 100)
      }
      const mouseupFn = () => {
        document.removeEventListener('mousemove', mousemoveFn)
        document.removeEventListener('mouseup', mouseupFn)
      }
      document.addEventListener('mousemove', mousemoveFn)
      document.addEventListener('mouseup', mouseupFn, { once: true })
    }

    // 不存在
    designTemp.reset()
    return
  }

  const index = designTemp.activeList.indexOf(uuid)
  if (index === -1) {
    const nowLayer = designData.getLayerByUuid(uuid)
    if (nowLayer) {
      let parent = nowLayer.$parent
      // 看父级是否被选中了
      while (parent) {
        if (designTemp.activeList.includes(parent.uuid)) {
          break
        }
        uuid = parent.uuid
        parent = parent.$parent
      }
    }

    // 非选中节点
    if (!e.ctrlKey && !e.shiftKey) {
      designTemp.activeList = []
    }
    if (!hasActionAuth.value && !designData.getLayerByUuid(uuid)?.spoVisible) {
      return
    }

    // 如果图层过多，可能不方便查看，选中时图层滚动到可见
    // 查询图层父级，全部展开
    const activeLayer = designData.getLayerByUuid(uuid)
    if (activeLayer) {
      let parent = activeLayer.$parent
      // 看父级是否被选中了
      while (parent) {
        foldBus.emit('unfold', parent.uuid)
        parent = parent.$parent
      }

      // 选中图层移动到可见
      const container = document.querySelector('.design-right .el-tabs__content') as HTMLElement
      const target = container?.querySelector(`[data-uuid="${uuid}"]`) as HTMLElement

      if (container && target) {
        const containerRect = container.getBoundingClientRect()
        const targetRect = target.getBoundingClientRect()

        const offset = targetRect.top - containerRect.top // 元素相对容器顶部的距离

        const targetTop = offset + container.scrollTop
        const targetBottom = targetTop + target.offsetHeight

        const minVisibleTop = container.scrollTop + 100
        const maxVisibleBottom = container.scrollTop + container.clientHeight

        const isAbove = targetTop < minVisibleTop
        const isBelow = targetBottom > maxVisibleBottom

        if (isAbove) {
          container.scrollTo({ top: targetTop - 100, behavior: 'smooth' })
        } else if (isBelow) {
          container.scrollTo({ top: targetBottom - container.clientHeight, behavior: 'smooth' })
        }
      }
    }

    designTemp.activeList.push(uuid)
    return
  }

  // 点击已选中节点
  if (e.ctrlKey || e.shiftKey) {
    designTemp.activeList.splice(index, 1)
  }
}

const sortedLayers = computed(() => {
  const len = designData.layers.length
  return Array.from({ length: len }).map((_, i) => i).reverse()
})

const allLayers = computed(() => {
  const layers: ILayerTypes[] = []

  designData.walk((layer) => {
    layers.push(layer)
  })

  return layers
})

//
const rootStyle = computed(() => {
  return {
    '--window-width': designTemp.designSize.width,
    '--window-height': designTemp.designSize.height,
    ...displayInfo.value.rootStyle,
  }
})

const isReady = computed(() => {
  return displayInfo.value.ready && designTemp.theme && designData.$ready
})

function findClosestScrollable(element: HTMLElement | null) {
  while (element && element !== document.documentElement) {
    const style = getComputedStyle(element)
    // 检查垂直或水平滚动
    const isScrollableY = (style.overflowY === 'auto' || style.overflowY === 'scroll')
      && element.scrollHeight > element.clientHeight
    const isScrollableX = (style.overflowX === 'auto' || style.overflowX === 'scroll')
      && element.scrollWidth > element.clientWidth

    if (isScrollableY || isScrollableX) {
      return element
    }
    element = element.parentElement
  }
  return null
}

function wheelFn(e: WheelEvent) {
  // 滚动事件
  const delta = e.deltaY * 0.2
  if (e.ctrlKey) {
    // Ctrl + 滚轮：缩放
    e.preventDefault()
    designTemp.scale = Math.round(designTemp.scale - e.deltaY * (e.deltaY ? 0.02 : 0.01))
    return
  }

  // 当前滚动元素的父级 存在滚动条时 禁止整体滚动
  const scrollableElement = findClosestScrollable(e.target as HTMLElement)
  if (scrollableElement) {
    e.stopPropagation()
    return
  }

  if (e.shiftKey) {
    // 横向
    pageOffset.value.x -= delta
  } else {
    // 纵向
    pageOffset.value.y -= delta
  }
}

const { currentDevice } = useMobileDevice()

const designPageStyle = computed(() => {
  return {
    '--design-width': Number.parseFloat(`${designTemp.designSize.width}`),
    '--design-height': Number.parseFloat(`${designTemp.designSize.height}`),
    '--page-offset-x': pageOffset.value.x,
    '--page-offset-y': pageOffset.value.y,
    ...displayInfo.value.pageStyle,
    'outline': currentDevice?.value?.name ? '' : '1px dashed #b9b9b9',
    'border-radius': currentDevice?.value?.name ? '30px' : '',
  }
})

const editPhoneStyle = computed(() => {
  const { width, height, top, left } = designPageStyle.value || {}
  const style = Object.assign({}, cloneDeep(designPageStyle.value), {
    outline: 'none',
    height: height ? `${Number.parseFloat(String(height)) * 1.05}px` : '',
    width: width ? `${Number.parseFloat(String(width)) * 1.1}px` : '',
    top: top ? `${Number.parseFloat(String(top)) - Number.parseFloat(String(height)) * 0.025}px` : '',
    left: left ? `${Number.parseFloat(String(left)) - Number.parseFloat(String(width)) * 0.05}px` : '',
    borderRadius: '',
  })

  return style
})

const editScaleStyle = computed(() => {
  if (designTemp.scale) {
    return {
      transform: `scale(${designTemp.scale / 100})`,
    }
  }
  return {}
})

onMounted(() => {
  document.body.addEventListener('mousedown', mousedownFn, { capture: true })
})

// 状态和url相互绑定，刷新页面不丢失状态。某些业务场景为了确保数据正常也对status做了修改，在切换showType时status会重新设置
watch(
  () => [designState.status, designTemp.showType],
  ([status, showType]) => {
    if (status === route.query.status && showType === route.query.showType) return
    const query = { ...route.query }
    if (status) {
      query.status = status
    }
    if (showType) {
      query.showType = showType
    }
    router.replace({ query })
  },
)

watch(
  () => route.query,
  async (query) => {
    if (query.showType) {
      if (designTemp.showType !== query.showType && designTemp.typeList.find(it => it.value === query.showType)) {
        designTemp.showType = query.showType as any
      }
    } else {
      designTemp.showType = designTemp.typeList[0].value
    }
  },
  { deep: true, immediate: true },
)

watch(
  () => [designState.statusList, route.query.status],
  () => {
    const list = designState.statusList
    const status = route.query.status
    if (list?.length) {
      const statusEffective = list.find(it => it.value === status)
      if (statusEffective && designState.status !== status) {
        designState.setStatus(status as string)
      } else if (!designState.status || !statusEffective) {
        designState.setStatus(list[0].value)
      }
    }
  },
  { deep: true },
)

// 右侧控制栏
const rightWidth = ref(250)
const editPageStyle = computed(() => {
  return {
    '--top-height': '64px',
    '--left-width': '64px',
    '--left-component-width': '250px',
    '--right-control-width': '40px',
    '--right-width': `${rightWidth.value}px`,
  }
})

onUnmounted(() => {
  document.body.removeEventListener('mousedown', mousedownFn, { capture: true })
})
onErrorCaptured((err) => {
  console.error('🚀', err)
  return true
})
</script>

<template>
  <div v-loading="isLoading" class="edit-page" :style="editPageStyle">
    <div class="top">
      <HiEditTop />
    </div>
    <div class="body">
      <template v-if="hasActionAuth">
        <HiEditLeft />
        <HiEditLeftComponent />
      </template>
      <div ref="rootRef" :key="designTemp.renderKey" class="preview-page" :style="rootStyle" @wheel.capture="wheelFn">
        <HiBackground />
        <div v-if="designData.$ready" class="edit-scale" :style="editScaleStyle">
          <!-- 渲染手机边框 -->
          <hi-phone v-if="isMobileMode && currentDevice?.name" class="design-page" :style="editPhoneStyle" />
          <!-- 主要渲染区域 -->
          <div v-if="isReady" ref="designPageRef" class="design-page guideline" :style="designPageStyle">
            <HiDesignLayer
              v-for="(val, index) in sortedLayers"
              :key="designData.layers[val].uuid"
              v-model:layer="designData.layers[val]"
              :data-index="index"
            />
            <template v-if="hasActionAuth">
              <HiMoveable
                v-for="(layer, idx) of allLayers"
                :key="layer.uuid"
                v-model:layer="allLayers[idx]"
              />
            </template>
          </div>
        </div>
      </div>
      <HiEditRightControl />
      <HiEditLine v-model="rightWidth" />
      <HiEditRight />
    </div>
    <HiEditTemporary :key="designTemp.activeList.join(',')" />
  </div>
</template>

<style scoped lang="scss">
.edit-page {
  --border-color: #e9edf5;

  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 5;

  .top {
    height: var(--top-height);
    position: relative;
    z-index: 20;
  }

  .body {
    height: calc(100% - var(--top-height));
    top: var(--top-height);
    display: flex;
  }

  :deep() {
    .el-tabs__content {
      height: calc(100% - 38px);
      padding: 0;
      overflow: auto;
    }

    .el-input-number {
      width: 60px;

      &.el-input-number--small.is-controls-right {
        margin-left: 6px;

        .el-input__wrapper {
          padding-left: 0;
          padding-right: 16px;
        }

        .el-input-number__decrease,
        .el-input-number__increase {
          width: 16px;
        }
      }
    }

    .moveable-dragging {
      .moveable-direction,
      .moveable-resizable,
      .moveable-custom-rotation,
      .moveable-border-radius,
      .moveable-rotation-control,
      .moveable-rotation-line {
        opacity: 0 !important;
      }
    }
    .moveable-control-box {
      // 自定义
      .moveable-dimension {
        position: absolute;
        height: 28px;
        line-height: 28px;
        padding: 0 8px;
        font-size: 12px;
        white-space: nowrap;
        color: #fff;
        background: #000;
        border-radius: 4px;
        transform: translate(10px, -100%);
        transition: opacity 0.2s;
      }
    }
  }
}

.preview-page {
  flex: 1;
  position: relative;
  width: 100%;
  height: 100%;
  padding: 2px;
  z-index: 5;

  .edit-scale {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 10;
  }

  .design-page {
    --page-offset-x: 0;
    --page-offset-y: 0;
  }

  section.has-auth {
    &:hover {
      outline: 1px dashed #b9b9b9;
    }
  }
}
</style>
