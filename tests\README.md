**如果主题较多，需要等待一段时间**

**⚠️部分问题需要业务场景注意**
2. 自定义变量可能渲染数据不一致，需要业务中逐步修改
3. 部分自定义组件可能是 3D、canvas 等，需要组件中处理
4. 部分业务逻辑和组件使用了随机数 Math.random() 、随机排序，需要业务、组件中修改

> 摇一摇的已处理，其他互动需逐步处理

---

图层频繁变化，不需要被比对
```html
<!-- 添加 data-test-mask -->
<div data-test-mask> </div>
```

安装 Playwright
```bash
pnpm add -D playwright
pnpm exec playwright install --with-deps
```

首次运行（生成快照）：
```bash
npx playwright test --update-snapshots
```

后续运行（自动对比 + 生成 diff 图）：
```bash
npx playwright test
```

如果你确认样式变动是预期的，可以更新快照：
```bash
npx playwright test --update-snapshots
```
