<script setup lang="ts">
import type { Arrayable } from 'element-plus/es/utils/index.mjs'
import type { IDesignTextRich } from './text-rich'
import { useDataAttr } from '../..'
import TextImageEdit from '../../components/textImageEdit.vue'
import TextShadowEditor from '../../components/textShadowEdit.vue'
import TextStrokeEdit from '../../components/textStrokeEdit.vue'
import { useFontLoader } from '../../hooks/useFontLoader'
import { useQuillManager } from './text-rich-utils'

const layer = defineModel<IDesignTextRich>('layer', { required: true })

const quillManager = useQuillManager(layer)
const { sectionStyle, currentImageWidth } = quillManager
function setStyle(name: string, value: boolean | string | number | Arrayable<number> | undefined) {
  quillManager.setStyle(name, value)
}
function insertImage() {
  quillManager.insertImage()
}

const { fontList, loadFont } = useFontLoader()
const lineHeightBind = useDataAttr(layer.value.style, 'line-height', 1.1)
const letterSpacingBind = useDataAttr(layer.value.style, 'letter-spacing', 0, 'em')
const textImageBind = useDataAttr(layer.value.style, 'backgroundImage', '')
const textShadowBind = useDataAttr(layer.value.style, 'textShadow', '')
const textStrokeBind = useDataAttr(layer.value.style, '-webkit-text-stroke', '')
const fontBind = useDataAttr(layer.value.style, 'fontFamily', '')

const isBgImage = computed(() => layer.value.style.backgroundImage)

async function handleFontChange(val: string) {
  const fontItem = fontList.find(item => item.value === val)
  // 如果有自定义字体，先加载
  if (fontItem?.url) {
    await loadFont({
      name: fontItem.name,
      url: fontItem.url,
      value: fontItem.value,
    })
  }
  fontBind.value = fontItem?.value || 'default'
}
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <span>字体</span>
        <el-select :model-value="fontBind" class="w-120" placeholder="请选择字体" @update:model-value="handleFontChange">
          <el-option v-for="item in fontList" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </div>
      <div class="setting-item">
        <span>字号</span>
        <el-input-number
          v-model="sectionStyle.fontSize"
          v-input-number
          :min="1"
          class="w-50"
          controls-position="right"
          @change="v => setStyle('fontSize', v)"
        />
      </div>
      <div class="setting-item">
        <span>文本颜色</span>
        <hi-color v-model="sectionStyle.color" @change="v => setStyle('color', v)" />
      </div>
      <div class="setting-item">
        <span></span>
        <ul class="style-box">
          <li data-tip="加粗" :class="{ cur: sectionStyle.isBold }" @click="setStyle('bold', !sectionStyle.isBold)">
            <icon-ph-text-bolder-bold />
          </li>
          <li data-tip="斜体" :class="{ cur: sectionStyle.isItalic }" @click="setStyle('italic', !sectionStyle.isItalic)">
            <icon-ph-text-italic-bold />
          </li>
          <li data-tip="下划线" :class="{ cur: sectionStyle.isUnderline }" @click="setStyle('underline', !sectionStyle.isUnderline)">
            <icon-ph-text-underline-bold />
          </li>
          <li data-tip="删除线" :class="{ cur: sectionStyle.isStrike }" @click="setStyle('strike', !sectionStyle.isStrike)">
            <icon-ph-text-strikethrough-bold />
          </li>
          <li data-tip="左对齐" :class="{ cur: sectionStyle.align === 'left' }" @click="setStyle('align', '')">
            <icon-ph-text-align-left-bold />
          </li>
          <li data-tip="居中对齐" :class="{ cur: sectionStyle.align === 'center' }" @click="setStyle('align', 'center')">
            <icon-ph-text-align-center-bold />
          </li>
          <li data-tip="右对齐" :class="{ cur: sectionStyle.align === 'right' }" @click="setStyle('align', 'right')">
            <icon-ph-text-align-right-bold />
          </li>
          <li data-tip="缩进" :class="{ cur: sectionStyle.textIndent }" @click="setStyle('indent', !sectionStyle.textIndent)">
            <icon-ph-text-indent-bold />
          </li>
          <li data-tip="竖排" :class="{ cur: sectionStyle.writingMode }" @click="sectionStyle.writingMode = sectionStyle.writingMode ? '' : 'vertical-rl'">
            <icon-custom-text-direction />
          </li>
          <li data-tip="顶对齐" :class="{ cur: sectionStyle.alignItems === 'start' }" @click="sectionStyle.alignItems = 'start'">
            <icon-ph-arrow-line-up-bold />
          </li>
          <li data-tip="居中对齐" :class="{ cur: sectionStyle.alignItems === 'center' }" @click="sectionStyle.alignItems = 'center'">
            <icon-ph-arrows-in-line-vertical-bold />
          </li>
          <li data-tip="底端对齐" :class="{ cur: sectionStyle.alignItems === 'end' }" @click="sectionStyle.alignItems = 'end'">
            <icon-ph-arrow-line-down-bold />
          </li>
          <li data-tip="插入图片" @click="insertImage">
            <icon-ph-image-bold />
          </li>
        </ul>
      </div>
      <div v-if="currentImageWidth" class="setting-item">
        <span>图片宽度</span>
        <el-input-number v-model="currentImageWidth" v-input-number :min="1" class="w-50" controls-position="right" />
      </div>
      <div class="setting-item">
        <span>行高</span>
        <el-input-number v-model="lineHeightBind" v-input-number :min="1" :step="0.1" class="w-50" controls-position="right" />
      </div>
      <div class="setting-item">
        <span>字间距</span>
        <el-input-number v-model="letterSpacingBind" v-input-number :min="0" :step="0.1" class="w-50" controls-position="right" />
      </div>
      <TextImageEdit v-if="isBgImage" v-model="textImageBind" />
      <TextStrokeEdit v-model="textStrokeBind" />
      <div class="setting-item">
        <span>文字阴影</span>
      </div>
      <TextShadowEditor v-model="textShadowBind" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.tag-box {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  margin-top: 5px;
}
ul.style-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: end;
  gap: 2px;
  font-size: 16px;

  li {
    width: 26px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background-color: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    background-color: #f5f5f5;
    transition: 0.2s;
    position: relative;
    &.cur {
      background-color: var(--el-color-primary);
      color: #fff;
    }
    &:hover {
      background-color: var(--el-color-primary-dark-2);
      color: #fff;
      &::before,
      &::after {
        display: block;
      }
    }
    &::before,
    &::after {
      position: absolute;
      z-index: 100;
      left: 50%;
      transform: translateX(-50%);
    }
    &::before {
      // 三角
      content: '';
      display: none;
      top: -6px;
      border: 6px solid transparent;
      border-top-color: rgba(0, 0, 0, 0.8);
    }
    &::after {
      content: attr(data-tip);
      display: none;
      top: -34px;
      padding: 8px 10px;
      color: #fff;
      font-size: 12px;
      line-height: 12px;
      border-radius: 4px;
      white-space: nowrap;
      background-color: rgba(0, 0, 0, 0.8);
    }
  }
}
</style>
