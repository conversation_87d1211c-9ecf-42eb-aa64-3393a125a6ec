<script setup lang="ts">
import { defineCustomEmits, injectScale, useDesignState, useDesignTemp } from '../..'
import { processStyle } from '../../utils'
import { DEFAULT_DATA, type IDesignWinningList } from './winning-list'

const layer = defineModel<IDesignWinningList>('layer', { required: true })

const scale = injectScale()
const designTemp = useDesignTemp()
const designState = useDesignState ()
const customEmits = defineCustomEmits(layer)

const data = computed(() => {
  return {
    ...DEFAULT_DATA,
    ...layer.value.data,
  }
})

interface Award {
  id: string
  avatar: string
  name: string
}

export type WinningList = {
  prizeName: string
  awardList: Award[]
}[]

const winningList = computed<WinningList>(() => {
  return designState.getLayerData('winningList') || []
})

const ulStyle = computed(() => {
  return processStyle({
    gap: `${data.value.avatarGap}px`,
    fontSize: `${data.value.avatarSize * 0.15}px`,
  }, scale)
})
const liStyle = computed(() => {
  return processStyle({
    width: `${data.value.avatarSize}px`,
    height: `${data.value.avatarSize}px`,
  }, scale)
})

function onDeleteItem(item: Award) {
  ElMessageBox.confirm('确定要取消当前用户的中奖资格吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '再想想',
    type: 'warning',
  }).then(() => {
    if (designTemp.isEdit) {
      ElMessageBox.alert('当前中奖名单是模拟数据，无法取消中奖，请创建活动后在大屏幕中操作', '需要在大屏幕中操作')
    } else {
      customEmits('winning-delete', item.id)
    }
  }).catch(() => {
    // do nothing
  })
}
</script>

<template>
  <div class="h-full w-full flex flex-col items-center overflow-y-auto">
    <template v-for="winning in winningList" :key="winning.prizeName">
      <ul :style="ulStyle" class="flex-1">
        <li
          v-for="(item, index) in winning.awardList"
          :key="item.id || index"
          :style="liStyle"
          class="overflow-hidden"
        >
          <img :src="item.avatar || data.defaultAvatar" class="img1" alt="">
          <img
            :src="data.avatarDecoration"
            class="img2"
            :class="{
              'animate-spin': data.avatarDecorationRotation,
            }"
            :style="{
              // 动画速度
              animationDuration: `${data.avatarDecorationRotationSpeed}s`,
            }"
            alt=""
          >
          <div
            class="name"
            :style="{
              color: data.nameColor,
            }"
          >
            <span>{{ item.name }}</span>
          </div>
          <div class="delete absolute z-10 hidden cursor-pointer items-center justify-center bg-black bg-opacity-50 text-white" @click="onDeleteItem(item)">
            <icon-ph-x-bold />
          </div>
        </li>
      </ul>
    </template>
  </div>
</template>

<style scoped lang="scss">
ul {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  align-content: center;
  li {
    position: relative;
    img,
    .name {
      position: absolute;
      left: 0;
    }

    .img1,
    .delete {
      width: 58%;
      aspect-ratio: 1;
      border-radius: 50%;
      top: 12%;
      left: 22%;
    }
    .img2 {
      width: 82%;
      aspect-ratio: 1;
      top: 0%;
      left: 10%;
      &:hover {
        & ~ .delete {
          display: flex;
        }
      }
    }

    .delete {
      &:hover {
        display: flex;
      }
    }

    .name {
      width: 100%;
      height: 26%;
      bottom: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      span {
        width: 100%;
        white-space: nowrap;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
</style>
