import { HiRequest } from '../request'

export default {
  // 自己的素材
  materialownPage: (params: any) => HiRequest.post('/oem/admin/promaterialown/page.htm', params),
  materialownBatchAdd: (params: any) => HiRequest.post('/oem/admin/promaterialown/batchAdd.htm', params),
  materialownDelete: (params: any) => HiRequest.post('/oem/admin/promaterialown/delete.htm', params),
  materialownUpdate: (params: any) => HiRequest.post('/oem/admin/promaterialown/update.htm', params),

  // 自己的标签
  materiallabelownBatch: (params: any) => HiRequest.post('/oem/admin/promateriallabelown/batch.htm', params),
  materiallabelownList: (params: any) => HiRequest.post('/oem/admin/promateriallabelown/list.htm', params),
  materiallabelownDelete: (params: any) => HiRequest.post('/oem/admin/promateriallabelown/delete.htm', params),
  materiallabelownAdd: (params: any) => HiRequest.post('/oem/admin/promateriallabelown/add.htm', params),
  materiallabelownUpdate: (params: any) => HiRequest.post('/oem/admin/promateriallabelown/update.htm', params),

  // 素材和标签的关联关系
  materialmateriallabelBatch: (params: any) => HiRequest.post('/oem/admin/promaterialmateriallabel/batch.htm', params),
  materialmateriallabelList: (params: any) => HiRequest.post('/oem/admin/promaterialmateriallabel/list.htm', params),
}
