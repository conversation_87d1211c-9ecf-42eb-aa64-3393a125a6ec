<script setup lang="ts">
import type { Label } from './label.vue'
import { differenceArray } from '@/utils'
import { cloneDeep } from 'lodash-es'

const props = defineProps<{
  labelList: Label[]
  editObj: any
  selLabelList: any[]
}>()
const emit = defineEmits<{
  (e: 'cancle'): void
  (e: 'save', val: any): void
}>()

export interface Material {
  url: string
  name: string
  type: string
}
export interface MaterialLinkLabel {
  id: string
  labelId: string
  materialId: string
}

const route = useRoute()

const dialogVisible = ref(true)
const selectedParentLabels = ref<number[]>([])
const selectedChildLabels = ref<number[]>([])
const parentLabel = computed(() => props.labelList.filter(item => !item.pId))
const childLabel = computed(() => props.labelList.filter(item => selectedParentLabels.value.includes(item.pId ?? 0)))
const isManage = computed(() => route.path.startsWith('/manage'))
const isOem = computed(() => route.path.startsWith('/oem'))
const isList = computed(() => [...selectedParentLabels.value, ...selectedChildLabels.value])

const newData = ref<Material>({
  name: '',
  url: '',
  type: '',
})
const editLabelIdList = computed(() => props.selLabelList.map(item => item.labelId))
const selLabelObj = computed(() => {
  const obj: any = {}
  props.selLabelList.forEach((item) => {
    obj[item.labelId] = item
  })
  return obj
})

function removeParentTag(tagValue: any) {
  const list = props.labelList.filter(item => tagValue === item.pId)
  selectedChildLabels.value = selectedChildLabels.value.filter(item => !list.map(i => i.id).includes(item))
}
function initData() {
  const parentLabels: number[] = []
  const childLabels: number[] = []
  props.labelList.forEach((item: Label) => {
    if (editLabelIdList.value.includes(item.id!)) {
      if (item.pId) {
        childLabels.push(item.id as number)
      } else {
        parentLabels.push(item.id as number)
      }
    }
  })
  selectedParentLabels.value = parentLabels
  selectedChildLabels.value = childLabels
  newData.value = cloneDeep(props.editObj)
}
async function query() {
  if (!newData.value.name) {
    ElMessage.error('请输入素材名称')
    return
  }
  const data: any = {}
  if (newData.value.name !== props.editObj.name) {
    data.name = newData.value.name
  }
  const diff = differenceArray(isList.value, editLabelIdList.value)
  if (diff.isDiff) {
    const delArr: number[] = []
    const addArr: any = []
    diff.del.forEach((item) => {
      delArr.push(selLabelObj.value[item].id)
    })
    diff.add.forEach((item) => {
      addArr.push({
        labelId: item,
        materialId: props.editObj.id,
      })
    })
    const arr: any = []
    if (addArr.length) {
      arr.push({ type: 'add', data: addArr })
    }
    if (delArr.length) {
      arr.push({ type: 'del', data: delArr })
    }
    data.labelUpdate = arr
  }
  await emit('save', data)
}
onMounted(() => {
  initData()
})
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="编辑素材"
    width="800px"
    destroy-on-close
    :close-on-click-modal="false"
    @close="$emit('cancle')"
  >
    <div class="content">
      <div class="mt-15 flex flex-a-c">
        <p class="item-label">素材名称：</p>
        <el-input v-model="newData.name" class="w-200" placeholder="请输入素材名称" />
      </div>
      <div class="mt-15 flex flex-a-c">
        <p class="item-label">一级分类：</p>
        <el-select v-model="selectedParentLabels" class="add-select" filterable allow-create multiple default-first-option placeholder="请选择一级分类" @remove-tag="removeParentTag">
          <el-option
            v-for="item in parentLabel"
            :key="item.id"
            :label="item.name"
            :value="item.id ?? 0"
          />
        </el-select>
      </div>
      <div v-if="isManage || isOem" class="mt-15 flex flex-a-c">
        <p class="item-label">二级分类：</p>
        <el-select v-model="selectedChildLabels" class="add-select" multiple filterable allow-create default-first-option placeholder="请选择二级分类">
          <el-option
            v-for="item in childLabel"
            :key="item.id"
            :label="item.name"
            :value="item.id ?? 0"
          />
        </el-select>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('cancle')">取消</el-button>
        <el-button type="primary" @click="query">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.content {
  padding: 10px 15px;
  .item-label {
    width: 90px;
  }
  .add-select {
    width: 500px;
  }
}
</style>
