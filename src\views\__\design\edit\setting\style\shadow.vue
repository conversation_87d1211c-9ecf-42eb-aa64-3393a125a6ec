<script setup lang="ts">
import type { IDesignLayer } from '../../../types'

defineOptions({ label: '阴影' })

const layer = defineModel<IDesignLayer>('layer', { required: true })

// box-shadow
// rgba(0, 0, 0, 0.5) 10px 20px 5px,     外
// rgba(0, 0, 0, 0.5) 0px 0px 0px inset  内

const shadowObj = ref({
  outer: { color: 'rgba(0,0,0,1)', x: 0, y: 0, fuzzy: 0 },
  inner: { color: 'rgba(0,0,0,1)', x: 0, y: 0, fuzzy: 0 },
})
watch(
  () => layer.value.style.boxShadow,
  () => {
    const { boxShadow } = layer.value.style
    if (!boxShadow) return

    const arr = boxShadow.split(/rgba|rgb|\(|\)|inset/).map(i => i.trim())
    {
      const colorArr = arr[2].split(',').map(i => i.trim())
      if (colorArr.length === 3) {
        colorArr.push('1')
      }
      const [x, y, fuzzy] = arr[3].split(' ').map(i => Number.parseFloat(i))
      shadowObj.value.outer = { color: `rgba(${colorArr.join(',')})`, x, y, fuzzy }
    }
    {
      const colorArr = arr[5].split(',').map(i => i.trim())
      if (colorArr.length === 3) {
        colorArr.push('1')
      }
      const [x, y, fuzzy] = arr[6].split(' ').map(i => Number.parseFloat(i))
      shadowObj.value.inner = { color: `rgba(${colorArr.join(',')})`, x, y, fuzzy }
    }
  },
  { immediate: true },
)
watch(
  shadowObj,
  () => {
    const { outer: out, inner } = shadowObj.value
    const str = `${out.color} ${out.x}px ${out.y}px ${out.fuzzy}px, ${inner.color} ${inner.x}px ${inner.y}px ${inner.fuzzy}px inset`
    if (layer.value.style.boxShadow !== str) {
      layer.value.style.boxShadow = str
    }
  },
  { deep: true },
)
</script>

<template>
  <div class="setting-wrap">
    <!-- 外 -->
    <div class="setting-item">
      <h3>外阴影</h3>
      <hi-color v-model="shadowObj.outer.color" />
    </div>
    <div class="setting-item justify-end!">
      <h3>横向</h3>
      <el-input-number v-model="shadowObj.outer.x" v-input-number controls-position="right" class="w-100" />
      <h3 class="ml-10">纵向</h3>
      <el-input-number v-model="shadowObj.outer.y" v-input-number controls-position="right" class="w-100" />
    </div>
    <div class="setting-item justify-end!">
      <h3>模糊</h3>
      <el-input-number v-model="shadowObj.outer.fuzzy" v-input-number controls-position="right" class="w-100" />
    </div>
    <!-- 内 -->
    <div class="setting-item">
      <h3>内阴影</h3>
      <hi-color v-model="shadowObj.inner.color" />
    </div>
    <div class="setting-item justify-end!">
      <h3>横向</h3>
      <el-input-number v-model="shadowObj.inner.x" v-input-number controls-position="right" class="w-100" />
      <h3 class="ml-10">纵向</h3>
      <el-input-number v-model="shadowObj.inner.y" v-input-number controls-position="right" class="w-100" />
    </div>
    <div class="setting-item justify-end!">
      <h3>模糊</h3>
      <el-input-number v-model="shadowObj.inner.fuzzy" v-input-number controls-position="right" class="w-100" />
    </div>
  </div>
</template>

<style scoped lang="scss">
</style>
