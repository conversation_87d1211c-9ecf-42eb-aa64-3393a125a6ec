declare module '*.scss' {
  const scss: Record<string, string>
  export default scss
}

declare module 'howler/dist/howler.core.min' {
  export * from 'howler'
  export { default } from 'howler'
}

// 以下是一些常用的类型声明，可以根据项目需要自行添加
declare module 'element-plus/dist/locale/zh-cn.mjs'
declare module 'nprogress'
declare module 'qrcode'
declare module 'vue-deepunref'
declare module 'resize-observer-polyfill'
declare module 'postcss-aspect-ratio-polyfill';
