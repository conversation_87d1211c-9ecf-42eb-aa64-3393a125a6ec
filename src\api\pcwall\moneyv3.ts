import { HiRequest } from '../request'

export default {
  configRead: (params: any) => HiRequest.post('/pro/hxc/web/promoneyv3config/read.htm', params),

  read: (params: any) => HiRequest.post('/pro/hxc/web/promoneyv3/read.htm', params),
  go: (params?: any) => HiRequest.post('/pro/hxc/web/promoneyv3/go.htm', params),
  again: (params?: any) => HiRequest.post('/pro/hxc/web/promoneyv3/again.htm', params),
  ranking: (params: any) => HiRequest.post('/pro/hxc/web/promoneyv3/ranking.htm', params),

  regeditPage: (params: any) => HiRequest.post('/pro/hxc/web/promoneyv3regedit/page.htm', params),
  regeditMembersCnt: (params: any) => HiRequest.post('/pro/hxc/web/promoneyv3regedit/membersCnt.htm', params),

  switch: (params: any) => HiRequest.post('/pro/hxc/web/promoneyv3/switch.htm', params),
}
