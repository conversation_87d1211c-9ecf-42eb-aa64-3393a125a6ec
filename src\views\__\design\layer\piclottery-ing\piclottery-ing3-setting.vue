<script setup lang="ts">
import type { IDesignPiclotteryIng3 } from './piclottery-ing3'
import { getDefaultMaterial, openSelectMaterial, useDataAttr } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'
import { defaultAnimateSpeed, defaultHeadSizeH, defaultHeadSizeW, defaultImgMode, defaultItemBorderColor, defaultPlaceHolderImg, defultColCount, defultRowCount } from './piclottery-ing3'

const layer = defineModel<IDesignPiclotteryIng3>('layer', { required: true })

const headSizeWBlind = useDataAttr(layer.value, 'headSizeW', defaultHeadSizeW)
const headSizeHBlind = useDataAttr(layer.value, 'headSizeH', defaultHeadSizeH)
const animiteSpeedBlind = useDataAttr(layer.value, 'animiteSpeed', defaultAnimateSpeed)
const itemBorderColorBind = useDataAttr(layer.value, 'itemBorderColor', defaultItemBorderColor)
const itemBorderWidthBind = useDataAttr(layer.value, 'itemBorderWidth', 0)
const rowCountBlind = useDataAttr(layer.value, 'rowCount', defultRowCount)
const colCountBlind = useDataAttr(layer.value, 'colCount', defultColCount)
const placeHolderImgBlind = computed(() => {
  return layer.value.placeHolderImg ?? defaultPlaceHolderImg
})
const imgModeBind = useDataAttr(layer.value, 'imgMode', defaultImgMode)

type IType = 'placeHolderImg'
async function updateMaterialFn(name: IType, isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, `${name}`)
    : await openSelectMaterial('PIC')
  if (result) {
    layer.value[name] = result
  } else if (isReset) {
    delete layer.value[name]
  }
}
const modes: { label: string, value: 'contain' | 'fill' | 'cover' }[] = [
  { label: '适应', value: 'contain' },
  { label: '拉伸', value: 'fill' },
  { label: '裁剪', value: 'cover' },
]
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>元素尺寸</h3>
      </div>
      <div class="setting-item">
        <h3></h3>
        <div class="flex flex-a-c">
          <p>宽：</p>
          <el-input-number v-model="headSizeWBlind" v-input-number :max="800" :min="50" controls-position="right" />
          <p class="ml-10">高：</p>
          <el-input-number v-model="headSizeHBlind" v-input-number :max="800" :min="50" controls-position="right" />
        </div>
      </div>
      <div class="setting-item">
        <h3>元素边框</h3>
        <el-input-number v-model="itemBorderWidthBind" v-input-number :max="100" :min="0" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3></h3>
        <div class="setting-item">
          <h3>颜色</h3>
          <hi-color v-model="itemBorderColorBind" />
        </div>
      </div>
      <div class="setting-item">
        <h3>行数</h3>
        <el-input-number v-model="rowCountBlind" v-input-number :max="30" :min="1" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>列数</h3>
        <el-input-number v-model="colCountBlind" v-input-number :max="100" :min="1" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>动画速度</h3>
        <el-input-number v-model="animiteSpeedBlind" v-input-number :max="30" :min="0" controls-position="right" />
      </div>
      <div class="setting-item mt-5!">
        <h3>头像默认图</h3>
        <div class="flex items-end">
          <div class="bgblank relative h-50 w-64">
            <MaterialThumbnail @select="updateMaterialFn('placeHolderImg')" @reset="updateMaterialFn('placeHolderImg', true)">
              <img v-if="placeHolderImgBlind" :src="placeHolderImgBlind" alt="" class="h-full w-full object-cover" />
            </MaterialThumbnail>
          </div>
        </div>
      </div>
      <div class="setting-item">
        <h3>图片显示模式</h3>
        <el-select v-model="imgModeBind" placeholder="选择填充方式" class="w-100">
          <el-option v-for="item in modes" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.d {
  width: 25px;
  height: 25px;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
  &:first-child {
    position: relative;
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 90%;
      height: 90%;
      margin: 5%;
      mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1em' height='1em' viewBox='0 0 256 256'%3E%3Cpath fill='%23000' d='m203.63 62.65l13.25-14.58a12 12 0 0 0-17.76-16.14l-13.24 14.56A100 100 0 0 0 52.37 193.35l-13.25 14.58a12 12 0 1 0 17.76 16.14l13.24-14.56A100 100 0 0 0 203.63 62.65M52 128a75.94 75.94 0 0 1 117.58-63.57l-100.91 111A75.6 75.6 0 0 1 52 128m76 76a75.5 75.5 0 0 1-41.58-12.43l100.91-111A75.94 75.94 0 0 1 128 204'/%3E%3C/svg%3E");
      mask-size: 100%;
      background-color: #999999;
      cursor: pointer;
    }
  }
}
</style>
