import { HiRequest } from '../request'

export default {
  read: (params: any) => HiRequest.post('/pro/hxc/mobile/properformancev3/read.htm', params),

  recordAdd: (params: any) => HiRequest.post('/pro/hxc/mobile/properformancev3record/add.htm', params),
  recordUpdate: (params: any) => HiRequest.post('/pro/hxc/mobile/properformancev3record/update.htm', params),
  recordRead: (params: any) => HiRequest.post('/pro/hxc/mobile/properformancev3record/read.htm', params),

  formList: (params: any) => HiRequest.post('/pro/hxc/mobile/properformancev3form/list.htm', params),
}
