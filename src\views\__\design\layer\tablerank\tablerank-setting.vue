<script setup lang="ts">
import type { IDesignIngTablerank, TableItemStyle } from './tablerank'
import { useDataAttr, useDesignState } from '../..'
import { defaultPadding, defaultShowTh } from './tablerank'

const layer = defineModel<IDesignIngTablerank>('layer', { required: true })
const designState = useDesignState()

const showThBlind = useDataAttr(layer.value, 'showTh', defaultShowTh)
// const showBorderBlind = useDataAttr(layer.value, 'showBorder', defaultShowBorder)
const paddingTopBind = useDataAttr(layer.value, 'paddingTop', defaultPadding[0])
const paddingRightBind = useDataAttr(layer.value, 'paddingRight', defaultPadding[1])
const paddingBottomBind = useDataAttr(layer.value, 'paddingBottom', defaultPadding[2])
const paddingLeftBind = useDataAttr(layer.value, 'paddingLeft', defaultPadding[3])

const dataSourceOptions = computed(() => {
  return designState.getLayerData('listDataSourceList') || []
})

const blindData = computed(() => {
  const defaultData = { title: [], data: [] }
  if (!layer.value.dataSource) {
    return defaultData
  }
  const data = designState.getLayerData('allTableStatistics') || {}
  return data[layer.value.dataSource]
})

const titleData = computed(() => {
  return blindData.value?.title || []
})

watch(() => layer.value.dataSource, () => {
  layer.value.contentStyle = layer.value.contentStyle.slice(0, titleData.value.length)
  for (let i = 0; i < titleData.value.length; i++) {
    const item: TableItemStyle = {
      fontSize: 14,
      fontColor: '#000000',
      fontBold: false,
      align: 'center',
      width: 20,
    }
    if (!layer.value.contentStyle[i]) {
      layer.value.contentStyle[i] = item
    }
  }
}, {
  immediate: true,
})
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>数据来源</h3>
        <el-select v-model="layer.dataSource" placeholder="请选择" style="width: 140px">
          <el-option v-for="item in dataSourceOptions" :key="item.key" :label="item.name" :value="item.key" />
        </el-select>
      </div>
      <div class="setting-item">
        <h3>标题</h3>
        <el-input-number v-model="layer.titleFontSize" v-input-number :max="100" :min="10" controls-position="right" />
        <hi-color v-model="layer.titleFontColor" />
      </div>
      <div class="setting-item">
        <el-table :data="titleData" style="width: 100%" border>
          <el-table-column prop="name" label="字段名" align="center" />
          <el-table-column prop="name" label="字号" align="center" width="60">
            <template #default="{ $index }">
              <el-input v-model="(layer.contentStyle[$index] as TableItemStyle).fontSize" type="number" style="width: 32px" :max="100" :min="1">
              </el-input>
            </template>
          </el-table-column>
          <el-table-column prop="address" label="颜色" width="120" align="center">
            <template #default="{ $index }">
              <hi-color v-model="(layer.contentStyle[$index] as TableItemStyle).fontColor" />
            </template>
          </el-table-column>
          <el-table-column prop="address" label="位置" width="80" align="center">
            <template #default="{ $index }">
              <el-select v-model="(layer.contentStyle[$index] as TableItemStyle).align" placeholder="请选择" style="width: 50px">
                <el-option label="左" value="left" />
                <el-option label="中" value="center" />
                <el-option label="右" value="right" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="address" label="占比" width="50" align="center">
            <template #default="{ $index }">
              <div class="flex" style="width: 60px">
                <el-input v-model="(layer.contentStyle[$index] as TableItemStyle).width" type="number" style="width: 32px" :max="100" :min="1">
                </el-input>
                %
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="address" label="加粗" width="50" align="center">
            <template #default="{ $index }">
              <el-switch v-model="(layer.contentStyle[$index] as TableItemStyle).fontBold" />
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="setting-item">
        <h3>显示标题行</h3>
        <el-switch v-model="showThBlind" />
      </div>
      <!-- <div class="setting-item">
        <h3>显示边框</h3>
        <el-switch v-model="showBorderBlind" />
      </div> -->
      <div class="setting-item">
        <h3>头像尺寸</h3>
        <el-input-number v-model="layer.avatarSize" v-input-number :max="120" :min="10" controls-position="right" />
      </div>
      <!-- 内边距 -->
      <div class="flex items-center justify-between">
        <label class="flex items-center">内边距</label>
      </div>
      <div class="setting-item justify-end!">
        <h3 class="ml-10">左</h3>
        <el-input-number v-model="paddingLeftBind" v-input-number controls-position="right" />
        <h3 class="ml-10">右</h3>
        <el-input-number v-model="paddingRightBind" v-input-number controls-position="right" />
      </div>
      <div class="setting-item justify-end!">
        <h3 class="ml-10">上</h3>
        <el-input-number v-model="paddingTopBind" v-input-number controls-position="right" />
        <h3 class="ml-10">下</h3>
        <el-input-number v-model="paddingBottomBind" v-input-number controls-position="right" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.d {
  width: 25px;
  height: 25px;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
  &:first-child {
    position: relative;
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 90%;
      height: 90%;
      margin: 5%;
      mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1em' height='1em' viewBox='0 0 256 256'%3E%3Cpath fill='%23000' d='m203.63 62.65l13.25-14.58a12 12 0 0 0-17.76-16.14l-13.24 14.56A100 100 0 0 0 52.37 193.35l-13.25 14.58a12 12 0 1 0 17.76 16.14l13.24-14.56A100 100 0 0 0 203.63 62.65M52 128a75.94 75.94 0 0 1 117.58-63.57l-100.91 111A75.6 75.6 0 0 1 52 128m76 76a75.5 75.5 0 0 1-41.58-12.43l100.91-111A75.94 75.94 0 0 1 128 204'/%3E%3C/svg%3E");
      mask-size: 100%;
      background-color: #999999;
      cursor: pointer;
    }
  }
}
</style>
