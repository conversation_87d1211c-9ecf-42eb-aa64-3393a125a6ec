<script setup lang="ts">
import * as THREE from 'three'
import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue'
import { injectScale, useDesignState } from '../..'
import { DEFAULT_DATA, type IDesign3dRotatingCube } from './3d-rotating-cube'

const designState = useDesignState()
const scale = injectScale()

interface IDisplayData {
  id: number
  img: string
  name: string
  count: number
}

const container = ref<HTMLDivElement | null>(null)
const layer = defineModel<IDesign3dRotatingCube>('layer', { required: true })

const awards = computed<IDisplayData[]>(() => {
  return designState.getLayerData('awards') || []
})

const data = computed(() => {
  return {
    ...DEFAULT_DATA,
    ...layer.value.data,
  }
})

// Three.js 相关变量
let scene: THREE.Scene
let camera: THREE.PerspectiveCamera
let renderer: THREE.WebGLRenderer
let cube: THREE.Mesh | null = null
let animationFrameId: number
let pauseTimeoutId: number | NodeJS.Timeout | null = null

// 纹理和材质相关
let currentMaterial: THREE.MeshBasicMaterial | null = null
let currentTexture: THREE.Texture | null = null
const defaultTextureUrl = new URL('./static/prize.png', import.meta.url).href

// 创建纹理（使用Canvas处理背景）
function createBackgroundTexture(url: string): Promise<THREE.Texture> {
  return new Promise((resolve, reject) => {
    const image = new Image()
    image.crossOrigin = 'anonymous'
    image.src = url

    image.onload = () => {
      const canvas = document.createElement('canvas')
      canvas.width = image.width
      canvas.height = image.height

      const ctx = canvas.getContext('2d')!
      ctx.fillStyle = '#ffffff' // 填充白色背景
      ctx.fillRect(0, 0, canvas.width, canvas.height)
      ctx.drawImage(image, 0, 0)

      const texture = new THREE.CanvasTexture(canvas)
      resolve(texture)
    }

    image.onerror = (error) => {
      console.error('纹理加载失败:', error)
      reject(error)
    }
  })
}

// 加载并配置纹理
async function loadTexture(url: string): Promise<THREE.Texture> {
  try {
    const texture = await createBackgroundTexture(url)
    texture.colorSpace = THREE.SRGBColorSpace
    texture.generateMipmaps = false
    if (renderer) {
      texture.anisotropy = renderer.capabilities.getMaxAnisotropy()
    }
    texture.needsUpdate = true
    return texture
  } catch (error) {
    console.error('加载纹理失败，使用默认纹理:', error)
    return loadTexture(defaultTextureUrl) // 回退到默认纹理
  }
}

// 创建立方体
async function createCube(textureUrl: string) {
  // 清理旧资源
  disposeCubeResources()

  // 加载纹理
  const texture = await loadTexture(textureUrl)

  // 创建材质
  currentMaterial = new THREE.MeshBasicMaterial({ map: texture })
  const materials = Array.from({ length: 6 }).fill(currentMaterial) as THREE.MeshBasicMaterial[]

  // 创建几何体和网格
  const geometry = new THREE.BoxGeometry(2, 2, 2)
  cube = new THREE.Mesh(geometry, materials)
  scene.add(cube)

  // 保存当前纹理引用
  currentTexture = texture
}

// 更新立方体贴图
async function updateCubeTexture(newUrl: string) {
  if (!cube || !currentMaterial) return

  try {
    // 加载新纹理
    const newTexture = await loadTexture(newUrl)

    // 释放旧纹理
    if (currentTexture) {
      currentTexture.dispose()
    }

    // 更新材质
    currentMaterial.map = newTexture
    currentMaterial.needsUpdate = true

    // 保存新纹理引用
    currentTexture = newTexture

    // 强制渲染更新
    renderer.render(scene, camera)
  } catch (error) {
    console.error('更新贴图失败:', error)
  }
}

// 清理立方体资源
function disposeCubeResources() {
  if (cube) {
    scene.remove(cube)
    cube.geometry.dispose()
    cube = null
  }

  if (currentMaterial) {
    currentMaterial.dispose()
    currentMaterial = null
  }

  if (currentTexture) {
    currentTexture.dispose()
    currentTexture = null
  }
}

onMounted(async () => {
  const width = container.value?.clientWidth || document.body.clientWidth
  const height = container.value?.clientHeight || document.body.clientHeight
  const aspect = width / height

  // 场景
  scene = new THREE.Scene()

  // 相机
  camera = new THREE.PerspectiveCamera(60, aspect, 0.1, 1000)
  camera.position.set(2.4, 0, 2.4)
  camera.lookAt(0, 0, 0)

  // 渲染器
  renderer = new THREE.WebGLRenderer({ alpha: true, antialias: true })
  renderer.setClearColor(0x000000, 0)
  renderer.setSize(width, height)
  container.value?.appendChild(renderer.domElement)

  // 初始创建立方体
  const initialUrl = awards.value?.[0]?.img || defaultTextureUrl
  await createCube(initialUrl)

  resetCubeRotation()
  animate()
  window.addEventListener('resize', onResize)
})

function resetCubeRotation() {
  if (!cube) return

  // 停止动画
  cancelAnimationFrame(animationFrameId)

  // 清理暂停计时器
  if (pauseTimeoutId) {
    clearTimeout(pauseTimeoutId)
    pauseTimeoutId = null
  }

  // 设置初始旋转
  cube.rotation.x = data.value.cubeInitPositionX
  cube.rotation.y = data.value.cubeInitPositionY
  cube.rotation.z = data.value.cubeInitPositionZ
  renderer.render(scene, camera)

  // 延迟后重新开始动画
  pauseTimeoutId = setTimeout(() => {
    animate()
    pauseTimeoutId = null
  }, 800)
}

// 动画循环
function animate() {
  if (!cube) return

  animationFrameId = requestAnimationFrame(animate)

  // 更新旋转
  cube.rotation.x += data.value.cubeRotationPositionX / 10
  cube.rotation.y += data.value.cubeRotationPositionY / 10
  cube.rotation.z += data.value.cubeRotationPositionZ / 10
  renderer.render(scene, camera)
}

// 监听初始位置变化
watch(() => [data.value.cubeInitPositionX, data.value.cubeInitPositionY, data.value.cubeInitPositionZ], () => {
  resetCubeRotation()
})

// 监听奖项图片变化
watch(() => awards.value?.[0]?.img, (newUrl) => {
  if (newUrl) {
    updateCubeTexture(newUrl)
  }
})

onBeforeUnmount(() => {
  // 清理动画
  cancelAnimationFrame(animationFrameId)

  // 清理暂停计时器
  if (pauseTimeoutId) {
    clearTimeout(pauseTimeoutId)
    pauseTimeoutId = null
  }

  // 移除事件监听
  window.removeEventListener('resize', onResize)

  // 释放资源
  disposeCubeResources()

  if (renderer) {
    renderer.dispose()
  }
})

// 窗口大小变化处理
function onResize() {
  const width = container.value?.clientWidth
  const height = container.value?.clientHeight
  if (!width || !height || !camera || !renderer) return

  camera.aspect = width / height
  camera.updateProjectionMatrix()
  renderer.setSize(width, height)
}

// 监听尺寸变化
watch(() => [layer.value.style.width, layer.value.style.height, scale.value], () => {
  onResize()
})
</script>

<template>
  <div class="outer-wrapper">
    <div ref="container" class="three-container"></div>
  </div>
</template>

<style scoped>
.outer-wrapper {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.three-container {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
