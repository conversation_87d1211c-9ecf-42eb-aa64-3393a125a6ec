<script setup lang="ts">
import type { IDesignLotteryIng2 } from './lottery-ing2'
import useThree from '@/views/__/three/useThree'
import BezierEasing from 'bezier-easing'
import gsap from 'gsap'
import { cloneDeep, sample } from 'lodash-es'
import * as THREE from 'three'
import { useDesignState } from '../..'
import { defaultPlaceHolderHeadImg, defaultrotateSpeed, defaultShowCube, defaultWallHeadSize } from './lottery-ing2'

const layer = defineModel<IDesignLotteryIng2>('layer', { required: true })

const designState = useDesignState()
const status = computed(() => designState.status)
// 参与人奖池
const regeditList = computed(() => {
  return designState.getLayerData('regeditList') || []
})

const onceNum = computed(() => {
  return designState.getLayerData('#每次抽取人数#')
})

const wallHeadSize = computed(() => layer.value.wallHeadSize ?? defaultWallHeadSize)
const rotateSpeed = computed(() => layer.value.rotateSpeed ?? defaultrotateSpeed)
const showCube = computed(() => layer.value.showCube ?? defaultShowCube)

const threeRef = ref<HTMLElement | null>(null)
const { scene, loadTexture, camera } = useThree({
  threeRef,
  hasHelp: false,
  hasControls: false,
})

const cache: Record<string, any> = {}
// 生成纹理
function createTexture(url: string) {
  let texture = cache[url]
  if (texture) {
    return texture
  }
  texture = loadTexture(url)
  cache[url] = texture
  return texture
}

class BaseShape {
  name = 'BaseShape'
  group: THREE.Group
  intervalHandler: NodeJS.Timeout | null = null
  constructor() {
    this.group = new THREE.Group()
    this.group.visible = false
    scene.add(this.group)
  }

  disposeItem(item: THREE.Object3D<THREE.Object3DEventMap>) {
    if (item instanceof THREE.Mesh || item instanceof THREE.Sprite) {
      item.material?.dispose()
      item.geometry?.dispose()
    } else if (item instanceof THREE.Group) {
      item.children.forEach((child) => {
        this.disposeItem(child)
      })
    }
  }

  init() {
    this.intervalHandler && clearInterval(this.intervalHandler)
    this.group.traverse((item) => {
      this.disposeItem(item)
    })
    this.group.clear()
  }

  play(person?: number) {
    console.log('play', person)
  }

  getEase(value = [0.66, 0.2, 0.45, 0.79] as [number, number, number, number]) {
    return BezierEasing(...value)
  }

  destory() {
    this.intervalHandler && clearInterval(this.intervalHandler)
    this.group.traverse((item) => {
      this.disposeItem(item)
    })
    this.group.clear()
    this.group.visible = false
  }
}

// 时空旋转
class ShapeTimerotate extends BaseShape {
  constructor() {
    super()
    this.name = 'ShapeGalaxy'
  }

  cardBoxSize = 150 // 立方体大小
  allCubeGroup = new THREE.Group()
  wallObjectGroup = new THREE.Group()
  async init() {
    super.init()
    // 相机
    camera.fov = 40
    camera.position.y = 200
    camera.position.z = 1200
    camera.updateProjectionMatrix()
    camera.lookAt(scene.position)
    scene.rotation.set(0, 0, 0)
    this.initShapeWall()
  }

  async initShapeWall() {
    // 可视区大小
    const { width, height } = this.getDisplayArea(-1000)
    // 半径
    const radius = Math.max(width, height)
    // 几行，高度/单个大小
    const headSize = wallHeadSize.value
    const radian = Math.PI / 180
    const rowCount = Math.floor(height / headSize)
    // 几列，周长/单个大小
    const colCount = Math.floor((2 * Math.PI * radius) / headSize)
    const itemR = 360 / colCount
    const geometry = new THREE.PlaneGeometry(headSize - 10, headSize - 10)
    for (let i = 0; i < rowCount; i++) {
      const y = (i - rowCount / 2 + 0.5) * headSize
      const centerVector = new THREE.Vector3(0, y, 0)
      for (let j = 0; j < colCount; j++) {
        const x = radius * Math.sin(radian * j * itemR)
        const z = radius * Math.cos(radian * j * itemR)

        const itemData = getItem()
        if (!itemData) return
        const material = new THREE.MeshBasicMaterial({
          map: createTexture(itemData.avatar),
          fog: false,
          transparent: true,
        })
        const threeMesh = new THREE.Mesh(geometry, material)
        threeMesh.position.set(x, y, z)
        threeMesh.lookAt(centerVector)
        this.wallObjectGroup.add(threeMesh)
      }
    }
    this.group.add(this.wallObjectGroup)
  }

  initShapeCube(person: number | undefined = 1) {
    const itemAngle = (2 * Math.PI) / person
    // 初始化立方体
    for (let i = 0; i < person; i++) {
      const cubeGroup = new THREE.Group()
      const itemData = getItem()
      if (!itemData) return
      const cube = new THREE.Mesh(
        this.getBox(person),
        new THREE.MeshBasicMaterial({
          side: THREE.DoubleSide,
          color: 0xFFFFFF,
          map: createTexture(itemData.avatar),
          transparent: true,
        }),
      )
      cubeGroup.add(cube)
      cubeGroup.position.x = 500 * Math.sin(i * itemAngle)
      cubeGroup.position.z = 500 * Math.cos(i * itemAngle)
      this.allCubeGroup.add(cubeGroup)
    }
    this.group.add(this.allCubeGroup)
  }

  getDisplayArea(z = 0) {
    const radian = Math.PI / 180
    const height = Math.round(2 * Math.tan((camera.fov / 2) * radian) * (camera.position.z - z))
    const width = Math.round(camera.aspect * height)
    return {
      width,
      height,
    }
  }

  getBox(person: number) {
    const r = (2 * Math.PI) / person // 两个方块之间的夹角
    const maxL = 2 * 500 * Math.sin(r / 2) * 0.8
    const rSize = maxL > this.cardBoxSize || maxL < 1 ? this.cardBoxSize : maxL
    return new THREE.BoxGeometry(rSize, rSize, rSize)
  }

  play(person?: number) {
    if (showCube.value) {
      this.initShapeCube(person)
    }
    gsap.to(this.allCubeGroup.rotation, { y: -2 * Math.PI, repeat: Infinity, duration: 50 / rotateSpeed.value, ease: 'none' })
    gsap.to(this.wallObjectGroup.rotation, { y: -2 * Math.PI, repeat: Infinity, duration: 100 / rotateSpeed.value, ease: 'none' })
    this.intervalHandler = setInterval(() => {
      this.allCubeGroup.children.forEach((group) => {
        group.children.forEach((object) => {
          if (object instanceof THREE.Mesh) {
            const itemData = getItem()
            if (!itemData) return
            object.material.map = createTexture(itemData.avatar)
          }
        })
      })
      this.updateItems()
    }, 100)
  }

  updateItems() {
    const meshChildren = this.wallObjectGroup.children.filter(
      item => item instanceof THREE.Mesh,
    ) as THREE.Mesh[]

    if (meshChildren.length === 0) return

    const countToUpdate = Math.min(5, meshChildren.length)

    const shuffledMeshes = [...meshChildren].sort(() => 0.5 - Math.random())

    for (let i = 0; i < countToUpdate; i++) {
      const object = shuffledMeshes[i]
      if (object && object.material instanceof THREE.MeshBasicMaterial) {
        const itemData = getItem()
        if (!itemData) continue
        object.material.map = createTexture(itemData.avatar)
        object.material.needsUpdate = true
      }
    }
  }
}

const shapeObj = ref<BaseShape | null>(null)
async function runAnimit(onceNum: number | undefined = 1) {
  shapeObj.value = new ShapeTimerotate()
  shapeObj.value.group.visible = true
  shapeObj.value.init()
  shapeObj.value.play(Math.min(onceNum, 30))
}
const defaultHeadImg = computed(() => {
  return layer.value.placeHolderHeadImg ?? defaultPlaceHolderHeadImg
})
function getItem() {
  if (regeditList.value.length === 0) {
    return {
      avatar: defaultHeadImg.value,
    }
  }
  const tem = cloneDeep(sample(regeditList.value))
  if (!tem.avatar) tem.avatar = defaultHeadImg.value
  const itemData = Object.assign({}, tem)
  return itemData
}

watch(
  () => [status.value, wallHeadSize.value, rotateSpeed.value, showCube.value],
  () => {
    shapeObj.value?.destory()
    runAnimit(onceNum.value)
  },
  { deep: true, immediate: true },
)

onUnmounted(() => {
  shapeObj.value?.destory()
})
</script>

<template>
  <div class="lottery-ing2-box">
    <div ref="threeRef" class="three-box"></div>
  </div>
</template>

<style scoped lang="scss">
.lottery-ing2-box {
  width: 100%;
  height: 100%;
  .three-box {
    width: 100%;
    height: 100%;
  }
}
</style>
