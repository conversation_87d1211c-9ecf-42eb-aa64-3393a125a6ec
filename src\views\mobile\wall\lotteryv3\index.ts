import { useDesignState, useDesignTemp } from 'design/index'
import { useImData } from '~/src/hooks/useImData'
import { randomAvatar } from '~/src/utils'
import { useLimit } from '../../hooks/useLimit'
import { useMobileStore } from '../../stores/useMobileStore'
import { fetchTheme } from '../../utils'
import { mockConfig, mockRound } from './mock'
import 'vant/lib/toast/style'

// 类型
interface IConfig {
  lotteryv3Id: number
}
interface IRound {
  id: number
  themeId: number
  title: string
  // 单次抽奖人数限制
  onceNum?: number
  // 当前批次号
  batchNo?: string
  // 重复中奖开关
  repeatWinSwitch?: 'Y' | 'N'
  // 参与人数展示开关
  showJoinNum?: string
  // 抽奖资格获取开关
  regeditSwitch?: 'Y' | 'N'
  // 资格获取方式类型
  regeditType?: 'CLICK' | 'KEYWORD'
  // 资格验证口令
  regeditKeyword?: string
}

export function useMobileLotteryV3() {
  const designTemp = useDesignTemp()
  const designState = useDesignState()

  const isDesignEdit = designTemp.isEdit

  designTemp.showType = 'mobile'

  const mobileStore = useMobileStore()
  const { wall, featureConfig, isShowAd, isWedding } = storeToRefs(mobileStore)

  // 互动配置
  const round = ref<IRound>()
  const config = ref<IConfig>()
  // 微信用户信息
  const wxuser = ref()
  // 广告
  const adResponse = ref()

  const roundId = computed(() => round.value?.id || '')

  // 活动限制
  const { dealLimit } = useLimit()

  // 查询是否报名
  const fetchRegeditRead = async () => {
    if (!roundId.value) return
    if (round.value?.regeditSwitch !== 'Y') {
      designState.setStatus('alreadySign')
      return
    }
    try {
      const regedit = await api.mobile.lotteryv3.regeditRead({
        where: {
          lotteryv3Id: roundId.value,
        },
      })
      designState.setStatus(regedit ? 'alreadySign' : 'sign')
    } catch {
      designState.setStatus('sign')
    }
  }

  // 报名
  const fetchRegeditInsert = async (keyword?: string) => {
    if (!roundId.value) return
    if (round.value?.regeditSwitch !== 'Y') return
    const data = await api.mobile.lotteryv3.regeditInsert({
      lotteryv3Id: roundId.value,
      keyword,
    })
    dealLimit(data)
    designState.setStatus('alreadySign')
  }

  // 主要作用是将数据暴露给图层组件
  designState
    .setStatusList([
      { label: '待报名', value: 'sign' },
      { label: '已报名', value: 'alreadySign' },
    ])
    .setLayerData({
      '#主题名称#': computed(() => designTemp.theme?.name),
      '#轮次标题#': computed(() => round.value?.title),
      'isShowAd': isShowAd,
      'isWedding': isWedding,
      'adResponse': computed(() => adResponse.value),
      'wxuser': computed(() => wxuser.value),
      'wall': computed(() => wall.value),

      'showRegeditComp': computed(() => isDesignEdit || round.value?.regeditSwitch === 'Y'),
      'showCodeInput': computed(() => isDesignEdit || round.value?.regeditType === 'KEYWORD'),
    })
    // 图层组件事件
    .setLayerEventList([
      {
        eventId: 'back-home',
        name: '返回首页',
        value() {
          window.parent.postMessage({ type: 'iframe:backHome' }, '*')
        },
      },
      // 广告交互
      {
        eventId: 'clickAd',
        value(data) {
          window.parent.postMessage({ type: 'iframe:clickAd', data }, '*')
        },
      },
      {
        eventId: 'SHOW_AD',
        value(data) {
          window.parent.postMessage({ type: 'iframe:SHOW_AD', data }, '*')
        },
      },
      {
        eventId: 'clickAwards',
        name: '中奖记录',
        value() {
          window.parent.postMessage({ type: 'iframe:clickAwards' }, '*')
        },
      },
      {
        eventId: 'confirm-regedit',
        value(val) {
          fetchRegeditInsert(val.code)
        },
      },

    ])

  const resetAllData = () => {
    designTemp.renderKey = Date.now()
    fetchRegeditRead()
  }

  watch(() => round.value?.themeId, themeId => fetchTheme(themeId || ''))
  watch(() => roundId.value, () => {
    if (isDesignEdit) return
    resetAllData()
  })

  tryOnMounted(() => {
    if (isDesignEdit) {
      // 设计模式mock数据
      config.value = mockConfig()
      round.value = mockRound()
      wxuser.value = {
        nickName: '测试用户',
        imgpath: randomAvatar(),
      }
    }
  })

  // 数据同步
  useImData({
    'im:lotteryv3:config': config,
    'im:lotteryv3': round,
    'im:wall': wall,
    'im:feature:config': featureConfig,
    'im:wxuser': wxuser,
    'im:adResponse': adResponse,
  })
}
