import type { IDesignLayer, IDesignSetup } from '../../types'
import { BisTypes, useDesignData } from '../..'
import { InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './listlottery-ing2-setting.vue'
import Comp from './listlottery-ing2.vue'
// 类型
export const type = 'listlottery-ing2'
export const defaultHeadCount = 100
export const defaultAnimateSpeed = 5
export const defaultHeadSize = 300
export const defalutTextStyle: textItemStyle = {
  fontSize: 40,
  fontColor: '#fff',
  fonBold: true,
}
// 数据类型约束
export interface IDesignListlotteryIng2 extends IDesignLayer {
  type: typeof type
  headCount?: number
  headSize?: number
  animiteSpeed?: number
  contentStyle: textItemStyle[]
  itemBgColors: string[]
}

export interface textItemStyle {
  fontSize: number
  fontColor: string
  fonBold: boolean
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.sportsIng,
    showType: ['pcwall'],
    status: ['ing', 'ready'],
    showInteractive: [InteractiveEnum.listlotteryv3],
    type,
    name: '名单抽奖3d动效',
    thumbnail: new URL('./listlottery-ing2.png', import.meta.url).href,
    Comp,
    CompSetting,
    defaultData() {
      const designData = useDesignData()
      const [width, height] = designData.option.drafts
      return {
        type,
        uuid: layerUuid(),
        name: '名单抽奖3d动效',
        contentStyle: [],
        itemBgColors: [''],
        style: {
          width: `${width}px`,
          height: `${height}px`,
          top: 0,
          left: 0,
        },
      }
    },
  })
}
