<script setup lang="ts">
import { injectScale } from '../..'
import { defaultValues, type IDesignParticle } from './particle'

const layer = defineModel<IDesignParticle>('layer', { required: true })

const boxRef = ref<HTMLElement>()
const displaySize = useElementSize(boxRef)
const scale = injectScale()

let displayCanvas: HTMLCanvasElement | null = null // 显示用的canvas
let bufferCanvas: HTMLCanvasElement | null = null // 离屏canvas
let displayCtx: CanvasRenderingContext2D | null = null
let bufferCtx: CanvasRenderingContext2D | null = null
let animationId: number | null = null
const particlesArray: Particle[] = []
let loadedImages: HTMLImageElement[] = []

const config = reactive({
  sprayCount: layer.value.sprayCount ?? defaultValues.sprayCount, // 喷射数量
  maxParticles: layer.value.maxParticles ?? defaultValues.maxParticles,
  full: layer.value.full ?? defaultValues.full, // 是否使用扇形发射
  collision: layer.value.collision ?? defaultValues.collision,
  emissionSpeed: layer.value.emissionSpeed ?? defaultValues.emissionSpeed, // 发射速度
  gravity: layer.value.gravity ?? defaultValues.gravity, // 使用层属性中的重力值，如果没有则使用默认值
  fanAngle: layer.value.fanAngle ?? defaultValues.fanAngle, // 扇形夹角度数
  emitAngle: layer.value.emitAngle ?? defaultValues.emitAngle, // 扇形整体角度（喷射方向）
  emitterX: 0,
  emitterY: 0,
  friction: 0.97, // 摩擦力
  bounce: 0.3, // 反弹
  minSize: (layer.value.minSize ?? defaultValues.minSize) * scale.value, // 最小大小
  maxSize: (layer.value.maxSize ?? defaultValues.maxSize) * scale.value,
  rotationSpeed: 0.3, // 旋转速度
})

const emitterX = computed(() => {
  return typeof layer.value.emitterX === 'string' && (layer.value.emitterX as string).includes('%')
    ? (Number.parseInt(layer.value.emitterX) / 100) * displaySize.width.value
    : layer.value.emitterX ?? 0
})

const emitterY = computed(() => {
  return typeof layer.value.emitterY === 'string' && (layer.value.emitterY as string).includes('%')
    ? (Number.parseInt(layer.value.emitterY) / 100) * displaySize.height.value
    : layer.value.emitterY ?? 0
})

watch([() => displaySize.width.value, () => displaySize.height.value], ([width, height]) => {
  if (displayCanvas && bufferCanvas) {
    displayCanvas.width = width
    displayCanvas.height = height
    bufferCanvas.width = width
    bufferCanvas.height = height

    particlesArray.forEach((particle) => {
      if (particle.grounded) {
        particle.reset()
      }
    })
  }
}, { deep: true })

watch([() => layer.value, () => scale.value], ([newLayer, scale]) => {
  const update: Partial<IDesignParticle> = {}
  Object.entries(defaultValues).forEach(([key, defaultValue]) => {
    const typedKey = key as keyof IDesignParticle
    if (typedKey === 'maxSize' || typedKey === 'minSize') {
      update[typedKey] = ((newLayer[typedKey] as number) ?? defaultValue) * scale
    } else {
      update[typedKey] = newLayer[typedKey] ?? defaultValue
    }
  })
  Object.assign(config, update)
}, { deep: true })

async function loadImages(images: string[]): Promise<HTMLImageElement[]> {
  return Promise.all(images.map(async (src) => {
    const img = new Image()
    img.crossOrigin = 'anonymous'
    return new Promise((resolve, reject) => {
      img.onload = () => {
        if (img.width === 0 || img.height === 0) {
          reject(new Error(`图片加载失败: ${src} - 尺寸为0`))
          return
        }
        resolve(img)
      }
      img.onerror = () => reject(new Error(`图片加载失败: ${src}`))
      img.src = src
    })
  }))
}

class Particle {
  x: number = 0
  y: number = 0
  size: number = 0
  speedX: number = 0
  speedY: number = 0
  rotation: number = 0
  rotationSpeed: number = 0
  bounce: number = 0
  gravity: number = 0
  friction: number = 0
  grounded: boolean = false
  image: HTMLImageElement | null = null

  constructor() {
    this.reset()
  }

  reset() {
    if (!config.full) {
      this.x = emitterX.value
      this.y = emitterY.value
      const halfFanAngle = config.fanAngle / 2
      const angle = ((config.emitAngle - halfFanAngle) + Math.random() * config.fanAngle) * (Math.PI / 180)
      const speed = Math.random() * config.emissionSpeed + 1
      this.speedX = Math.cos(angle) * speed
      this.speedY = Math.sin(angle) * speed
    } else {
      this.x = Math.random() * displayCanvas!.width
      this.y = 0
      this.speedX = 0
      this.speedY = Math.random() * config.emissionSpeed + 1
    }

    this.size = Math.random() * (config.maxSize - config.minSize) + config.minSize
    this.rotation = Math.random() * 2 * Math.PI
    this.rotationSpeed = Math.random() * config.rotationSpeed - config.rotationSpeed / 2
    this.bounce = config.bounce
    this.gravity = config.gravity
    this.friction = config.friction
    this.grounded = false
    this.image = loadedImages[Math.floor(Math.random() * loadedImages.length)]
  }

  update() {
    if (!this.grounded) {
      this.speedY += this.gravity
      this.x += this.speedX
      this.y += this.speedY
      this.speedX *= this.friction
      this.speedY *= this.friction
      this.rotation += this.rotationSpeed

      if (config.collision) {
        // 边界检查
        if (this.x < this.size / 2) {
          this.x = this.size / 2
          this.speedX *= -1
        }
        if (this.x > displayCanvas!.width - this.size / 2) {
          this.x = displayCanvas!.width - this.size / 2
          this.speedX *= -1
        }

        // 底部碰撞
        if (this.y > displayCanvas!.height - this.size / 2) {
          this.y = displayCanvas!.height - this.size / 2
          this.speedY *= -this.bounce
          if (Math.abs(this.speedY) < 0.5) {
            this.speedY = 0
            this.grounded = true
          }
        }
      }
    }
  }

  draw() {
    if (!bufferCtx || !this.image) return
    if (this.image.width === 0 || this.image.height === 0) {
      console.warn('跳过绘制尺寸为0的图片')
      return
    }

    bufferCtx.save()
    try {
      bufferCtx.translate(this.x, this.y)
      bufferCtx.rotate(this.rotation)
      bufferCtx.drawImage(this.image, -this.size / 2, -this.size / 2, this.size, this.size)
    } catch (error) {
      console.error('绘制粒子时出错:', error)
    } finally {
      bufferCtx.restore()
    }
  }
}

let lastTime = 0
const maxInitialParticles = 0 // 初始最大粒子数量

function animate(currentTime: number) {
  if (!bufferCtx || !displayCtx || !loadedImages.length) return
  if (displayCanvas?.width === 0 || displayCanvas?.height === 0) {
    console.warn('画布尺寸为0，跳过动画帧')
    animationId = requestAnimationFrame(animate)
    return
  }

  // 在离屏canvas上清除
  bufferCtx.clearRect(0, 0, bufferCanvas!.width, bufferCanvas!.height)

  while (particlesArray.length >= config.maxParticles) {
    particlesArray.shift()
  }
  // 创建新粒子
  const elapsedTime = currentTime - lastTime
  const particlesToCreate = Math.floor(elapsedTime / 1000 * config.sprayCount)

  if (lastTime === 0) {
    for (let i = 0; i < Math.min(particlesToCreate, maxInitialParticles); i++) {
      particlesArray.push(new Particle())
    }
  } else {
    for (let i = 0; i < particlesToCreate; i++) {
      if (particlesArray.length < config.maxParticles) {
        particlesArray.push(new Particle())
      } else {
        break
      }
    }
  }

  if (particlesToCreate > 0) {
    lastTime = currentTime
  }

  // 在离屏canvas上更新和绘制粒子
  particlesArray.forEach((particle) => {
    particle.update()
    particle.draw()
  })

  // 将离屏canvas内容复制到显示canvas
  displayCtx.clearRect(0, 0, displayCanvas!.width, displayCanvas!.height)
  displayCtx.drawImage(bufferCanvas!, 0, 0)

  animationId = requestAnimationFrame(animate)
}

async function initCanvas() {
  if (animationId !== null) {
    cancelAnimationFrame(animationId)
  }

  if (!displaySize.width.value || !displaySize.height.value) {
    console.warn('容器尺寸为0，等待有效尺寸')
    return
  }

  // 创建显示canvas
  displayCanvas = document.createElement('canvas')
  displayCanvas.width = displaySize.width.value
  displayCanvas.height = displaySize.height.value
  boxRef.value?.appendChild(displayCanvas)
  displayCtx = displayCanvas.getContext('2d')

  // 创建离屏canvas
  bufferCanvas = document.createElement('canvas')
  bufferCanvas.width = displaySize.width.value
  bufferCanvas.height = displaySize.height.value
  bufferCtx = bufferCanvas.getContext('2d')

  if (!displayCtx || !bufferCtx) return

  try {
    loadedImages = await loadImages(layer.value.contestant)
    animate(performance.now())
  } catch (error) {
    console.error('加载图片时出错：', error)
  }
}

// 监听图片列表的变化
watch(() => layer.value.contestant, (newImages) => {
  if (newImages.length === 0) {
    console.warn('至少需要一张图像。保留最后一张图像。')
    return
  }

  loadImages(newImages).then((images) => {
    const oldImages = loadedImages
    loadedImages = images as HTMLImageElement[]

    // 更新现有粒子的图片
    particlesArray.forEach((particle) => {
      if (!oldImages.includes(particle.image!)) {
        particle.image = loadedImages[Math.floor(Math.random() * loadedImages.length)]
      }
    })
  }).catch((error) => {
    console.error('Error loading new images:', error)
  })
}, { deep: true })

onMounted(() => {
  initCanvas()
})

onUnmounted(() => {
  if (animationId !== null) {
    cancelAnimationFrame(animationId)
  }
})
</script>

<template>
  <div ref="boxRef" class="h-full w-full" />
</template>
