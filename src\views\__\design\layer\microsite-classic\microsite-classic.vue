<script setup lang="ts">
import type { IDesignMicrositeClassic } from './microsite-classic'
import { actList } from '../../data/actList'
import { defineCustomEmits, useDesignState, useDesignTemp } from '../../index'
import { toAct } from '../../utils'

const layer = defineModel<IDesignMicrositeClassic>('layer', { required: true })

const designTemp = useDesignTemp()
const designState = useDesignState()
const router = useRouter()
const customEmits = defineCustomEmits(layer)
const isOem = ref(router.currentRoute.value.path.startsWith('/oem'))

const hideActs = ['packetwall', 'mysterybox', 'ninegrids', 'center', 'awards', 'wheelsurf', 'wxuser-center', 'wxuser-awards']
// 开启的互动列表
const openActArr = computed(() => {
  return designState.getLayerData('openActArr') || []
})
const realActivity = computed(() => {
  let arr = []
  const temActs = actList.filter(item => !hideActs.includes(item.actType))
  if (openActArr.value.includes('showAll')) {
    arr = temActs
  } else {
    arr = temActs.filter(item => openActArr.value.includes(item.actType) || openActArr.value.includes(`wall${item.actType}`))
  }
  const len = arr.length
  const tmp: any = []
  let i = 0
  while (i < len) {
    tmp.push(arr.slice(i, i + 8))
    i += 8
  }
  return tmp
})
const isMsgOpen = computed(() => {
  return designState.getLayerData('isMsgOpen') || false
})
const featureConfig = computed(() => {
  return designState.getLayerData('featureConfig') || {}
})
const wall = computed(() => {
  return designState.getLayerData('wall') || {}
})
const userInfo = computed(() => {
  return designState.getLayerData('wxuser') || {}
})
const tabArr = computed(() => {
  if (openActArr.value.includes('showAll')) {
    return [{ type: 'msg', text: '上墙', route: 'wall-msg' }, { type: 'act', text: '互动' }, { type: 'me', text: '我', route: 'wxuser-center' }]
  }
  const arr: { type: string, text: string, route?: string }[] = [{ type: 'me', text: '我的', route: 'wxuser-center' }]
  if (realActivity.value.length > 0) {
    arr.unshift({ type: 'act', text: '互动' })
  }
  if (isMsgOpen.value) {
    arr.unshift({ type: 'msg', text: '上墙', route: 'wall-msg' })
  }
  return arr
})
const isShowAd = computed(() => {
  return designState.getLayerData('isShowAd') || false
})
const adResponse = computed(() => {
  return designState.getLayerData('adResponse') || {}
})
const clickFlag = ref<string>('act')

function onTab(type: string, route?: string) {
  if (designTemp.isEdit) return
  if (route) {
    toAct(route)
  }
  clickFlag.value = type
}
const clickStyle = computed(() => {
  return (type: string) => {
    return {
      'color': clickFlag.value === type ? '#7eafff' : '#fff',
      '--color': clickFlag.value === type ? '#7eafff' : '#fff',
    }
  }
})
const arrowstyle = computed(() => {
  return (v: string) => {
    return clickFlag.value === v ? { borderColor: '#fff transparent transparent transparent' } : {}
  }
})

const _isShowAdvertisement = computed(() => {
  if (isOem.value) {
    return false
  }
  if (featureConfig.value && featureConfig.value.phoneAdvertisementSwitch === 'Y') {
    return false
  }
  return true
})

// 扫描所有图标
const svgObj: Record<string, any> = {}
const svgList = import.meta.glob('./assets/svg/*.svg', { query: '?raw', import: 'default', eager: true })
for (const [key, value] of Object.entries(svgList)) {
  const arr = key.split(/\/|\./)
  const name = `${arr[4]}`
  svgObj[name] = value
}

function linkTo(type: string) {
  if (designTemp.isEdit) return
  // 经典版微站访问签到页
  toAct(type)
}

function onEmitClickAd() {
  customEmits('clickAd', Object.assign({}, toRaw(adResponse.value), { adStatus: 'click' }))
}
tryOnMounted(() => {
  if (isShowAd.value) {
    customEmits('SHOW_AD', toRaw(adResponse.value))
  }
})
</script>

<template>
  <div class="microsite-classic-box">
    <div class="wrap">
      <div class="topbox flex flex-d-c flex-a-c flex-j-sa">
        <div class="title">{{ wall.theme }}</div>
        <div class="user-info">
          <img class="wx-head" :src="userInfo.imgpath" />
          <p class="nick-name">{{ userInfo.nickName }}</p>
        </div>
      </div>

      <transition name="fade">
        <div v-if="clickFlag === 'act' && realActivity.length > 0" class="box">
          <div v-if="isShowAd && adResponse.imageUrl && _isShowAdvertisement" class="ad-box" @click="onEmitClickAd">
            <img class="ad" :src="adResponse.imageUrl" alt="">
          </div>

          <div v-if="!_isShowAdvertisement" class="use-tip">
            <span>点击下方图标参与互动</span>
            <div tag="img" class="close" src="./assets/close.png" @click="clickFlag = ''"></div>
          </div>

          <div class="interaction-box">
            <HiSwiper>
              <div v-for="(actArr, index) in realActivity" :key="index" class="slide">
                <div v-for="item in actArr" :key="item.actType" class="act-item flex flex-d-c flex-a-c" @click="linkTo(item.actType)">
                  <div class="icon" :style="{ '--color': item.color }" v-html="svgObj[`${item.actType}`]"></div>
                  <span>{{ item.actName }}</span>
                </div>
              </div>
            </HiSwiper>
          </div>
        </div>
      </transition>

      <footer class="flex flex-j-sa">
        <div v-for="item in tabArr" :key="item.type" class="footer-item flex flex-d-c flex-a-c flex-j-sb" :style="clickStyle(item.type)" @click="onTab(item.type, item.route)">
          <div class="arrow" :style="arrowstyle(item.type)" />
          <div class="fotter-icon" v-html="svgObj[`center_${item.type}`]"></div>
          {{ item.text }}
        </div>
      </footer>
    </div>
  </div>
</template>

<style scoped lang="scss">
.microsite-classic-box {
  width: 100%;
  height: 100%;
}
.wrap {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}
.topbox {
  position: relative;
  z-index: 2;
  height: calc(100% - 298px);
  justify-content: space-evenly;
}

.title {
  margin: 0;
  width: 84.4%;
  font-size: 20px;
  font-weight: normal;
  line-height: 1.3;
  padding: 0 7.8%;
  text-align: center;
  max-height: 50px;
  overflow: hidden;
  word-wrap: break-word;
  color: #fff;
  box-sizing: content-box;
}

.user-info {
  width: 90%;
  margin: 0 auto;
  text-align: center;
}

.wx-head {
  margin-bottom: 7.5px;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  border: 2px solid #fff;
  object-fit: cover;
  object-position: center;
}

.nick-name {
  width: 100%;
  text-align: center;
  line-height: 1.4;
  color: #fff;
  max-height: 40px;
  overflow: hidden;
}

footer {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 70px;
  font-size: 12px;
  padding-bottom: 12px;

  .footer-item {
    width: 30%;
    text-align: center;
    cursor: pointer;
  }
  .fotter-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    :deep() {
      svg {
        width: 100%;
        height: 100%;
        object-fit: contain;
        object-position: center;
        fill: var(--color);
      }
    }
  }
}

.arrow {
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 10px solid transparent;
}

.interaction-box {
  width: 100%;
  height: 180px;
  padding: 7px 7px 0px 7px;
  background-color: #fff;

  .slide {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    height: 100%;
    padding-bottom: 10px;

    .act-item {
      width: 25%;
      padding: 7px 0;
      text-decoration: none;
      color: #333;
      font-size: 12px;
      cursor: pointer;

      .img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
      }

      span {
        margin-top: 6px;
        line-height: 1;
      }
    }
  }
}
.box {
  position: absolute;
  bottom: 69px;
  left: 1.5%;
  width: 97%;
  border-radius: 4px;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.5);
  padding: 0;
  overflow: hidden;
}

.use-tip {
  height: 50px;
  padding: 0 21px 0 27px;
  margin: 0;
  width: 100%;
  font-size: 12px;
  color: #000;
  font-weight: normal;
  background: #ffffff;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &:before {
    content: '';
    display: block;
    width: 3px;
    height: 15px;
    position: absolute;
    left: 21px;
    top: 50%;
    transform: translateY(-50%);
    background: #599ed4;
  }

  .close {
    width: 0.24rem;
  }
}
.icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: 0.2s;
  :deep() {
    svg {
      width: 100%;
      height: 100%;
      object-fit: contain;
      object-position: center;
      fill: var(--color);
    }
  }
}
.ad-box {
  width: 100%;
  cursor: pointer;
  img.ad {
    width: 100%;
    object-position: bottom center;
    transform: translateY(2px);
  }
}
</style>
