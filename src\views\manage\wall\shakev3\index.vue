<script setup lang="ts">
import HiDesign from 'design/index.vue'
import { useDesignTemp } from '~/src/views/__/design'
import { useMobileShakev3 } from '~/src/views/mobile/wall/shakev3'
import { usePcwallShakev3 } from '~/src/views/pcwall/shakev3'

definePage({ meta: { label: '摇一摇' } })

const interactive = 'shakev3'
const designTemp = useDesignTemp()

watch(
  () => designTemp.showType,
  (v) => {
    if (v === 'mobile') {
      useMobileShakev3()
    } else {
      usePcwallShakev3()
    }
  },
  { immediate: true },
)
</script>

<template>
  <HiDesign :interactive="interactive" />
</template>

<style scoped lang="scss">
</style>
