<script setup lang="ts">
import { getDefaultMaterial, openSelectMaterial, useDataAttr } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'
import { DEFAULT_DATA, type IDesignPiclotteryIng } from './piclottery-ing'

const layer = defineModel<IDesignPiclotteryIng>('layer', { required: true })

const patternNameMap = {
  'left-to-right': '横向平推',
  'right-to-left': '横向逆推',
  'top-to-bottom': '纵向平推',
  'bottom-to-top': '纵向逆推',
  'center-out': '中心扩散',
  'diagonal': '斜线蔓延',
  'spiral': '螺旋穿梭',
}

const targetCellSizeBind = useDataAttr(layer.value.data, 'targetCellSize', DEFAULT_DATA.targetCellSize)
const animationModeBind = useDataAttr(layer.value.data, 'animationMode', DEFAULT_DATA.animationMode)
const animationSpeedBind = useDataAttr(layer.value.data, 'animationSpeed', DEFAULT_DATA.animationSpeed)
const placeholderHeadImgBind = useDataAttr(layer.value.data, 'placeholderHeadImg', DEFAULT_DATA.placeholderHeadImg)

type IType = 'placeholderHeadImg'
async function updateMaterialFn(name: IType, isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, `${name}`)
    : await openSelectMaterial('PIC')
  if (result) {
    layer.value.data[name] = result
  } else if (isReset) {
    delete layer.value.data[name]
  }
}
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>动画模式</h3>
        <el-select v-model="animationModeBind" placeholder="动画模式" class="w-140px">
          <el-option v-for="(value, key) in patternNameMap " :key="key" :label="value" :value="key">
          </el-option>
        </el-select>
      </div>
      <div class="setting-item">
        <h3>动画速度</h3>
        <el-input-number
          v-model="animationSpeedBind"
          v-input-number
          :min="0"
          controls-position="right"
        />
      </div>
      <div class="setting-item">
        <h3>图片大小</h3>
        <el-input-number
          v-model="targetCellSizeBind"
          v-input-number
          :min="10"
          controls-position="right"
        />
      </div>
      <div class="setting-item mt-5!">
        <h3>头像默认图</h3>
        <div class="flex items-end">
          <div class="bgblank relative h-50 w-64">
            <MaterialThumbnail @select="updateMaterialFn('placeholderHeadImg')" @reset="updateMaterialFn('placeholderHeadImg', true)">
              <img v-if="placeholderHeadImgBind" :src="placeholderHeadImgBind" alt="" class="h-full w-full object-cover" />
            </MaterialThumbnail>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
