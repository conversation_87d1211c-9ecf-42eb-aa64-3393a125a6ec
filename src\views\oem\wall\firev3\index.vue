<script setup lang="ts">
import HiDesign from 'design/index.vue'
import { useDesignTemp } from '~/src/views/__/design'
import { useMobileFirev3 } from '~/src/views/mobile/wall/firev3'
import { usePcwallFirev3 } from '~/src/views/pcwall/firev3'

definePage({ meta: { label: '射击游戏' } })

const interactive = 'firev3'
const designTemp = useDesignTemp()

watch(
  () => designTemp.showType,
  (v) => {
    if (v === 'mobile') {
      useMobileFirev3()
    } else {
      usePcwallFirev3()
    }
  },
  { immediate: true },
)
</script>

<template>
  <HiDesign :interactive="interactive" />
</template>

<style scoped lang="scss">
</style>
