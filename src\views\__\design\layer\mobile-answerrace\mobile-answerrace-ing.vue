<script setup lang="ts">
import type { YesOrNo } from '~/types/wall/common'
import { showToast } from 'vant'
import { computed } from 'vue'
import {
  defineCustomEmits,
  injectScale,
  useDesignState,
  useDesignTemp,
} from '../../index'
import { processStyle } from '../../utils'
import {
  DEFAULT_DATA,
  type IDesignMobileAnswerraceIng,
} from './mobile-answerrace-ing'
import { ingMockConfig } from './mock'

const layer = defineModel<IDesignMobileAnswerraceIng>('layer', { required: true })

const designTemp = useDesignTemp()
const designState = useDesignState()
const customEmits = defineCustomEmits(layer)

const timestamp = useTimestamp()

const data = computed(() => ({
  ...DEFAULT_DATA,
  ...layer.value.data,
}))

const scale = injectScale()
const rootVars = computed(() =>
  processStyle(
    {
      '--progress-question-color': data.value.progressQuestionColor,
      '--progress-bar-default-color': data.value.progressBarDefaultColor,
      '--progress-bar-filled-color': data.value.progressBarFilledColor,
      '--progress-text-color': data.value.progressTextColor,
      '--question-color': data.value.questionColor,
      '--option-selected-color': data.value.optionSelectedColor,
      '--option-selected-bg': data.value.optionSelectedBackground,
      '--option-selected-border': data.value.optionSelectedBorderColor,
      '--option-error-color': data.value.optionErrorColor,
      '--option-error-bg': data.value.optionErrorBackground,
      '--option-error-border': data.value.optionErrorBorderColor,
      '--option-unselected-color': data.value.optionUnselectedColor,
      '--option-unselected-bg': data.value.optionUnselectedBackground,
      '--option-unselected-border': data.value.optionUnselectedBorderColor,
      '--answer-status-color': data.value.answerStatusColor,
      '--annotation-color': data.value.annotationColor,
    },
    scale.value,
  ),
)

const currentSubject = computed(() => designState.getLayerData('currentSubject') || {})
const currentQuestionNumber = computed(() => {
  if (designTemp.isEdit) {
    return 1
  }
  return designState.getLayerData('currentQuestionNumber')
})
const isAutoSubmit = computed(() => designState.getLayerData('isAutoSubmit'))
const startTime = computed(() => designState.getLayerData('startTime'))
const previewEndTime = computed(() => designState.getLayerData('previewEndTime'))
const answerEndTime = computed(() => designState.getLayerData('answerEndTime'))
const subjectStatus = computed<'321' | 'preview' | 'answer' | 'result'>(() => {
  if (designTemp.isEdit && ingMockConfig.value.questionFlowStatus) {
    return ingMockConfig.value.questionFlowStatus
  }
  return designState.getLayerData('subjectStatus')
})
const isOut = computed(() => {
  if (designTemp.isEdit && ingMockConfig.value.userAnswerStatus) {
    return ingMockConfig.value.userAnswerStatus === 'out'
  }
  return designState.getLayerData('isOut')
})
const answerRecord = computed(() => {
  return designState.getLayerData('answerRecord')
})

const isRecordRight = computed(() => {
  if (designTemp.isEdit && ingMockConfig.value.userAnswerStatus) {
    return ingMockConfig.value.userAnswerStatus === 'correct'
  }
  return answerRecord.value?.answerResult === 'Y'
})

const indexToLetter = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']

const subjectContent = computed(() => {
  if (designTemp.isEdit && ingMockConfig.value.questionType) {
    const sourceObj = {
      TEXT: null,
      IMAGE: new URL('@/assets/answer/tudou.png', import.meta.url).href,
      VIDEO: 'https://res3.hixianchang.com/qn/material/0/27c777eee76f4bd6a6cdf956a823d2c6.mp4',
      AUDIO: new URL('@/assets/answer/audio.MP3', import.meta.url).href,
    }
    return {
      type: ingMockConfig.value.questionType,
      content: '发芽了的土豆能吃吗？',
      resource: sourceObj[ingMockConfig.value.questionType as keyof typeof sourceObj],
    }
  }
  return JSON.parse(currentSubject.value.subjectContent || '{}')
})

const subjectContentType = computed(() => subjectContent.value?.type || 'TEXT')

const isRadioType = computed(() => currentSubject.value.type === 'RADIO' || designTemp.isEdit)

interface OptionsJson {
  img?: string
  rightAnswer: YesOrNo
  title: string
  index?: number// 后台管理用来标记是哪个答案
}
const optionJson = computed<OptionsJson[]>(() => {
  if (designTemp.isEdit && ingMockConfig.value.optionType) {
    return [
      { title: '能吃', rightAnswer: 'N', index: 0 },
      { title: '不能吃', rightAnswer: 'N', index: 1 },
      { title: '洗干净能吃', rightAnswer: 'N', index: 2 },
      { title: '削了芽能吃', img: ingMockConfig.value.optionType === 'TEXT' ? '' : new URL('@/assets/answer/answer1.png', import.meta.url).href, rightAnswer: 'Y', index: 3 },
    ]
  }
  return JSON.parse(currentSubject.value.options || '[]').map((item: OptionsJson, index: number) => {
    item.index = index
    return item
  })
})

const remark = computed(() => {
  if (designTemp.isEdit && ingMockConfig.value.optionType) {
    return '土豆发芽后，毒素会集中在芽上，吃了会中毒。'
  }
  return currentSubject.value.remark
})

const questionHasImage = computed(() => optionJson.value.some(item => item.img))

const stageCountdown = computed(() => {
  if (subjectStatus.value === '321' && startTime.value) {
    return Math.min(3, Math.max(0, Math.ceil((startTime.value - timestamp.value) / 1000)))
  }
  return 3
})

const progressPercent = computed<number>(() => {
  if (designTemp.isEdit) {
    return 80
  }
  if (subjectStatus.value === '321') {
    return 100
  }
  const now = currentSubject.value?.state === 'PAUSE' ? Date.now() : timestamp.value
  if (subjectStatus.value === 'preview') {
    const raw = (now - startTime.value) / (previewEndTime.value - startTime.value)
    return 100 - (Math.min(1, Math.max(0, raw)) * 100)
  }
  if (subjectStatus.value === 'answer') {
    const raw = (now - previewEndTime.value) / (answerEndTime.value - previewEndTime.value)
    return 100 - (Math.min(1, Math.max(0, raw)) * 100)
  }
  return 0
})

const checkedAnswer = ref<Set<OptionsJson>>(new Set())
const rightAnswer = computed(() => optionJson.value.filter(item => item.rightAnswer === 'Y'))
const rightAnswerCount = computed(() => rightAnswer.value.length)

const isSubmit = ref(false)

watch(() => currentSubject.value?.id, () => {
  checkedAnswer.value.clear()
  isSubmit.value = false
})

watch(() => [answerRecord.value?.id, optionJson.value] as const, (v) => {
  const [id, options = []] = v
  if (!id || !options?.length) {
    checkedAnswer.value.clear()
    return
  }
  checkedAnswer.value.clear()
  JSON.parse(answerRecord.value?.option || '[]')?.forEach((item: any) => {
    const str = JSON.stringify(item)
    options?.forEach((option) => {
      if (str === JSON.stringify(option)) {
        checkedAnswer.value.add(option)
      }
    })
  })
})

function selectOption(option: OptionsJson) {
  if (subjectStatus.value !== 'answer') return
  if (isOut.value) return
  if (answerRecord.value?.id) return
  if (isSubmit.value) return

  if (isRadioType.value) {
  // 单选框：直接清空并添加当前选项
    checkedAnswer.value.clear()
    checkedAnswer.value.add(option)
  } else {
  // 复选框：切换选中状态
    if (checkedAnswer.value.has(option)) {
      checkedAnswer.value.delete(option)
    } else {
      checkedAnswer.value.add(option)
    }
  }

  if (isAutoSubmit.value) {
    if (isRadioType.value || option.rightAnswer === 'N' || checkedAnswer.value.size === rightAnswerCount.value) {
      confirm()
    }
  }
}

function confirm() {
  if (designTemp.isEdit) return
  if (checkedAnswer.value.size === 0) {
    showToast('请作答')
    return
  }
  let isRight = checkedAnswer.value.size === rightAnswerCount.value
  checkedAnswer.value.forEach((item) => {
    if (item.rightAnswer === 'N') {
      isRight = false
    }
  })
  isSubmit.value = true
  customEmits('report', {
    subjectId: currentSubject.value.id,
    option: JSON.stringify(Array.from(checkedAnswer.value)),
    useTime: Date.now() - previewEndTime.value, // 毫秒
    answerResult: isRight ? 'Y' : 'N',
  })
}
</script>

<template>
  <div class="answer-race" :style="rootVars">
    <!-- 321 倒计时 -->
    <p v-if="subjectStatus === '321'" class="stage-countdown">
      <Transition>
        <span :key="stageCountdown">{{ stageCountdown }}</span>
      </Transition>
    </p>

    <!-- 看题 / 答题阶段 -->
    <template v-else>
      <!-- 题号 -->
      <h2 v-if="currentQuestionNumber" class="answer-race__number">第 {{ currentQuestionNumber }} 题</h2>

      <!-- 进度条 -->
      <div v-if="progressPercent && progressPercent >= 0" class="answer-race__progress progress">
        <div class="progress__track">
          <div class="progress__filled" :style="{ width: `${progressPercent}%` }"></div>
        </div>
        <img :src="data.progressDecorationImage" class="progress__timer" />
      </div>

      <p class="answer-race__status">
        <template v-if="['preview', 'answer'].includes(subjectStatus)">
          {{ subjectStatus === 'answer' ? '请作答' : '请看题' }}
        </template>
      </p>

      <!-- 题干 -->
      <div class="answer-race__stem stem">
        <img v-if="subjectContentType === 'IMAGE'" :src="subjectContent.resource" alt="" class="block min-h-120px w-full rd-10px bg-#F1F3F4 py-8px" />
        <video
          v-else-if="subjectContentType === 'VIDEO'"
          :src="subjectContent.resource"
          controls
          autoplay
          disablepictureinpicture
          playsinline
          preload="auto"
          webkit-playsinline
          x-webkit-airplay
          x5-playsinline
          x5-video-player-type="h5-page"
          class="w-full rd-10px bg-#F1F3F4"
        ></video>
        <audio v-else-if="subjectContentType === 'AUDIO'" :src="subjectContent.resource" controls class="w-full rd-20px bg-#F1F3F4 py-8px"></audio>
        <p class="stem__text" :style="{ fontSize: `${data.questionFontSize}px` }">
          {{ isRadioType ? '(单选题)' : '(多选题)' }}
          {{ subjectContent.content }}
        </p>
      </div>

      <!-- 选项列表 -->
      <ul
        class="answer-race__options"
        :class="[
          questionHasImage ? 'answer-race__options--double' : '',
          subjectStatus === 'preview' ? 'pointer-events-none' : '',
        ]"
      >
        <li v-for="(opt, idx) in optionJson" :key="idx" class="option" @click="selectOption(opt)">
          <img v-if="opt.img" :src="opt.img" alt="" class="option__image" />
          <p
            class="option__text"
            :class="{
              // 选择
              'option__text--checked': subjectStatus === 'answer' && checkedAnswer.has(opt),
              // 正确
              'option__text--correct': (checkedAnswer.has(opt) || designTemp.isEdit) && subjectStatus === 'result' && opt.rightAnswer === 'Y',
              // 错误
              'option__text--error': (checkedAnswer.has(opt) || (designTemp.isEdit && opt.index === 0)) && subjectStatus === 'result' && !isRecordRight && !isOut && opt.rightAnswer === 'N',
            }"
          >
            {{ indexToLetter[idx] }}. {{ opt.title }}
          </p>
        </li>
      </ul>

      <!-- 被淘汰提示 -->
      <p v-if="isOut" class="answer-race__status font-bold">
        {{ data.eliminatedText }}
      </p>

      <template v-else>
        <!-- 正确答案 & 注释：仅查看答案阶段展示 -->
        <div v-if="subjectStatus === 'result'" class="answer-race__result result">
          <p class="result__text"> {{ isRecordRight ? data.answerCorrectText : '回答错误' }}</p>
          <p class="result__text"> 正确答案：{{ rightAnswer.map(item => indexToLetter[item.index!]).join(',') }}</p>
          <p v-if="remark" class="result__annotation">注释：{{ remark }}</p>
        </div>

        <!-- 提交按钮：仅答题阶段 & 非自动提交 -->
        <button v-if="subjectStatus === 'answer' && !isAutoSubmit && !isOut && !answerRecord?.id && !isSubmit" class="result__submit-btn" @click="confirm">
          <img :src="data.submitButtonImage" alt="" class="h-full w-full object-contain">
        </button>
      </template>
    </template>
  </div>
</template>

<style scoped lang="scss">
.v-enter-active,
.v-leave-active {
  transition: opacity 0.8s ease;
  position: absolute;
}
.v-enter-from,
.v-leave-to {
  opacity: 0;
}

.answer-race {
  height: 100%;
  overflow: auto;
  padding: 16px;
  color: #fff;

  .stage-countdown {
    font-size: 120px;
    font-weight: 700;
    text-align: center;
    margin: 20vh auto 0;
    width: 220px;
    height: 220px;
    border-radius: 50%;
    padding-left: 8px;
    padding-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fec500;
    font-weight: bold;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    background-image: url('./assets/downbg.png');
    text-shadow: -5px 0 #c5e0f9;
  }

  &__number {
    color: var(--progress-question-color);
    letter-spacing: 2px;
    text-align: center;
  }

  &__progress {
    margin-top: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 80%;
    margin-inline: auto;
  }

  &__status {
    margin: 20px 0;
    text-align: center;
    font-size: 16px;
    color: var(--answer-status-color);
  }

  &__stem {
    width: 90%;
    margin-inline: auto;
    text-align: center;

    .stem__text {
      margin-top: 20px;
      font-weight: 700;
      color: var(--question-color);
    }
  }

  &__options {
    margin-top: 20px;
    width: 100%;
    padding-inline: 12px;
    display: grid;
    gap: 14px;
    grid-template-columns: 1fr;

    &--double {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  .option {
    &__image {
      width: 100%;
      height: 70px;
      border: 1px solid #fff;
      border-radius: 4px;
      object-fit: cover;
      margin-bottom: 6px;
    }

    &__text {
      border: 1px solid var(--option-unselected-border);
      background: var(--option-unselected-bg);
      color: var(--option-unselected-color);
      padding: 12px 16px;
      border-radius: 6px;
      font-size: 16px;

      &--correct,
      &--checked {
        border-color: var(--option-selected-border);
        background: var(--option-selected-bg);
        color: var(--option-selected-color);
      }
      &--error {
        border-color: var(--option-error-border);
        background: var(--option-error-bg);
        color: var(--option-error-color);
      }
    }
  }

  &__result {
    margin: 20px 0;
    width: 100%;
    text-align: center;

    .result__text {
      margin-bottom: 8px;
      font-size: 18px;
      font-weight: 700;
      color: var(--answer-status-color);
    }

    .result__annotation {
      margin-bottom: 8px;
      padding-inline: 16px;
      font-size: 16px;
      line-height: 1.5;
      color: var(--annotation-color);
    }
  }

  .result__submit-btn {
    display: block;
    margin: 30px auto;
    width: 70%;
    max-height: 120px;
  }
}

/* progress bar */
.progress {
  &__track {
    flex: 1;
    height: 14px;
    background: var(--progress-bar-default-color);
    border-radius: 20px;
    overflow: hidden;
    margin-inline: 8px;
  }

  &__filled {
    height: 100%;
    background: var(--progress-bar-filled-color);
    border-radius: 20px;
    transition: all 0.2s;
  }

  &__timer {
    width: 20px;
    height: 20px;
    object-fit: contain;
  }
}
</style>
