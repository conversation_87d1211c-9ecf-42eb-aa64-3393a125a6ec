<script setup lang="ts">
import type { IDesignShakeIng1 } from './shake-ing1'
import { getDefaultMaterial, openSelectMaterial } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'

const layer = defineModel<IDesignShakeIng1>('layer', { required: true })
type IType = 'flag'
async function updateMaterialFn(name: IType, index: number, isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, `${name}${index !== undefined ? `.${index}` : ''}`)
    : await openSelectMaterial('PIC')
  if (result || (isReset && index === undefined)) {
    const arr = layer.value[name] as string[]
    arr[index] = result
  }
}

function remove(name: IType, index: number) {
  const arr = layer.value[name] as string[]
  arr.splice(index, 1)
}
function add(name: IType, index: number, defaultValue: string) {
  const arr = layer.value[name] as string[]
  arr.splice(index + 1, 0, defaultValue)
}

function removeColumnColor(index: number) {
  const arr = layer.value.columnColors as string[]
  arr.splice(index, 1)
}
function aadColumnColor(index: number, defaultValue: string) {
  const arr = layer.value.columnColors as string[]
  arr.splice(index + 1, 0, defaultValue)
}
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item flex-items-start!">
        <h3>名次标识</h3>
        <div>
          <div
            v-for="(item, index) in layer.flag"
            :key="index"
            class="relative mb-10 h-60 w-120 flex"
          >
            <MaterialThumbnail @select="updateMaterialFn('flag', index)" @reset="updateMaterialFn('flag', index, true)">
              <img :src="item" class="bgblank w-90 object-contain">
            </MaterialThumbnail>
            <div class="ml-6 w-20 pt-10">
              <icon-ph:minus-bold v-if="layer.flag.length > 1" @click.stop="remove('flag', index)" />
              <icon-ph:plus-bold @click.stop="add('flag', index, item)" />
            </div>
          </div>
        </div>
      </div>
      <div class="setting-item flex-items-start!">
        <h3>柱子颜色</h3>
        <div>
          <div
            v-for="(item, index) in layer.columnColors"
            :key="index"
            class="relative h-40 w-140 flex"
          >
            <hi-color v-model="layer.columnColors[index]" type="both" />
            <div class="ml-6 h-35 w-20">
              <icon-ph:minus-bold v-if="layer.columnColors.length > 1" @click.stop="removeColumnColor(index)" />
              <icon-ph:plus-bold @click.stop="aadColumnColor(index, item)" />
            </div>
          </div>
        </div>
      </div>
      <div class="setting-item flex-items-start!">
        <h3>柱子背景色</h3>
        <div>
          <hi-color v-model="layer.columnBgcolor" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
</style>
