<script setup lang="ts">
import { defineCustomEmits, injectScale, useDesignState, useDesignTemp } from '../../index'
import { processStyle } from '../../utils'
import { DEFAULT_DATA, type IDesignWinningList4 } from './winning-list4'

const layer = defineModel<IDesignWinningList4>('layer', { required: true })

const designTemp = useDesignTemp()
const designState = useDesignState()
const customEmits = defineCustomEmits(layer)

const scale = injectScale()

const data = computed(() => {
  return { ...DEFAULT_DATA, ...layer.value.data }
})

// 参与人奖池
const winningList = computed(() => {
  return designState.getLayerData('winningList2') || []
})

const importformShowOption = computed(() => {
  return designState.getLayerData('importformShowOption') || []
})

const imageStyle = computed(() => {
  return processStyle({
    width: `${data.value.imageWidth}px`,
    height: `${data.value.imageHeight}px`,
    borderRadius: `${data.value.imageRadius}px`,
    border: `${data.value.borderWidth}px solid ${data.value.borderColor}`,
    objectFit: data.value.imageMode,
  }, scale.value)
})

const itemGap = computed(() => processStyle(`${data.value.itemGap}px`, scale.value))

const innerGap = computed(() => processStyle(`${data.value.innerGap}px`, scale.value))

const contentStyle = computed(() => {
  return data.value?.contentStyle?.map((item) => {
    return processStyle({
      flex: 1,
      fontSize: `${item.fontSize}px`,
      color: item.fontColor || '#000',
      fontWeight: item.fonBold ? 'bold' : 'normal',
    }, scale.value)
  })
})

function onDeleteItem(item: any) {
  ElMessageBox.confirm('确定要取消当前用户的中奖资格吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '再想想',
    type: 'warning',
  }).then(() => {
    if (designTemp.isEdit) {
      ElMessageBox.alert('当前中奖名单是模拟数据，无法取消中奖，请创建活动后在大屏幕中操作', '需要在大屏幕中操作')
    } else {
      customEmits('winning-delete', item.id)
    }
  }).catch(() => {
    // do nothing
  })
}
</script>

<template>
  <div class="piclottery-ing1-box h-full w-full">
    <div
      class="h-full w-full flex flex-wrap items-center justify-around overflow-y-auto text-white"
      :style="{
        gap: itemGap,
      }"
    >
      <div
        v-for="item in winningList"
        :key="item.id"
        class="item flex flex-col items-center justify-center"
        :style="{
          gap: innerGap,
        }"
      >
        <div class="relative overflow-hidden">
          <img
            :src="item?.avatar || data.placeholderHeadImg"
            :style="imageStyle"
          >
          <div
            class="delete absolute inset-0 z-10 hidden cursor-pointer items-center justify-center bg-black bg-opacity-80 text-white"
            :style="imageStyle"
            @click="onDeleteItem(item)"
          >
            <icon-ph:trash-bold />
          </div>
        </div>
        <p
          v-if="importformShowOption?.[0]"
          :style="contentStyle?.[0]"
          class="line-clamp-1 line-height-tight"
          :class="{
            invisible: !item.nameD0,
          }"
        >
          {{ item.nameD0 || '-' }}
        </p>
        <p
          v-if="importformShowOption?.[1]"
          :style="contentStyle?.[1]"
          class="line-clamp-1 line-height-tight"
          :class="{
            invisible: !item.nameD1,
          }"
        >
          {{ item.nameD1 || '-' }}
        </p>
        <p
          v-if="importformShowOption?.[2]"
          :style="contentStyle?.[2]"
          class="line-clamp-1 line-height-tight"
          :class="{
            invisible: !item.nameD2,
          }"
        >
          {{ item.nameD2 || '-' }}
        </p>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.item {
  &:hover {
    .delete {
      display: flex;
    }
  }
}
</style>
