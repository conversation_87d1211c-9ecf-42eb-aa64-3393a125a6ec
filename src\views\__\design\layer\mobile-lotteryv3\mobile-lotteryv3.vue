<script setup lang="ts">
import { showDialog } from 'vant'
import { defineCustomEmits, useDataAttr, useDesignState } from '../../index'
import { DEFAULT_DATA, type IDesignMobileLotteryv3 } from './mobile-lotteryv3'
import 'vant/lib/dialog/style'

const layer = defineModel<IDesignMobileLotteryv3>('layer', { required: true })

const designState = useDesignState()
const customEmits = defineCustomEmits(layer)

const confirmBtnImgBind = useDataAttr(layer.value.data, 'confirmBtnImg', DEFAULT_DATA.confirmBtnImg)

// 如果配置了 抽奖资格获取，需要添加该组件进行报名

const showRegeditComp = computed(() => designState.getLayerData('showRegeditComp'))
const showCodeInput = computed(() => designState.getLayerData('showCodeInput'))

const code = ref('')

function onConfirm() {
  if (showCodeInput.value && !code.value) {
    showDialog({ title: '提示', message: '请输入口令' })
    return
  }
  customEmits('confirm-regedit', { code: code.value })
}
</script>

<template>
  <div v-if="showRegeditComp" class="mobile-lotteryv3-box size-full flex flex-col items-center justify-around px-10 py-20">
    <input v-if="showCodeInput" v-model.trim="code" placeholder="请输入口令" class="h-60 w-full rd-6 border-none px-10px text-center" />
    <img
      :src="confirmBtnImgBind"
      class="my-10 w-90% cursor-pointer"
      :style="showCodeInput && !code ? {
        filter: 'grayscale(100%)',
      } : {}"
      @click="onConfirm"
    />
  </div>
</template>

<style scoped lang="scss"></style>
