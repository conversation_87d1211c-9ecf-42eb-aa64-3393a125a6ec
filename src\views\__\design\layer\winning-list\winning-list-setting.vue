<script setup lang="ts">
import { getDefaultMaterial, injectScale, openSelectMaterial, useDataAttr } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'
import { processStyle } from '../../utils'
import { DEFAULT_DATA, type IDesignWinningList } from './winning-list'

const layer = defineModel<IDesignWinningList>('layer', { required: true })

const avatarSizeBind = useDataAttr(layer.value.data, 'avatarSize', DEFAULT_DATA.avatarSize)
const avatarGapBind = useDataAttr(layer.value.data, 'avatarGap', DEFAULT_DATA.avatarGap)
const defaultAvatarBind = useDataAttr(layer.value.data, 'defaultAvatar', DEFAULT_DATA.defaultAvatar)
const avatarDecorationBind = useDataAttr(layer.value.data, 'avatarDecoration', DEFAULT_DATA.avatarDecoration)
const avatarDecorationRotationBind = useDataAttr(layer.value.data, 'avatarDecorationRotation', DEFAULT_DATA.avatarDecorationRotation)
const avatarDecorationRotationSpeedBind = useDataAttr(layer.value.data, 'avatarDecorationRotationSpeed', DEFAULT_DATA.avatarDecorationRotationSpeed)

const prizeColorBind = useDataAttr(layer.value.data, 'prizeColor', DEFAULT_DATA.prizeColor)
const nameColorBind = useDataAttr(layer.value.data, 'nameColor', DEFAULT_DATA.nameColor)

async function updateMaterialFn(name: 'defaultAvatar' | 'avatarDecoration', isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, `data.${name}`)
    : await openSelectMaterial('PIC')
  if (result) {
    layer.value.data[name] = result
  } else if (isReset) {
    delete layer.value.data[name]
  }
}

const scale = injectScale()

const scaleStyle = computed(() => {
  return processStyle({
    gap: `${avatarGapBind.value}px`,
    width: layer.value.style.width,
    height: layer.value.style.height,
    fontSize: `${avatarSizeBind.value}px`,
  }, scale)
})

function getVisibleCount({
  containerWidth,
  containerHeight,
  itemWidth,
  itemHeight,
  gap,
}: {
  containerWidth: number
  containerHeight: number
  itemWidth: number
  itemHeight: number
  gap: number
}) {
  let count = 0
  let top = 0
  while (top < containerHeight) {
    let left = 0
    while (left < containerWidth - itemWidth) {
      const right = left + itemWidth
      const bottom = top + itemHeight

      const isVisible
        = right > 0
          && left < containerWidth
          && bottom > 0
          && top < containerHeight

      if (isVisible) count++

      left += itemWidth + gap
    }
    top += itemHeight + gap
  }

  return count
}

const maybeMaxCount = computed(() => {
  const containerWidth = Number.parseFloat(scaleStyle.value.width)
  const containerHeight = Number.parseFloat(scaleStyle.value.height)
  const itemWidth = Number.parseFloat(scaleStyle.value.fontSize)
  const itemHeight = Number.parseFloat(scaleStyle.value.fontSize)
  const gap = Number.parseFloat(scaleStyle.value.gap)

  return getVisibleCount({
    containerWidth,
    containerHeight,
    itemWidth,
    itemHeight,
    gap,
  })
})
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <span>尺寸</span>

        <el-input-number v-model="avatarSizeBind" v-input-number controls-position="right" :min="10" :step="1" class="ml-20 w-80px!" />
      </div>
      <div class="setting-item">
        <span>间隔</span>
        <el-input-number v-model="avatarGapBind" v-input-number controls-position="right" :step="1" :min="0" class="ml-20 w-80px!" />
      </div>
      <div class="my-16px text-right text-12px text-#666">
        预估最多可展示头像数量 {{ maybeMaxCount }}
      </div>
      <div class="setting-item">
        <h3>默认头像</h3>
        <div class="bgblank relative h-60 w-160">
          <MaterialThumbnail @select="updateMaterialFn('defaultAvatar')" @reset="updateMaterialFn('defaultAvatar', true)">
            <img :src="defaultAvatarBind">
          </MaterialThumbnail>
        </div>
      </div>
      <div class="setting-item">
        <h3>头像背景</h3>
        <div class="bgblank relative h-60 w-160">
          <MaterialThumbnail @select="updateMaterialFn('avatarDecoration')" @reset="updateMaterialFn('avatarDecoration', true)">
            <img :src="avatarDecorationBind">
          </MaterialThumbnail>
        </div>
      </div>
      <div class="setting-item">
        <span>头像背景旋转</span>
        <el-switch v-model="avatarDecorationRotationBind" />
      </div>
      <div v-if="avatarDecorationRotationBind" class="setting-item">
        <div class="flex items-center">
          <span>背景旋转速度</span>
          <el-tooltip content="每隔几秒旋转一圈，数值越小速度越快" placement="top">
            <icon-ph-question />
          </el-tooltip>
        </div>
        <div class="flex items-center">
          <el-input-number v-model="avatarDecorationRotationSpeedBind" controls-position="right" :min="0.1" :max="10" class="w-90px!" />秒
        </div>
      </div>
      <div class="setting-item">
        <span>昵称颜色</span>
        <hi-color v-model="nameColorBind" />
      </div>
      <div class="setting-item">
        <span>奖品文字颜色</span>
        <hi-color v-model="prizeColorBind" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
