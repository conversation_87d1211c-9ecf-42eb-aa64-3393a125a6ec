<script setup lang="ts">
import { useImageInfo } from '~/src/hooks/useImageInfo'
import { injectScale, useDesignState, useDesignTemp } from '../..'
import { processStyle } from '../../utils'
import { defaultData, type IDesignSeglotteryIng } from './seglottery-ing'

const layer = defineModel<IDesignSeglotteryIng>('layer', { required: true })
const scale = injectScale()
const designState = useDesignState()
const designTemp = useDesignTemp()
const isDesignEdit = designTemp.isEdit
// 抽取人数
const onceCount = computed<number>(() => {
  return designState.getLayerData('#每次抽取人数#') || 1
})
const playEndDaley = computed(() => {
  return designState.getLayerData('playEndDaley')
})

const itemCount = computed(() => layer.value.itemCount ?? defaultData.itemCount)
// const colNum = computed(() => layer.value.colNum ?? defaultData.colNum)
const roolBgcolor = computed(() => layer.value.roolBgcolor ?? defaultData.roolBgcolor)
const roolBorderColor = computed(() => layer.value.roolBorderColor ?? defaultData.roolBorderColor)
const roolWidth = computed(() => layer.value.roolWidth ?? defaultData.roolWidth)
const roolHeight = computed(() => layer.value.roolHeight ?? defaultData.roolHeight)
const roolLeft = computed(() => layer.value.roolLeft ?? defaultData.roolLeft)
const roolTop = computed(() => layer.value.roolTop ?? defaultData.roolTop)
const itemBgImg = computed(() => layer.value.itemBgImg ?? defaultData.itemBgImg)
const roolFontcolor = computed(() => layer.value.roolFontcolor ?? defaultData.roolFontcolor)
const roolFontSize = computed(() => layer.value.roolFontSize ?? defaultData.roolFontSize)
const noStartImg = computed(() => layer.value.noStartImg ?? defaultData.noStartImg)
const showLabel = computed(() => layer.value.showLabel ?? defaultData.showLabel)
const animationMode = computed(() => layer.value.animationMode ?? defaultData.animationMode)
const bgImgInfo = useImageInfo(itemBgImg.value)

const wrapRef = ref<HTMLElement | null>(null)
const cellBoxSize = useElementSize(wrapRef, undefined, { box: 'border-box' })
const realCount = computed(() => {
  if (isDesignEdit) {
    // 设计编辑模式下，使用预设的最大渲染数量
    return itemCount.value
  }
  // 实际抽取人数
  return Math.min(onceCount.value, itemCount.value)
})

const itemStyle = computed(() => {
  let itemWidth = 0
  const setWidth = getWidth(itemCount.value) // 预设宽度
  // 如果实际使用的时候，算出来的宽度大于预设宽度的两倍，则使用预设宽度的两倍，否则使用实际计算出来的宽度
  // if (!isDesignEdit) {
  //   const realWidht = getWidth(realCount.value) // 实际抽取数量计算得宽度
  //   if (realWidht > setWidth * 1.5) {
  //     itemWidth = setWidth * 1.5
  //   } else {
  //     itemWidth = realWidht
  //   }
  // } else {
  // 设计编辑模式下，直接使用预设宽度
  itemWidth = setWidth
  // }
  const itemHeight = itemWidth / Number(bgImgInfo.ratio.value)
  return processStyle({
    width: `${Math.floor(itemWidth)}px`,
    height: `${Math.floor(itemHeight)}px`,
  })
})

function getWidth(count: number) {
  let itemWidth = 0
  for (let i = 0; i < count + 1; i++) {
    const x = cellBoxSize.width.value / i
    const y = (cellBoxSize.height.value * Number(bgImgInfo.ratio.value)) / Math.ceil(count / i)
    const w = Math.min(Math.floor(x), Math.floor(y))
    if (w > itemWidth) {
      itemWidth = w
    }
  }
  return Math.floor(itemWidth)
}

const segStyle = computed(() => {
  return processStyle({
    '--rool-border-color': roolBorderColor.value,
    '--rool-bgcolor': roolBgcolor.value,
    '--rool-fontcolor': roolFontcolor.value,
    '--rool-font-size': `${roolFontSize.value * scale.value}px`,
    '--rool-wrap-height': `${roolHeight.value}%`,
    '--rool-wrap-width': `${roolWidth.value}%`,
    '--rool-wrap-top': `${roolTop.value}%`,
    '--rool-wrap-left': `${roolLeft.value}%`,
    '--transition-duration': `${50}ms`,
    '--rool-gap': `${(layer.value.roolGap ?? defaultData.roolGap) * scale.value}px`,
    '--rool-label-color': layer.value.roolLabelcolor ?? defaultData.roolLabelcolor,
    '--rool-label-size': `${(layer.value.roolLabelSize ?? defaultData.roolLabelSize) * scale.value}px`,
  })
})

type RollList = Array<Array<string | number>>
interface Record {
  namesList: Array<string | number>
  id: string
  [key: string]: any
}
// 参与人奖池
interface SegRollItem {
  length: number
  [key: string]: any
}

const rollList = computed<RollList>(() => {
  const list: RollList = []
  const segRollList: SegRollItem[] = designState.getLayerData('segRollList') || []
  segRollList.forEach((item: SegRollItem) => {
    if (item && item.length) {
      const arr = Array.isArray(item) ? item : Object.values(item)
      list.push([...arr, ...arr])
    }
  })
  return list
})

const labelList = computed<RollList>(() => {
  return designState.getLayerData('roolLabelList') || []
})
const playStatus = computed<Array<'noplay' | 'playing' | 'playover'>>(() => {
  return designState.getLayerData('playStatus') || []
})
const recordList = computed<Record[]>(() => {
  return designState.getLayerData('onceRecordList') || []
})

const palyIndexList = ref<number[]>([0, 0, 0])

const timeIntervalId = ref<NodeJS.Timeout | null>(null)
const rollTimeMs = computed(() => {
  return animationMode.value === 'slotMachine' ? 120 : 40
})

function startPlay() {
  timeIntervalId.value && clearInterval(timeIntervalId.value)
  timeIntervalId.value = setInterval(() => {
    playStatus.value?.forEach((status, index) => {
      if (status === 'playing') {
        if (palyIndexList.value[index] >= rollList.value[index].length - 1) {
          palyIndexList.value[index] = 0
        } else {
          palyIndexList.value[index]++
        }
      }
    })
  }, rollTimeMs.value)
}

const resultItem = computed(() => {
  return (i: number) => {
    const record = recordList.value[i]
    if (record) {
      return record.namesList || []
    } else {
      return []
    }
  }
})
const symbolStyle = computed(() => {
  const roolLabelPostion = layer.value.roolLabelPostion ?? defaultData.roolLabelPostion
  return processStyle({
    flexDirection: roolLabelPostion === 'right' ? 'row' : 'column',
  })
})

const symbol2Style = computed(() => {
  const roolLabelPostion = layer.value.roolLabelPostion ?? defaultData.roolLabelPostion
  return (index: number) => {
    return processStyle({
      'flexDirection': roolLabelPostion === 'right' ? 'row' : 'column',
      'transition-delay': `${playEndDaley.value[index]}ms`,
    })
  }
})
watch(() => [playStatus, animationMode.value], () => {
  if (playStatus.value?.length) {
    startPlay()
  }
}, { immediate: true, deep: true })

onMounted(() => {
})
onBeforeUnmount(() => {
  timeIntervalId.value && clearInterval(timeIntervalId.value)
})

const transitionName = computed(() => {
  const _animationMode = animationMode.value
  return (type: string) => {
    if (_animationMode !== 'slotMachine') {
      return type === 'playover' ? 'fade-over' : 'fade-up'
    }
    return type === 'playover' ? 'flip-over' : 'flip-up'
  }
})
</script>

<template>
  <div ref="wrapRef" class="seg-lottery-ing" :style="segStyle">
    <div v-for="i in realCount" :key="`item-${i}`" class="item-container" :style="itemStyle">
      <div class="itembg-wrap" :style="{ backgroundImage: `url(${itemBgImg})` }">
        <div class="reel-wrap">
          <div v-for="(item, index) in rollList" :key="`reel-${index}`" class="reel">
            <transition :name="transitionName('play')" mode="out-in">
              <div v-if="playStatus[index] !== 'playover'" :key="String(item[palyIndexList[index]]) + String(palyIndexList[index])" class="symbol-strip-container" :style="symbolStyle">
                <div v-if="playStatus[index] !== 'noplay'" class="symbol-value">{{ item[palyIndexList[index]] }}</div>
                <div v-if="showLabel && playStatus[index] !== 'noplay'" class="symbol-label">{{ labelList[index] }}</div>
              </div>
            </transition>
            <transition :name="transitionName('playover')" mode="out-in">
              <div v-if="playStatus[index] === 'playover'" :key="item[palyIndexList[index]]" class="symbol-strip-container" :style="symbol2Style(index)">
                <div class="symbol-value">{{ resultItem(i - 1)[index] }}</div>
                <div v-if="showLabel" class="symbol-label">{{ labelList[index] }}</div>
              </div>
            </transition>
            <img v-if="playStatus[index] === 'noplay'" :src="noStartImg" class="no-start-img" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.seg-lottery-ing {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-wrap: wrap;
  .item-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .itembg-wrap {
    width: 95%;
    height: 95%;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    overflow: hidden;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }
  .reel-wrap {
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    width: var(--rool-wrap-width, 80%);
    height: var(--rool-wrap-height, 80%);
    left: var(--rool-wrap-left, 10%);
    top: var(--rool-wrap-top, 10%);
    gap: var(--rool-gap, 10px);
  }
  .reel {
    //width: 30%;
    flex: 1;
    height: 100%;
    background-color: var(--rool-bgcolor, #f0f0f0);
    border: 5px solid var(--rool-border-color, #ccc);
    box-shadow: inset 0 0 10px var(--rool-border-color);
    filter: drop-shadow(0 0 8px var(--rool-border-color)) drop-shadow(0 0 15px var(--rool-border-color));
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 5%;
    overflow: hidden;
    position: relative;
    .no-start-img {
      position: absolute;
      width: 100%;
      height: 100%;
      object-fit: contain;
    }

    .symbol-strip-container {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: row;
      width: 100%;
      height: 100%;
      position: absolute;

      .symbol-value {
        font-size: var(--rool-font-size, 16px);
        color: var(--rool-fontcolor, #000);
        margin-right: 5px;
      }
      .symbol-label {
        font-size: var(--rool-label-size, 14px);
        color: var(--rool-label-color, #333);
      }
    }
  }
}

// Flip Up Animation
.flip-up-enter-active,
.flip-up-leave-active {
  transition: all var(--transition-duration);
}

.flip-up-enter-from {
  transform: translateY(100%);
  opacity: 0;
  transform-origin: top center;
}

.flip-up-leave-to {
  transform: translateY(-80%);
  opacity: 0;
  transform-origin: bottom center;
}

.flip-over-enter-active {
  transition: transform 600ms ease-in-out;
}
.flip-over-enter-from {
  transform: translateY(100%);
  transform-origin: top center;
}
.flip-over-leave-active {
  transition-delay: 0ms !important;
}

.fade-over-enter-active {
  transition: all 600ms ease-in-out;
}
.fade-over-enter-from {
  opacity: 0;
}
.fade-over-leave-active {
  transition-delay: 0ms !important;
}
</style>
