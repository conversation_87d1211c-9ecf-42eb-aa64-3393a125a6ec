<script setup lang="ts">
import type { CSSProperties } from 'vue'

const swiperRef = ref<HTMLElement>()
const swiperWrapRef = ref<HTMLElement>()
const slots = useSlots()

const swiperSize = useElementSize(swiperRef)
const swiperWidth = computed(() => Math.round(swiperSize.width.value))

const transitionTime = 360
const currentIndex = ref(0)
const offsetX = ref(0)

const count = computed(() => {
  return (slots.default?.()[0].children?.length || 0) as number
})

function updatePosition() {
  if (!swiperWrapRef.value) return
  const left = swiperWidth.value * -1 * currentIndex.value + offsetX.value
  swiperWrapRef.value.style.transform = `translate3d(${left}px, 0, 0)`
  // swiperWrapRef.value.style.marginLeft = `${left}px`
}

function startFn(e: TouchEvent | MouseEvent) {
  e.preventDefault()

  if (swiperWrapRef.value) {
    swiperWrapRef.value.style.transition = ''
  }

  const startX = e instanceof MouseEvent ? e.clientX : e.touches[0].clientX
  const moveFn = (e: TouchEvent | MouseEvent) => {
    if (!swiperWrapRef.value) return
    const moveX = e instanceof TouchEvent ? e.touches[0].clientX : e.clientX
    offsetX.value = Math.round(moveX - startX) * 0.8
    updatePosition()
  }
  const endFn = async (e: TouchEvent | MouseEvent) => {
    if (!swiperWrapRef.value) return

    swiperWrapRef.value.style.transition = `${transitionTime}ms ease-out`
    await nextTick()

    if (Math.abs(offsetX.value) > swiperWidth.value * 0.2) {
      const nextIndex = currentIndex.value + (offsetX.value > 0 ? -1 : 1)
      if (nextIndex >= 0 && nextIndex < count.value) {
        currentIndex.value = nextIndex
      }
    }

    if (Math.abs(offsetX.value) < 6) {
      const target = e.target
      const clickEvent = new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        view: window,
      })
      target?.dispatchEvent(clickEvent)
    }

    offsetX.value = 0
    updatePosition()

    if (e instanceof TouchEvent) {
      document.removeEventListener('touchmove', moveFn)
      document.removeEventListener('touchend', endFn)
    } else {
      document.removeEventListener('mousemove', moveFn)
      document.removeEventListener('mouseup', endFn)
    }
  }
  if (e instanceof TouchEvent) {
    document.addEventListener('touchmove', moveFn)
    document.addEventListener('touchend', endFn)
  } else {
    document.addEventListener('mousemove', moveFn)
    document.addEventListener('mouseup', endFn)
  }
}

const swiperWrapStyle = computed(() => {
  const style: CSSProperties = {
    width: `${swiperWidth.value * count.value}px`,
    height: '100%',
  }
  return style
})
</script>

<template>
  <div ref="swiperRef" class="swiper-box" @touchstart="startFn" @mousedown="startFn">
    <div ref="swiperWrapRef" class="swiper-wrap" :style="swiperWrapStyle">
      <slot></slot>
    </div>
    <div class="dot-box">
      <span v-for="i in count" :key="i" :class="{ cur: currentIndex === (i - 1) }"></span>
    </div>
  </div>
</template>

<style scoped lang="scss">
.swiper-box {
  position: relative;
  width: 100%;
  height: 100%;
  user-select: none;

  .swiper-wrap {
    position: relative;
    display: flex;
    flex-wrap: nowrap;

    > * {
      position: relative;
      flex: 1;
      height: 100%;
    }
  }

  .dot-box {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;

    span {
      width: 7px;
      height: 7px;
      border-radius: 50%;
      background-color: rgb(204, 204, 204);
      margin: 0 5px;
      cursor: pointer;
      transition: 0.3s;

      &.cur {
        background-color: rgb(153, 153, 153);
      }
    }
  }
}
</style>
