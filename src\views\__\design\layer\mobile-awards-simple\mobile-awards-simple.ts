import type { IDesignLayer, IDesignSetup } from '../../types'
import { merge } from 'lodash-es'
import { BisTypes, useDesignState } from '../../'
import { layerUuid } from '../../utils'
import CompSetting from './mobile-awards-simple-setting.vue'
import Comp from './mobile-awards-simple.vue'

// 类型
export const type = 'mobile-awards-simple'

// NOTE: 基于 mobile-awards 组件修改，区别在 这个组件不是弹窗显示

// 数据类型约束
export interface IDesignMobileAwardsSimple extends IDesignLayer {
  type: typeof type
  data: any
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    type,
    bisType: BisTypes.result,
    showType: ['mobile'],
    name: '移动端中奖结果',
    status: ['finish'],
    thumbnail: new URL('./mobile-awards-simple.png', import.meta.url).href,
    // 渲染前置条件
    mountedCondition: computed(() => {
      const designState = useDesignState()
      const record = designState.getLayerData('record')
      const awardCount = designState.getLayerData('awardCount')
      const awards = designState.getLayerData('awards')
      return !!record && (awardCount !== undefined || !!awards)
    }),
    Comp,
    CompSetting,
    defaultData(options): IDesignMobileAwardsSimple {
      return merge({
        uuid: layerUuid(),
        name: '移动端中奖结果',
        type,
        style: {},
        data: {},
      }, options as IDesignMobileAwardsSimple)
    },
  })
}
