<script setup lang="ts">
import { getDefaultMaterial, openSelectMaterial, useDataAttr } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'
import { defaultData, type IDesignMobileGameRule } from './mobile-game-rule'

const layer = defineModel<IDesignMobileGameRule>('layer', { required: true })

const isImageVersion = computed(() => {
  return (layer.value.version || 1) >= 2
})

// v2
const textBind = useDataAttr(layer.value.data, 'text', defaultData.text)
const textColorBind = useDataAttr(layer.value.data, 'textColor', defaultData.textColor || '')
const closeImgBind = useDataAttr(layer.value.data, 'closeImg', defaultData.closeImg)
const titleImgBind = useDataAttr(layer.value.data, 'titleImg', defaultData.titleImg)
const bgImgBind = useDataAttr(layer.value.data, 'bgImg', defaultData.bgImg)

async function updateMaterialFn(dataKey: keyof typeof layer.value.data, isReset: boolean = false) {
  if (!dataKey) {
    return
  }
  const result = isReset
    ? await getDefaultMaterial(layer.value, `data.${dataKey}`)
    : await openSelectMaterial('PIC')
  if (result) {
    layer.value.data[dataKey] = result
  } else if (isReset) {
    delete layer.value.data[dataKey]
  }
}

// v1
const closeColorBind = useDataAttr(layer.value.data, 'closeColor', '')
const titleColorBind = useDataAttr(layer.value.data, 'titleColor', '')
const titleBgColorBind = useDataAttr(layer.value.data, 'titleBgColor', '')
const textBgColorBind = useDataAttr(layer.value.data, 'textBgColor', '')
const textBorderColorBind = useDataAttr(layer.value.data, 'textBorderColor', '')
</script>

<template>
  <div v-if="layer.data" class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <ElInput v-model="textBind" size="default" type="textarea" :rows="4" placeholder="请填写游戏规则内容" />
      </div>

      <template v-if="isImageVersion">
        <div class="setting-item">
          <div class="setting-item-label">标题图标</div>
          <div class="setting-item-content">
            <div class="thumbnail-box bgblank">
              <MaterialThumbnail @select="updateMaterialFn('titleImg')" @reset="updateMaterialFn('titleImg', true)">
                <img v-if="titleImgBind" :src="titleImgBind" />
              </MaterialThumbnail>
            </div>
          </div>
        </div>
        <div class="setting-item">
          <div class="setting-item-label">背景图</div>
          <div class="setting-item-content">
            <div class="thumbnail-box bgblank">
              <MaterialThumbnail @select="updateMaterialFn('bgImg')" @reset="updateMaterialFn('bgImg', true)">
                <img v-if="bgImgBind" :src="bgImgBind" />
              </MaterialThumbnail>
            </div>
          </div>
        </div>
        <div class="setting-item">
          <div class="setting-item-label">关闭图标</div>
          <div class="setting-item-content">
            <div class="thumbnail-box bgblank">
              <MaterialThumbnail @select="updateMaterialFn('closeImg')" @reset="updateMaterialFn('closeImg', true)">
                <img v-if="closeImgBind" :src="closeImgBind" />
              </MaterialThumbnail>
            </div>
          </div>
        </div>
        <div class="setting-item">
          <span>文本颜色</span>
          <hi-color v-model="textColorBind" />
        </div>
      </template>

      <template v-else>
        <div class="setting-item">
          <span>关闭按钮颜色</span>
          <hi-color v-model="closeColorBind" />
        </div>
        <div class="setting-item">
          <span>标题颜色</span>
          <hi-color v-model="titleColorBind" />
        </div>
        <div class="setting-item">
          <span>标题背景颜色</span>
          <hi-color v-model="titleBgColorBind" />
        </div>
        <div class="setting-item">
          <span>文本颜色</span>
          <hi-color v-model="textColorBind" />
        </div>
        <div class="setting-item">
          <span>文本背景颜色</span>
          <hi-color v-model="textBgColorBind" />
        </div>
        <div class="setting-item">
          <span>边框颜色</span>
          <hi-color v-model="textBorderColorBind" />
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped lang="scss">
.setting-wrap {
  padding: 10px 0;
}
.thumbnail-box {
  width: 60px;
  height: 60px;
  outline: 1px solid #e6ebed;
  border-radius: 4px;
  position: relative;

  img {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    object-fit: contain;
  }
}
</style>
