import type { CSSProperties } from 'vue'
import type { ILayerTypes } from '../types'
import type { IDesignGroup } from './../layer/group/group'
import { useDesignData, useDesignTemp } from '..'
import { layerUuid } from '../utils'

const designData = useDesignData()
const designTemp = useDesignTemp()

// 组合
export const canMergeToGroup = computed(() => {
  // 获取第一个元素的父级作为基准
  const firstLayer = designData.getLayerByUuid(designTemp.activeList[0])
  if (!firstLayer) return false // 处理元素不存在的情况

  const baseParent = firstLayer.$parent

  // 检查所有选中元素的父级是否一致
  for (const uuid of designTemp.activeList) {
    const layer = designData.getLayerByUuid(uuid)
    if (!layer || layer.$parent !== baseParent) {
      return false
    }
  }

  // 如果所有元素都是顶层元素（baseParent=undefined）或父级是group类型
  return !baseParent || baseParent.type === 'group'
})

export function mergeToGroup() {
  if (!canMergeToGroup.value) return

  const baseParent = (designData.getLayerByUuid(designTemp.activeList[0])?.$parent as IDesignGroup)?.layers || designData.layers

  // fixed 此处的图层顺序不应该使用activeList，应该使用图层原有的顺序
  const layerIndex: Record<string, number> = {}

  baseParent.forEach(({ uuid }, index) => layerIndex[uuid] = index)

  const selectLayers = designTemp.activeList.map((uuid) => {
    const layer = baseParent.find(layer => layer.uuid === uuid)
    return layer
  }).filter(i => i) as ILayerTypes[]

  selectLayers.sort((a, b) => layerIndex[a.uuid] - layerIndex[b.uuid])

  // 将分组添加到设计数据中
  let currentLayerIndex = -1
  // 查找一个存在的位置
  for (let i = 0; i < baseParent.length; i++) {
    const item = baseParent[i]
    if (designTemp.activeList.includes(item.uuid)) {
      currentLayerIndex = i
      break
    }
  }

  // 从旧位置移除元素
  designTemp.activeList.forEach((uuid) => {
    const index = baseParent.findIndex(layer => layer.uuid === uuid)
    baseParent.splice(index, 1)
  })

  // 计算分组的位置和尺寸(如果图层中存在有NaN的会造成计算错误)
  const left = Math.min(...selectLayers.map(item => Number.parseInt(`${item.style.left || '0'}`)))
  const top = Math.min(...selectLayers.map(item => Number.parseInt(`${item.style.top || '0'}`)))
  const width = Math.max(...selectLayers.map(item => Number.parseInt(`${item.style.left || '0'}`) + Number.parseInt(`${item.style.width || ''}`))) - left
  const height = Math.max(...selectLayers.map(item => Number.parseInt(`${item.style.top || '0'}`) + Number.parseInt(`${item.style.height || ''}`))) - top

  // 校准图层位置
  selectLayers.forEach((item) => {
    item.style.left = `${Number.parseInt(`${item.style.left || '0'}`) - left}px`
    item.style.top = `${Number.parseInt(`${item.style.top || '0'}`) - top}px`
  })

  const groupStyle: CSSProperties = {}
  if (left) groupStyle.left = `${left}px`
  if (top) groupStyle.top = `${top}px`
  if (width) groupStyle.width = `${width}px`
  if (height) groupStyle.height = `${height}px`

  const group: IDesignGroup = {
    type: 'group',
    uuid: `group-${layerUuid()}`,
    layers: selectLayers,
    name: `新分组${baseParent.filter(layer => layer.type === 'group').length}`,
    style: groupStyle,
  }

  if (Number.isNaN(left) || Number.isNaN(top) || Number.isNaN(width) || Number.isNaN(height)) {
    console.error('分组位置或尺寸计算错误', group, selectLayers)
  }

  if (currentLayerIndex === -1) {
    baseParent.unshift(group)
  } else {
    baseParent.splice(currentLayerIndex, 0, group)
  }

  designTemp.activeList = [group.uuid]
}

// 拆分 //////////////
// 当前选择只有一个分组时，可以拆分
export const canSplitGroup = computed(() => {
  if (designTemp.activeList.length !== 1) {
    return false
  }
  const layer = designData.getLayerByUuid(designTemp.activeList[0])
  return layer?.type === 'group'
})
export function splitGroup() {
  const layer = designData.getLayerByUuid(designTemp.activeList[0])
  if (!layer || layer.type !== 'group') return

  // 组的位置信息
  const groupLeft = Number.parseInt(`${layer.style.left || '0'}`)
  const groupTop = Number.parseInt(`${layer.style.top || '0'}`)

  // 组所在的层级位置
  const parentLayer = (layer.$parent as IDesignGroup)?.layers || designData.layers
  const groupIndex = parentLayer.findIndex(item => item.uuid === layer.uuid)
  // 删除组
  parentLayer.splice(groupIndex, 1)

  // 遍历组内层
  layer.layers.reverse().forEach((item) => {
    item.style.left = `${groupLeft + Number.parseInt(`${item.style.left || '0'}`)}px`
    item.style.top = `${groupTop + Number.parseInt(`${item.style.top || '0'}`)}px`
    parentLayer.splice(groupIndex, 0, item)
  })

  const uuid = layer.layers[0]?.uuid
  if (uuid) {
    designTemp.activeList = [uuid]
  } else {
    designTemp.reset()
  }
}
