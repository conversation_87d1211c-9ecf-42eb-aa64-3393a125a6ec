<script setup lang="ts">
import type { IDesignLotteryIng5 } from './lottery-ing5'
import { getDefaultMaterial, openSelectMaterial, useDataAttr } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'
import { defaultanimateSpeed, defaultHeadSize, defaultPlaceHolderHeadImg } from './lottery-ing5'

const layer = defineModel<IDesignLotteryIng5>('layer', { required: true })

const headSizeBlind = useDataAttr(layer.value, 'headSize', defaultHeadSize)
const animateSpeedBlind = useDataAttr(layer.value, 'animateSpeed', defaultanimateSpeed)
const placeHolderHeadImgBlind = computed(() => {
  return layer.value.placeHolderHeadImg ?? defaultPlaceHolderHeadImg
})

type IType = 'maskImg' | 'placeHolderHeadImg'
async function updateMaterialFn(name: IType, isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, `${name}`)
    : await openSelectMaterial('PIC')
  if (result) {
    layer.value[name] = result
  } else if (isReset) {
    delete layer.value[name]
  }
}
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>头像大小</h3>
        <el-slider v-model="headSizeBlind" :min="5" :max="50" :step="1" class="mx-10 flex-1" />
      </div>
      <div class="setting-item">
        <h3>动画速度</h3>
        <el-slider v-model="animateSpeedBlind" :min="0" :max="30" :step="1" class="mx-10 flex-1" />
      </div>
      <div class="setting-item mt-5!">
        <div class="flex items-center">
          头像剪影
          <el-tooltip effect="dark">
            <template #content>有颜色区显示，透明区域隐藏</template>
            <icon-ph-question-bold class="ml-5" />
          </el-tooltip>
        </div>
        <div class="flex items-end">
          <div class="d bgblank" @click="layer.maskImg = ''"></div>
          <div class="bgblank relative h-50 w-64">
            <MaterialThumbnail @select="updateMaterialFn('maskImg')" @reset="updateMaterialFn('maskImg', true)">
              <img v-if="layer.maskImg" :src="layer.maskImg" alt="" class="h-full w-full object-cover" />
            </MaterialThumbnail>
          </div>
        </div>
      </div>
      <div class="setting-item mt-5!">
        <h3>头像默认图</h3>
        <div class="flex items-end">
          <div class="bgblank relative h-50 w-64">
            <MaterialThumbnail @select="updateMaterialFn('placeHolderHeadImg')" @reset="updateMaterialFn('placeHolderHeadImg', true)">
              <img v-if="placeHolderHeadImgBlind" :src="placeHolderHeadImgBlind" alt="" class="h-full w-full object-cover" />
            </MaterialThumbnail>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.d {
  width: 25px;
  height: 25px;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
  &:first-child {
    position: relative;
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 90%;
      height: 90%;
      margin: 5%;
      mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1em' height='1em' viewBox='0 0 256 256'%3E%3Cpath fill='%23000' d='m203.63 62.65l13.25-14.58a12 12 0 0 0-17.76-16.14l-13.24 14.56A100 100 0 0 0 52.37 193.35l-13.25 14.58a12 12 0 1 0 17.76 16.14l13.24-14.56A100 100 0 0 0 203.63 62.65M52 128a75.94 75.94 0 0 1 117.58-63.57l-100.91 111A75.6 75.6 0 0 1 52 128m76 76a75.5 75.5 0 0 1-41.58-12.43l100.91-111A75.94 75.94 0 0 1 128 204'/%3E%3C/svg%3E");
      mask-size: 100%;
      background-color: #999999;
      cursor: pointer;
    }
  }
}
</style>
