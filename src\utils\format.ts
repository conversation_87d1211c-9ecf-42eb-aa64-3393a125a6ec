import { fenToYuan } from '~/src/utils/math'

/**
 * 辅助函数，用于在数字前补零，使其至少为两位数
 * @param num 数字
 * @param size 最小长度
 * @returns 字符串
 */
export function padZero(num: number | string, size: number = 2): string {
  let s = num.toString()
  while (s.length < size) s = `0${s}`
  return s
}

/**
 * 格式化倒计时
 * @param endTime 结束时间
 * @param format 输出格式，例如 'd:h:m:s', 'h:m:s', 'm:s', 's'
 * @param nowTime 当前时间
 * @param padZero 是否补零
 * @returns 格式化后的倒计时字符串
 */
type Time = number | string | Date
interface CountdownFormatOptions {
  format: 'd:h:m:s' | 'h:m:s' | 'm:s' | 's'
  nowTime: Time
  padZero: boolean
}
export function countdownFormat(
  endTime: Time,
  options: CountdownFormatOptions,
): string {
  const { format = 'm:s', nowTime = Date.now(), padZero: _padZero = false } = options
  const strlen = _padZero ? 2 : 1

  const now = new Date(nowTime).getTime()
  const end = new Date(endTime).getTime()
  const diff = end - now
  if (diff <= 0) {
    const zeroFormats: Record<string, string> = {
      'd:h:m:s': '0:0:0:0',
      'h:m:s': '0:0:0',
      'm:s': '0:0',
      's': '0',
    }
    return zeroFormats[format].split(':').map(v => padZero(v, strlen)).join(':') || '00'
  }

  const totalSeconds = Math.ceil(diff / 1000)
  const seconds = totalSeconds % 60
  const totalMinutes = Math.floor(totalSeconds / 60)
  const minutes = totalMinutes % 60
  const totalHours = Math.floor(totalMinutes / 60)
  const hours = totalHours % 24
  const days = Math.floor(totalHours / 24)

  switch (format) {
    case 'd:h:m:s':
      return `${days}:${padZero(hours, strlen)}:${padZero(minutes, strlen)}:${padZero(seconds, strlen)}`
    case 'h:m:s':
      // 如果总小时数超过24小时，继续累加小时数
      return `${padZero(totalHours, strlen)}:${padZero(minutes, strlen)}:${padZero(seconds, strlen)}`
    case 'm:s':
      return `${padZero(totalMinutes, strlen)}:${padZero(seconds, strlen)}`
    case 's':
      return `${padZero(totalSeconds, strlen)}`
    default:
      return '00'
  }
}

export function dealMoney(money: number) {
  return money ? fenToYuan(money) : '0.00'
}
