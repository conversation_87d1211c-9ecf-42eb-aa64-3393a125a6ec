<script setup lang="ts">
import type { IDesignListlotteryIng1 } from './listlottery-ing1'
import Gsap from 'gsap'
import { sample, shuffle, throttle } from 'lodash-es'
import * as THREE from 'three'
import { CSS3DObject, CSS3DRenderer, CSS3DSprite } from 'three/examples/jsm/renderers/CSS3DRenderer.js'
import { useDesignState } from '../..'
import { defalutTextStyle, defaultHeadCount, defaultHeadSize, defaultItemDirection, defaultrotateSpeed } from './listlottery-ing1'

const layer = defineModel<IDesignListlotteryIng1>('layer', { required: true })
const designState = useDesignState()
const status = computed(() => designState.status)

const timeline = Gsap.timeline({ repeat: -1, paused: false })
const headCount = computed(() => layer.value.headCount ?? defaultHeadCount)
const headSize = computed(() => layer.value.headSize ?? defaultHeadSize)
// const isScaleAnimation = computed(() => layer.value.isScaleAnimation ?? defaultIsScaleAnimation)
const rotateSpeed = computed(() => layer.value.rotateSpeed ?? defaultrotateSpeed)
const itemDirection = computed(() => layer.value.itemDirection ?? defaultItemDirection)

// 参与人奖池
const regeditList = computed(() => {
  return designState.getLayerData('regeditList') || []
})
const importformShowOption = computed<Record<number, boolean>>(() => {
  return designState.getLayerData('importformShowOption') || []
})

const threeRoot = ref<HTMLElement>()
const threeRootSize = useElementSize(threeRoot)

let requestAnimationFrameId: number | null = null

let camera: THREE.PerspectiveCamera
let scene: THREE.Scene
let renderer: CSS3DRenderer
let group: THREE.Group | null = null
let lastUpdateTime = 0 // 用于控制更新频率
const updateInterval = 200 // 每 200ms 更新一次
const itemsToUpdatePerTick = 5 // 每次更新5个
let updateSortList: number[] = [] // 用于存储更新的索引
const updateIndex = ref(0) // 用于记录更新到的位置

function getViewSizeAtZ(z = 0) {
  if (!camera) return { width: 100, height: 100 }
  const cameraZ = camera.position.z - z
  const fov = camera.fov * (Math.PI / 180)
  const height = 2 * Math.tan(fov / 2) * Math.abs(cameraZ)
  const width = height * camera.aspect
  return { width, height }
}
function getItem() {
  const tem = sample(regeditList.value)
  const itemData = Object.assign({}, tem)
  return itemData
}
function getItemBgColor() {
  const arr = layer.value.itemBgColors as string[]
  const index = Math.floor(Math.random() * arr.length)
  return arr[index]
}

function getItemContent(itemData?: any) {
  const arr: string[] = []
  layer.value.contentStyle.forEach((item, index) => {
    if (importformShowOption.value[index]) {
      if (itemData[`nameD${index}`]) {
        arr.push(`
      <div style="font-size:${item.fontSize}px;color:${item.fontColor};font-weight:${item.fonBold ? 'bold' : 'normal'}">${itemData[`nameD${index}`]}</div>
      `)
      }
    }
  })
  const str = arr.join('')
  return str
}

function createElement(itemData?: any) {
  const element = document.createElement('div')
  element.setAttribute('class', 'css3d')
  element.style = `
    width: ${headSize.value}px;
    height:${headSize.value}px;
    font-size: 40px;
    background: ${getItemBgColor()};
  `
  element.innerHTML = getItemContent(itemData)
  return element
}

function initShape() {
  if (!scene) {
    return
  }
  initUpdateSort() // 重新生成形状时，也重新初始化更新顺序
  updateIndex.value = 0 // 重置更新索引

  if (group) {
    const children = group.children
    scene.remove(group)
    group = null
    for (const item of children) {
      if (item instanceof CSS3DObject || item instanceof CSS3DSprite) {
        item.element.remove()
      }
    }
  }

  group = new THREE.Group()
  scene.add(group)

  timeline.clear()
  timeline.to(group.rotation, {
    duration: 100,
    y: -Math.PI * 2,
    ease: 'none',
  })
  timeline.timeScale(rotateSpeed.value)

  const { width, height } = getViewSizeAtZ(0)
  const sphereRadius = Math.min(width, height) * 0.42

  const count = headCount.value
  const vector = new THREE.Vector3()

  for (let i = 0; i < count; i++) {
    const phi = Math.acos(-1 + (2 * i) / count)
    const theta = Math.sqrt(count * Math.PI) * phi

    const itemData = getItem()
    if (!itemData) continue
    const element = createElement(itemData)
    let object = null
    if (itemDirection.value === 'screen') {
      object = new CSS3DSprite(element)
    } else {
      object = new CSS3DObject(element)
    }
    object.position.setFromSphericalCoords(sphereRadius, phi, theta)
    vector.copy(object.position).multiplyScalar(2)
    object.lookAt(vector)
    group.add(object)
  }
}

function updateItems() {
  if (!group || group.children.length === 0 || !updateSortList.length) {
    return
  }

  const children = group.children
  const count = children.length
  const numToUpdate = Math.min(itemsToUpdatePerTick, count, updateSortList.length)

  for (let i = 0; i < numToUpdate; i++) {
    // 如果 updateIndex 超出 updateSortList 的范围，则从头开始
    if (updateIndex.value >= updateSortList.length) {
      updateIndex.value = 0
    }

    const targetChildIndex = updateSortList[updateIndex.value]

    // 确保 targetChildIndex 在 children 数组的有效范围内
    if (targetChildIndex >= 0 && targetChildIndex < count) {
      const objectToUpdate = children[targetChildIndex] as CSS3DObject | CSS3DSprite
      if (objectToUpdate && objectToUpdate.element) {
        const itemData = getItem() // 获取新的数据项
        if (!itemData) continue

        objectToUpdate.element.innerHTML = getItemContent(itemData)
      }
    }
    updateIndex.value++
  }
}

function init() {
  if (!threeRoot.value) {
    return
  }
  scene = new THREE.Scene()
  // 调整 far clipping plane 的值，例如从 1000 增加到 6000 或更大
  // 计算一个合适的 far 值：camera.position.z + sphereRadius + 一些余量
  // sphereRadius 大约是 1043，camera.position.z 是 3000，所以 far 至少需要 3000 + 1043 = 4043。
  // 我们设置一个更安全的值，比如 8000，以确保所有物体都在视锥体内。
  // 同时，near 值也可能需要调整，如果 sphereRadius 很大，物体可能离相机很近。
  // 最近距离约为 3000 - 1043 = 1957。当前的 near=1 是可以的。
  camera = new THREE.PerspectiveCamera(45, window.innerWidth / window.innerHeight, 1, 8000) // 将 1000 修改为 8000
  camera.position.z = 3000
  renderer = new CSS3DRenderer()
  renderer.setSize(0, 0)
  threeRoot.value.appendChild(renderer.domElement)
}

function resize(width: number, height: number) {
  if (renderer) {
    renderer.setSize(width, height)
  }
  if (camera) {
    camera.aspect = width / height
    camera.updateProjectionMatrix()
  }
}

function render() {
  requestAnimationFrameId = requestAnimationFrame(render)

  const now = performance.now()
  if (now - lastUpdateTime > updateInterval) {
    updateItems()
    lastUpdateTime = now
  }

  renderer.render(scene, camera)
}

watch(
  () => [threeRootSize.width.value, threeRootSize.height.value],
  ([w, h]) => {
    resize(w, h)
  },
)

watch(
  () => [rotateSpeed.value],
  () => {
    timeline.timeScale(rotateSpeed.value)
  },
)

const initReady = ref(false)
watch(
  () => [regeditList.value, status.value, headCount.value, headSize.value, layer.value.itemBgColors, layer.value.contentStyle, layer.value.itemDirection],
  () => {
    if (!initReady.value || !regeditList.value.length) {
      return
    }
    // 当 headCount 变化时，也需要重新初始化更新列表
    if (updateSortList.length !== headCount.value) { // 简单判断是否需要更新
      initUpdateSort()
      updateIndex.value = 0
    }
    throttle(() => {
      initShape()
    }, 200, { leading: false })()
  },
  { deep: true },
)

function initContentStyle() {
  for (let i = 0; i < 3; i++) {
    if (!layer.value.contentStyle[i]) {
      layer.value.contentStyle[i] = Object.assign({}, defalutTextStyle)
    } else {
      layer.value.contentStyle[i] = Object.assign({}, defalutTextStyle, layer.value.contentStyle[i])
    }
  }
}
function initUpdateSort() {
  const arr = []
  for (let i = 0; i < headCount.value; i++) {
    arr.push(i)
  }
  // 打乱顺序，复制给 updateSortList
  updateSortList = shuffle(arr)
}

onMounted(async () => {
  initUpdateSort()
  initContentStyle()
  init()
  render()
  initReady.value = true
})
onUnmounted(() => {
  // 清理资源
  if (requestAnimationFrameId) {
    cancelAnimationFrame(requestAnimationFrameId)
    requestAnimationFrameId = null
  }
})
</script>

<template>
  <div ref="threeRoot" class="three-root"></div>
</template>

<style scoped lang="scss">
.three-root {
  width: 100%;
  height: 100%;
  :deep() {
    .css3d {
      border-radius: 50%;
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      color: #fff;
      text-align: center;
      transform: translateZ(0);
      will-change: transform;
      // backface-visibility: hidden; // 注意：如果希望背面也显示（例如双面元素），可以注释或移除此行
      > div {
        width: 80%;
        max-height: 100%;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        line-height: 1.3;
      }
    }
  }
}
</style>
