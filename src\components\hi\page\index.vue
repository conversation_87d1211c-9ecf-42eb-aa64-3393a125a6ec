<script setup lang="ts">
interface IPage {
  total?: number
  pageIndex?: number
  pageSize?: number
}

defineProps<{
  layout?: string
}>()

const _page = defineModel<IPage>()
</script>

<template>
  <el-pagination
    v-if="_page?.total"
    v-model:current-page="_page.pageIndex"
    v-model:page-size="_page.pageSize"
    class="my-10 flex justify-center"
    background
    :layout="layout || 'total, sizes, prev, pager, next, jumper'"
    :page-sizes="[10, 20, 30, 50, 100, 200, 300]"
    :total="_page.total"
  >
    <slot></slot>
  </el-pagination>
</template>

<style scoped lang="scss"></style>
