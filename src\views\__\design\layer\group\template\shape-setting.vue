<script setup lang="ts">
import type { IDesignShape } from '../../shape/shape'
import { openSelectMaterial, useDataAttr } from '../../..'
import { useShapeUtils } from '../../shape/shape-utils'

defineProps<{
  label: string
}>()
const layer = defineModel<IDesignShape>('setting', { required: true })

async function updateMaterialFn() {
  const result = await openSelectMaterial('SHAPE')
  if (result) {
    layer.value.data = result
  }
}
const { colors, reset } = useShapeUtils(layer)
const bgColorBind = useDataAttr(layer.value.style, 'background', 'rgba(0, 0, 0, 0)')
</script>

<template>
  <div class="setting-wrap">
    <h3>{{ label }}</h3>
    <div class="setting-item">
      <div class="thumbnail-box bgblank" @click="updateMaterialFn">
        <img :src="layer.data">
      </div>
    </div>
    <div class="setting-item">
      <el-button type="danger" @click="reset">重置</el-button>
    </div>
    <div v-for="i in colors.length" :key="i" class="setting-item">
      <span>形状颜色{{ i }}</span>
      <hi-color v-model="colors[i - 1]" />
    </div>
    <div class="setting-item">
      <h3>背景色</h3>
      <hi-color v-model="bgColorBind" type="both" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.setting-wrap {
  padding: 0 !important;
  .setting-item {
    justify-content: flex-end !important;
  }
}
.thumbnail-box {
  width: 100%;
  aspect-ratio: 16 / 9;
  outline: 1px solid #e6ebed;
  position: relative;
  img {
    position: absolute;
    width: 100%;
    height: 100%;
  }

  > img {
    object-fit: contain;
  }
}
</style>
