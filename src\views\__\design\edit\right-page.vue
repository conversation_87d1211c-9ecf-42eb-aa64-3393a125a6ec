<script setup lang="ts">
import type { IDesignBackground } from '../types'
import { cloneDeep } from 'lodash-es'
import { hasAuth } from '~/src/utils/auth'
import { objectFits, openSelectMaterial, provideElementSize, useDesignData, useDesignTemp } from '..'

provideElementSize()

const designData = useDesignData()
const designTemp = useDesignTemp()

const hasActionAuth = computed(() => hasAuth(['man', 'oem', 'spo.hi.vip', 'spo.ft']))

const objectFit = computed({
  get() {
    return designData.option.objectFit || 'contain'
  },
  set(v) {
    if (v === 'none' || v === 'contain') {
      delete designData.option.objectFit
    } else {
      designData.option.objectFit = v
    }
  },
})

const defaultBackground: Required<IDesignBackground> = {
  type: 'background',
  showType: 'repeat',
  color: 'rgba(0,0,0,0)',
  image: '',
  bgColor: 'rgba(0,0,0,0)',
  size: 0.15,
  blur: 0,
}
const backgroundOption = ref<Required<IDesignBackground>>(cloneDeep(defaultBackground))

watch(
  () => designData.background,
  (v = {}) => {
    const value = Object.assign({}, defaultBackground, v)
    if (JSON.stringify(value) !== JSON.stringify(backgroundOption.value)) {
      backgroundOption.value = value
    }
  },
  { immediate: true },
)
watch(
  () => backgroundOption.value,
  (v) => {
    const tmp: Partial<IDesignBackground> = cloneDeep(v)
    Object.keys(defaultBackground).forEach((key) => {
      // @ts-ignore
      if (tmp[key] === defaultBackground[key]) {
        // @ts-ignore
        delete tmp[key]
      }
    })
    if (!tmp.image) {
      delete tmp.bgColor
      delete tmp.size
      delete tmp.blur
    }
    if (Object.keys(tmp).length === 0) {
      designData.background = undefined
    } else {
      designData.background = tmp
    }
  },
  { deep: true },
)

async function updateMaterialFn(type: 'themeImg' | 'backgroundImage') {
  if (!designTemp.theme) return
  const result = await openSelectMaterial('PIC')
  if (result) {
    if (type === 'themeImg') {
      designTemp.theme.themeImg = result
    } else {
      backgroundOption.value.image = result
    }
  }
}

const percentageTooltip = (v: number) => `${(v * 100).toFixed()}%`

const recommendList = ref<string[]>([
  '',
  // 'https://res.dev.hixianchang.com/qn/material/e5c467cff6c2f79a701ad2bc271d088b.gif',
  // 'https://res.dev.hixianchang.com/qn/material/feb396ee23a07b5371b797e99d0d6753.gif',
  // 'https://res.dev.hixianchang.com/qn/material/c365b88f226fa169614105cd21293551.png',
  // 'https://res.dev.hixianchang.com/qn/material/58db39bd392ac3c4163b6447c35b77eb.png',
  // 'https://res.dev.hixianchang.com/qn/material/4d49374f03bc9724f07df2ce2e3c36a6.png',
  // 'https://res.dev.hixianchang.com/qn/material/92d1d1440eace413f431446c8041d92e.png',
  // 'https://res.dev.hixianchang.com/qn/material/af2c66db774241175607854c4824ce45.png',
  // 'https://res.dev.hixianchang.com/qn/material/1ff4deb4164235ace5c58e8540ab3b06.png',
])
</script>

<template>
  <div class="setting-block my-10px">
    <div class="setting-wrap">
      <div v-if="designTemp.theme" class="setting-item">
        <span>主题名</span>
        <el-input v-model="designTemp.theme.name" class="w-130" :maxlength="6" placeholder="请输入主题名" />
      </div>
      <template v-if="hasActionAuth">
        <div v-if="designTemp.theme" class="setting-item">
          <span class="flex items-center">
            封面
            <el-tooltip effect="dark" content="建议尺寸: 240*160">
              <icon-ph-question-bold class="ml-5" />
            </el-tooltip>
          </span>
          <div class="bgblank h-80 w-120" @click="updateMaterialFn('themeImg')">
            <img v-if="designTemp.theme.themeImg" :src="designTemp.theme.themeImg" />
          </div>
        </div>
        <div class="setting-item">
          <span class="flex items-center">
            适配模式
            <el-tooltip effect="dark" content="请保存后在大屏幕看效果">
              <icon-ph-question-bold class="ml-5" />
            </el-tooltip>
          </span>
          <el-select v-model="objectFit" class="w-130">
            <el-option v-for="item in objectFits.filter(i => i.value !== 'none')" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <!-- 背景颜色 -->
        <div class="setting-item items-center">
          <span>背景色</span>
          <hi-color v-model="backgroundOption.color" type="both" />
        </div>
      </template>
    </div>

    <div v-if="hasActionAuth" class="setting-wrap">
      <span>叠加背景</span>
      <!-- 背景叠加 -->
      <div class="setting-item">
        <span>图片</span>
        <div class="flex flex-col items-end">
          <div class="bgblank h-80 w-175" @click="updateMaterialFn('backgroundImage')">
            <img v-if="backgroundOption.image" :src="backgroundOption.image" />
          </div>
          <ul class="recommend-box">
            <li v-for="item in recommendList" :key="item" class="bgblank" @click="backgroundOption.image = item">
              <img v-if="item" :src="item" />
            </li>
          </ul>
        </div>
      </div>
      <template v-if="backgroundOption.image">
        <div class="setting-item">
          <span>模式</span>
          <el-radio-group v-model="backgroundOption.type" class="ml-4">
            <el-radio value="background">背景</el-radio>
            <el-radio value="mask">遮罩</el-radio>
          </el-radio-group>
        </div>
        <div v-if="backgroundOption.type === 'background'" class="setting-item items-center">
          <span>显示模式</span>
          <el-radio-group v-model="backgroundOption.showType" class="ml-4">
            <el-radio value="repeat">平铺</el-radio>
            <el-radio value="fill">拉伸</el-radio>
            <el-radio value="contain">适应</el-radio>
          </el-radio-group>
        </div>
        <div class="setting-item items-center">
          <span>颜色</span>
          <hi-color v-model="backgroundOption.bgColor" type="both" />
        </div>
        <div
          v-if="backgroundOption.type === 'mask' || backgroundOption.showType === 'repeat'"
          class="setting-item items-center"
        >
          <span>尺寸</span>
          <el-slider
            v-model="backgroundOption.size"
            :min="0.01"
            :max="1"
            :step="0.01"
            class="pl-30 pr-10"
            :format-tooltip="percentageTooltip"
          />
        </div>
        <div class="setting-item items-center">
          <span>模糊</span>
          <el-slider
            v-model="backgroundOption.blur"
            :min="0"
            :max="30"
            class="pl-30 pr-10"
            :format-tooltip="v => `${v}px`"
          />
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped lang="scss">
.setting-block {
  background-color: #eef2f8;
}
.setting-wrap {
  background-color: #fff;
  padding: 0 12px;
  > span {
    font-weight: bold;
    line-height: 40px;
    white-space: nowrap;
  }
  .setting-item {
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    > span {
      white-space: nowrap;
    }
  }
}
.recommend-box {
  width: 175px;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-top: 5px;
  li {
    width: 40px;
    height: 40px;
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
    &:first-child {
      position: relative;
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 90%;
        height: 90%;
        margin: 5%;
        mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1em' height='1em' viewBox='0 0 256 256'%3E%3Cpath fill='%23000' d='m203.63 62.65l13.25-14.58a12 12 0 0 0-17.76-16.14l-13.24 14.56A100 100 0 0 0 52.37 193.35l-13.25 14.58a12 12 0 1 0 17.76 16.14l13.24-14.56A100 100 0 0 0 203.63 62.65M52 128a75.94 75.94 0 0 1 117.58-63.57l-100.91 111A75.6 75.6 0 0 1 52 128m76 76a75.5 75.5 0 0 1-41.58-12.43l100.91-111A75.94 75.94 0 0 1 128 204'/%3E%3C/svg%3E");
        mask-size: 100%;
        background-color: #999999;
      }
    }
  }
}
</style>
