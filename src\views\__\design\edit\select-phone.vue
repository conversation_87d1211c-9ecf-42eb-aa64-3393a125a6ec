<script setup lang="ts">
import { useMobileDevice } from '~/src/views/__/design/hooks/useMobileDevice'

const { previewMobileDevice, allMobileDevices } = useMobileDevice()
</script>

<template>
  <div
    class="mobile-side"
  >
    <ul class="max-h-400px overflow-auto">
      <li
        v-for="item in allMobileDevices"
        :key="item.name"
        class="cursor-pointer px-10 py-14 text-13px hover:text-blue-500"
        :class="{
          'text-blue-500': item.name === previewMobileDevice,
        }"
        @click="previewMobileDevice = item.name"
      >
        <span style="float: left">{{ item.name }}</span>
        <span v-if="item.width" class="float-right text-13px text-[var(--el-text-color-secondary)]">
          {{ item.width }}
          x
          {{ item.height }}
        </span>
      </li>
    </ul>
  </div>
</template>
