<script setup lang="ts">
import type { MaybeReadonlyRefOrGetter, Placement } from '@floating-ui/vue'
import { cloneDeep } from 'lodash-es'
import { openSelectMaterial } from '..'

withDefaults(
  defineProps<{
    placement?: MaybeReadonlyRefOrGetter<Placement | undefined>
    recommend?: boolean
  }>(),
  {
    placement: 'bottom-end',
    recommend: true, // 推荐
  },
)
const emits = defineEmits<{
  (e: 'change', val: string | undefined): void
}>()
const mode = ref<'pure' | 'gradient' | 'image'>()
const selectColor = defineModel<string | undefined>({ required: true })

const pureColor = ref<string>('')
const gradientColor = ref<string>('linear-gradient(90deg, #ff0000 0%, #0000ff 100%)')
interface IBackground {
  color: string // transparent
  image: string
  repeat: string // no-repeat, repeat-x, repeat-y, repeat
  position: string
  size: string // contain, cover, 100% 100%
}
const defaultBackground = {
  color: 'rgba(0, 0, 0, 0)',
  image: '',
  repeat: 'no-repeat',
  position: 'center',
  size: 'contain',
}
const background = ref<IBackground>(defaultBackground)

// 通过正则表达式解析background
function parseBackground(val: string): IBackground {
  // 增强的正则表达式，支持更多格式
  const regex = /^(rgba?\([^)]+\)|#[0-9a-fA-F]{3,8}|\w+)?\s*(?:(url\(['"]?[^'"]+['"]?\))\s*)?(no-repeat|repeat|repeat-x|repeat-y)?\s*(?:(scroll|fixed)\s*)?(center|left|right|top|bottom|[0-9%]+(?:\s+[0-9%]+)?)?\s*(?:(\/\s*(cover|contain|[0-9%]+(?:\s+[0-9%]+)?))\s*)?(content-box|padding-box|border-box)?\s*(content-box|padding-box|border-box)?$/
  const matches = val.match(regex)
  if (!matches) {
    return defaultBackground
  }

  // 提取匹配的值
  let [
    ,
    color,
    image,
    repeat,
    ,
    position,
    ,
    size,
  ] = matches

  // 提取image中的链接
  if (image) {
    const regex = /url\(['"]?([^'"]+)['"]?\)/
    const match = image.match(regex)
    if (match) {
      image = match[1]
    }
  }

  // 返回解析结果
  return {
    color: color?.trim() || 'rgba(0, 0, 0, 0)',
    image: image?.trim() || '',
    repeat: repeat?.trim() || 'repeat',
    position: position?.trim() || 'center',
    size: size?.trim() || 'contain',
  }
}

function beforeEnterFn() {
  // 初始颜色
  if (selectColor.value) {
    if (selectColor.value.includes('linear-gradient')) {
      gradientColor.value = selectColor.value
      mode.value = 'gradient'
    } else if (selectColor.value.includes('url(')) {
      // 自动解析 background
      background.value = parseBackground(selectColor.value)
      mode.value = 'image'
    } else {
      pureColor.value = selectColor.value
      mode.value = 'pure'
    }
  } else {
    mode.value = 'pure'
  }
}

function changeFn(val?: string) {
  selectColor.value = val
  emits('change', val)
  // 校准 mode
  if (val !== undefined) {
    if (val.includes('linear-gradient')) {
      if (mode.value !== 'gradient') {
        gradientColor.value = val
        mode.value = 'gradient'
      }
    } else if (val.includes('url(')) {
      mode.value = 'image'
    } else {
      if (mode.value !== 'pure') {
        pureColor.value = val
        mode.value = 'pure'
      }
    }
  }
}

watch(
  () => [mode.value, pureColor.value, gradientColor.value, background.value] as const,
  ([m, p, g, b]) => {
    if (m === 'pure') {
      changeFn(p)
    } else if (m === 'gradient') {
      changeFn(g)
    } else if (m === 'image') {
      const val = `${b.color} url("${b.image}") ${b.repeat} ${b.position}/${b.size}`
      changeFn(val)
    }
  },
  { deep: true },
)

const normalColor = ref([
  'rgb(255, 255,255)',
  'rgb(255, 84, 72)',
  'rgb(255, 202, 40)',
  'rgb(24, 207, 161)',
  'rgb(18, 97, 255)',
  'rgb(77, 143, 243)',
  'rgb(113, 113, 239)',
  'rgb(79, 89, 117)',
  'rgb(0, 0, 0)',
])
const swatchColors = ref<string[]>([
  'rgb(255, 255,255)',
  'rgb(255, 84, 72)',
  'rgb(255, 202, 40)',
  'rgb(24, 207, 161)',
  'rgb(18, 97, 255)',
  'rgb(77, 143, 243)',
  'rgb(113, 113, 239)',
  'rgb(79, 89, 117)',
  'rgb(96, 38, 65)',
  'rgb(34, 107, 104)',

  'rgb(107, 121, 142)',
  'rgb(238, 241, 246)',
  'rgb(255, 203, 208)',
  'rgb(189, 230, 255)',
  'rgb(151, 236, 185)',
  'rgb(255, 195, 109)',
  'rgb(149, 244, 226)',
  'rgb(204, 208, 255)',
  'rgb(236, 202, 241)',
  'rgb(255, 190, 208)',

  'rgb(69, 70, 94)',
  'rgb(168, 184, 208)',
  'rgb(232, 116, 116)',
  'rgb(89, 199, 249)',
  'rgb(86, 183, 134)',
  'rgb(234, 153, 36)',
  'rgb(46, 203, 190)',
  'rgb(141, 145, 255)',
  'rgb(194, 119, 208)',
  'rgb(255, 121, 162)',

  'rgb(0, 0, 0)',
  'rgb(122, 144, 178)',
  'rgb(196, 67, 60)',
  'rgb(33, 150, 237)',
  'rgb(72, 145, 108)',
  'rgb(214, 123, 3)',
  'rgb(35, 161, 147)',
  'rgb(105, 108, 180)',
  'rgb(161, 88, 179)',
  'rgb(191, 76, 118)',
])

const storageKey = 'hi:next:color:recent'
const recentColors = useStorage<{ pure: string[], gradient: string[] }>(storageKey, { pure: [], gradient: [] })
function recentColorsChange(val: string[]) {
  // 保留最近的10个颜色
  val = val.slice(0, 10)
  if (mode.value === 'pure') {
    recentColors.value.pure = val
  } else if (mode.value === 'gradient') {
    recentColors.value.gradient = val
  }
}
function afterLeaveFn() {
  // 当前颜色加入缓存
  if (mode.value === 'pure') {
    const copyColor = cloneDeep(recentColors.value.pure)
    if (!copyColor.includes(pureColor.value)) {
      copyColor.unshift(pureColor.value)
      recentColorsChange(copyColor)
    }
  } else if (mode.value === 'gradient') {
    const copyColor = cloneDeep(recentColors.value.gradient)
    if (!copyColor.includes(gradientColor.value)) {
      copyColor.unshift(gradientColor.value)
      recentColorsChange(copyColor)
    }
  }
}

// 图片
// rgba(0, 0, 0, 0) url("paper.gif") no-repeat center/contain
// #000 url("paper.gif") no-repeat center/contain
// #fff000 url("paper.gif") no-repeat center/contain
// rgb(2, 23, 4) url("paper.gif") no-repeat center/contain
// rgba(234, 133, 2) url("paper.gif") no-repeat center/contain
const Repeats: { label: string, value: string }[] = [
  { label: '无', value: 'no-repeat' },
  { label: '横向', value: 'repeat-x' },
  { label: '纵向', value: 'repeat-y' },
  { label: '平铺', value: 'repeat' },
]
const Sizes: { label: string, value: string }[] = [
  { label: '适应', value: 'contain' },
  { label: '裁剪', value: 'cover' },
  { label: '拉伸', value: '100% 100%' },
]

async function selectMaterialFn() {
  const result = await openSelectMaterial('PIC')
  if (result) {
    background.value.image = result
  }
}

function beforeClose(e: MouseEvent) {
  const target = e.target as HTMLElement
  if (!target) return

  if (target.closest('.color-box')) return false
  if (target.closest('.el-overlay')) return false
}

const eyeDropper = window.EyeDropper && new window.EyeDropper()

function openEyeDropper() {
  if (!eyeDropper) {
    return
  }
  eyeDropper
    .open({ })
    .then((result: { sRGBHex: string }) => {
      pureColor.value = result.sRGBHex
    })
    .catch((err: any) => {
      console.log('🚀 ~ openEyeDropper ~ err:', err)
    })
}
</script>

<template>
  <div class="color-box">
    <hi-floating
      trigger="click"
      class="h-500 w-260"
      :placement="placement"
      :before-close="beforeClose"
      @after-leave="afterLeaveFn"
      @before-enter="beforeEnterFn"
    >
      <div class="mx-10 mt-10 flex items-center justify-between">
        <el-radio-group v-model="mode" size="small">
          <el-radio-button value="pure">纯色</el-radio-button>
          <el-radio-button value="gradient">渐变</el-radio-button>
          <el-radio-button value="image">图片</el-radio-button>
        </el-radio-group>
        <el-tooltip v-if="mode === 'pure' && eyeDropper" content="取色">
          <icon-ic:baseline-colorize class="cursor-pointer" @click="openEyeDropper" />
        </el-tooltip>
      </div>

      <t-color-picker-panel
        v-if="mode === 'pure'"
        v-model="pureColor"
        format="RGBA"
        enable-alpha
        borderless
        style="box-shadow: none;"
        :color-modes="['monochrome']"
        :recent-colors="recentColors.pure"
        :swatch-colors="swatchColors"
        @recent-colors-change="recentColorsChange"
      />
      <t-color-picker-panel
        v-else-if="mode === 'gradient'"
        v-model="gradientColor"
        format="RGBA"
        enable-alpha
        borderless
        style="box-shadow: none;"
        :color-modes="['linear-gradient']"
        :recent-colors="recentColors.gradient"
        :swatch-colors="swatchColors"
        @recent-colors-change="recentColorsChange"
      />
      <div v-else-if="mode === 'image'" class="image-box">
        <!-- 图片选择 -->
        <div class="setting-item">
          <div class="bgblank thumbnail-box" @click="selectMaterialFn">
            <img v-if="background.image" :src="background.image" />
          </div>
        </div>
        <!-- 平铺 -->
        <div class="setting-item">
          <label>平铺方式</label>
          <el-select v-model="background.repeat" placeholder="选择平铺方式" class="w-100">
            <el-option v-for="item in Repeats" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <!-- 显示模式 -->
        <div class="setting-item">
          <label>显示模式</label>
          <el-select v-model="background.size" placeholder="选择显示模式" class="w-100">
            <el-option v-for="item in Sizes" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
      </div>
      <template #reference>
        <div class="current-color bgblank">
          <span :style="{ background: selectColor }"></span>
        </div>
      </template>
    </hi-floating>
    <div v-if="recommend" class="normal-color-box">
      <div
        v-for="item in normalColor"
        :key="item"
        class="normal-color-item"
        :style="{ backgroundColor: item }"
        @click="changeFn(item)"
      />
      <div class="normal-color-item nocolor" @click="changeFn('')" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.color-box {
  display: flex;
  justify-content: right;
  align-items: center;
  :deep() {
    .t-input--auto-width {
      min-width: 0;
    }
    .t-input {
      width: 32px;
      padding: 0;
      border: 1px solid #ddd;
      .t-input__prefix {
        background-size: 16px;
        background-image: url(@/assets/bgblank.svg);
        margin: 0;
        .color-inner {
          width: 32px;
          height: 32px;
          border: 0;
        }
      }
      .t-input__inner,
      .t-input__input-pre {
        display: none;
      }
      .t-color-picker__swatches--items {
        max-height: 100px;
      }
    }
  }
}
.current-color {
  position: relative;
  width: 32px;
  height: 32px;
  border: 1px solid #ddd;
  span {
    position: absolute;
    width: 100%;
    height: 100%;
  }
}
.normal-color-box {
  width: 90px;
  .normal-color-item {
    width: 14px;
    height: 14px;
    border: 1px solid #ddd;
    margin: 1px 2px;
    float: left;
  }
  .nocolor {
    position: relative;
    &:after {
      content: '';
      position: absolute;
      top: 100%;
      left: 0;
      transform: rotateZ(-45deg);
      transform-origin: 0 0;
      width: 16px;
      height: 1px;
      background: red;
    }
  }
}
.image-box {
  padding: 10px;
  .thumbnail-box {
    width: 100%;
    aspect-ratio: 16 / 9;
    > img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
  .setting-item {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
</style>
