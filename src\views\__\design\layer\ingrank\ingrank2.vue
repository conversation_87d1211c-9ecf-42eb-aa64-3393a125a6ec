<script setup lang="ts">
import { cloneDeep } from 'lodash-es'
import { defineCustomEmits, injectScale, useDesignState } from '../..'
import { processStyle } from '../../utils'
import { defaultData, type IDesignIngRank2 } from './ingrank2'

const layer = defineModel<IDesignIngRank2>('layer', { required: true })
const customEmits = defineCustomEmits(layer)
const scale = injectScale()
const designState = useDesignState()

interface IDisplayData {
  id: number
  name: string
  avatar: string
  score: number | string // 分数，
  rank?: number // 名次
}

const dataSource = computed(() => { return layer.value.dataSource ?? defaultData.dataSource })

const outerData = computed<IDisplayData[]>(() => {
  // 轮次
  if (dataSource.value === 'round') {
    return designState.getLayerData('ingRankingsRound') || []
  }
  // 题目
  if (dataSource.value === 'subject') {
    return designState.getLayerData('ingRankingsSubject') || []
  }
  return []
})

const count = computed(() => {
  return layer.value.maxCount ?? defaultData.maxCount
})
// const showTime = computed(() => {
//   return layer.value.showTime ?? defaultData.showTime
// })
const showData = computed(() => {
  const list = cloneDeep(outerData.value.slice(0, count.value))
  // list.forEach((item) => {
  //   if (!showTime.value) {
  //     if (typeof item.score === 'string') {
  //       item.score = item.score.split('/')[0] // 处理分数格式
  //     }
  //   }
  // })
  return list
})

watch(
  () => [count.value, dataSource.value],
  () => {
    customEmits('ingrankCount', {
      num: count.value,
      type: dataSource.value,
    })
  },
  { immediate: true },
)

const wapStyle = computed(() => {
  const avatarSize = layer.value.avatarSize ?? defaultData.avatarSize!
  return processStyle({
    '--avatar-size': `${avatarSize}px`,
    '--name-text-size': `${layer.value.nameTextSize ?? defaultData.nameTextSize}px`,
    '--name-text-color': layer.value.nameTextColor ?? defaultData.nameTextColor,
    '--socre-text-size': `${layer.value.socreTextSize ?? defaultData.socreTextSize}px`,
    '--socre-text-color': layer.value.socreTextColor ?? defaultData.socreTextColor,
  }, scale.value)
})
</script>

<template>
  <div class="rank-ing-box">
    <div class="wrap" :style="wapStyle">
      <transition-group name="show" tag="div" class="cell-box">
        <div v-for="(item, index) in showData" :key="index" class="cell">
          <div class="header">
            <img v-if="item?.avatar" :src="item?.avatar" class="avatar-item" :class="[`item-${index}`]">
          </div>
          <div class="content">
            <div class="nickname limit">
              {{ item?.name }}
            </div>
            <div class="score">{{ item?.score }}</div>
          </div>
        </div>
      </transition-group>
    </div>
  </div>
</template>

<style scoped lang="scss">
.rank-ing-box {
  width: 100%;
  height: 100%;
  overflow: auto;
  .wrap {
    height: 100%;
    width: 100%;
  }
  .cell-box {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    gap: 10px 0;
    padding: 10px;
  }
  .cell {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .header {
    width: var(--avatar-size, 40px);
    height: var(--avatar-size, 40px);
    border-radius: 50%;
    overflow: hidden;
    .avatar-item {
      width: 100%;
      height: 100%;
      border-radius: 50%;
    }
  }

  .content {
    display: flex;
    width: calc(100% - var(--avatar-size, 40px));
    height: 100%;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-left: 10px;
    line-height: 1.5;
    .nickname {
      width: 100%;
      color: var(--name-text-color, #000);
      font-size: var(--name-text-size, 16px);
      text-align: left;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .score {
      width: 100%;
      text-align: left;
      overflow: hidden;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: var(--socre-text-color, #000);
      font-size: var(--socre-text-size, 14px);
    }
    .show-enter {
      opacity: 0;
      transform: scale(0);
    }

    .show-enter-active {
      transition: all 1s ease;
    }
  }
}
</style>
