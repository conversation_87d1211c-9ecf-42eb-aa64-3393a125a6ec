<script setup lang="ts">
import type { IDesignPiclotteryIng3 } from './piclottery-ing3'
import gsap from 'gsap'
import { sample, throttle } from 'lodash-es'
import * as THREE from 'three'
import { CSS3DObject, CSS3DRenderer, CSS3DSprite } from 'three/examples/jsm/renderers/CSS3DRenderer.js'
import { useDesignState } from '../..'
import { defaultAnimateSpeed, defaultHeadSizeH, defaultHeadSizeW, defaultImgMode, defaultItemBorderColor, defaultPlaceHolderImg, defultColCount, defultRowCount } from './piclottery-ing3'

const designState = useDesignState()
// 参与人奖池
const regeditList = computed(() => {
  return designState.getLayerData('regeditList') || []
})
const layer = defineModel<IDesignPiclotteryIng3>('layer', { required: true })

const threeRoot = ref<HTMLElement>()
const threeRootSize = useElementSize(threeRoot)

let requestAnimationFrameId: number | null = null

let camera: THREE.PerspectiveCamera
let scene: THREE.Scene
let renderer: CSS3DRenderer
let group: THREE.Group | null = null
const colGroups: THREE.Group[] = []
const timeline = gsap.timeline({ repeat: -1, paused: false })
const cameraZ = 700

const headSizeW = computed(() => layer.value.headSizeW ?? defaultHeadSizeW)
const headSizeH = computed(() => layer.value.headSizeH ?? defaultHeadSizeH)
const animiteSpeed = computed(() => layer.value.animiteSpeed ?? defaultAnimateSpeed)
const itemBorderColor = computed(() => layer.value.itemBorderColor || defaultItemBorderColor)
const itemBorderWidth = computed(() => layer.value.itemBorderWidth || 0)
const imgMode = computed(() => layer.value.imgMode || defaultImgMode)
const placeHolderImg = computed(() => {
  return layer.value.placeHolderImg ?? defaultPlaceHolderImg
})

function getItem() {
  const tem = sample(regeditList.value)
  const itemData = Object.assign({}, tem)
  return itemData
}

function createElement(itemData?: any) {
  const element = document.createElement('div')
  element.setAttribute('class', 'css3d')
  element.style = `
    width: ${headSizeW.value - Math.min(5, headSizeW.value * 0.05)}px;
    height:${headSizeH.value - Math.min(5, headSizeH.value * 0.05)}px;
    border: ${itemBorderWidth.value}px solid ${itemBorderColor.value};
  `
  const imgStyle = `
    object-fit: ${imgMode.value};
  `
  const str = `
      <img style="${imgStyle}" src="${itemData?.avatar || placeHolderImg.value}" alt="头像" />
  `
  element.innerHTML = str
  return element
}

function initShape() {
  if (!scene) {
    return
  }
  if (group) {
    colGroups.forEach((colGroup) => {
      while (colGroup.children.length > 0) {
        const child = colGroup.children[0]
        if (child instanceof CSS3DObject) {
          child.element.remove()
        }
        colGroup.remove(child)
      }
      group?.remove(colGroup)
    })
    colGroups.length = 0

    if (scene.children.includes(group)) {
      scene.remove(group)
    }
    group = null
  }

  group = new THREE.Group()
  scene.add(group)
  const viewSizeAtOrigin = getDisplayArea(0)
  const actualRowCount = layer.value.rowCount ?? defultRowCount
  const colCount = layer.value.colCount ?? defultColCount
  const radius = viewSizeAtOrigin.width * 1.5
  const l = headSizeW.value * colCount
  // 计算l的弧长角度范围
  const angleRange = l / radius // 弧长公式：l = r * θ => θ = l / r
  const itemR = angleRange / colCount
  const startAngle = (Math.PI - angleRange) / 2 // 起始角度

  for (let i = 0; i < colCount; i++) {
    const currentItemAngle = startAngle + (i + 0.5) * itemR
    const x = radius * Math.cos(currentItemAngle)
    const z = -radius * Math.sin(currentItemAngle)

    const currentColGroup = new THREE.Group()
    group.add(currentColGroup)
    colGroups.push(currentColGroup)

    for (let j = 0; j < actualRowCount; j++) {
      const y = (j - (actualRowCount - 1) / 2) * headSizeH.value

      const itemData = getItem()
      if (!itemData) continue

      const element = createElement(itemData)
      // const object = new CSS3DSprite(element)
      const object = new CSS3DObject(element)
      object.position.set(x, y, z)
      currentColGroup.add(object)
    }
  }

  timeline.clear()
  play()
  timeline.timeScale(animiteSpeed.value)
}

function play() {
  colGroups.forEach((colGroup, index) => {
    let delay = 0 // 每个列组的延迟
    if (index < colGroups.length / 2) {
      delay = 0.8 * index
    } else {
      delay = 0.8 * (colGroups.length - index)
    }
    colGroup.children.forEach((object) => {
      // 翻转
      timeline.to(object.rotation, {
        duration: Math.random() * 0.5 + 0.5,
        x: object.rotation.x + (Math.PI * 2),
        ease: 'elastic.inOut',
        repeat: -1,
      }, delay)
    })
  })
}
function getDisplayArea(z = 0) {
  const radian = Math.PI / 180
  const distance = Math.abs(camera.position.z - z)
  const height = 2 * Math.tan((camera.fov / 2) * radian) * distance
  const width = camera.aspect * height
  return {
    width,
    height,
  }
}
function init() {
  if (!threeRoot.value) {
    return
  }
  scene = new THREE.Scene()
  camera = new THREE.PerspectiveCamera(45, window.innerWidth / window.innerHeight, 1, 1000)
  camera.position.z = cameraZ
  renderer = new CSS3DRenderer()
  renderer.setSize(0, 0)
  threeRoot.value.appendChild(renderer.domElement)
}

function resize(width: number, height: number) {
  if (renderer) {
    renderer.setSize(width, height)
  }
  if (camera) {
    camera.aspect = width / height
    camera.updateProjectionMatrix()
  }
}

function render() {
  requestAnimationFrameId = requestAnimationFrame(render)
  renderer.render(scene, camera)
}

watch(
  () => [threeRootSize.width.value, threeRootSize.height.value],
  ([w, h]) => {
    resize(w, h)
  },
)

watch(
  () => [animiteSpeed.value],
  () => {
    timeline.timeScale(animiteSpeed.value)
  },
)

const initReady = ref(false)
function throttlnitShape() {
  if (!initReady.value) {
    return
  }
  throttle(() => {
    initShape()
  }, 300, { leading: false })()
}
watch(
  () => [regeditList.value, headSizeW.value, headSizeH.value, layer.value.itemBorderColor, layer.value.itemBorderWidth, layer.value.imgMode, layer.value.rowCount, layer.value.colCount, placeHolderImg.value],
  () => throttlnitShape(),
  { deep: true },
)

onMounted(async () => {
  init()
  render()
  initReady.value = true
  throttlnitShape()
})
onUnmounted(() => {
  // 清理资源
  if (requestAnimationFrameId) {
    cancelAnimationFrame(requestAnimationFrameId)
    requestAnimationFrameId = null
  }
})
</script>

<template>
  <div ref="threeRoot" class="three-root"></div>
</template>

<style scoped lang="scss">
.three-root {
  width: 100%;
  height: 100%;
  :deep() {
    .css3d {
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      color: #fff;
      text-align: center;
      transform: translateZ(0);
      will-change: transform;
      border-radius: 3%;
      // animation-name: flip-vertical;
      // animation-duration: 0.5s;
      // animation-iteration-count: infinite;
      // animation-timing-function: ease-in-out;
      img {
        width: 100%;
        height: 100%;
        object-position: center;
      }
    }
  }
}
/* 定义翻转动画 */
@keyframes flip-vertical {
  0% {
    transform: rotateX(0deg);
  }
  100% {
    transform: rotateX(360deg);
  }
}
</style>
