import { BisTypes, useDesignData } from '../..'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './down-setting.vue'
import Comp from './down.vue'

// 类型
export const type = 'down'

export interface IDesignDown extends IDesignLayer {
  type: typeof type
  color: string
  shadowColor?: string
}

export const defaultColor = 'rgba(254, 197, 0, 1)'
export const defaultShadowColor = 'rgba(197, 223, 248, 0.8)'

export function setup(app: IDesignSetup) {
  const designData = useDesignData()

  let status: string[]
  switch (app.interactive) {
    case InteractiveEnum.shakev3:
      status = ['321', 'ing']
      break
    default:
      status = ['321', 'ing']
      break
  }

  app.registry({
    bisType: BisTypes.countDown,
    type,
    name: '倒计时',
    thumbnail: new URL('./down.png', import.meta.url).toString(),
    status,
    Comp,
    CompSetting,
    defaultData() {
      return {
        uuid: layerUuid(),
        name: '倒计时',
        type,
        color: defaultColor,
        shadowColor: defaultShadowColor,
        style: {
          width: `${designData.option.drafts[0] || 1280}px`,
          height: `${designData.option.drafts[1] || 800}px`,
          top: '0',
          left: '0',
        },
      }
    },
  })
}
