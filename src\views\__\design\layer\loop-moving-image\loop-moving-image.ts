import type { IDesignLayer, IDesignSetup } from '../../types'
import { merge } from 'lodash-es'
import { BisTypes, openSelectMaterial, ResourceUtil } from '../../'
import { layerUuid } from '../../utils'
import CompSetting from './loop-moving-image-setting.vue'
import Comp from './loop-moving-image.vue'

// 类型
export const type = 'loop-moving-image'
export const defalutPlayMode = 'loop'
// 数据类型约束
export interface IDesignLoopMovingImage extends IDesignLayer {
  type: typeof type
  playMode?: string
  data: {
    duration: number
    src: string
    autoRotate: boolean
  }
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.common,
    type,
    name: '循环左右运动图像',
    thumbnail: new URL('loop-moving-image.png', import.meta.url).href,
    Comp,
    CompSetting,
    async defaultData(options) {
      const src = (options as IDesignLoopMovingImage)?.data?.src || await openSelectMaterial()
      if (!src) return
      const { width, height } = await ResourceUtil.loadImage(src, { draftScale: 0.2 })

      return merge({
        uuid: layerUuid(),
        name: '循环左右运动图像',
        type,
        style: {
          width: `${width * 5}px`,
          height: `${height}px`,
        },
        data: {
          duration: 5,
          src,
          autoRotate: true,
        },
      }, options as IDesignLoopMovingImage)
    },
  })
}
