export function isBaoliwei() {
  const urls = ['game.polyv.net', 'game.polyv.cn', 'test-game.polyv.cn']
  return urls.includes(location.hostname)
}

// 微赞
export function isVzan() {
  const urls = ['game.vzan.com', 't-game.vzan.com', 'game-pre.vzan.com']
  return urls.includes(location.hostname)
}

// 获得场景
export function isHuodechangjing() {
  const urls = ['game.csslcloud.net', 't-game.csslcloud.net']
  return urls.includes(location.hostname)
}

export function isLiveAgent() {
  return isBaoliwei() || isVzan() || isHuodechangjing()
}
