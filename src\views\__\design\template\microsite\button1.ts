import type { IDesignTemplateSetup } from '../../types'
import { layerUuid } from '../../utils'

export function setup(app: IDesignTemplateSetup) {
  app.registry({
    label: '互动按钮',
    thumbnail: new URL(`./button1.png`, import.meta.url).href,
    defaultData() {
      return {
        type: 'group',
        uuid: layerUuid(),
        templateId: `template-A14njl6uo1`,
        name: '互动按钮',
        style: {
          width: '100px',
          height: '100px',
          left: '25px',
          top: '217px',
          background: 'rgba(255, 255, 255, 0.2)',
          borderRadius: '4px',
        },
        layers: [
          {
            uuid: layerUuid(),
            name: '名称',
            type: 'text',
            data: '​活动介绍',
            style: {
              width: '100px',
              height: '30px',
              left: 0,
              top: '66px',
              lineHeight: 2,
              textAlign: 'center',
              color: 'rgb(255, 255,255)',
            },
          },
          {
            uuid: layerUuid(),
            name: '图标',
            data: '//res3.hixianchang.com/qn/lib/microsite/icon/intro.png',
            type: 'image',
            color: 'rgb(255, 255,255)',
            style: {
              width: '40px',
              height: '40px',
              left: '30px',
              top: '18px',
            },
          },
        ],
      }
    },
  })
}
