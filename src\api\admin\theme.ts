import { HiRequest } from '../request'
import { themeUpgrade } from '../themeUpgrade'

export default {
  read: (params: any) => themeUpgrade(HiRequest.post('/pro/hxc/protheme/read.htm', params)),
  readByWallId: (params: any) => themeUpgrade(HiRequest.post('/pro/hxc/protheme/readByWallId.htm', params)),
  update: (params: any) => HiRequest.post('/pro/hxc/protheme/update.htm', params),
  add: (params: any) => HiRequest.post('/pro/hxc/protheme/add.htm', params),
}
