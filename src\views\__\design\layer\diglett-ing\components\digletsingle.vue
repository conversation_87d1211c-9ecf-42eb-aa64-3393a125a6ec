<script setup lang="ts">
import type { CSSProperties } from 'vue'
import type { BehitItem } from '../diglett-ing'
import { timer } from '@/utils'
import { playAudio } from '~/src/utils/audio'

const props = defineProps<{
  size: number
  beHitList: BehitItem[]
  behitBtomPos?: number
  holeImg?: string
  hitImg?: string
  scoreColor?: string
  hitShake?: boolean
  bownImg?: string
  behitHide?: boolean
  successAudio?: string
  failAudio?: string
  isMobile?: boolean
}>()

const emits = defineEmits<{
  (event: 'updateScore', score: number): void
}>()

const stopFalg = ref<boolean>(true)
type Status = '' | 'in' | 'out'
const aniName = ref<Status>('')

const chuiziDown = ref<boolean>(false)
const beHited = ref<boolean>(false)
const showScore = ref<boolean>(false)
const showBown = ref<boolean>(false)
const hideBehit = ref<boolean>(false)
const hitItemIndex = ref<number>(0)
const hitItem = computed(() => props.beHitList[hitItemIndex.value] || {
  img_normal: '',
  img_yun: '',
  score: 0,
})

watch(() => beHited.value, (n, o) => {
  if (!n && o && props.behitHide) {
    hideBehit.value = true
  }
})

const objectStyle = computed(() => {
  if (!aniName.value) return {}
  let img = beHited.value ? hitItem.value.img_yun : hitItem.value.img_normal
  if (hideBehit.value) {
    img = ''
  }
  const style: CSSProperties = {
    backgroundImage: `url(${img})`,
    animationName: `diglett-ani-${aniName.value}`,
    animationTimingFunction: 'cubic-bezier(0.63, 0.46, 0.21, 1)',
    animationDuration: '.7s',
    animationIterationCount: '1',
    animationFillMode: 'forwards',
  }
  return style
})
let interIn: NodeJS.Timeout | undefined
let interOut: NodeJS.Timeout | undefined
async function endFn() {
  hideBehit.value = false
  if (stopFalg.value) {
    aniName.value = ''
    return
  }
  switch (aniName.value) {
    case '':
      aniName.value = 'in'
      break
    case 'in':
      interIn && clearInterval(interIn)
      interIn = setInterval(() => {
        aniName.value = 'out'
        clearInterval(interIn)
      }, 1000)
      break
    case 'out':
      setItem()
      aniName.value = ''
      interOut && clearInterval(interOut)
      interOut = setInterval(() => {
        aniName.value = 'in'
        clearInterval(interOut)
      }, Math.random() * 2000 + 1000)
      break
    default:
      break
  }
}
function hitSuccess() {
  emits('updateScore', hitItem.value!.score)
}

async function beHitStart() {
  if (beHited.value || chuiziDown.value) return
  chuiziDown.value = true
  if (aniName.value === 'in') {
    interIn && clearInterval(interIn)
    hitSuccess()
    props.successAudio && playAudioFn(props.successAudio)
    beHited.value = true
    showScore.value = true
    showBown.value = true
    await timer(500)
    showBown.value = false
    await timer(500)
    beHited.value = false
    aniName.value = 'out'
  } else {
    props.failAudio && playAudioFn(props.failAudio)
  }
}
function beHitEnd() {
  chuiziDown.value = false
}

function setItem() {
  hitItemIndex.value = Math.floor(Math.random() * props.beHitList.length)
}

const wrapStyle = computed(() => {
  const style: CSSProperties = {
    '--width': `${props.size}px`,
    '--object-wrap-bottom': `${props.behitBtomPos}%`,
    '--color': props.scoreColor,
    '--score-font-size': `${Math.min(40, Math.ceil(props.size / 3.5))}px`,
  }
  return style
})

const dongStyle = computed(() => {
  const img = props.holeImg
  const style: CSSProperties = {
    backgroundImage: `url(${img})`,
  }
  return style
})
const chuiziStyle = computed(() => {
  const img = props.hitImg
  const style: CSSProperties = {
    backgroundImage: `url(${img})`,
  }
  return style
})

const bownStyle = computed(() => {
  const style: CSSProperties = {
    backgroundImage: `url(${props.bownImg})`,
  }
  return style
})

function playAudioFn(audiourl: string) {
  if (props.isMobile) {
    playAudio(audiourl).play()
  }
}

defineExpose({
  hitIt: beHitStart,
})

onMounted(async () => {
  setItem()
  await timer(Math.random() * 3000 + 1000)
  stopFalg.value = false
  aniName.value = 'in'
})

onUnmounted(() => {
  stopFalg.value = true
  interIn && clearInterval(interIn)
  interOut && clearInterval(interOut)
})
</script>

<template>
  <div class="item-box" :style="wrapStyle">
    <div class="dong" :style="dongStyle"></div>
    <div class="object-wrap">
      <div class="object" :style="objectStyle" @animationend="endFn" @click="beHitStart">
        <div v-if="showBown && bownImg" :style="bownStyle" class="bown"></div>
      </div>
    </div>
    <div v-if="showScore" class="score" @animationend="showScore = false">{{ `+${hitItem?.score}` }}</div>
    <div v-if="chuiziDown" :style="chuiziStyle" :class="[hitShake ? 'cuizi' : 'chuizi2']" @animationend="beHitEnd"></div>
  </div>
</template>

<style scoped lang="scss">
.item-box {
  --width: 100px;
  --height: calc(var(--width) * 1);
  --object-wrap-bottom: 18%;
  // 锤子偏移位置（提供参数控制）
  --cuizi-offset-x: 10%;
  --cuizi-offset-y: -50%;
  --cuizi-rotate: 60deg;

  width: var(--width);
  height: var(--height);
  position: relative;
  display: flex;
  justify-content: center;
  align-items: flex-end;

  > div {
    position: absolute;
  }

  .dong {
    width: var(--width);
    aspect-ratio: 3 / 2;
    background-repeat: no-repeat;
    background-position: bottom center;
    background-size: contain;
    z-index: 1;
  }
  .object-wrap {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    width: calc(var(--width) * 2);
    height: calc(var(--height) * 1.5);
    border-radius: 0 0 100% 100%;
    bottom: var(--object-wrap-bottom);
    z-index: 2;
    overflow: hidden;
    .object {
      transform: translateY(100%);
      aspect-ratio: 4 / 5;
      width: var(--width);
      background-repeat: no-repeat;
      background-position: center bottom;
      background-size: contain;
    }
    .bown {
      width: calc(var(--width) * 0.8);
      height: calc(var(--height) * 0.8);
      position: absolute;
      top: 20%;
      left: 50%;
      transform: translate(-50%, -20%);
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
    }
  }
  .score {
    position: absolute;
    top: 0;
    left: 50%;
    z-index: 9;
    font-size: var(--score-font-size, 30px);
    color: var(--color);
    animation-name: score-ani;
    animation-duration: 0.7s;
    animation-iteration-count: 1;
  }
  .cuizi {
    width: calc(var(--width) * 1.3);
    aspect-ratio: 1 / 1;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    z-index: 3;
    transform-origin: 100% 70%;
    animation-name: diglett-ani-rotate;
    animation-duration: 0.3s;
    animation-iteration-count: 1;
    animation-direction: alternate;
    animation-timing-function: cubic-bezier(1, 0.8, 0.1, 1);
  }
  .chuizi2 {
    width: calc(var(--width) * 1.3);
    aspect-ratio: 1 / 1;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    z-index: 3;
    transform-origin: 100% 100%;
    animation-name: diglett-ani-rotate2;
    animation-duration: 0.3s;
    animation-iteration-count: 1;
    transform: translate3d(var(--cuizi-offset-x), var(--cuizi-offset-y), 0) rotateZ(0deg);
  }
  @keyframes score-ani {
    0% {
      transform: translateY(50%) scale(0.8) rotate(0deg);
    }
    50% {
      transform: translateY(0) scale(1) rotate(-20deg);
    }
    100% {
      transform: translateY(-100%) scale(0.8) rotate(-40deg);
      opacity: 0.5;
    }
  }
  @keyframes diglett-ani-rotate {
    0% {
      transform: translate3d(var(--cuizi-offset-x), var(--cuizi-offset-y), 0) rotateZ(var(--cuizi-rotate));
    }
    100% {
      transform: translate3d(var(--cuizi-offset-x), var(--cuizi-offset-y), 0) rotateZ(0deg);
    }
  }
  @keyframes diglett-ani-rotate2 {
    0% {
    }
    100% {
    }
  }
}
</style>

<style>
@keyframes diglett-ani-in {
  0% {
    transform: translateY(100%) scale(0.5);
  }
  100% {
    transform: translateY(0) scale(1);
  }
}
@keyframes diglett-ani-out {
  0% {
    transform: translateY(0) scale(1);
  }
  100% {
    transform: translateY(100%) scale(0.5);
  }
}
</style>
