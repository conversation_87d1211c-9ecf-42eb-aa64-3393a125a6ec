<script setup lang="ts">
import HiDesign from 'design/index.vue'
import { useDesignState, useDesignTemp } from '../__/design'
import { ModeEnum } from '../__/design/types'

definePage({ meta: { label: '调试' } })

const designTemp = useDesignTemp()
const designState = useDesignState()

const interactive = 'listlotteryv3'
designTemp.theme = {
  id: -1,
  name: '调试主题',
  module: 'shakev3',
  themeImg: '',
  webContent: JSON.stringify({
    version: 3,
    option: {
      drafts: [
        1280,
        800,
      ],
    },
    style: {},
    layers: [
      {
        type: 'piclottery-ing',
        uuid: 'layer-9vq5sat2ce',
        name: '进行中名单抽奖3d动效',
        contentStyle: [
          {
            fontSize: 27,
            fontColor: 'rgba(255, 253, 82, 1)',
            fonBold: false,
          },
          {
            fontSize: 27,
            fontColor: 'rgba(255, 253, 82, 1)',
            fonBold: true,
          },
          {
            fontSize: 27,
            fontColor: 'rgba(255, 253, 82, 1)',
            fonBold: false,
          },
        ],
        itemBgColors: [
          'rgba(247, 0, 0, 0.23)',
        ],
        style: {
          width: '1280px',
          height: '800px',
          top: '0px',
          left: '0px',
        },
        show: [
          'ing',
        ],
        animiteSpeed: 7,
        isPercent: 1,
      },
      {
        type: 'group',
        uuid: 'layer-u1m4urngst',
        layers: [
          {
            type: 'group',
            uuid: 'layer-itngw8eox2',
            layers: [
              {
                uuid: 'layer-y3wil6goka',
                name: '轮次标题',
                type: 'text',
                data: '#轮次标题#',
                style: {
                  width: '339px',
                  height: '48px',
                  left: '254px',
                  top: '0px',
                  color: 'rgba(255, 253, 82, 1)',
                  fontSize: '26px',
                  fontWeight: 'bold',
                },
                show: [
                  'ready',
                  'finish',
                  'winlist',
                ],
                spoVisible: true,
              },
              {
                uuid: 'layer-wq5fntjflr',
                name: '轮次标题背景',
                data: '//res3.hixianchang.com/qn/material/2290848/up/921b039b80cb2bf27007686f25a4163c.png',
                type: 'image',
                style: {
                  width: '845px',
                  height: '35px',
                  left: '0px',
                  top: '22px',
                },
                ratio: 24.84,
                show: [
                  'ready',
                  'winlist',
                  'finish',
                ],
              },
            ],
            name: '轮次标题',
            style: {
              left: '166px',
              top: '0px',
              width: '845px',
              height: '57px',
            },
            show: [
              'ready',
              'winlist',
              'finish',
            ],
          },
          {
            type: 'group',
            uuid: 'layer-fv399gir3t',
            layers: [
              {
                uuid: 'layer-n0sewtbdy6',
                name: '标题',
                imgData: '//res3.hixianchang.com/qn/material/0/9771e7e07034701269809a8b14550f1d.png',
                type: 'img-video',
                style: {
                  width: '256px',
                  height: '53.152542372881356px',
                  left: '467px',
                  top: '0px',
                },
                ratio: 4.82,
                show: [],
              },
              {
                uuid: 'layer-oql8p4prgi',
                name: '奖品名称',
                type: 'text',
                data: '#奖品名称#',
                style: {
                  width: '384px',
                  height: '240px',
                  left: '398px',
                  top: '62px',
                  color: 'rgba(255, 253, 82, 1)',
                  fontWeight: 'bold',
                  fontSize: '20px',
                },
                show: [
                  'finish',
                  'winlist',
                ],
              },
              {
                uuid: 'layer-95qc1dtw8o',
                name: '关闭名单',
                imgData: '//res3.hixianchang.com/qn/material/2290848/up/10a8554da36804a183e94c912ddea302.png',
                type: 'img-video',
                style: {
                  width: '40px',
                  height: '40px',
                  left: '568px',
                  top: '605px',
                },
                ratio: 1,
                show: [
                  'finish',
                  'winlist',
                ],
                events: [
                  {
                    event: 'click',
                    type: 'business',
                    value: 'hideWinlist',
                    keyboard: ' ',
                  },
                ],
              },
              {
                uuid: 'layer-4grbdgonvv',
                name: '名单抽奖结果',
                type: 'winning-list3',
                style: {
                  width: '1180px',
                  height: '565px',
                  left: '0px',
                  top: '29px',
                  background: 'rgba(0, 0, 0, 0.4)',
                  padding: '112px 20px 20px 20px',
                  borderRadius: '15px',
                },
                data: {
                  contentStyle: [
                    {
                      fontColor: 'rgba(255, 253, 82, 1)',
                      fonBold: true,
                      fontSize: 14,
                    },
                    {
                      fontColor: 'rgba(255, 253, 82, 1)',
                      fonBold: true,
                      fontSize: 14,
                    },
                    {
                      fontColor: 'rgba(255, 253, 82, 1)',
                      fonBold: true,
                      fontSize: 14,
                    },
                  ],
                  bgColor: 'rgba(0, 0, 0, 0.2)',
                  itemGap: 24,
                },
                show: [
                  'finish',
                  'winlist',
                ],
              },
            ],
            name: '中奖名单',
            style: {
              left: '0px',
              top: '42px',
              width: '1180px',
              height: '670px',
              background: '',
            },
            show: [
              'finish',
              'winlist',
            ],
          },
        ],
        name: '中奖名单',
        style: {
          left: '51px',
          top: '63px',
          width: '1180px',
          height: '712px',
        },
        show: [
          'finish',
          'winlist',
        ],
        animate: [
          {
            type: 'In',
            animate: 'animate__zoomInDown',
          },
          {
            type: 'Out',
            animate: 'animate__zoomOut',
          },
        ],
      },
      {
        uuid: 'layer-m5o9wtmea7',
        name: '启动中',
        imgData: '//res3.hixianchang.com/qn/up/e2af46e6baab630dd54df67583e5ee1e.png',
        type: 'img-video',
        style: {
          width: '1320px',
          height: '800px',
          left: '0px',
          top: '0px',
        },
        show: [
          'staring',
        ],
        bgType: 'video',
        videoData: '//res3.hixianchang.com/qn/material/0/4e478fe8f764fb45366dcd9d3edc4040.mp4',
        isPercent: 1,
      },
      {
        type: 'group',
        uuid: 'layer-xxq2z64imf',
        layers: [
          {
            type: 'group',
            uuid: 'layer-zxqamar44k',
            layers: [
              {
                uuid: 'layer-iruj0jju6h',
                name: '按钮',
                imgData: '//res3.hixianchang.com/qn/material/2290848/up/4d4e50e410e974c17865031ee5b34a67.png',
                type: 'img-video',
                style: {
                  width: '26px',
                  height: '26px',
                  left: '134px',
                  top: '5px',
                },
                ratio: 1,
                show: [
                  'ready',
                ],
                events: [
                  {
                    event: 'click',
                    type: 'business',
                    value: 'showWinlist',
                    keyboard: '',
                  },
                ],
              },
              {
                uuid: 'layer-5m6clm0d84',
                name: '人文字',
                type: 'text',
                data: '#总中奖人数#',
                style: {
                  width: '96px',
                  height: '61px',
                  left: '83px',
                  top: '0px',
                  color: 'rgba(255, 253, 82, 1)',
                  textAlign: 'left',
                  fontWeight: 'bold',
                },
                show: [
                  'ready',
                ],
              },
              {
                uuid: 'layer-gp8it708jq',
                name: '中奖名单文字',
                type: 'text',
                data: '中奖名单：',
                style: {
                  width: '96px',
                  height: '61px',
                  left: '0px',
                  top: '1px',
                  color: 'rgba(255, 253, 82, 1)',
                  textAlign: 'right',
                  fontSize: '14px',
                },
                show: [
                  'ready',
                ],
              },
            ],
            name: '中奖名单',
            style: {
              left: '767px',
              top: '666px',
              width: '179px',
              height: '62px',
            },
            show: [
              'winlist',
              'ready',
            ],
          },
          {
            type: 'group',
            uuid: 'layer-q1x8yyk3ra',
            layers: [
              {
                uuid: 'layer-0rm24coe88',
                name: '人文字',
                type: 'text',
                data: '人',
                style: {
                  width: '29px',
                  height: '33px',
                  left: '156px',
                  top: '0px',
                  color: 'rgba(255, 253, 82, 1)',
                  fontSize: '14px',
                },
                show: [
                  'ready',
                ],
                lock: false,
              },
              {
                uuid: 'layer-6py2z4xlsa',
                name: '参与人数文字',
                type: 'text',
                data: '参与人数：',
                style: {
                  width: '111px',
                  height: '37px',
                  left: '0px',
                  top: '0px',
                  color: 'rgba(255, 253, 82, 1)',
                  textAlign: 'right',
                  fontSize: '14px',
                },
                show: [
                  'ready',
                ],
                lock: false,
              },
              {
                uuid: 'layer-e88rmfiprn',
                name: '数量',
                type: 'text',
                data: '#参与人数#',
                style: {
                  'width': '60px',
                  'height': '33px',
                  'left': '101px',
                  'top': '1px',
                  'color': 'rgba(255, 253, 82, 1)',
                  'background': '',
                  'borderRadius': '20px',
                  'lineHeight': 1.2,
                  '-webkit-text-stroke': '0px ',
                  'fontWeight': 'bold',
                  'fontSize': '17px',
                },
                show: [
                  'ready',
                ],
                isPercent: 0,
                events: [
                  {
                    event: 'click',
                    type: 'business',
                    value: null,
                  },
                ],
              },
            ],
            name: '参与人数',
            style: {
              left: '318px',
              top: '667px',
              width: '185px',
              height: '37px',
            },
            show: [
              'ready',
            ],
          },
          {
            uuid: 'layer-040kxfau69',
            name: '开始按钮',
            imgData: '//res3.hixianchang.com/qn/material/2290848/up/4c54934f02d01af660bbfef019950454.png',
            type: 'img-video',
            style: {
              width: '158px',
              height: '48px',
              left: '561px',
              top: '660px',
            },
            show: [
              'ready',
            ],
            events: [
              {
                event: 'click',
                type: 'business',
                value: 'start',
                keyboard: ' ',
              },
            ],
            animate: [],
            ratio: 3.29,
          },
          {
            type: 'group',
            uuid: 'layer-so68ir4noo',
            layers: [
              {
                type: 'group',
                uuid: 'layer-bxth01iwig',
                layers: [
                  {
                    uuid: 'layer-eea3kvyxv4',
                    name: '数量',
                    type: 'text',
                    data: '#每次抽取人数#',
                    style: {
                      width: '76px',
                      height: '34px',
                      left: '32px',
                      top: '0px',
                      color: 'rgba(255, 253, 82, 1)',
                      fontSize: '18px',
                      lineHeight: 1.2,
                      fontWeight: 'bold',
                    },
                    spoVisible: false,
                    show: [
                      'ready',
                    ],
                  },
                  {
                    uuid: 'layer-hkfpietpjy',
                    name: '增加',
                    data: '//res3.hixianchang.com/qn/material/2290848/up/a42b7916f63cc71d4b6cf18f9aa3d4c2.png',
                    type: 'image',
                    style: {
                      width: '26px',
                      height: '26px',
                      left: '114px',
                      top: '4px',
                    },
                    ratio: 1,
                    show: [
                      'ready',
                    ],
                    isPercent: 0,
                    spoVisible: false,
                    events: [
                      {
                        event: 'click',
                        type: 'business',
                        value: 'addOnceNum',
                        keyboard: 'ArrowDown',
                      },
                    ],
                  },
                  {
                    uuid: 'layer-qy6ivc51p6',
                    name: '减少',
                    data: '//res3.hixianchang.com/qn/material/2290848/up/2ba8af4faf02685598499f985e17c0c5.png',
                    type: 'image',
                    style: {
                      width: '26px',
                      height: '26px',
                      left: '0px',
                      top: '4px',
                    },
                    ratio: 1,
                    show: [
                      'ready',
                    ],
                    spoVisible: false,
                    events: [
                      {
                        event: 'click',
                        type: 'business',
                        value: 'minOnceNum',
                        keyboard: 'ArrowLeft',
                      },
                    ],
                  },
                ],
                name: '单次抽取',
                style: {
                  left: '0px',
                  top: '40px',
                  width: '140px',
                  height: '34px',
                  background: 'rgba(0, 0, 0, 0.36)',
                  borderRadius: '183px',
                },
                show: [
                  'ready',
                ],
              },
              {
                uuid: 'layer-0nl1mp0ubf',
                name: '单次抽取',
                type: 'text',
                data: '单次抽取',
                style: {
                  width: '135px',
                  height: '37px',
                  left: '2px',
                  top: '0px',
                  color: 'rgba(255, 253, 82, 1)',
                },
                spoVisible: true,
                show: [
                  'ready',
                ],
              },
            ],
            name: '单次抽取',
            style: {
              left: '568px',
              top: '558px',
              width: '140px',
              height: '74px',
            },
          },
          {
            uuid: 'layer-xi9gp15v34',
            name: '底部装饰',
            data: '//res3.hixianchang.com/qn/material/2290848/up/66e987d8319335f0dab0ecb3866cc518.png',
            type: 'image',
            style: {
              width: '992px',
              height: '72px',
              left: '144px',
              top: '647px',
            },
            show: [
              'ready',
            ],
          },
          {
            type: 'lottery-prize',
            uuid: 'layer-434lurt9j7',
            name: '奖品名称',
            data: [],
            style: {
              left: '438px',
              top: '116px',
              width: '402px',
              height: '105px',
              background: '',
              borderRadius: '10px',
              padding: '20px',
            },
            show: [
              'ready',
            ],
            prizeImgWidth: 1,
            prizeImgHeight: 1,
            coutFontSize: 18,
            nameColor: 'rgba(255, 253, 82, 1)',
            countColor: 'rgba(255, 253, 82, 1)',
          },
          {
            type: 'group',
            uuid: 'layer-be5g98t1yf',
            layers: [
              {
                uuid: 'layer-ojmqt1enwy',
                name: '轮次标题',
                type: 'text',
                data: '#轮次标题#',
                style: {
                  width: '339px',
                  height: '48px',
                  left: '254px',
                  top: '0px',
                  color: 'rgba(255, 253, 82, 1)',
                  fontSize: '26px',
                  fontWeight: 'bold',
                },
                show: [
                  'ready',
                  'finish',
                  'winlist',
                ],
                spoVisible: true,
              },
              {
                uuid: 'layer-x0b401d5q7',
                name: '轮次标题背景',
                data: '//res3.hixianchang.com/qn/material/2290848/up/921b039b80cb2bf27007686f25a4163c.png',
                type: 'image',
                style: {
                  width: '845px',
                  height: '35px',
                  left: '0px',
                  top: '22px',
                },
                ratio: 24.84,
                show: [
                  'ready',
                  'winlist',
                  'finish',
                ],
              },
            ],
            name: '轮次标题',
            style: {
              left: '215px',
              top: '63px',
              width: '845px',
              height: '57px',
            },
            show: [
              'ready',
            ],
          },
          {
            type: 'lottery-prize',
            uuid: 'layer-geuzbd0w3l',
            name: '奖品',
            data: [],
            style: {
              left: '437px',
              top: '175px',
              width: '402px',
              height: '393px',
              background: '',
              borderRadius: '10px',
              padding: '20px',
            },
            show: [
              'ready',
            ],
            prizeImgWidth: 100,
            prizeImgHeight: 100,
            nameFontSize: 22,
            showPrizeName: false,
            showPrizeCount: false,
          },
          {
            uuid: 'layer-0pfnasdcoj',
            name: '奖品装饰',
            data: '//res3.hixianchang.com/qn/material/1950289/up/b010bd90650c75da4581d3536ae44105.png',
            type: 'image',
            style: {
              width: '338px',
              height: '337px',
              left: '469px',
              top: '211px',
            },
            ratio: 1,
            show: [
              'ready',
            ],
          },
        ],
        name: '准备页',
        style: {
          width: '1280px',
          height: '800px',
          left: '0px',
          top: '0px',
        },
        isPercent: 0,
        show: [
          'ready',
        ],
      },
      {
        uuid: 'layer-9i1g0bksfi',
        name: '蒙版',
        type: 'empty',
        style: {
          width: '1280px',
          height: '800px',
          background: 'rgba(0, 0, 0, 0.2)',
          left: '0px',
          top: '0px',
        },
        show: [
          'ready',
        ],
        isPercent: 1,
      },
      {
        type: 'listlottery-ing3',
        uuid: 'layer-ouvbul83mk',
        name: '名单抽奖3d动效',
        contentStyle: [
          {
            fontSize: 27,
            fontColor: 'rgba(255, 253, 82, 1)',
            fonBold: false,
          },
          {
            fontSize: 27,
            fontColor: 'rgba(255, 253, 82, 1)',
            fonBold: false,
          },
          {
            fontSize: 27,
            fontColor: 'rgba(255, 253, 82, 1)',
            fonBold: false,
          },
        ],
        itemBgColors: [
          'rgba(247, 0, 0, 0.23)',
        ],
        style: {
          width: '1280px',
          height: '800px',
          top: '0px',
          left: '0px',
        },
        show: [
          'ready',
        ],
        animiteSpeed: 3,
        isPercent: 1,
      },
      {
        uuid: 'layer-v0vvql6vmd',
        name: '背景',
        data: '//res3.hixianchang.com/qn/material/2290848/up/b85be92e827aeed35cab8066f45a5f97.mp4',
        type: 'video',
        base: true,
        muted: true,
        loop: true,
        autoplay: true,
        style: {
          width: '1280px',
          height: '800px',
          left: '0px',
          top: '0px',
          filter: 'blur(0px) brightness(103%) contrast(100%) grayscale(0%) saturate(100%) hue-rotate(0deg)',
        },
        show: [
          'ready',
          'ing',
          'winlist',
          'finish',
        ],
        isPercent: 1,
        spoVisible: true,
      },
    ],
    music: [
      {
        state: 'ready',
        list: [
          {
            url: '//res3.hixianchang.com/qn/material/0/2bf25939c1983b3f5d786533218bf3d5.mp3',
            loop: true,
            uuid: '811ea5de8ad04a89a900c52a1dbf673b',
          },
        ],
      },
      {
        state: 'winlist',
        list: [
          {
            url: '//res3.hixianchang.com/qn/material/0/610159a1ded42392ecbf06f4ced03b3c.mp3',
            loop: true,
            uuid: 'a7ac11fa848f48a4b06326e1cfa51523',
          },
        ],
      },
      {
        state: 'staring',
        list: [
          {
            url: '//res3.hixianchang.com/qn/material/0/610159a1ded42392ecbf06f4ced03b3c.mp3',
            loop: true,
            uuid: 'cd5df8d0b3aa4951ac9b4e16f9b09e1f',
          },
        ],
      },
      {
        state: 'ing',
        list: [
          {
            url: '//res3.hixianchang.com/qn/material/0/610159a1ded42392ecbf06f4ced03b3c.mp3',
            loop: true,
            uuid: 'c9fed322f2ff4931885a9916a51e6b3a',
          },
        ],
      },
      {
        state: 'finish',
        list: [
          {
            url: '//res3.hixianchang.com/qn/material/0/94dc5f8c39f8adec041fa5d573f0d2ca.mp3',
            loop: true,
            uuid: 'f4966e65022744bc8dceec43b134d074',
          },
        ],
      },
    ],
  }),
  mobContent: JSON.stringify({
    version: 1,
    option: {
      drafts: [375, 820],
    },
    style: {},
    layers: [
      {
        uuid: 'layer-jrxuwda3w7',
        name: '标题',
        type: 'text-rich',
        data: '<p class="ql-align-center"><span style="font-size: 26px; color: rgb(0, 0, 0);">活动介绍</span></p>',
        style: {
          display: 'flex',
          alignItems: 'center',
          width: '253px',
          height: '66px',
          left: '61px',
          top: '23px',
          background: 'linear-gradient(90deg,rgba(255, 255, 255, 0) 0%,rgba(255, 255, 255, 0.58) 37.66%,rgba(255, 255, 255, 0.58) 72.73%,rgba(255, 255, 255, 0) 100%)',
          borderRadius: '6px',
        },
      },
      {
        uuid: 'layer-jrxuwddw7',
        name: '标题',
        type: 'text-rich',
        data: '<p class="ql-align-center"><span style="font-size: 26px; color: rgb(0, 0, 0);">哈哈哈</span></p>',
        style: {
          display: 'flex',
          alignItems: 'center',
          width: '253px',
          height: '66px',
          left: '61px',
          top: '150px',
          background: 'linear-gradient(90deg,rgba(255, 255, 255, 0) 0%,rgba(255, 255, 255, 0.58) 37.66%,rgba(255, 255, 255, 0.58) 72.73%,rgba(255, 255, 255, 0) 100%)',
          borderRadius: '6px',
        },
      },
      {
        uuid: 'layer-dhmgu1088t',
        name: '背景色',
        type: 'empty',
        style: {
          width: '375px',
          height: '820px',
          left: '0',
          top: '0',
          backgroundColor: '#34495e',
        },
      },
    ],
  }),
}

designState
  .setStatusList([
    { label: '准备中', value: 'ready' },
    { label: '倒计时', value: '321' },
    { label: '进行中', value: 'ing' },
    { label: '排行榜', value: 'finish' },
  ])
  .setLayerData({
    '#测试占位#': computed(() => 300),
    '#参与人数#': computed(() => 300),
  })

designState.status = 'ing'
designTemp.mode = ModeEnum.edit
// designTemp.mode = ModeEnum.preview
designTemp.showType = 'pcwall'
</script>

<template>
  <HiDesign :interactive="interactive" />
</template>

<style scoped lang="scss">
</style>
