<script setup lang="ts">
import type { IDesignMaterialType } from '../../../types'
import type { MaterialLinkLabel } from './editmaterial.vue'
import type { ImageMaterial } from './image.vue'
import type { MaterialLabel } from './uploaddia.vue'
import { usePage } from '~/src/hooks/usePage'
import HiAudio from './audio.vue'
import { classicsBlueIconUrls } from './data'
import HiEditmaterial from './editmaterial.vue'
import HiImage from './image.vue'
import HiLabel from './label.vue'
import HiUploaddia from './uploaddia.vue'
import HiVideo from './video.vue'

const props = defineProps<{
  materialType: IDesignMaterialType
}>()
const emit = defineEmits<{
  (e: 'selection', val: ImageMaterial): void
}>()

const actionUrl = '/pro/hxc/procommonfile/material/upload.htm'

const labelRefs = ref()
const uploadVisible = ref(false)
const labelList = ref([])
const idList = ref<number[]>([])
const menuType = ref('my_material')
const menu = [
  {
    name: '我的素材',
    value: 'my_material',
  },
]

const isMyMaterial = computed(() => menuType.value === 'my_material')
const isMusic = computed(() => props.materialType === 'MUSIC')
const isVideo = computed(() => props.materialType === 'VIDEO')
const isPic = computed(() => props.materialType === 'PIC')
const designMaterialComp = computed(() => isMusic.value ? HiAudio : isVideo.value ? HiVideo : HiImage)

if (isPic.value) {
  menu.push({
    name: '互动图标',
    value: 'icon_material',
  })
}

const { isLoading, page, pageData, refetch } = usePage({
  async queryFn(query) {
    const data = {
      ...query,
      where: {
        type: props.materialType,
        labelIdList: idList.value.length ? [idList.value.at(-1)] : undefined,
        state: isMyMaterial.value ? undefined : 'ON',
      },
      sort: {
        createDate: 'desc',
        id: 'desc',
      },
    }
    if (isMyMaterial.value) {
      return api.admin.material.pageOwn(data)
    }
    return api.admin.material.page(data)
  },
  immediate: false,
  defaultPage: {
    pageSize: 50,
  },
})

// 菜单
async function handleSwitchMenu(item: { name: string, value: string }) {
  menuType.value = item.value
  idList.value = []

  if (item.value === 'icon_material') {
    pageData.value.dataList = classicsBlueIconUrls.map(itme => ({
      id: itme.url,
      name: itme.name,
      type: 'ICON',
      state: 'ON',
      url: itme.url,
    }))
    return
  }

  await fetchLabelList()
  refetch()
}

// 标签
async function onLabelUpsert({ name, id }: { name: string, id: string }) {
  try {
    if (id) {
      await api.admin.label.updateOwn({
        where: {
          id: Number(id),
        },
        update: {
          name,
        },
      })
      ElMessage.success('修改成功')
    } else {
      await api.admin.label.addOwn({ name, type: props.materialType })
      ElMessage.success('添加成功')
    }
    await fetchLabelList()
  } catch (error) {
    console.error(error)
  }
}
async function onLabelRemove(id: number) {
  try {
    await ElMessageBox.confirm('是否删除标签', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    await api.admin.label.deleteOwn({
      where: {
        id,
      },
    })
    idList.value = []
    await fetchLabelList()
    refetch()
    ElMessage.success('删除成功')
  } catch (error) {
    console.error(error)
  }
}
async function onLabelChange(v: number[]) {
  idList.value = v
  refetch()
}
async function fetchLabelList() {
  const data: any = {
    where: {
      type: props.materialType,
    },
  }
  if (isMyMaterial.value) {
    labelList.value = await api.admin.label.listOwn(data)
    return
  }
  labelList.value = await api.admin.label.list(data)
}

// 上传素材
async function postMaterial(data: MaterialLabel) {
  const where: any = {}
  if (data.dataList?.length) {
    where.dataList = data.dataList
  }
  if (data.labelIdList?.length) {
    where.labelIdList = data.labelIdList
  }
  if (!where.dataList) return
  await api.admin.material.batchAddOwn({
    where,
  })
  refetch()
  uploadVisible.value = false
}
async function onMaterialRemove(id: string | number) {
  try {
    await ElMessageBox.confirm('是否删除素材', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    await api.admin.material.deleteOwn({
      where: {
        id,
      },
    })
    ElMessage.success('删除成功')
    refetch()
  } catch (error) {
    console.log(error)
  }
}
function confirmFn(val: ImageMaterial) {
  emit('selection', val)
}

// 编辑素材
const editLabelList = ref<MaterialLinkLabel[]>([])
const editDialogVisible = ref(false)
const editObj = ref<ImageMaterial>()
async function onMsterialEdit(item: ImageMaterial) {
  editObj.value = item
  const data = await api.admin.material.listLabel({
    where: {
      materialId: item.id,
    },
  })
  editLabelList.value = data
  await nextTick()
  editDialogVisible.value = true
}
async function onEditMsterialSave(data: any) {
  try {
    const { name, labelUpdate } = data
    if (name) {
      await api.admin.material.updateOwn({
        where: { id: editObj.value?.id },
        update: { name },
      })
      pageData.value.dataList.forEach((item: any) => {
        if (item.id === editObj.value?.id) {
          item.name = name
        }
      })
    }
    if (labelUpdate) {
      await api.admin.material.batchLabel(labelUpdate)
    }
    ElMessage.success('保存成功')
    editDialogVisible.value = false
  } catch (error) {
    console.log(error)
  }
}

async function init() {
  const fetchTagsPromise = fetchLabelList()
  const labelSelPromise = Promise.resolve()

  // if (props.materiaId) {
  //   labelSelPromise = api.admin.material.listLabel({
  //     where: {
  //       materialId: props.materiaId,
  //     },
  //   }).then((data) => {
  //     idList.value = data.map((item: any) => item.labelId)
  //     labelRefs.value?.resetLabelRefFn(idList.value)
  //   })
  // }

  await Promise.all([fetchTagsPromise, labelSelPromise])
  refetch()
}

onMounted(() => {
  init()
})
</script>

<template>
  <div class="material-box">
    <div class="material-btns">
      <div>
        <p v-for="item of menu" :key="item.value">
          <el-button
            class="mb-10 h-40 w-100"
            :type="menuType === item.value ? 'primary' : ''"
            :text="menuType !== item.value"
            @click="handleSwitchMenu(item)"
          >
            {{ item.name }}
          </el-button>
        </p>
      </div>
      <p v-if="isMyMaterial">
        <el-button class="mb-10 w-100" type="primary" @click="uploadVisible = true">
          <span>上传素材</span><icon-ph-plus-bold class="ml-3 size-15" />
        </el-button>
      </p>
    </div>
    <!-- 音乐素材 -->
    <div v-loading="isLoading" class="right-box">
      <HiLabel
        v-if="menuType !== 'icon_material'"
        ref="labelRefs"
        :label-list="labelList"
        :menu-type="menuType"
        @upsert="onLabelUpsert"
        @change="onLabelChange"
        @remove="onLabelRemove"
      />
      <el-scrollbar v-if="pageData.dataList" class="w-full">
        <component
          :is="designMaterialComp"
          :menu-type="menuType"
          :material-list="pageData.dataList"
          @selection="confirmFn"
          @delete="onMaterialRemove"
          @edit="onMsterialEdit"
        ></component>
      </el-scrollbar>
      <hi-page v-if="menuType !== 'icon_material'" v-model="page" layout="total, prev, pager, next, jumper" />
    </div>
    <HiUploaddia
      v-if="uploadVisible"
      :label-list="labelList"
      :material-type="materialType"
      :action-url="actionUrl"
      @cancle="uploadVisible = false"
      @save="postMaterial"
    />
    <HiEditmaterial
      v-if="editDialogVisible"
      :edit-obj="editObj"
      :label-list="labelList"
      :sel-label-list="editLabelList"
      @cancle="editDialogVisible = false"
      @save="onEditMsterialSave"
    />
  </div>
</template>

<style scoped lang="scss">
.material-box {
  height: 70vh;
  display: flex;
  position: relative;

  .material-btns {
    min-height: 200px;
    border-right: 1px solid #e6ebed;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 10px;
  }

  .right-box {
    flex: 1;
    display: flex;
    position: relative;
    overflow: hidden;
    flex-direction: column;
  }
}

.progress-container {
  --progress-bar-width: 0;

  width: 100%;
  height: 5px;
  background-color: #e6ebed;
  border-radius: 2.5px;
  overflow: hidden;
  margin-top: 5px;

  .progress-bar {
    height: 100%;
    background-color: #1261ff;
    width: var(--progress-bar-width);
  }
}
</style>
