<script setup lang="ts">
import type { IDesignSelect } from './select'
import { useDataAttr, useDesignState } from '../..'

const layer = defineModel<IDesignSelect>('layer', { required: true })

const designState = useDesignState()

const allBindKeys = computed(() => {
  return Object.keys(designState.layerData || [])?.filter(key => key.startsWith('%') && key.endsWith('%')).map((key) => {
    return {
      label: key.slice(1, -1),
      value: key,
    }
  })
})

const allDataSources = computed(() => {
  return designState.getLayerData('selectDataSources') || []
})

const placeholderColorBind = useDataAttr(layer.value.data, 'placeholderColor', '#a8abb2')

const textColorBind = useDataAttr(layer.value.data, 'textColor', '#303133')
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>数据来源</h3>
        <el-select v-model="layer.data.dataSource" placeholder="请选择" style="width: 140px">
          <el-option v-for="item in allDataSources" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="setting-item">
        <h3>绑定数据</h3>
        <el-select v-model="layer.data.bindKey" placeholder="请选择" style="width: 140px">
          <el-option v-for="item in allBindKeys" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="setting-item">
        <h3>默认颜色</h3>
        <hi-color v-model="placeholderColorBind" />
      </div>
      <div class="setting-item">
        <h3>选中颜色</h3>
        <hi-color v-model="textColorBind" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
