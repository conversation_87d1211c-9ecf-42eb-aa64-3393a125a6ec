<script setup lang="ts">
import { faker } from '@faker-js/faker/locale/zh_CN'

interface IRanking {
  id: number
  name: string
  avatar: string
  score: number
}

const list: IRanking[] = []
for (let i = 0; i < 10; i++) {
  list.push({
    id: faker.number.int({ min: 10, max: 100 }),
    name: faker.person.fullName(),
    avatar: '',
    score: faker.number.int({ min: 10, max: 100 }),
  })
}
console.log(JSON.stringify(list))
console.clear()

const list1 = ref<IRanking[]>([
  { id: 23, name: '师奕辰', avatar: '', score: 49 },
  { id: 73, name: '相刚', avatar: '', score: 43 },
  { id: 99, name: '溥熙瑶', avatar: '', score: 87 },
  // { id: 92, name: '彭馥君', avatar: '', score: 68 },
  // { id: 97, name: '罕国芳', avatar: '', score: 89 },
  { id: 20, name: '买英', avatar: '', score: 22 },
  { id: 36, name: '浑诗雨', avatar: '', score: 96 },
  { id: 96, name: '公冶志明', avatar: '', score: 85 },
  { id: 91, name: '蓝民', avatar: '', score: 59 },
  { id: 74, name: '虞玉珍', avatar: '', score: 22 },
])

const list2 = ref<IRanking[]>([
  { id: 99, name: '溥熙瑶', avatar: '', score: 90 },
  { id: 57, name: '潭一诺', avatar: '', score: 89 },
  { id: 73, name: '相刚', avatar: '', score: 48 },
  { id: 89, name: '贰语桐', avatar: '', score: 69 },
  { id: 20, name: '买英', avatar: '', score: 30 },
  { id: 91, name: '蓝民', avatar: '', score: 70 },
  { id: 15, name: '后浩', avatar: '', score: 50 },
  { id: 81, name: '纳喇洁', avatar: '', score: 29 },
  { id: 96, name: '公冶志明', avatar: '', score: 100 },
  { id: 72, name: '展斌', avatar: '', score: 56 },
  { id: 76, name: '展斌2', avatar: '', score: 56 },
])

// 首次时自动生成相应数量的坑位，然后根据新数据进行填充
// const copyList: (IRanking | null)[] = cloneDeep(oldList.value)
// for (let i = 0, len = 10 - copyList.length; i < len; i++) {
//   copyList.push(null)
// }

const copyList: (IRanking | null)[] = Array.from({ length: 10 }, () => null)
const resultList = ref<(IRanking | null)[]>(copyList)

function updateList(newList: IRanking[]) {
  const resultObj: Record<number, (IRanking | null)> = {}
  resultList.value.forEach((item) => {
    if (item) {
      resultObj[item.id] = item
    }
  })

  const newObj: Record<number, IRanking> = {}
  newList.forEach((item) => {
    if (newObj[item.id]) {
      console.error('id重复', item.id)
    }
    newObj[item.id] = item
  })

  const waitRemoveList: { score?: number, index: number }[] = []
  // 1.将已经存在的坑位数据进行更新
  // 2.挑出旧列表中需要移除的数据, 并排序
  resultList.value.forEach((item, index) => {
    if (item) {
      if (newObj[item.id]) {
        item.score = newObj[item.id].score
      } else {
        waitRemoveList.push({ score: item.score, index })
      }
    } else {
      waitRemoveList.push({ index })
    }
  })
  // 排序
  waitRemoveList.sort((a, b) => {
    if (!a.score || !b.score) {
      return Math.random() - 0.5
    }
    return b.score - a.score
  })

  // 3.挑出新列表不在就列表中的数据，并排序
  const waitAddList: IRanking[] = []
  newList.forEach((item) => {
    if (!resultObj[item.id]) {
      waitAddList.push(item)
    }
  })
  waitAddList.sort((a, b) => b.score - a.score)
  // 4.将新列表中的数据按照排序替换到旧列表中
  waitAddList.forEach((item) => {
    if (waitRemoveList.length) {
      const { index } = waitRemoveList.pop()!
      resultList.value[index] = item
    } else {
      console.warn('新增的多')
    }
  })
  if (waitRemoveList.length) {
    console.warn('删除的多')
    waitRemoveList.forEach(({ index }) => {
      resultList.value[index] = null
    })
  }
}

setTimeout(() => {
  updateList(list1.value)
}, 2000)
setTimeout(() => {
  updateList(list2.value)
}, 6000)
</script>

<template>
  <div class="test-box">
    <div>
      <h3>旧列表</h3>
      <ul>
        <li v-for="item in list1" :key="item.id">
          <span>{{ item.id }}</span>
          <span>{{ item.name }}</span>
          <span>{{ item.score }}</span>
        </li>
      </ul>
    </div>
    <div>
      <h3>结果列表</h3>
      <ul>
        <li v-for="(item, index) in resultList" :key="index">
          <span>{{ item?.id }}</span>
          <span>{{ item?.name }}</span>
          <span>{{ item?.score }}</span>
        </li>
      </ul>
    </div>
    <div>
      <h3>新列表</h3>
      <ul>
        <li v-for="item in list2" :key="item.id">
          <span>{{ item.id }}</span>
          <span>{{ item.name }}</span>
          <span>{{ item.score }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>

<style scoped lang="scss">
.test-box {
  display: flex;
  justify-content: center;
  gap: 10px;
  font-size: 18px;
  line-height: 26px;
  ul {
    border: 1px solid #ccc;
  }
  span {
    display: inline-block;
    width: 80px;
  }
}
</style>
