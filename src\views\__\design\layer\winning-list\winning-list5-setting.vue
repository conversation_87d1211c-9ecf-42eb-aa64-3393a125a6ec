<script setup lang="ts">
import { useDataAttr } from '../..'
import { DEFAULT_DATA, type IDesignWinningList5 } from './winning-list5'

const layer = defineModel<IDesignWinningList5>('layer', { required: true })
const gapBind = useDataAttr(layer.value.data, 'gap', DEFAULT_DATA.gap) // 间距

const backgroundColorBind = useDataAttr(layer.value.data, 'backgroundColor', DEFAULT_DATA.backgroundColor) // 卡片背景
const borderRadiusBind = useDataAttr(layer.value.data, 'borderRadius', DEFAULT_DATA.borderRadius) // 圆角
const cardWidthBind = useDataAttr(layer.value.data, 'cardWidth', DEFAULT_DATA.cardWidth) // 卡片间距
const cardHeightBind = useDataAttr(layer.value.data, 'cardHeight', DEFAULT_DATA.cardHeight) // 卡片间距

const numColorBind = useDataAttr(layer.value.data, 'numColor', DEFAULT_DATA.numColor) // 数字颜色
const numFontSizeBind = useDataAttr(layer.value.data, 'numFontSize', DEFAULT_DATA.numFontSize) // 数字字号
const numFontWeightBind = useDataAttr(layer.value.data, 'numFontWeight', DEFAULT_DATA.numFontWeight) // 数字加粗

const cardBorderShowBind = useDataAttr(layer.value.data, 'cardBorderShow', DEFAULT_DATA.cardBorderShow) // 是否显示数字边框
const cardBorderColorBind = useDataAttr(layer.value.data, 'cardBorderColor', DEFAULT_DATA.cardBorderColor) // 边框颜色
const cardBorderWidthBind = useDataAttr(layer.value.data, 'cardBorderWidth', DEFAULT_DATA.cardBorderWidth) // 边框粗细
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>间距 </h3>
        <el-input-number v-model="gapBind" v-input-number :min="0" :max="100" controls-position="right" />
      </div>

      <!-- 卡片外观 -->
      <h2>卡片</h2>
      <div class="setting-item">
        <h3>背景色</h3>
        <hi-color v-model="backgroundColorBind" />
      </div>
      <div class="setting-item">
        <h3>圆角</h3>
        <el-input-number v-model="borderRadiusBind" v-input-number :min="0" :max="50" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>宽度</h3>
        <el-input-number v-model="cardWidthBind" v-input-number :min="10" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>高度</h3>
        <el-input-number v-model="cardHeightBind" v-input-number :min="10" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>边框</h3>
        <el-switch v-model="cardBorderShowBind" active-text="显示" inactive-text="隐藏" />
      </div>

      <template v-if="cardBorderShowBind">
        <h2>边框</h2>
        <div class="setting-item">
          <h3>边框颜色</h3>
          <hi-color v-model="cardBorderColorBind" />
        </div>
        <div class="setting-item">
          <h3>边框宽度</h3>
          <el-input-number v-model="cardBorderWidthBind" v-input-number :min="1" :max="10" controls-position="right" />
        </div>
      </template>

      <!-- 数字文字 -->
      <h2>数字</h2>
      <div class="setting-item">
        <h3>数字颜色</h3>
        <hi-color v-model="numColorBind" />
      </div>
      <div class="setting-item">
        <h3>数字字号</h3>
        <el-input-number v-model="numFontSizeBind" v-input-number :min="10" :max="80" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>数字加粗</h3>
        <el-switch v-model="numFontWeightBind" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
