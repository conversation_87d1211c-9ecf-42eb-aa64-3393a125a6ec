import type { IMobileAwardsAwards, IMobileAwardsRecord } from 'design/layer/mobile-awards/mobile-awards.vue'
import type { ProAnswerracev3, ProAnswerracev3Config, ProAnswerracev3Subject, ProAnswerracev3Team } from '~/types/wall/answerracev3'
import type { YesOrNo } from '~/types/wall/common'
import { useDesignState, useDesignTemp } from 'design/index'
import { useImData } from '~/src/hooks/useImData'
import { randomAvatar, timer } from '~/src/utils'
import { useMobileStore } from '../../stores/useMobileStore'
import { fetchTheme } from '../../utils'
import { mockAwards, mockRecord, mockTeamList } from '../../utils/mock'
import { mockConfig, mockRound } from './mock'
import 'vant/lib/toast/style'

export function useMobileAnswerracev3() {
  const designTemp = useDesignTemp()
  const designState = useDesignState()

  const isDesignEdit = designTemp.isEdit

  designTemp.showType = 'mobile'

  const mobileStore = useMobileStore()
  const { wall, isShowAd, isWedding } = storeToRefs(mobileStore)
  const timestamp = useTimestamp()

  const route = useRoute()

  // 互动配置
  const round = ref<ProAnswerracev3>()
  const roundId = computed(() => round.value?.id || '')
  const config = ref<ProAnswerracev3Config>()

  // 微信用户信息
  const wxuser = ref()
  // 广告
  const adResponse = ref()

  // 报名信息
  const regedit = ref()
  // 奖品信息
  const awards = ref<IMobileAwardsAwards>()
  const awardCount = ref<number>()
  // 名次信息
  const record = ref<IMobileAwardsRecord>()
  // 答题信息
  const answerRecord = ref()

  // 团队
  const isTeamMode = computed(() => round.value?.joinType === 'TEAM')
  const teamList = ref<Partial<ProAnswerracev3Team>[]>([])
  const teamId = ref()

  // 是否被淘汰，轮次变化时重置
  const isOut = ref(false)

  // 是否自动提交答案
  const isAutoSubmit = computed(() => round.value?.submitType === 'AUTO_SUBMIT')

  // 题目列表
  const subjectList = ref<ProAnswerracev3Subject[]>([])

  const currentSubject = computed<ProAnswerracev3Subject | null>(() => {
    if (!round.value?.subjectId) return null
    return subjectList.value.find(item => item.id === round.value?.subjectId) || null
  })

  const currentQuestionNumber = computed(() => {
    return (subjectList.value.findIndex(item => item.id === round.value?.subjectId)) + 1
  }) // 当前题号

  const state = computed(() => {
    if (designTemp.isEdit) {
      return designState.status
    }
    // 未报名（未开始），WIN_LIST未开始，其余根据状态来
    if (!round.value?.state || round.value?.state === 'WIN_LIST' || (!regedit.value && !isOut.value && round.value?.state === 'IN')) {
      return 'ready'
    }
    return {
      NOT_STARTED: 'ready',
      WIN_LIST: 'ready',
      IN: 'ing',
      FINISH: 'finish',
    }[round.value.state]
  })

  // 题目开始时间
  const startTime = computed(() => {
    return Number(currentSubject.value?.startTime || 0)
  })

  // 看题结束时间
  const previewEndTime = computed(() => {
    return startTime.value + (round.value?.watchTime || 0) * 1000
  })

  // 答题结束时间
  const answerEndTime = computed(() => {
    if (round.value?.answerTimeType === 'UNIFIED_TIME') {
      return previewEndTime.value + (round.value?.answerTime || 0) * 1000
    }
    // 读取题目配置
    return previewEndTime.value + (currentSubject.value?.answerTime || 0) * 1000
  })

  // 题目状态，
  // 未到开始（321）、开始-看题结束（看题）、看题结束-答题结束（答题）、大于答题结束（展示答题）
  const subjectStatus = computed<'321' | 'preview' | 'answer' | 'result'>(() => {
    const now = currentSubject.value?.state === 'PAUSE' ? Date.now() : timestamp.value
    if (!startTime.value || now < startTime.value) {
      return '321'
    }
    if (now < previewEndTime.value) {
      return 'preview'
    }
    if (now < answerEndTime.value) {
      return 'answer'
    }
    return 'result'
  })

  const loadRegistration = async () => {
    if (isDesignEdit) {
      regedit.value = {}
      return
    }
    if (!roundId.value) return
    try {
      regedit.value = await api.mobile.answerracev3.regeditRead({
        where: {
          answerracev3Id: roundId.value,
        },
      })
      teamId.value = regedit.value?.teamId
      isOut.value = false // 重置被淘汰状态
      // ANSWERRACEV3_OUT 被淘汰   ANSWERRACEV3_IN 答题进行中
    } catch (err: any) {
      const state = err.data?.state
      isOut.value = state === 'ANSWERRACEV3_OUT'
    } finally {
      if (route.query.teamId && !teamId.value) {
        teamId.value = route.query.teamId
      }
    }
  }

  const submitRegistration = async (teamId = 0) => {
    if (isDesignEdit) {
      return loadRegistration()
    }
    if (!roundId.value) return
    try {
      await api.mobile.answerracev3.regeditInsert({
        answerracev3Id: roundId.value,
        teamId,
      })
      await loadRegistration()
    } catch (err: any) {
      const state = err.data?.state
      isOut.value = state === 'ANSWERRACEV3_OUT'
    }
  }
  watch(teamId, (v, o) => {
    if (isTeamMode.value && v && Number(v) !== Number(o) && state.value === 'ready') {
      submitRegistration(v)
    }
  })

  const loadTeamList = async () => {
    if (isDesignEdit) {
      teamList.value = mockTeamList()
      return
    }
    teamList.value = await api.mobile.answerracev3.teamList({
      where: {
        answerracev3Id: roundId.value,
      },
    })
  }

  // 获取题目
  const loadSubjectList = async () => {
    if (isDesignEdit) {
      return
    }
    subjectList.value = await api.mobile.answerracev3.subjectList({
      where: {
        answerracev3Id: roundId.value,
      },
    })
  }

  interface RecordReportData {
    subjectId: string | number
    optionId: string | number
    useTime: number // 毫秒
    answerResult: YesOrNo
  }

  const reportAnswer = async (data: RecordReportData) => {
    if (isDesignEdit) {
      return
    }
    try {
      answerRecord.value = await api.mobile.answerracev3.recordReport({
        answerracev3Id: roundId.value,
        ...data,
      })
    } catch (err: any) {
      const state = err.data?.state
      isOut.value = state === 'ANSWERRACEV3_OUT'
    }
  }

  const loadAnswerRecord = async (controller: AbortController | null = null) => {
    if (isDesignEdit) {
      return
    }
    if (!roundId || !round.value?.subjectId) return
    try {
      answerRecord.value = await api.mobile.answerracev3.recordRead({
        where: {
          answerracev3Id: roundId.value,
          subjectId: round.value?.subjectId,
        },
      }, { signal: controller?.signal })
    } catch (err: any) {
      const state = err.data?.state
      isOut.value = state === 'ANSWERRACEV3_OUT'
    }
  }

  async function loadAward(awardsId = 0) {
    if (isDesignEdit) {
      await timer(200)
      awards.value = mockAwards()
      return
    }
    if (!awardsId) {
      awards.value = undefined
      return
    }
    awards.value = await api.mobile.awards.read({ where: { id: awardsId } })
  }

  const loadResult = async (retry = 0) => {
    if (isDesignEdit) {
      await timer(200)
      record.value = mockRecord()
      await loadAward()
      return
    }
    if (!round.value?.subjectId) {
      return
    }
    try {
      const [answerRec, awardRec] = await Promise.all([
        api.mobile.answerracev3.recordResult({ where: { answerracev3Id: round.value.id } }),
        api.mobile.awards.recordRead({ where: { module: 'answerracev3', moduleId: round.value.id } }),
      ])
      if (awardRec) {
        Object.assign(answerRec, {
          awardsId: awardRec.awardsId,
          redpackAmount: awardRec.redpackAmount,
          status: awardRec.status,
        })
      }

      record.value = Object.assign({ count: 0, ranking: 0, redpackAmount: 0, isTeam: isTeamMode.value, teamId: regedit.value?.teamId }, answerRec)

      // 参与排名就当做不淘汰，正常显示排名
      if (round.value?.outRankSwitch === 'Y' && record.value?.isOut) {
        record.value.isOut = false
      }

      if (record.value?.awardsId) {
        await loadAward(record.value?.awardsId)
      } else {
        awards.value = undefined
        awardCount.value = (await api.mobile.awards.count({ where: { module: 'answerracev3', moduleId: round.value.id } })) ?? 0
      }
    } catch (err: any) {
      if (err?.data?.msg === '报名信息不存在') {
        awardCount.value = 0
        record.value = { count: 0, ranking: 0, redpackAmount: '', isTeam: isTeamMode.value, teamId: regedit.value?.teamId }
      } else if (retry < 5) {
        // 2^retry × 1 s
        // 为了避免活动状态是“活动处理中”不显示结果弹窗，多查询几次
        setTimeout(() => loadResult(retry + 1), 2 ** retry * 1_000)
      } else {
        console.error('loadResult failed: ', err)
      }
    }
  }

  // 主要作用是将数据暴露给图层组件
  designState
    .setStatusList([
      {
        label: '等待页',
        value: 'ready',
      },
      { label: '进行中', value: 'ing' },
      { label: '结果页', value: 'finish' },
    ])
    .setLayerData({
      // 主题 & 轮次信息
      '#主题名称#': computed(() => designTemp.theme?.name),
      '#轮次标题#': computed(() => round?.value?.title),

      // 广告 / 活动上下文
      isShowAd,
      isWedding,
      'adResponse': computed(() => adResponse.value),
      'wxuser': computed(() => wxuser.value),
      'wall': computed(() => wall.value),

      // 报名信息
      regedit,

      // 成绩信息
      record,
      // 奖品信息
      awards,
      'awardCount': computed(() => awardCount.value),
      'scoreUnit': '分',

      // 答题核心数据
      answerRecord, // 答题信息
      currentSubject,
      currentQuestionNumber,
      isAutoSubmit,
      startTime,
      previewEndTime,
      answerEndTime,
      subjectStatus,
      'isOut': computed(() => isOut.value),

      isTeamMode,
      'teamList': computed(() => teamList.value),
      'rulesText': computed(() => round?.value?.rule),

      '%teamId%': teamId,
    })
    // 图层组件事件
    .setLayerEventList([
      {
        eventId: 'back-home',
        name: '返回首页',
        value() {
          window.parent.postMessage({ type: 'iframe:backHome' }, '*')
        },
      },
      // 广告交互
      {
        eventId: 'clickAd',
        value(data) {
          window.parent.postMessage({ type: 'iframe:clickAd', data }, '*')
        },
      },
      {
        eventId: 'SHOW_AD',
        value(data) {
          window.parent.postMessage({ type: 'iframe:SHOW_AD', data }, '*')
        },
      },
      {
        eventId: 'clickAwards',
        name: '中奖记录',
        value() {
          window.parent.postMessage({ type: 'iframe:clickAwards' }, '*')
        },
      },
      // 提交答案
      {
        eventId: 'report',
        value(data) {
          reportAnswer(data)
        },
      },
    ])

  const resetAllData = () => {
    record.value = undefined
    answerRecord.value = undefined
    teamId.value = ''
    teamList.value = []
    designTemp.renderKey = Date.now()
  }

  watch(() => round.value?.themeId, themeId => fetchTheme(themeId || ''))
  watch(() => roundId.value, () => {
    if (isDesignEdit) return
    resetAllData()
  })

  watch(() => round.value?.subjectId, async (v, _o, onCleanup) => {
    if (v) {
      const controller = new AbortController()
      // 需要更新题目（题目里的开始时间变了）
      const index = subjectList.value.findIndex(item => item.id === v)
      const subject = await api.mobile.answerracev3.subjectRead({ where: { id: v } }, { signal: controller.signal })
      if (index === -1) {
        subjectList.value.push(subject)
      } else {
        subjectList.value.splice(index, 1, subject)
      }
      loadAnswerRecord(controller)
      onCleanup(() => {
        // 终止过期请求
        controller.abort()
      })
    }
  })

  // 变化监控
  watch(
    () => [state.value, roundId.value || 0] as const,
    async ([v]) => {
      if (!round.value) return

      if (v === 'ready') {
        if (isTeamMode.value && !teamList.value.length) {
          await loadTeamList()
        }
        subjectList.value = []
      }

      if (['ready', 'ing'].includes(v)) {
        await loadRegistration()
        if (!regedit.value) {
          if (v === 'ing' && round.value?.ingJoinSwitch !== 'Y') {
            return
          }
          if (!isTeamMode.value) {
            await submitRegistration()
          } else if (!teamId.value) {
            teamId.value = ''
          }
        }
      } else if (!regedit.value) {
        // 如果已经结束了还没有报名信息，这里查询一遍
        await loadRegistration()
      }

      if (v === 'ing') {
        await Promise.all([
          loadSubjectList(),
          loadAnswerRecord().catch(),
        ])
      }
      if (v === 'finish') {
        await loadResult()
      }
      designState.setStatus(state.value)
    },
    { immediate: true, deep: true },
  )

  tryOnMounted(() => {
    if (isDesignEdit) {
      // 设计模式mock数据
      config.value = mockConfig()
      round.value = mockRound()
      wxuser.value = {
        nickName: '测试用户',
        imgpath: randomAvatar(),
      }
      teamId.value = ''
    }
  })

  // 数据同步
  useImData({
    'im:answerracev3:config': config,
    'im:answerracev3': round,
    'im:wall': wall,
    'im:wxuser': wxuser,
    'im:adResponse': adResponse,
  })
}
