<script setup lang="ts">
import type { IDesignLayerEventInteract } from '../../../types'
import { useDesignState } from '../../..'

defineOptions({ label: '跳转页面' })
const deisgnState = useDesignState()

const event = defineModel<IDesignLayerEventInteract>('event', { required: true })
</script>

<template>
  <div>
    <el-select v-model="event.value!" placeholder="请选择页面">
      <el-option
        v-for="item in deisgnState.statusList"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
  </div>
</template>

<style scoped lang="scss">
</style>
