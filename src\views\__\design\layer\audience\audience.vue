<script setup lang="ts">
import type { CSSProperties } from 'vue'
import { injectScale } from '../..'
import AudienceAniSvg from './assets/ani.svg?raw'
import AudienceSvg from './assets/audience.svg?raw'
import { defaultColor, type IDesignAudience } from './audience'

const scale = injectScale()
const layer = defineModel<IDesignAudience>('layer', { required: true })
const audienceRef = ref<HTMLElement>()

const { width: audienceWidth, height: audienceHeight } = useElementSize(audienceRef)

const bgWidth = 482 * scale.value
const bgHeight = 120 * scale.value

const resultWidth = computed(() => {
  if (!audienceHeight.value) return 1

  return bgWidth * audienceHeight.value / bgHeight
})

const count = computed(() => Math.ceil(audienceWidth.value / resultWidth.value))

const itemStyle = computed(() => {
  const style: CSSProperties = {
    width: `${resultWidth.value.toFixed(0)}px`,
  }
  return style
})

const audienceImage = computed(() => {
  const fillColor = (layer.value.color || defaultColor).replace('#', '%23')
  return `data:image/svg+xml;utf8,${AudienceSvg.replace('currentColor', fillColor)}`
})

function delay() {
  return {
    animationDelay: `${Math.random() * 1000}ms`,
  }
}
</script>

<template>
  <div ref="audienceRef" class="audience-box" :style="{ color: layer.color || defaultColor }">
    <div v-for="item in count" :key="item" class="item-box" :style="itemStyle">
      <img class="bg" :src="audienceImage" />
      <div class="ani-box" :style="delay()">
        <div class="ani" v-html="AudienceAniSvg"></div>
        <img class="logo" :src="layer.data" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.audience-box {
  position: relative;
  height: 100%;
  display: flex;
  overflow: hidden;
}
.item-box {
  position: relative;
  height: 100%;
  flex-shrink: 0;
  .bg,
  .ani-box,
  .ani {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }
  .ani-box {
    animation: wake 1s linear infinite;
  }
  .logo {
    position: absolute;
    width: 34%;
    height: 34%;
    top: 26%;
    left: 39%;
    transform: rotate(4deg);
  }
}
@keyframes wake {
  0% {
    transform: translateY(0) rotate(0deg);
  }

  25% {
    transform: translateY(20px) rotate(-15deg);
  }

  50% {
    transform: translateY(0) rotate(0deg);
  }

  75% {
    transform: translateY(20px) rotate(10deg);
  }

  to {
    transform: translateY(0) rotate(0deg);
  }
}
</style>
