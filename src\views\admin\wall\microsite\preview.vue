<script setup lang="ts">
import HiDesign from 'design/index.vue'
import { useDesignTemp } from '~/src/views/__/design'
import { ModeEnum } from '~/src/views/__/design/types'
import { useMobileMicrosite } from '~/src/views/mobile/wall/microsite'

definePage({ meta: { label: '微站' } })

const designTemp = useDesignTemp()

designTemp.mode = ModeEnum.preview
designTemp.showType = 'mobile'
designTemp.fetchTheme()

const interactive = 'microsite'

useMobileMicrosite()
</script>

<template>
  <HiDesign :interactive="interactive" />
</template>

<style scoped lang="scss">
</style>
