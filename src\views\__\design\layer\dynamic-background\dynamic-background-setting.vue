<script setup lang="ts">
import type { IDesignDynamicBackground } from './dynamic-background'
import { getDefaultMaterial, openSelectMaterial } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'

const layer = defineModel<IDesignDynamicBackground>('layer', { required: true })

async function updateMaterialFn(name: 'front' | 'back' | 'race', isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, `data.${name}`)
    : await openSelectMaterial('PIC')
  if (result || isReset) {
    layer.value.data[name] = result
  }
}
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>前景</h3>
        <div class="bgblank relative h-60 w-160">
          <MaterialThumbnail @select="updateMaterialFn('front')" @reset="updateMaterialFn('front', true)">
            <img :src="layer.data.front">
          </MaterialThumbnail>
        </div>
      </div>
      <div class="setting-item">
        <h3>背景</h3>
        <div class="bgblank relative h-60 w-160" @click="updateMaterialFn('back')">
          <img :src="layer.data.back">
        </div>
      </div>
      <div class="setting-item">
        <h3>赛道</h3>
        <div class="bgblank relative h-60 w-160" @click="updateMaterialFn('race')">
          <img :src="layer.data.race">
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
