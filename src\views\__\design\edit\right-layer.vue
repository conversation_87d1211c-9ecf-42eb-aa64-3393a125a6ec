<script setup lang="ts">
import type { IDesignGroup } from '../layer/group/group'
import type { IDesignData, ILayerTypes } from '../types'
import Sortable from 'sortablejs'
import { hasAuth } from '~/src/utils/auth'
import { useDesignData, useDesignTemp } from '..'
import { checkGroupVisible } from '../utils'
import RightLayerAction from './right-layer-action.vue'
import RightLayerItem from './right-layer-item.vue'

const designData = useDesignData()
const designTemp = useDesignTemp()

const hasActionAuth = computed(() => hasAuth(['man', 'oem', 'spo.hi.vip', 'spo.ft']))

const rightLayerRef = ref()
const allSortableGroups: Sortable[] = []

const groupCount = computed(() => {
  let count = 0
  const calc = (layers: ILayerTypes[]) => {
    layers.forEach((layer) => {
      if (layer.type === 'group') {
        count++
        calc(layer.layers)
      }
    })
  }
  calc(designData.layers)
  return count
})

// 监听分组数量变化、顶级图层数量变化，重新初始化拖拽
watch(() => [groupCount.value, designData.layers.length], () => {
  sortableDestroy()
  nextTick(() => {
    sortableInit()
  })
})

function sortableInit() {
  const dom = rightLayerRef.value
  const groupElements = dom.querySelectorAll('.group')

  groupElements.forEach((groupElement: HTMLElement) => {
    const sortable = new Sortable(groupElement, {
      group: 'shared', // 设置分组共享，允许跨分组拖拽
      animation: 150, // 设置动画时长
      swapThreshold: 0.5, // 设置交换阈值
      chosenClass: '',
      onStart(evt) {
        evt.item.classList?.add('sortable-chosen')
      },
      onEnd(evt) {
        const { item, to } = evt
        const newIndex = evt.newIndex || 0
        const uuid = item.dataset.uuid
        evt.item.classList?.remove('sortable-chosen')
        // 记录的下标信息
        const oldPosition = item.dataset.index ? item.dataset.index.split(',').map(Number) : []
        const newPosition = to.dataset.index ? to.dataset.index.split(',').map(Number) : []

        const fromDeleteIndex = oldPosition[oldPosition.length - 1]
        let itemToMove: ILayerTypes | IDesignData['layers'] | null = null // 需要移动的元素
        let fromItem: IDesignData['layers'] | null = designData.layers // 来源，需要删除对应下标数据
        let toItem: IDesignData['layers'] | null = designData.layers // 目标，需要添加对应下标数据

        while (oldPosition.length) {
          const index = oldPosition.shift()
          if (!fromItem || index === undefined) {
            break
          }
          if (oldPosition.length !== 0) {
            fromItem = (fromItem[index] as IDesignGroup)?.layers
          } else {
            itemToMove = fromItem[index]
          }
        }

        while (newPosition.length) {
          const index = newPosition.shift()
          if (!toItem || index === undefined) {
            break
          }
          toItem = (toItem[index] as IDesignGroup)?.layers
        }

        if (fromDeleteIndex > newIndex) {
          fromItem.splice(fromDeleteIndex, 1)
        }

        if (itemToMove) {
          toItem?.splice(newIndex, 0, itemToMove)
        }

        if (fromDeleteIndex <= newIndex) {
          fromItem.splice(fromDeleteIndex, 1)
        }

        if (uuid) {
          designTemp.activeList = [uuid]
        }
      },
    })
    allSortableGroups.push(sortable)
  })
}

function sortableDestroy() {
  allSortableGroups.forEach((sortable) => {
    sortable?.destroy?.()
  })
  allSortableGroups.splice(0, allSortableGroups.length)
}

const renderLayers = computed(() => {
  return designData.layers
    .map((item, index) => {
      return {
        item,
        index,
      }
    })
    .filter(({ item }) => {
      if (hasActionAuth.value) {
        return true
      }
      if (item.type === 'group') {
        // 如果是组，判断组内是否有显示权限的图层
        return checkGroupVisible(item as IDesignGroup)
      }
      return item.spoVisible
    })
})

// VIP会员的身份状态获取存在延迟，通过监听权限变化，重新初始化拖拽
watch(() => hasActionAuth.value && rightLayerRef.value, (value) => {
  if (value) {
    sortableDestroy()
    nextTick(() => {
      sortableInit()
    })
  } else {
    sortableDestroy()
  }
})

onUnmounted(() => {
  sortableDestroy()
})
</script>

<template>
  <div ref="rightLayerRef" class="right-layer">
    <RightLayerAction class="sticky left-0 right-0 top-0 z-1000" />

    <div class="group">
      <RightLayerItem
        v-for="({ item, index }) in renderLayers"
        :key="item.uuid"
        v-model:layer="designData.layers[index]"
        :index="[index]"
        :depth="0"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.group {
  --border-color: #eee;
  padding-bottom: 20px;
  :deep() {
    .sortable-chosen {
      .group {
        display: none !important;
      }
    }
  }
}
</style>
