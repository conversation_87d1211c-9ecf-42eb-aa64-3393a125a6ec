<script setup lang="ts">
import type { Howl } from 'howler/dist/howler.core.min'
import type { CSSProperties } from 'vue'
import type { IDesignMusic } from '../../types'
import { cloneDeep } from 'lodash-es'
import { uuid } from '~/src/utils'
import { provideElementSize, useDesignData, useDesignState } from '../..'
import HiAudio from './__/audio.vue'

provideElementSize()

interface Mark {
  style: CSSProperties
  label: string
}
type Marks = Record<number, Mark | string>

const defaultValues: any = {
  url: '',
  loop: false,
  playCount: 1,
  volume: 1,
  rate: 1,
  delay: 0,
  offset: 0,
  duration: 0,
}

const designState = useDesignState()
const designData = useDesignData()

const musicPlayer = ref<Howl | null>(null)
const fold = ref<Record<string, boolean>>({})
const trim = ref<Record<string, number[]>>({})
const marks = ref<Record<string, Marks>>({})

const designMusicData = ref<IDesignMusic[]>([])
initMusicData()

watch(() => designMusicData.value, (music) => {
  let musicData = cloneDeep(music)
  musicData.forEach((item) => {
    item.list.forEach((listItem) => {
      Object.keys(listItem).forEach((key) => {
        if (listItem[key as keyof typeof listItem] == null || listItem[key as keyof typeof listItem] === defaultValues[key]) {
          delete listItem[key as keyof typeof listItem]
        }
      })
    })
    // 如果list是空的，就删除
    item.list = item.list.filter(listItem => listItem.url)
  })

  // 如果所有的list都是空的，就删除
  musicData = musicData.filter(item => item.list.length > 0)

  designData.music = musicData
}, {
  deep: true,
  immediate: true,
})

function loaded(e: Howl, item: any) {
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`
  }
  // 确保只初始化一次
  if (!marks.value[item.uuid]) {
    if (item.offset || item.duration) {
      const start = Math.round((item.offset / 1000 / e.duration()) * 100)
      const end = Math.round(((item.offset + item.duration) / 1000 / e.duration()) * 100)
      trim.value[item.uuid] = [start, end]
    }
  }
  musicPlayer.value = e
  marks.value[item.uuid] = { 0: '0:00', 100: formatTime(e.duration()) }
}

function selection(val: string, list: any, index: number) {
  if (designMusicData.value) {
    list[index].url = val
  }
}

function formatTooltipForVolume(e: string | number) {
  return `${Math.round(Number(e) * 100)}%`
}

function formatAndTrimTooltip(e: string | number) {
  if (!musicPlayer.value) return ''
  const time = Math.round((Number(e) / 100) * musicPlayer.value?.duration())
  const minutes = Math.floor(time / 60)
  const seconds = time % 60
  return `${minutes}:${seconds < 10 ? `0${seconds}` : seconds}`
}

function marksChange(v: any, list: any[], index: number) {
  if (!musicPlayer.value) return
  const [start, end] = v
  const duration = musicPlayer.value.duration()
  const offset = Math.round((start / 100) * duration * 1000)
  const durationMs = Math.round((end / 100) * duration * 1000) - offset
  list[index].offset = offset
  list[index].duration = durationMs
}

function handleMusicAdd(data: IDesignMusic) {
  fold.value[data.state] = true
  data.list.push({
    ...defaultValues,
    uuid: uuid(),
  })
}

function handleMusicRemove(data: any, index: number) {
  musicPlayer.value?.stop()
  data.list.splice(index, 1)
}

function handleDragStart(event: DragEvent, index: number) {
  event.dataTransfer?.setData('text/plain', index.toString())
}

function handleDrop(event: DragEvent, data: IDesignMusic) {
  const draggedIndex = event.dataTransfer?.getData('text/plain')
  if (draggedIndex !== null) {
    const target = event.currentTarget as HTMLElement
    const targetIndex = Number(target.getAttribute('data-index'))
    const draggedItem = data.list.splice(Number(draggedIndex), 1)[0]
    data.list.splice(targetIndex, 0, draggedItem)
  }
}

function handleDragOver(event: DragEvent) {
  event.preventDefault()
}

function initMusicData() {
  designMusicData.value = designData.music
    ? cloneDeep(designData.music).map(item => ({
        ...item,
        list: item.list.map(listItem => ({
          ...defaultValues,
          ...listItem,
          uuid: listItem.uuid || uuid(),
        })),
      }))
    : []

  if (!designMusicData.value.length) {
    designMusicData.value = designState.statusList.map(item => ({
      state: item.value,
      list: [
        {
          ...defaultValues,
          uuid: uuid(),
        },
      ],
    }))
  } else {
    // 如果没有state的，就添加
    const existingStates = new Set(designMusicData.value.map(music => music.state))
    designState.statusList.forEach((item) => {
      if (!existingStates.has(item.value)) {
        designMusicData.value.push({
          state: item.value,
          list: [
            {
              ...defaultValues,
              uuid: uuid(),
            },
          ],
        })
      }
    })
    // 按照状态排序
    designMusicData.value.sort((a, b) => {
      return designState.statusList.findIndex(item => item.value === a.state)
        - designState.statusList.findIndex(item => item.value === b.state)
    })
  }
}

watch(
  () => designData.$ready,
  (v) => {
    if (v) {
      initMusicData()
    }
  },
)

onMounted(() => {
  if (designState.statusList.length) {
    fold.value[designState.statusList[0].value] = true
  }
})
</script>

<template>
  <div class="h-full">
    <div v-for="data in designMusicData" :key="data.state" class="tab-setting">
      <div class="tab-title" @click="fold[data.state] = !fold[data.state]">
        <span>{{ designState.statusMap[data.state]?.label }}音效</span>
        <div class="cursor-pointer">
          <el-tooltip content="添加音效" placement="top">
            <icon-ph-plus class="cursor-pointer" @click.stop="handleMusicAdd(data)" />
          </el-tooltip>
          <span class="inline-block w-20 text-right">
            <icon-ph-caret-down-bold :style="[fold[data.state] ? 'transform: rotate(180deg);' : '']" />
          </span>
        </div>
      </div>
      <el-collapse-transition>
        <div v-show="fold[data.state]">
          <div
            v-for="(item, index) in data.list"
            :key="item.uuid"
            :data-index="index"
            draggable="true"
            @dragstart="(event) => handleDragStart(event, index)"
            @drop="(event) => handleDrop(event, data)"
            @dragover="handleDragOver"
          >
            <div class="tab-content">
              <div class="flex justify-between">
                <h4>音效{{ index + 1 }}</h4>
                <el-tooltip content="删除" placement="top">
                  <icon-ph-trash class="cursor-pointer" @click="handleMusicRemove(data, index)" />
                </el-tooltip>
              </div>
              <HiAudio
                class="my-10"
                :url="item.url"
                :loop="item.loop"
                :play-count="item.playCount"
                :volume="item.volume"
                :rate="item.rate"
                :trim="trim[item.uuid]"
                @selection="v => selection(v, data.list, index)"
                @loaded="v => loaded(v, item)"
              />
              <template v-if="item.url">
                <div class="item-setting">
                  <span class="name">循环播放</span>
                  <el-switch v-model="item.loop" :active-value="true" :inactive-value="false" />
                </div>
                <!-- 播放次数 -->
                <div v-if="!item.loop" class="item-setting">
                  <span class="name">播放次数</span>
                  <el-input-number v-model="item.playCount" v-input-number class="w-80!" controls-position="right" :min="1" :max="100" />
                </div>
                <!-- 延迟时间 -->
                <div class="item-setting">
                  <span class="name">延迟时间（ms）</span>
                  <el-input-number v-model="item.delay" v-input-number class="w-80!" controls-position="right" :min="0" />
                </div>
                <!-- rate -->
                <div class="item-setting">
                  <span class="name">播放速度（n）</span>
                  <el-input-number v-model="item.rate" v-input-number class="w-80!" controls-position="right" :step="0.1" :min="0.1" :max="4" />
                </div>
                <div class="mb-15">
                  <span class="name">音量</span>
                  <div class="slider-block">
                    <el-slider v-model="item.volume" :step="0.01" :min="0" :max="1" :format-tooltip="formatTooltipForVolume" />
                  </div>
                </div>
                <!-- {{ trim[item.uuid] }} -->
                <div v-if="marks[item.uuid]" class="mb-15">
                  <span class="name">截取</span>
                  <div class="slider-block">
                    <el-slider v-model="trim[item.uuid]" range :marks="marks[item.uuid]" size="small" :format-tooltip="formatAndTrimTooltip" @change="(v) => marksChange(v, data.list, index)" />
                  </div>
                </div>
              </template>
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </div>
  </div>
</template>

<style scoped lang="scss">
.tab-setting {
  width: 100%;
  height: 100%;
  .tab-title {
    width: 100%;
    height: 32px;
    background: #f5f7fb;
    font-size: 12px;
    color: #333;
    font-weight: 500;
    line-height: 32px;
    padding: 0 16px;
    display: flex;
    justify-content: space-between;
    cursor: pointer;
  }
  .tab-content {
    padding: 8px 16px 20px;
    outline: 1px dashed #ebeef5;
  }
}

.item-setting {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}

.slider-block {
  width: 95%;
  display: flex;
  align-items: center;
  ::v-deep() {
    .el-slider__button {
      height: 12px !important;
      width: 12px !important;
    }
  }
}
.slider-block .el-slider {
  margin-top: 0;
  margin-left: 12px;
}
</style>
