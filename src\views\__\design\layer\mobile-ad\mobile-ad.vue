<script setup lang="ts">
import type { IDesignMobileAd } from './mobile-ad'
import { defineCustomEmits, useDesignState, useDesignTemp } from '../../index'

const layer = defineModel<IDesignMobileAd>('layer', { required: true })

const designState = useDesignState()
const designTemp = useDesignTemp()
const customEmits = defineCustomEmits(layer)

const adResponse = computed(() => {
  return designState.getLayerData('adResponse') || {}
})

const isShowAd = computed(() => {
  return designState.getLayerData('isShowAd')
})

function onEmitClickAd() {
  customEmits('clickAd', Object.assign({}, toRaw(adResponse.value), { adStatus: 'click' }))
}

watch(() => isShowAd.value, (val) => {
  if (val) {
    customEmits('SHOW_AD', toRaw(adResponse.value))
  }
}, { immediate: true })
</script>

<template>
  <div
    v-if="isShowAd && (adResponse.imageUrl || designTemp.isEdit)"
    class="mobile-ad-box size-full"
    :class="{
      'bg-[rgba(227,227,227,0.8)]': designTemp.isEdit,
    }"
    @click="onEmitClickAd"
  >
    <img v-if="adResponse.imageUrl" class="ad" :src="adResponse.imageUrl" alt="">
  </div>
</template>

<style scoped lang="scss">
.mobile-ad-box {
  width: 100%;
  cursor: pointer;
  img.ad {
    width: 100%;
    height: 100%;
  }
}
</style>
