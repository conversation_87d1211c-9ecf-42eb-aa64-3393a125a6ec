<script setup lang="ts">
import HiDesign from 'design/index.vue'
import { useDesignTemp } from '~/src/views/__/design'
import { useMobileMicrosite } from '~/src/views/mobile/wall/microsite'
import { usePcwallMicrosite } from '~/src/views/pcwall/microsite'

definePage({ meta: { label: '微站' } })

const interactive = 'microsite'
const designTemp = useDesignTemp()

watch(
  () => designTemp.showType,
  (v) => {
    if (v === 'mobile') {
      useMobileMicrosite()
    } else {
      usePcwallMicrosite()
    }
  },
  { immediate: true },
)
</script>

<template>
  <HiDesign :interactive="interactive" />
</template>

<style scoped lang="scss">
</style>
