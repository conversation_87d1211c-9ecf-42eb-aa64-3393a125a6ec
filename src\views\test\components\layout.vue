<script lang="ts" setup>
import { locale } from '@/utils/element-plus'
import 'uno.css'

const router = useRouter()
// 筛选出所有 /test/three/ 开头的路由，然后将路由分组后形成菜单并排序
const rootRoutes = router.getRoutes().filter(i => i.path.startsWith('/test/'))

const groupRoutes = rootRoutes.reduce((prev, cur) => {
  const path = cur.path.split('/')
  const key = path[2]
  let value = path[3]
  if (!value) {
    value = key
  }
  // 字符串只保留数字
  const valueNumber = value.replace(/\D/g, '')

  const meta = cur.meta
  if (meta.noMenu) return prev

  if (!prev[key])
    prev[key] = []
  prev[key].push({
    title: meta.label || value,
    to: cur.path,
    // @ts-expect-error
    sort: meta.sort || Number.parseInt(valueNumber) || Infinity,
  })
  return prev
}, {} as Record<string, { title: string, to: string, sort: number }[]>)

const nameAlias: Record<string, string> = {
  design: '设计器',
}

for (const [key, item] of Object.entries(groupRoutes)) {
  if (!nameAlias[key] && item[0]?.title) {
    nameAlias[key] = item[0].title
  }
}

// 遍历 nameAlias 并生成sort
const menu: { title: string, children: { title: string, to: string }[] }[] = []
Object.keys(nameAlias).forEach((key) => {
  const title = nameAlias[key] || key
  const children = groupRoutes[key] || []
  children.sort((a, b) => {
    const c = Number.parseInt(a.title) - Number.parseInt(b.title)
    if (c !== 0)
      return c
    return a.sort - b.sort
  })
  menu.push({ title, children })
})
// 检查错误
for (const key in groupRoutes) {
  if (!nameAlias[key]) {
    console.error('未设置别名', key)
  }
}
</script>

<template>
  <router-view v-if="$route.meta.hiddenLayout" />
  <div v-else class="h-full flex">
    <hi-menu :menu-tree="menu" logo-text="测试" />
    <div class="right-box flex-1">
      <el-config-provider :locale="locale">
        <router-view v-slot="{ Component, route }">
          <transition name="fade" mode="out-in" :appear="true">
            <div :key="route.path" class="content-box">
              <component :is="Component" />
            </div>
          </transition>
        </router-view>
      </el-config-provider>
    </div>
  </div>
</template>

<style scoped lang="scss">
.header-box {
  height: 64px;
  line-height: 64px;
  border-bottom: 1px solid #ccc;
  padding: 0 25px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.right-box {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.content-box {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #eef2f8;
}
</style>
