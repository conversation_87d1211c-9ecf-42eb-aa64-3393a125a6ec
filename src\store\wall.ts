import type { IWall } from '~/types/wall/wall'
import { envUtils } from '../utils/env'

export const useWallStore = defineStore('wall', () => {
  const wallConfig = ref<IWall>()
  const route = useRoute()

  const fetchWallConfig = async (wallId = route.query.wallId) => {
    if (!wallId && envUtils.isDev) {
      console.error('fetchWallConfig wallId is required')
      return
    }
    const res = await api.admin.wall.read({
      where: { id: wallId },
    })
    wallConfig.value = res
  }

  return {
    wallConfig,
    fetchWallConfig,
  }
})
