<script setup lang="ts">
import type { IDesignShape } from './shape'
import { useShapeUtils } from './shape-utils'

const layer = defineModel<IDesignShape>('layer', { required: true })

const { renderSvg } = useShapeUtils(layer)

// svg中的样式表会造成相互间影响，所以需要使用iframe来隔离
const iframeHtml = computed(() => {
  return `
    <html>
      <head>
        <style>html, body { margin: 0; padding: 0; width: 100%; height: 100%; }</style>
      </head>
      <body>${renderSvg.value || ''}</body>
    </html>
  `
})

const iframeRef = ref<HTMLIFrameElement | null>(null)
watch(
  () => [iframeRef.value, iframeHtml.value],
  () => {
    if (!iframeRef.value) return
    const iframe = iframeRef.value
    const doc = iframe.contentDocument || iframe.contentWindow?.document
    if (!doc) return
    doc.open()
    doc.write(iframeHtml.value)
    doc.close()
  },
  { immediate: true },
)
</script>

<template>
  <iframe
    ref="iframeRef"
    class="svg-box"
    frameborder="0"
    scrolling="no"
  />
</template>

<style scoped lang="scss">
.svg-box {
  width: 100%;
  height: 100%;
  pointer-events: none;
}
</style>
