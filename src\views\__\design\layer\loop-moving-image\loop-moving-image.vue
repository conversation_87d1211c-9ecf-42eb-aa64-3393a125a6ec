<script setup lang="ts">
import type { IDesignLoopMovingImage } from './loop-moving-image'
import gsap from 'gsap'
import { MotionPathPlugin } from 'gsap/MotionPathPlugin'

import { defalutPlayMode } from './loop-moving-image'

gsap.registerPlugin(MotionPathPlugin)

const layer = defineModel<IDesignLoopMovingImage>('layer', { required: true })

const duration = computed(() => layer.value.data.duration)
const src = computed(() => layer.value.data.src)
const autoRotate = computed(() => layer.value.data.autoRotate)

const loopBoxRef = useTemplateRef<HTMLDivElement>('loopBoxRef')
const imageRef = useTemplateRef<HTMLImageElement>('imageRef')

function goBackMove() {
  gsap.killTweensOf(imageRef.value)
  const maxX = (loopBoxRef.value?.offsetWidth || 0) - (imageRef.value?.offsetWidth || 0)
  gsap.set(imageRef.value, { x: 0, scaleX: 1 })
  const tl = gsap.timeline({
    repeat: -1,
  })
  // Move from x = 0 to x = maxX
  tl.to(imageRef.value, {
    x: maxX,
    duration: duration.value / 2,
    ease: 'power1.inOut',
  })
  if (autoRotate.value) {
    // Flip horizontally before moving back
    tl.set(imageRef.value, { scaleX: -1 })
  }
  // Move from x = maxX back to x = 0
  tl.to(imageRef.value, {
    x: 0,
    duration: duration.value / 2,
    ease: 'power1.inOut',
  })
  if (autoRotate.value) {
    // Flip horizontally before moving back
    tl.set(imageRef.value, { scaleX: 1 })
  }
}

const playMode = computed(() => layer.value.playMode || defalutPlayMode)

function flyOver() {
  gsap.killTweensOf(imageRef.value)
  const boxWitdh = loopBoxRef.value?.offsetWidth || 0
  const imageWidth = imageRef.value?.offsetWidth || 0
  const rightFlag = playMode.value === 'rightLeft' ? 1 : -1
  gsap.set(imageRef.value, { x: (boxWitdh * (Math.random() * 0.1 + 1) + imageWidth) * rightFlag, scaleX: 1 })
  gsap.to(imageRef.value, {
    x: -(boxWitdh - imageWidth) * rightFlag,
    duration: duration.value,
    ease: 'none',
    repeat: -1,
  })
}

function play() {
  if (playMode.value === 'loop') {
    goBackMove()
  }
  if (playMode.value === 'rightLeft') {
    flyOver()
  }
  if (playMode.value === 'leftRight') {
    flyOver()
  }
}

watch(() => [layer.value?.style.width, layer.value?.style.height, duration.value, src.value, autoRotate.value, playMode.value], () => {
  play()
})

onBeforeUnmount(() => {
  gsap.killTweensOf(imageRef.value)
})
</script>

<template>
  <div
    ref="loopBoxRef"
    class="loop-moving-image-box"
  >
    <img
      ref="imageRef"
      class="img"
      :src
      @load="play"
    >
  </div>
</template>

<style scoped lang="scss">
.loop-moving-image-box {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  overflow: hidden;
}
.img {
  max-width: 33%;
  max-height: 100%;
}
</style>
