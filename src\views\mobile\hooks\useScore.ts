// 成绩逻辑
interface UseScore {
  reportFn: (oldScore: number) => Promise<number>
  getCurScoreFn?: () => Promise<number>
  timeout?: number
}

export function useScore({
  reportFn,
  getCurScoreFn,
  timeout = 1000,
}: UseScore) {
  // 是否正在上报
  const isReporting = ref(false)
  // 等待上报分数
  const score = ref(0)
  // 当前总分, 用于页面显示 （服务端+本地临时等待上报）
  const scoreCount = ref(0)
  // 通过服务器获取分数
  const getScore = async () => {
    if (getCurScoreFn) {
      const res = await getCurScoreFn?.() as number
      scoreCount.value = res
    }
  }
  // 上报分数
  const reportScore = async () => {
    if (isReporting.value) {
      return
    }
    if (!score.value) {
      return
    }
    isReporting.value = true
    try {
      const oldScore = score.value
      const newCount = await reportFn(oldScore) as number
      if (newCount) {
        scoreCount.value = newCount
      }
      score.value = score.value - oldScore
    } catch (error) {
      console.error(error)
    } finally {
      isReporting.value = false
    }
  }
  // 分数变化定时上报
  const { pause, resume } = useIntervalFn(() => reportScore(), timeout || 2000, {
    immediate: false,
  })
  // 停止上报
  const stopReport = (finalSubmit = false) => {
    pause()
    if (finalSubmit) {
      reportScore()
    }
  }
  // 开始上报，首次需要调用
  const startReport = () => {
    reportScore()
    resume()
  }

  return {
    getScore,
    scoreCount,
    score,
    isReporting,
    stopReport,
    startReport,
  }
}
