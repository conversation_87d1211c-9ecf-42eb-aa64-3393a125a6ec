## next

全新设计器，兼容大屏幕和移动端，灵活性高，扩展性强。

```shell
http-server . \
  -p 443 \
  --cors -S \
  -C ./cert/.dev.hidanmu.com.pem \
  -K ./cert/.dev.hidanmu.com.key
```

## TODO

- [ ] 新建标签输入文字后按下回车会刷新页面参数丢失
- [ ] 颜色快捷选择，勾选最后的清空颜色实际变成了黑色
- [x] 富文本焦点问题、字号/颜色问题
- [ ] 图层选择优化，图层是分组时如果当前状态下所有子图层都不可见，分组图层是否有必要渲染？
- [ ] 分组子图层移入移出出现 NaNpx
- [x] 权限控制。右侧图层管理员增加普通用户是否可见图标（分组不允许操作）
- [x] 支持跨标签页/跨主题复制粘贴 (已实现，在feat-copy分支)
- [x] 支持粘贴时自动创建图层（文字、图片、视频等）
- [ ] 检测不允许多个窗口同时编辑同一个主题？
- [ ] 流程中必备组件保存时检测，尽量规避出现设计好无法使用的情况
- [x] 如果设置项没改就不要有该项
- [x] image-roll backgroundSize渲染错误
- [x] 图层尺寸为百分比时禁止可视化拖拽
- [x] 大屏幕上下轮切换时动画比较混乱
- [x] 竖版左侧滚动图超出（圣诞）因为图层的宽度有小数引起
- [x] 进行中中途提示加油动画(一类问题)
- [x] 左右增加百分比控制
- [x] 非必要不使用 mountedCondition
- [x] 组件的尺寸和位置交互需要优化重写，同时支持百分比或者px，支持left、right、top、bottom，如果配置了百分比尺寸最外层相对最顶层窗口，否则按照实际渲染
- [x] 切换互动轮次（状态）后，根据事件显示的图层要根据新状态判断能否显示，不能显示的图层需要隐藏
- [ ] 内部键盘事件需要映射到iframe外部
- [ ] designData中的 music 有问题，当前未配置时不应有相关配置，配置默认都应该是设置了才有，否则没有该节点
- [x] 进行中排行数量可调，默认10名
- [x] 将mode的读取放到designTemp中
- [x] 图片等素材添加前的选择需要做到组件中
- [x] 自定义组件（竖版摇一摇）
- [ ] 主题版本升级兼容方案
- [x] 富文本组件双击bug
- [ ] 用户本地时间校准
- [x] 图层事件的重复执行问题
- [x] 活动中快捷键配置
- [x] 素材库路径规范 /qn/material/0/， /qn/material/{userId}/
- [x] 移除 standard 方法
- [x] 页面设置中增加页面背景色、背景图设置
- [ ] 页面设置中增加视频设置
- [ ] design/index.ts 代码需要整理，涉及到大菠萝的依赖和history等问题
- [x] api文件名称等规范化
- [ ] 自定义组件分类划分（可以考虑根据目录为）
- [x] 图层显示前需要确认好数据
- [x] 当前项目实现简单登录，管理端和主办方的登录页面需要支持登录好后跳回
- [x] 图层被删除后，其他图层关联的事件删除
- [x] 图层动画设计有问题
- [ ] 音视频的自动播放，假如图层组件中存在自动播放的音视频打开网页后需要用户手动触发一个确认窗口
- [x] iframe通信问题解决
- [x] 视频图层
- [x] 组件分类需要根据状态选择自动筛选出需要的分类
- [ ] 自定义组件效果，区分手机端、大屏幕
- [ ] 设计器鼠标框选，支持多选图层
- [ ] 图层位置是否支持设置底部距离或者右边距离，或者fixed
- [ ] 调研图层是否支持百分比尺寸
- [ ] 滤镜（页面滤镜、图层滤镜）
- [ ] 音频
- [x] 考虑移除最外层的tranform的缩放，底层逻辑尽量避免使用缩放
- [x] custom-event 重写，使用mitt替换
- [x] 基础文本组件支持换行
- [x] 显示/隐藏选择支持分组内选择
- [x] 添加的图层，后面的状态默认可见，前面的状态默认不可见
- [x] 设计器的缩放（通过ctrl+滚轮进行缩放，通过滚轮或者shift+滚轮进行上线左右偏移移动）
- [x] 设计稿尺寸(一般不做修改)
- [x] 图层多选时拖拽某个图层，其他图层应同步拖动
- [x] 外链跳转拦截
- [x] 文本组件去除边距，支持上中下对齐，默认居中对齐，尺寸可以随意调整
- [x] 新组件的缩放问题容易被忽略
- [x] 图层上移下移，置顶置底
- [x] 形状
- [x] 富文本
- [x] 文字
- [x] 图片
- [x] 图层对齐
- [x] 图层分组
- [x] 图层动画预览
- [x] 图层触发事件完善
- [x] 图层类型归类，方便左侧出现同类替换推荐
- [x] 设计器左侧(组件、文本、图片、形状、音乐、视频)
- [x] 文本图层支持富文本形式
- [x] 添加图层
- [x] 图层对齐功能
- [x] 图层名称修改
- [x] 素材库图片选择
- [x] 辅助线、网格
- [x] 快捷键（图层 ctrl+c,ctrl+v, delete, 选中图层的上下左右移动）
- [x] 辅助线、网格
- [x] 拖拽、尺寸修改
- [x] 组件横向扩展
- [x] 素材库（考虑如何分类，尽量业务无关）
- [x] 恢复、撤销
- [x] 快捷键
- [x] 支持图层的新增、删除、修改（改名字）、复制、锁定
- [x] 透明度
- [x] 对齐
- [x] 层级（拖拽）
- [x] 根据状态和图层关系提示出图层中缺少的必备图层（不一定要内置好，只需要建主题时选择是为哪个互动创建）
- [x] 显示和隐藏
- [x] 图层组能力
- [x] 按照状态决定部分图层禁止被操作
- [x] 自定义图层需要有分类，同分类下的组件数据结构需要一致，选择图层时可以进行图层样式修改
- [x] 图层事件（内链、外链、电话、播放视频、播放音频、播放动画）
- [x] 根据业务中定义的状态进行页面切换
- [x] 业务中数据的更新需要通过组件的定义进行自定义事件或者 bus 等进行组件数据的更新

## 接口

### 管理端、后台管理

- 新窗口
- url 中携带主题 themeId

### 大屏幕、手机端

- iframe

### 接口（删除操作都需要经过二次确认）
>
> 接口调用（加密、非加密）

- 管理员
  - 素材分类查询、新增、删除、修改
  - 官方素材上传、分页查询、删除、修改（官方素材的上传目录需要特殊分隔开）

- 主办方
  - 官方素材分类查询
  - 官方素材分页查询
  - 我的素材分类查询、新增、删除、修改
  - 我的素材上传、分页查询、删除、修改

- 大屏幕（wallFlag/mobileFlag + id查询，最好有查询缓存，因为大字段影响数据库io）
  - 根据主题id查询大屏幕主题设计
- 参与者
  - 根据主题id查询移动端设计

### 素材库

```
按钮：
摇一摇: 前景、背景、赛道、参赛物、领奖台

```

### 跑流程

设计器
  超管
  主办方
  大屏幕
  移动端
素材库
  svg、图片、音频、视频
主办方管理端

#
