interface LineConfig {
  text: string
  fontSize: number
  fontFamily?: string // 可选字体族
  color: string
  maxWidth: number // 当前行的最大宽度
  overflow: 'ellipsis' | 'truncate' // 超出处理方式
  y: number // 文字基线 y 坐标
  x?: number // 文字起始 x 坐标，默认为 0
  textAlign?: CanvasTextAlign // 文本对齐方式，默认为 'left'
}

interface CanvasConfig {
  width: number
  height: number
  backgroundColor?: string
}

/**
 * 创建一个自定义的 Canvas 元素，并在上面绘制多行文本。
 * @param canvasConfig Canvas 的配置，包括宽度、高度和背景色。
 * @param lines 一个包含每行文本配置的数组。
 * @returns 生成的 HTMLCanvasElement。
 */
export function createCanvasWithText(
  canvasConfig: CanvasConfig,
  lines: LineConfig[],
): HTMLCanvasElement {
  const canvas = document.createElement('canvas')
  canvas.width = canvasConfig.width
  canvas.height = canvasConfig.height
  const ctx = canvas.getContext('2d')

  if (!ctx) {
    throw new Error('Failed to get 2D context from canvas')
  }

  // 绘制背景色
  if (canvasConfig.backgroundColor) {
    ctx.fillStyle = canvasConfig.backgroundColor
    ctx.fillRect(0, 0, canvas.width, canvas.height)
  }

  // 绘制每一行文本
  lines.forEach((line) => {
    ctx.font = `${line.fontSize}px ${line.fontFamily || 'sans-serif'}`
    ctx.fillStyle = line.color
    ctx.textAlign = line.textAlign || 'left'

    let textToDraw = line.text
    let textWidth = ctx.measureText(textToDraw).width
    const xPosition = line.x || 0

    if (textWidth > line.maxWidth) {
      if (line.overflow === 'ellipsis') {
        const ellipsis = '...'
        const ellipsisWidth = ctx.measureText(ellipsis).width
        let newText = textToDraw
        // 循环缩减文本直到适合宽度（加上省略号）
        while (
          ctx.measureText(newText + ellipsis).width > line.maxWidth
          && newText.length > 0
        ) {
          newText = newText.slice(0, -1)
        }
        // 如果缩减后为空，并且省略号本身就超宽，则尝试只显示部分省略号或不显示
        if (newText.length === 0 && ellipsisWidth > line.maxWidth) {
          if (ctx.measureText('..').width <= line.maxWidth) textToDraw = '..'
          else if (ctx.measureText('.').width <= line.maxWidth) textToDraw = '.'
          else textToDraw = ''
        } else {
          textToDraw = newText + (newText.length < line.text.length ? ellipsis : '')
        }
      } else if (line.overflow === 'truncate') {
        // 对于 'truncate'，fillText 的第四个参数 maxWidth 会自动处理截断
        // 但如果需要精确控制或确保不绘制超出部分，可以手动截断
        let newText = textToDraw
        while (ctx.measureText(newText).width > line.maxWidth && newText.length > 0) {
          newText = newText.slice(0, -1)
        }
        textToDraw = newText
      }
    }
    ctx.fillText(textToDraw, xPosition, line.y, line.overflow === 'truncate' ? line.maxWidth : undefined)
  })

  return canvas
}

// 示例用法:
/*
const myCanvas = createCanvasWithText(
  { width: 300, height: 150, backgroundColor: '#f0f0f0' },
  [
    {
      text: '第一行长文本，需要省略号处理效果展示',
      fontSize: 20,
      color: 'blue',
      maxWidth: 280,
      overflow: 'ellipsis',
      y: 30,
      x: 10,
    },
    {
      text: '第二行直接截断，不显示省略号',
      fontSize: 16,
      fontFamily: 'Arial',
      color: 'red',
      maxWidth: 150,
      overflow: 'truncate',
      y: 70,
      x: 10,
      textAlign: 'left',
    },
    {
      text: '第三行正常显示',
      fontSize: 18,
      color: 'green',
      maxWidth: 300,
      overflow: 'truncate', // 或 'ellipsis'，如果不需要处理
      y: 110,
      x: 150,
      textAlign: 'center'
    },
  ]
);
document.body.appendChild(myCanvas);
*/
