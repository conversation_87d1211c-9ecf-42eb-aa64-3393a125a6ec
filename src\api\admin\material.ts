import { HiRequest } from '../request'

export default {
  // 官方素材
  list: (params: any) => HiRequest.post('/pro/hxc/promaterial/list.htm', params),
  page: (params: any) => HiRequest.post('/pro/hxc/promaterial/page.htm', params),

  // 素材和标签的关联关系
  listLabel: (params: any) => HiRequest.post('/pro/hxc/promaterialmateriallabel/list.htm', params),
  batchLabel: (params: any) => HiRequest.post('/pro/hxc/promaterialmateriallabel/batch.htm', params),

  // 个人素材
  addOwn: (params: any) => HiRequest.post('/pro/hxc/promaterialown/add.htm', params),
  deleteOwn: (params: any) => HiRequest.post('/pro/hxc/promaterialown/delete.htm', params),
  readOwn: (params: any) => HiRequest.post('/pro/hxc/promaterialown/read.htm', params),
  listOwn: (params: any) => HiRequest.post('/pro/hxc/promaterialown/list.htm', params),
  pageOwn: (params: any) => HiRequest.post('/pro/hxc/promaterialown/page.htm', params),
  batchAddOwn: (params: any) => HiRequest.post('/pro/hxc/promaterialown/batchAdd.htm', params),
  updateOwn: (params: any) => HiRequest.post('/pro/hxc/promaterialown/update.htm', params),
}
