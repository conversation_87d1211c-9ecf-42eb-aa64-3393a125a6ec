<script setup lang="ts">
import type { IDesignRankings1 } from './rankings1'
import { getDefaultMaterial, openSelectMaterial } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'

const layer = defineModel<IDesignRankings1>('layer', { required: true })

async function updateMaterialFn(name: 'default' | 'decoration', isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, `${name}`)
    : await openSelectMaterial('PIC')
  if (result) {
    layer.value[name] = result
  } else if (isReset) {
    delete layer.value[name]
  }
}
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>起始名次</h3>
        <el-input-number v-model="layer.startRanking" v-input-number controls-position="right" :step="1" />
      </div>
      <div class="setting-item">
        <span>尺寸</span>
        <el-slider v-model="layer.itemWidth" :min="60" :max="200" :step="1" class="ml-20 flex-1" />
        <span class="w-30 text-right">{{ layer.itemWidth }}</span>
      </div>
      <div class="setting-item">
        <span>间隔</span>
        <el-input-number v-model="layer.gap" v-input-number controls-position="right" :step="1" />
      </div>
      <div class="setting-item">
        <h3>默认头像</h3>
        <div class="bgblank relative h-60 w-160">
          <MaterialThumbnail @select="updateMaterialFn('default')" @reset="updateMaterialFn('default', true)">
            <img :src="layer.default">
          </MaterialThumbnail>
        </div>
      </div>
      <div class="setting-item">
        <h3>头像背景</h3>
        <div class="bgblank relative h-60 w-160">
          <MaterialThumbnail @select="updateMaterialFn('decoration')" @reset="updateMaterialFn('decoration', true)">
            <img :src="layer.decoration">
          </MaterialThumbnail>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
