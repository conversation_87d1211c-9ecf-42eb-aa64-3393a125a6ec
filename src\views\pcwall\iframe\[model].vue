<script lang="ts" setup>
import { cloneDeep } from 'lodash-es'
import { timer } from '~/src/utils'

definePage({ meta: { label: 'iframe嵌套' } })

type Model = 'shakev3' | 'goldcoinv3' | 'diglettv3' | 'moneyv3' | 'performancev3' | 'lotteryv3' | 'listlotteryv3' | 'piclotteryv3' | 'seglottery'

const iframeRef = ref<HTMLIFrameElement>()
const route = useRoute()
const model = computed(() => (route.params as { model: Model }).model || 'shakev3')

const query: string[] = []
if (route.query.wallFlag) {
  query.push(`wallFlag=${route.query.wallFlag}`)
}

const iframeUrl = computed(() => {
  return `${location.href.split('/iframe/')[0]}/${model.value}?${query.join('&')}`
})

///////////////////
const config = ref<any>()
const round = ref<any>()
// 对接现有的长链接服务
const connectTime = ref(0)
const maxId: Record<string, number> = {}

async function fetchAllConfig() {
  const allConfig = await api.pcwall.common.allConfig({
    where: { moduleArr: [`${model.value}Config`] },
    except: ['deleteTag', 'createDate', 'updateDate'],
  })
  Object.keys(allConfig).forEach((key) => {
    if (key === 'timeStamp') {
      connectTime.value = allConfig[key]
    }
    if (key === `${model.value}Config`) {
      config.value = allConfig[key]
    }
  })
}
async function fetchRound(id: number) {
  round.value = await api.pcwall[model.value].read({ where: { id } })
}
watch(
  () => config.value?.[`${model.value}Id`],
  (v) => {
    fetchRound(v)
  },
)

async function goConnect() {
  await fetchAllConfig()
  // 启动长链接
  let valid = 1
  const next = async () => {
    try {
      const post: any = {
        timeStamp: connectTime.value,
        moduleArr: [
          { module: model.value, id: maxId[model.value] || -1, pageSize: 100 },
        ],
      }
      if (valid === 1) post.valid = 1
      const result = await api.pcwall.common.connect(post)
      if (!result) return
      if (valid === 1) valid = 0
      Object.keys(result).forEach((key) => {
        if (key === 'config') {
          fetchAllConfig()
        }
        if (key === model.value) {
          if (result[key].id) {
            maxId[key] = Math.max(maxId[key] || -1, result[key].id)
          }
          const dataList = result[key].dataList
          for (const item of dataList) {
            if (item.cmd === 'notice') {
              const data = item.data
              if (data.cmd === `pro_${model.value}_update`) {
                fetchRound(config.value?.[`${model.value}Id`])
              }
              if (data.cmd === 'pro_answerracev3_subjectupdate') {
                fetchRound(config.value?.[`${model.value}Id`])
              }
            }
            maxId[key] = Math.max(maxId[key] || -1, item.id)
          }
        }
      })
    } catch (e) {
      await timer(300)
      console.error(e)
    } finally {
      next()
    }
  }
  if (!['microsite'].includes(model.value)) {
    next()
  }
}
goConnect()

///////////////////
function postMessage(type: string, message: any) {
  const contentWindow = iframeRef.value?.contentWindow
  if (contentWindow) {
    contentWindow.postMessage({ type, message: cloneDeep(message) }, '*')
  }
}
watch(config, () => postMessage(`im:${model.value}:config`, config.value), { deep: true })
watch(round, () => postMessage(`im:${model.value}`, round.value), { deep: true })
function onMessage(
  { data }:
  { data: { type: string, message?: any } },
) {
  if (data.type === 'iframe:ready') {
    const contentWindow = iframeRef.value?.contentWindow
    if (contentWindow) {
      if (config.value) {
        postMessage(`im:${model.value}:config`, config.value)
      }
      if (round.value) {
        postMessage(`im:${model.value}`, round.value)
      }
    }
  }
}

const roundSwitchIng = ref(false)
async function switchRound(drt = 'down') {
  // 活动轮次切换
  try {
    if (roundSwitchIng.value) return
    roundSwitchIng.value = true
    const apiModule = api.pcwall[model.value]
    if (apiModule && 'switch' in apiModule) {
      await apiModule.switch({ wallFlag: round.value.wallFlag, type: drt })
    }
    await timer(300)
  } catch (e) {
    console.log(e)
  }
  roundSwitchIng.value = false
}

onKeyStroke('ArrowDown', (e) => {
  e.preventDefault()
  console.log('down')
  switchRound('down')
})

onKeyStroke('ArrowUp', (e) => {
  e.preventDefault()
  console.log('up')
  switchRound('up')
})

useEventListener('message', onMessage)

// const { isPreview } = useDesignTemp()

// const isDev = envUtils.isDev
// function switchPlay() {
//   postMessage('im:audioOpenSwitch', true)
// }
</script>

<template>
  <div class="iframe-box">
    <!-- <el-button v-if="isDev" class="absolute left-0 top-0 z-99999" @click="switchPlay">播放音效</el-button> -->
    <iframe
      ref="iframeRef"
      frameborder="0"
      scrolling="no"
      :src="iframeUrl"
    ></iframe>
  </div>
</template>

<style scoped lang="scss">
.iframe-box {
  width: 100%;
  height: 100%;
  font-size: 0px;
  overflow: hidden;
}
iframe {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
