import type { IDesignLayer, IDesignSetup } from '../../types'
import { merge } from 'lodash-es'
import { BisTypes, openSelectMaterial, ResourceUtil } from '../../'
import { layerUuid } from '../../utils'
import CompSetting from './rotate-setting.vue'
import Comp from './rotate.vue'

// 类型
export const type = 'rotate'

// 数据类型约束
export interface IDesignRotate extends IDesignLayer {
  type: typeof type
  data: {
    duration: number
    src: string
    autoRotate: boolean
  }
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.common,
    type,
    name: '旋转',
    thumbnail: new URL('rotate.png', import.meta.url).href,
    Comp,
    CompSetting,
    async defaultData(options) {
      const src = (options as IDesignRotate)?.data?.src || await openSelectMaterial()
      if (!src) return
      const { width, height } = await ResourceUtil.loadImage(src, { draftScale: 0.2 })

      return merge({
        uuid: layerUuid(),
        name: '旋转',
        type,
        style: {
          width: `${width}px`,
          height: `${height}px`,
        },
        data: {
          duration: 5,
          src,
        },
      }, options as IDesignRotate)
    },
  })
}
