import { merge } from 'lodash-es'
import { BisTypes, useDesignData } from '../../'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './mobile-money-ing-setting.vue'
import Comp from './mobile-money-ing.vue'

// 类型
export const type = 'mobile-money-ing'

// 数据类型约束
export interface IDesignMobileMoneyIng extends IDesignLayer {
  type: typeof type
  data: {
    participants: string[]
    participantWidth: number // 百分比
    autoScale: boolean // 滑动时自动缩小
    bottomImg: string
    showUp: boolean
    upImg: string
  }
}

// 注册组件
export function setup(app: IDesignSetup) {
  const designData = useDesignData()
  app.registry({
    bisType: BisTypes.sportsIng,
    showType: ['mobile'],
    type,
    status: ['ing'],
    name: '手机端-数钱',
    showInteractive: [InteractiveEnum.moneyv3],
    thumbnail: new URL('./mobile-money-ing.png', import.meta.url).href,
    Comp,
    CompSetting,
    defaultData(options): IDesignMobileMoneyIng {
      return merge({
        uuid: layerUuid(),
        name: '手机端-数钱',
        type,
        style: {
          width: `${designData.option.drafts[0] || 375}px`,
          height: `${(designData.option.drafts[1] || 630) - 60}px`,
          top: '60px',
        },
        data: {
          participants: [
            new URL('./assets/yuyuelongmen/cur1.png', import.meta.url).href,
            new URL('./assets/yuyuelongmen/cur2.png', import.meta.url).href,
            new URL('./assets/yuyuelongmen/cur3.png', import.meta.url).href,
          ],
          showUp: true,
          upImg: new URL('./assets/up.png', import.meta.url).href,
          bottomImg: new URL('./assets/yuyuelongmen/bottom-bg.png', import.meta.url).href,
          participantWidth: 60,
          autoScale: true,
        },
      }, options as IDesignMobileMoneyIng)
    },
  })
}
