<script setup lang="ts">
import type { IDesignLotteryIng1 } from './lottery-ing1'
import { getDefaultMaterial, openSelectMaterial, useDataAttr } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'
import { defaultHeadCount, defaultHeadSize, defaultPlaceHolderHeadImg, defaultrotateSpeed } from './lottery-ing1'

const layer = defineModel<IDesignLotteryIng1>('layer', { required: true })

const headCountBlind = useDataAttr(layer.value, 'headCount', defaultHeadCount)
const headSizeBlind = useDataAttr(layer.value, 'headSize', defaultHeadSize)
const rotateSpeedBlind = useDataAttr(layer.value, 'rotateSpeed', defaultrotateSpeed)

const placeHolderHeadImgBlind = computed(() => {
  return layer.value.placeHolderHeadImg ?? defaultPlaceHolderHeadImg
})

type IType = 'placeHolderHeadImg'
async function updateMaterialFn(name: IType, isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, `${name}`)
    : await openSelectMaterial('PIC')
  if (result) {
    layer.value[name] = result
  } else if (isReset) {
    delete layer.value[name]
  }
}
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>头像数量</h3>
        <el-input-number v-model="headCountBlind" v-input-number :max="1000" :min="10" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>头像大小</h3>
        <el-input-number v-model="headSizeBlind" v-input-number :max="100" :min="5" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>转动速度</h3>
        <el-input-number v-model="rotateSpeedBlind" v-input-number :max="30" :min="0" controls-position="right" />
      </div>
      <div class="setting-item mt-5!">
        <h3>头像默认图</h3>
        <div class="flex items-end">
          <div class="bgblank relative h-50 w-64">
            <MaterialThumbnail @select="updateMaterialFn('placeHolderHeadImg')" @reset="updateMaterialFn('placeHolderHeadImg', true)">
              <img v-if="placeHolderHeadImgBlind" :src="placeHolderHeadImgBlind" alt="" class="h-full w-full object-cover" />
            </MaterialThumbnail>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>
