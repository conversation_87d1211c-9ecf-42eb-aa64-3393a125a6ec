<script lang="ts" setup>
import type { IPadsignConfig } from '~/src/api/pcwall/padsign'
import { useClipboard } from '@vueuse/core'
import { useQRCode } from '@vueuse/integrations/useQRCode'
import { isVideo } from '~/src/utils'
import { envUtils } from '~/src/utils/env'
import styleImage from './assets/style.png'
import { defaultConfig } from './index'

const actConfig = defineModel<IPadsignConfig>('config', { required: true })

interface ISpecialEffect {
  name: string
  logo?: string
}
const route = useRoute()
const index = ref(-1)
const editDialog = ref(false)
const editData = ref<ISpecialEffect>()
const specialEffect = ref(JSON.parse(actConfig.value.specialEffect))
const effectOptions = ref<ISpecialEffect[]>([
  { name: 'Shape1', logo: 'https://res3.hixianchang.com/qn/up/5b34a30a5adf9d3fae71eec278c20e93.png' },
  { name: 'Shape2' },
  { name: 'Shape3' },
])
const effectImages: Record<string, { name: string, img: string }> = {
  Shape1: {
    name: '自定义logo/文字',
    img: new URL('./assets/Shape1.png', import.meta.url).href,
  },
  Shape2: {
    name: '3D球形',
    img: new URL('./assets/Shape2.png', import.meta.url).href,
  },
  Shape3: {
    name: '3D螺旋',
    img: new URL('./assets/Shape3.png', import.meta.url).href,
  },
}

const domain = window.location.origin
const wallFlag = route.query.wallFlag as string
const pcurl = `${domain}/next/pcwall/padsign?wallFlag=${wallFlag}`
const mobileurl = `${domain}/next/pcwall/padsign/pad?wallFlag=${wallFlag}`

const qrqudeImg = useQRCode(mobileurl, {
  width: 400,
  margin: 1,
})

const _fontSize = ref(JSON.parse(actConfig.value.fontSize))
watchEffect(() => {
  actConfig.value.fontSize = JSON.stringify(_fontSize.value)
})

function handleEffect(item: ISpecialEffect) {
  if (specialEffect.value.length >= 20) {
    ElMessage.error('最多只能选择20个特效')
    return
  }
  const data: { name: string, logo?: string } = {
    name: item.name,
  }
  if (item.name === 'Shape1') {
    data.logo = item.logo
  }
  specialEffect.value.push(data)
}

function handleDeleteEffect(item: ISpecialEffect) {
  specialEffect.value.splice(specialEffect.value.indexOf(item), 1)
}

function handleEditShape1(item: ISpecialEffect) {
  editData.value = item
  editDialog.value = true
  index.value = specialEffect.value.indexOf(item)
}

function handleBgImg(res: string) {
  actConfig.value.bgPc = `${envUtils.resdomain}/${res}`
}
function handleBgMobile(res: string) {
  actConfig.value.bgMobile = `${envUtils.resdomain}/${res}`
}
function handleLogo(res: string) {
  editData.value!.logo = `${envUtils.resdomain}/${res}`
}

function handleSave() {
  specialEffect.value[index.value] = editData.value
  editDialog.value = false
}

const { copy, copied } = useClipboard({ legacy: true })

function urlcopy(v: string) {
  if (!copied.value) {
    copy(v)
    ElMessage.success('复制成功')
  }
}

function open(v: string) {
  window.open(v)
}

function qrcode() {
  ElMessageBox({
    title: '二维码',
    message: h('img', { src: qrqudeImg.value, alt: '二维码', style: 'width: 250px; height: 250px;margin: 0 72px;' }),
    dangerouslyUseHTMLString: true,
  })
}

watch(specialEffect, () => {
  actConfig.value.specialEffect = JSON.stringify(specialEffect.value)
}, {
  deep: true,
})
</script>

<template>
  <div class="pb-66 pt-10">
    <el-form class="mt-20" label-width="140px" :model="actConfig" label-position="right">
      <el-divider content-position="left"><h3>签名样式</h3></el-divider>
      <el-form-item label="" label-width="60px">
        <img class="upload h-150 w-250" :src="styleImage" alt="" />
      </el-form-item>
      <el-divider content-position="left"><h3>签名设置</h3></el-divider>
      <div class="flex">
        <div class="line min-w-800">
          <el-form-item label="背景图/视频">
            <div class="flex items-end">
              <div class="upload h-150 w-250">
                <video
                  v-if="isVideo(actConfig.bgPc)"
                  class="h-full w-full"
                  :src="actConfig.bgPc "
                  autoplay
                  loop
                  muted
                />
                <img v-else class="h-full w-full" :src="actConfig.bgPc || defaultConfig.bgPc" alt="" />
              </div>
              <div class="ml-20 flex flex-col justify-end">
                <p class="mt-10">
                </p>
                <div>
                  <hi-upload-media :show-file-list="false" @success="handleBgImg">
                    <el-button type="primary">上传背景</el-button>
                  </hi-upload-media>
                </div>
                <p class="mt-10">
                  <el-button type="primary" plain @click=" actConfig.bgPc = ''">恢复默认</el-button>
                </p>
              </div>
              <div class="ml-10 flex">
                <el-tooltip effect="dark" content="建议尺寸: 1920*1080(16:9)">
                  <icon-ph-question-bold class="size-20 color-#999" />
                </el-tooltip>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="移动端背景图">
            <div class="flex items-end">
              <div class="upload h-150 w-250">
                <img class="h-full w-full" :src="actConfig.bgMobile || defaultConfig.bgMobile" alt="" />
              </div>
              <div class="ml-20 flex flex-col justify-end">
                <p class="mt-10">
                </p>
                <div>
                  <hi-upload-img :show-file-list="false" @success="handleBgMobile">
                    <el-button type="primary">上传背景</el-button>
                  </hi-upload-img>
                </div>
                <p class="mt-10">
                  <el-button type="primary" plain @click=" actConfig.bgMobile = ''">恢复默认</el-button>
                </p>
              </div>
              <div class="ml-10 flex">
                <el-tooltip effect="dark" content="建议尺寸: 1366*1024(4:3)">
                  <icon-ph-question-bold class="size-20 color-#999" />
                </el-tooltip>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="特效设置">
            <div>
              <template v-for="item in effectOptions" :key="item.name">
                <el-button type="primary" @click="handleEffect(item)">{{ effectImages[item.name].name }}<icon-ph-plus-bold class="ml-3 size-15" /></el-button>
              </template>

              <div class="mt-10">
                <div class="mt-10 gap-5 text-14 font-bold">已选择效果</div>
                <div class="w-600 flex flex-wrap gap-10">
                  <div v-for="item in specialEffect" :key="item.name" :src="item" class="relative">
                    <img class="upload h-100 w-167" :src="effectImages[item.name].img" alt="" />
                    <div class="absolute cursor-pointer -right-10 -top-4" @click="handleDeleteEffect(item)">
                      <icon-ph-x-circle-fill />
                    </div>
                    <div class="text-center">
                      {{ effectImages[item.name].name }}
                      <a v-if="item.name === 'Shape1'" class="ml-10" href="javascript:void(0);" @click.prevent="handleEditShape1(item)">编辑</a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="笔画颜色">
            <el-color-picker v-model="actConfig.fontColor" />
          </el-form-item>
          <el-form-item label="笔尖粗细">
            <div class="slider-demo-block">
              <el-slider v-model="_fontSize" range :min="1" :max="20" />
            </div>
          </el-form-item>
          <el-form-item label="签名播报动效">
            <div class="flex flex-col">
              <el-switch v-model="actConfig.animationEffectSwitch" active-value="Y" inactive-value="N" />
              <div v-if="actConfig.animationEffectSwitch === 'Y'" class="effect-box">
                <img src="./assets/huoyan.png" alt="">
              </div>
            </div>
          </el-form-item>
        </div>
        <div class="flex flex-1 flex-col">
          <el-form-item label="大屏幕地址">
            <div class="flex flex-col">
              <el-input :value="pcurl" class="w-300" placeholder="大屏幕地址" :disabled="true" />
              <div class="mt-10">
                <el-button type="primary" plain @click="urlcopy(pcurl)">复制地址</el-button>
                <el-button type="primary" plain @click=" open(pcurl)">打开大屏幕</el-button>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="手机端地址">
            <div class="flex flex-col">
              <el-input :value="mobileurl" class="w-300" placeholder="大屏幕地址" :disabled="true" />
              <div class="mt-10">
                <el-button type="primary" plain @click="urlcopy(mobileurl)">复制地址</el-button>
                <el-button type="primary" plain @click="qrcode">查看二维码</el-button>
              </div>
            </div>
          </el-form-item>
          <!-- 签名播报动效  -->
        </div>
      </div>
    </el-form>
    <el-dialog v-model="editDialog" title="编辑" width="600px">
      <el-form class="mt-20" label-width="140px" :model="editData" label-position="right">
        <el-form-item label="logo">
          <div class="flex items-end">
            <img class="upload w-250" :src="editData!.logo" alt="" />
            <div class="ml-20 flex flex-col justify-end">
              <div>
                <hi-upload-img :show-file-list="false" @success="handleLogo">
                  <el-button type="primary">上传替换</el-button>
                </hi-upload-img>
              </div>
              <p class="mt-10">
                <el-button type="primary" plain @click="editData!.logo = effectOptions[0].logo">恢复默认</el-button>
              </p>
            </div>
            <div class="ml-10 flex items-center">
              <el-tooltip effect="dark" content="建议尺寸: 240*160">
                <icon-ph-question-bold class="size-20 color-#999" />
              </el-tooltip>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSave">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.upload {
  margin-top: 10px;
  border: 1px solid #e0e0e0;
}
.line {
  border-right: 1px solid #e0e0e0;
}
.slider-demo-block {
  width: 300px;
  display: flex;
  align-items: center;
}
.slider-demo-block .el-slider {
  margin-top: 0;
  margin-left: 12px;
}
.effect-box {
  width: 150px;
  height: 150px;
  background-color: #f0f0f0;
  margin-top: 10px;
  img {
    width: 100%;
    height: 100%;
  }
}
</style>
