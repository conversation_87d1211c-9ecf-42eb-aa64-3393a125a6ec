import { BisTypes } from '../..'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './fire-ing-setting.vue'
import Comp from './fire-ing.vue'

// 类型
export const type = 'fire-ing'
export interface EnemysItem {
  score: number
  img: string
}
// 数据类型约束
export interface IDesignFireIng extends IDesignLayer {
  type: typeof type
  enemys: EnemysItem[]
  plane: string
  bullet: string
  bulletSuper?: string
  failScore: number
  data: any
  bombAudios: string[]
  bulletAudio?: string
  bulletSuperAudio?: string
  bombImgs: string[]
  enemyDensity: number // 敌人密度
  enemySpeed: number // 敌人速度
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.sportsIng,
    showType: ['pcwall', 'mobile'],
    status: ['ready', 'ing'],
    type,
    name: '射击游戏进行中',
    thumbnail: new URL('./fire-ing.png', import.meta.url).href,
    showInteractive: [InteractiveEnum.firev3],
    Comp,
    CompSetting,
    defaultData() {
      return {
        uuid: layerUuid(),
        name: '射击游戏进行中',
        type,
        enemys: [
          {
            img: new URL(`./assets/em_1.png`, import.meta.url).href,
            score: 1,
          },
          {
            img: new URL(`./assets/em_2.png`, import.meta.url).href,
            score: 2,
          },
          {
            img: new URL(`./assets/em_3.png`, import.meta.url).href,
            score: 3,
          },
        ],
        failScore: 3,
        plane: new URL(`./assets/plane.png`, import.meta.url).href,
        bullet: new URL(`./assets/pao.png`, import.meta.url).href,
        bombImgs: [
          new URL(`./assets/b1.png`, import.meta.url).href,
          new URL(`./assets/b2.png`, import.meta.url).href,
          new URL(`./assets/b3.png`, import.meta.url).href,
        ],
        bulletAudio: new URL(`./assets/default_biu.mp3`, import.meta.url).href,
        bulletSuperAudio: new URL(`./assets/bui_continuous.mp3`, import.meta.url).href,
        bombAudios: [
          new URL(`./assets/default_bomb.mp3`, import.meta.url).href,
          new URL(`./assets/default_bomb1.mp3`, import.meta.url).href,
        ],
        enemyDensity: 1,
        enemySpeed: 5,
        data: [],
        style: {
          width: '100%',
          height: '100%',
          top: '0',
          left: '0',
        },
      }
    },
  })
}
