import { HiRequest } from '../request'

export default {
  configRead: (params: any) => HiRequest.post('/pro/hxc/mobile/profirev3config/read.htm', params),
  read: (params: any) => HiRequest.post('/pro/hxc/mobile/profirev3/read.htm', params),
  ranking: (params: any) => HiRequest.post('/pro/hxc/mobile/profirev3/ranking.htm', params),

  reportRecord: (params: any) => HiRequest.post('/pro/hxc/mobile/profirev3record/report.htm', params),
  readRecord: (params: any) => HiRequest.post('/pro/hxc/mobile/profirev3record/read.htm', params),
  recordCurScore: (params: any) => HiRequest.post('/pro/hxc/mobile/profirev3record/curScore.htm', params),

  readRegedit: (params: any) => HiRequest.post('/pro/hxc/mobile/profirev3regedit/read.htm', params),
  insertRegedit: (params: any) => HiRequest.post('/pro/hxc/mobile/profirev3regedit/insert.htm', params),
  quitRegedit: (params: any) => HiRequest.post('/pro/hxc/mobile/profirev3regedit/quit.htm', params),

  listTeam: (params: any) => HiRequest.post('/pro/hxc/mobile/profirev3team/list.htm', params),
  readTeam: (params: any) => HiRequest.post('/pro/hxc/mobile/profirev3team/read.htm', params),

}
