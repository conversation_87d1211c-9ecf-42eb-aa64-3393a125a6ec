/**
 * NProgress
 */
:root {
  // --nprogress-color: #29d;
  // --nprogress-color: #f8697d;
}

/* Make clicks pass-through */
#nprogress {
  pointer-events: none;

  .bar {
    background: var(--nprogress-color);

    position: fixed;
    z-index: 1031;
    top: 0;
    left: 0;

    width: 100%;
    height: 2px;
  }

  /* Fancy blur effect */
  .peg {
    display: block;
    position: absolute;
    right: 0px;
    width: 100px;
    height: 100%;
    box-shadow:
      0 0 10px var(--nprogress-color),
      0 0 5px var(--nprogress-color);
    opacity: 1;

    -webkit-transform: rotateZ(3deg) translate(0px, -4px);
    -ms-transform: rotateZ(3deg) translate(0px, -4px);
    transform: rotateZ(3deg) translate(0px, -4px);
  }

  .spinner {
    display: block;
    position: fixed;
    z-index: 1031;
    top: 15px;
    right: 15px;
  }

  .spinner-icon {
    width: 18px;
    height: 18px;
    box-sizing: border-box;

    border: solid 2px transparent;
    border-top-color: var(--nprogress-color);
    border-left-color: var(--nprogress-color);
    border-radius: 50%;

    -webkit-animation: nprogress-spinner 400ms linear infinite;
    animation: nprogress-spinner 400ms linear infinite;
  }
}

.nprogress-custom-parent {
  overflow: hidden;
  position: relative;
}

.nprogress-custom-parent #nprogress .spinner,
.nprogress-custom-parent #nprogress .bar {
  position: absolute;
}

@-webkit-keyframes nprogress-spinner {
  0% {
    -webkit-transform: rotateZ(0deg);
  }

  100% {
    -webkit-transform: rotateZ(360deg);
  }
}

@keyframes nprogress-spinner {
  0% {
    transform: rotateZ(0deg);
  }

  100% {
    transform: rotateZ(360deg);
  }
}
