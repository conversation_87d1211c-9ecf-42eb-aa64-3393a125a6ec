import { BisTypes, useDesignState } from '../..'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './rankings2-setting.vue'
import Comp from './rankings2.vue'

// 类型
export const type = 'rankings2'

export interface IDesignRankings2 extends IDesignLayer {
  type: typeof type
  data: { name: string, avatar: string }[]
  decoration: [string, string, string]
}

export function setup(app: IDesignSetup) {
  let status: string[]
  switch (app.interactive) {
    case InteractiveEnum.shakev3:
      status = ['finish']
      break
    default:
      status = ['finish']
      break
  }

  app.registry({
    dev: true,
    showType: ['pcwall'],
    bisType: BisTypes.rankingList,
    thumbnail: new URL('./rankings2.png', import.meta.url).toString(),
    showInteractive: [InteractiveEnum.shakev3, InteractiveEnum.moneyv3, InteractiveEnum.goldcoinv3, InteractiveEnum.diglettv3, InteractiveEnum.firev3],
    type,
    name: '排行榜2',
    status,
    Comp,
    mountedCondition: computed(() => {
      const designState = useDesignState()
      const rankings = designState.getLayerData('endRankings')
      return !!rankings
    }),
    CompSetting,
    defaultData() {
      return {
        uuid: layerUuid(),
        name: '排行榜2',
        type,
        data: [],
        decoration: [
          new URL(`./assets/2_1.png`, import.meta.url).href,
          new URL(`./assets/2_2.png`, import.meta.url).href,
          new URL(`./assets/2_3.png`, import.meta.url).href,
        ],
        style: {
          width: '600px',
          height: '500px',
          top: '160px',
          left: '130px',
        },
      }
    },
  })
}
