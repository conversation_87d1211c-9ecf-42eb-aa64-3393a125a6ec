<script setup lang="ts">
import { injectScale, useDesignState, useDesignTemp } from '../../index'
import { processStyle } from '../../utils'
import { DEFAULT_DATA, type IDesignSeglotteryIng1 } from './seglottery-ing1'

const layer = defineModel<IDesignSeglotteryIng1>('layer', { required: true })
const scale = injectScale()
const designState = useDesignState()
const designTemp = useDesignTemp()

const data = computed(() => {
  return {
    ...DEFAULT_DATA,
    ...layer.value.data,
  }
})

type RollList = Array<Array<string | number>>
interface Record {
  namesList: Array<string | number>
  id: string
  [key: string]: any
}
// 参与人奖池
const rollList = computed<RollList>(() => {
  return designState.getLayerData('segRollList') || []
})
const labelList = computed<RollList>(() => {
  return designState.getLayerData('roolLabelList') || []
})
const playStatus = computed<Array<'noplay' | 'playing' | 'playover'>>(() => {
  return designState.getLayerData('playStatus') || []
})
const recordList = computed<Record[]>(() => {
  return designState.getLayerData('onceRecordList') || []
})

// 抽取人数
const onceCount = computed<number>(() => {
  return designState.getLayerData('#每次抽取人数#') || 1
})

const realCount = computed(() => {
  // 实际抽取人数
  return designTemp.isEdit ? data.value.maxRenderCount : Math.min(onceCount.value, data.value.maxRenderCount)
})

const playEndDaley = computed(() => {
  return designState.getLayerData('playEndDaley')
})

const palyIndexList = ref<number[]>([0, 0, 0])

const timeIntervalId = ref<NodeJS.Timeout | null>(null)
const rollTimeMs = computed(() => {
  return data.value.animationMode === 'slotMachine' ? 150 : 50
})

const cardStyle = computed(() => processStyle({
  backgroundColor: data.value.backgroundColor,
  borderRadius: `${data.value.borderRadius}px`,
  padding: `${data.value.cardGap / 2}px ${data.value.cardGap}px`,
  gap: `${data.value.cardGap}px`,
}, scale.value))

const rootVars = computed(() => {
  return processStyle({
    '--card-item-width': `${(data.value.labelShow ? data.value.labelWidth : 0) + data.value.numWidth}px`,
    '--label-color': data.value.labelColor,
    '--label-font-size': `${Math.round(data.value.labelFontSize)}px`,
    '--label-font-weight': data.value.labelFontWeight ? 'bold' : 'normal',
    '--label-left-margin': `${Math.round(data.value.labelLeftMargin)}px`,
    '--num-color': data.value.numColor,
    '--num-font-size': `${Math.round(data.value.numFontSize)}px`,
    '--num-font-weight': data.value.numFontWeight ? 'bold' : 'normal',
    '--num-width': `${Math.round(data.value.numWidth)}px`,
    '--num-height': `${Math.round(data.value.numHeight)}px`,
    '--num-border-color': data.value.numBorderColor,
    '--num-border-width': data.value.numBorderShow ? `${data.value.numBorderWidth}px` : 0,
    '--num-border-radius': `${Math.round(data.value.numBorderRadius)}px`,
    '--transition-duration': `${50}ms`,
  }, scale.value)
})

function startPlay() {
  timeIntervalId.value && clearInterval(timeIntervalId.value)
  timeIntervalId.value = setInterval(() => {
    playStatus.value?.forEach((status, index) => {
      if (status === 'playing') {
        if (palyIndexList.value[index] >= rollList.value[index].length - 1) {
          palyIndexList.value[index] = 0
        } else {
          palyIndexList.value[index]++
        }
      }
    })
  }, rollTimeMs.value)
}

const resultItem = computed(() => {
  return (i: number) => {
    const record = recordList.value[i]
    if (record) {
      return record.namesList || []
    } else {
      return []
    }
  }
})
watch(() => [playStatus, data.value.animationMode], () => {
  if (playStatus.value?.length) {
    startPlay()
  }
}, { immediate: true, deep: true })

onBeforeUnmount(() => {
  timeIntervalId.value && clearInterval(timeIntervalId.value)
})

const transitionName = computed(() => {
  const _animationMode = data.value.animationMode
  return (type: string) => {
    if (_animationMode !== 'slotMachine') {
      return type === 'playover' ? 'fade-over' : 'fade-up'
    }
    return type === 'playover' ? 'flip-over' : 'flip-up'
  }
})
</script>

<template>
  <div
    class="seat-card-flex"
    :style="{ gap: `${data.gap}px`, ...rootVars }"
  >
    <div
      v-for="i in realCount"
      :key="`item-${i}`"
      class="seat-card"
      :style="cardStyle"
    >
      <div
        v-for="(item, index) in rollList"
        :key="`card-${index}`"
        class="block"
      >
        <div class="num-container">
          <transition :name="transitionName('play')" mode="out-in">
            <span v-if="playStatus[index] !== 'playover'" :key="String(item[palyIndexList[index]])" class="num line-clamp-1">
              {{ playStatus[index] === 'noplay' ? '-' : item[palyIndexList[index]] }}
            </span>
          </transition>
          <transition :name="transitionName('playover')" mode="out-in">
            <span
              v-if="playStatus[index] === 'playover'"
              :key="item[palyIndexList[index]]"
              class="num line-clamp-1"
              :style="{
                'transition-delay': `${playEndDaley[index]}ms`,
              }"
            >
              {{ resultItem(i - 1)[index] }}
            </span>
          </transition>
        </div>
        <span v-if="data.labelShow" class="label">{{ labelList[index] }} </span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.seat-card-flex {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.seat-card {
  display: flex;
  align-items: center;

  .block {
    width: var(--card-item-width);
    display: flex;
    align-items: center;
    line-height: 1;
  }
  .num-container {
    flex-shrink: 0;
    width: var(--num-width);
    height: var(--num-height);
    border: var(--num-border-width) solid var(--num-border-color);
    border-radius: var(--num-border-radius);
    overflow: hidden;
  }
  .num {
    width: 100%;
    height: 100%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: var(--num-font-size);
    font-weight: var(--num-font-weight);
    color: var(--num-color);
  }

  .label {
    font-size: var(--label-font-size);
    font-weight: var(--label-font-weight, 400);
    color: var(--label-color);
    text-align: center;
    margin-left: var(--label-left-margin, 0);
    word-break: break-word;
  }
}

// Flip Up Animation
.flip-up-enter-active,
.flip-up-leave-active {
  transition: all var(--transition-duration);
}

.flip-up-enter-from {
  transform: translateY(100%);
  opacity: 0;
  transform-origin: top center;
}

.flip-up-leave-to {
  transform: translateY(-80%);
  opacity: 0;
  transform-origin: bottom center;
}

.flip-over-enter-active {
  transition: transform 600ms ease-in-out;
}
.flip-over-enter-from {
  transform: translateY(100%);
  transform-origin: top center;
}
.flip-over-leave-active {
  transition-delay: 0ms !important;
}

.fade-over-enter-active {
  transition: all 600ms ease-in-out;
}
.fade-over-enter-from {
  opacity: 0;
}
.fade-over-leave-active {
  transition-delay: 0ms !important;
}
</style>
