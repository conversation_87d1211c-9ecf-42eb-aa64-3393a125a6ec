import type { AxiosRequestConfig } from 'axios'

interface IDebugState {
  debug?: boolean
  cacheIgnore?: boolean
  policyIgnore?: boolean
  serviceHost?: string
  userDomain?: string
  localDebug?: boolean // 是否本地调试
  vConsole?: boolean // 是否开启vConsole
  system?: string // 当前访问的系统
}
const key = 'debug'
const cacheValue = JSON.parse(localStorage.getItem(key) || '{}')

export const debugState = useStorage<IDebugState>(key, cacheValue)

export function setupDebug(config: AxiosRequestConfig = {}) {
  if (!debugState || !debugState.value)
    return
  if (config && config.headers) {
    const { cacheIgnore, serviceHost, userDomain } = debugState.value
    if (cacheIgnore) {
      config.headers['x-cache-ignore'] = 'true'
    }
    if (serviceHost) {
      config.headers['x-service-host'] = serviceHost
    }
    if (userDomain) {
      config.headers['x-user-domain'] = userDomain
    }
  }
}

export function toggleDebugger(force?: boolean) {
  debugState.value.debug = force === undefined ? !debugState.value.debug : force
}
export function toggleCacheIgnore(force?: boolean) {
  debugState.value.cacheIgnore = force === undefined ? !debugState.value.cacheIgnore : force
}
export function togglePolicyIgnore(force?: boolean) {
  debugState.value.policyIgnore = force === undefined ? !debugState.value.policyIgnore : force
}
export function toggleLocalDebug(force?: boolean) {
  debugState.value.localDebug = force === undefined ? !debugState.value.localDebug : force
}

let vConsoleLoading = false
export async function toggleVConsole(force?: boolean) {
  try {
    debugState.value.vConsole = force !== undefined ? force : !debugState.value.vConsole
    if (vConsoleLoading)
      return
    vConsoleLoading = true
    // @ts-expect-error
    if (!window.vConsole && debugState.value.vConsole) {
      const lib = document.createElement('script')
      lib.src = `${import.meta.env.VITE_APP_RESDOMAIN}/qn/lib/cdn/vconsole.min.js`
      document.body.appendChild(lib)
      await new Promise(r => (lib.onload = r))
      const jsDom = document.createElement('script')
      jsDom.innerHTML = 'var vConsole = new VConsole();'
      document.body.appendChild(jsDom)
    }
    // eslint-disable-next-line no-async-promise-executor
    await new Promise<void>(async (resolve, reject) => {
      for (let i = 0; i < 100; i++) {
        // @ts-expect-error
        if (window.vConsole) {
          resolve()
          return
        }
        await new Promise(r => setTimeout(r, 100))
      }
      reject(new Error('加载vConsole环境失败'))
    })
    if (debugState.value.vConsole) {
      // @ts-expect-error
      window.vConsole?.showSwitch()
    } else {
      // @ts-expect-error
      window.vConsole?.hideSwitch()
    }
  } finally {
    vConsoleLoading = false
  }
}

// 注入 vconsole
watch(() => debugState.value?.vConsole, (v, o) => {
  if (o === undefined && localStorage.getItem('dd')) {
    toggleVConsole(true)
  } else if (v) {
    toggleVConsole(true)
  }
}, { immediate: true })
