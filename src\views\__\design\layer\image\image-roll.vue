<script setup lang="ts">
import type { CSSProperties } from 'vue'
import { useImageInfo } from '~/src/hooks/useImageInfo'
import { useDesignState } from '../..'
import { defaultBSize, defaultDirection, defaultDuration, type IDesignImageRoll } from './image-roll'

const layer = defineModel<IDesignImageRoll>('layer', { required: true })
const designState = useDesignState()
const aniBoxRef = ref<HTMLElement>()
const aniBoxSize = useElementSize(aniBoxRef)

let promiseResolve: PromiseWithResolvers<void> | undefined

const aniTempPlay = ref(false)
// 按照活动状态进行播放
const isAllowPlay = computed(() => {
  if (!layer.value.playStates?.length) return true
  return layer.value.playStates.includes(designState.status)
})
watch(
  () => isAllowPlay.value,
  (v) => {
    if (v) {
      aniTempPlay.value = true
      promiseResolve = undefined
    } else {
      if (layer.value.forceStop) {
        aniTempPlay.value = false
        return
      }
      promiseResolve = Promise.withResolvers()
      promiseResolve.promise.then(() => {
        aniTempPlay.value = false
      })
    }
  },
)

const imgPath = computed(() => {
  const url = layer.value.data
  return url
  // const width = layer.value.style.width
  // const height = layer.value.style.height
  // if (!width || !height) return url
  // return `${url}?imageView2/2/w/${Number.parseInt(`${width}`)}/h/${Number.parseInt(`${height}`)}`
})

const imgSize = useImageInfo(imgPath)

const bSize = computed(() => {
  const { backgroundSize = defaultBSize } = layer.value.style

  const arr = `${backgroundSize}`.split(' ')
  if (arr.length === 1) {
    arr.push(arr[0])
  }
  let sizeX: string | number = Number.parseFloat(arr[0])
  if (Number.isNaN(sizeX)) {
    sizeX = arr[0]
  } else if (sizeX === 0) {
    sizeX = 'auto'
  } else {
    sizeX = sizeX / 100
  }
  let sizeY: string | number = Number.parseFloat(arr[1])
  if (Number.isNaN(sizeY)) {
    sizeY = arr[1]
  } else if (sizeY === 0) {
    sizeY = 'auto'
  } else {
    sizeY = sizeY / 100
  }

  if (sizeX === 'auto' && sizeY === 'auto') {
    return 'auto'
  }
  if (sizeX === 'contain' && sizeY === 'contain') {
    return `contain`
  }

  return {
    x: sizeX,
    y: sizeY,
  }
})
const bSizeStyle = computed(() => {
  if (typeof bSize.value === 'string') {
    return bSize.value
  }
  const x = typeof bSize.value.x === 'number' ? `${bSize.value.x * 100}%` : bSize.value.x
  const y = typeof bSize.value.y === 'number' ? `${bSize.value.y * 100}%` : bSize.value.y
  return `${x} ${y}`
})

const resultSize = computed(() => {
  if (bSize.value === 'contain') {
    const box = { width: aniBoxSize.width.value, height: aniBoxSize.height.value }
    const img = { width: imgSize.width.value, height: imgSize.height.value }
    // 1. 盒子的宽高比
    const boxRatio = box.width / box.height
    // 2. 图片的宽高比
    const imgRatio = img.width / img.height

    // 3. 图片的实际宽高
    let resultWidth = 0
    let resultHeight = 0
    if (boxRatio > imgRatio) {
      // 盒子更宽
      resultWidth = box.height / img.height * img.width
      resultHeight = box.height
    } else {
      // 图片更宽
      resultWidth = box.width
      resultHeight = box.width / img.width * img.height
    }
    return { width: resultWidth, height: resultHeight }
  } else if (bSize.value === 'auto') {
    return { width: imgSize.width.value, height: imgSize.height.value }
  } else {
    // 第一个是auto，第二个是百分比，返回高度按照百分比计算，宽度按照图片比例计算
    if (bSize.value.x === 'auto') {
      return { width: aniBoxSize.height.value * (imgSize.width.value / imgSize.height.value), height: aniBoxSize.height.value }
    }
    // 第一个是百分比，第二个是auto，返回宽度按照百分比计算，高度按照图片比例计算
    if (bSize.value.y === 'auto') {
      return { width: aniBoxSize.width.value, height: aniBoxSize.width.value * (imgSize.height.value / imgSize.width.value) }
    }
    // 两个都是百分比，分别按照百分比计算
    if (typeof bSize.value.x === 'number' && typeof bSize.value.y === 'number') {
      return { width: aniBoxSize.width.value * bSize.value.x, height: aniBoxSize.height.value * bSize.value.y }
    }
    console.warn('resultSize 错误')
    return { width: 0, height: 0 }
  }
})

const direction = computed(() => layer.value.direction ?? defaultDirection)

const aniBoxStyle = computed(() => {
  // 可能出现内容比盒子还大的情况
  let widthOffset = (aniBoxSize.width.value % resultSize.value.width) / 2
  if (aniBoxSize.width.value < resultSize.value.width) {
    widthOffset = -(resultSize.value.width % aniBoxSize.width.value) / 2
  }
  let heightOffset = (aniBoxSize.height.value % resultSize.value.height) / 2
  if (aniBoxSize.height.value < resultSize.value.height) {
    heightOffset = -(resultSize.value.height % aniBoxSize.height.value) / 2
  }

  const style: CSSProperties = {
    'backgroundImage': `url(${imgPath.value})`,
    'backgroundSize': bSizeStyle.value,
    'animationName': `ani-box-${direction.value}`,
    'animationDuration': `${layer.value.duration || defaultDuration}s`,
    'animationIterationCount': 'infinite',
    'animationTimingFunction': 'linear',
    'animationPlayState': (isAllowPlay.value || aniTempPlay.value) ? 'running' : 'paused',
    '--base-width': `${layer.value.reverse ? '-' : ''}${resultSize.value.width.toFixed(2)}px`,
    '--base-width-offset': `${widthOffset.toFixed(2)}px`, // 居中
    '--base-height': `${layer.value.reverse ? '-' : ''}${resultSize.value.height.toFixed(2)}px`,
    '--base-height-offset': `${heightOffset.toFixed(2)}px`, // 居中
  }
  return style
})
function iterationFn() {
  if (promiseResolve) {
    promiseResolve.resolve()
  }
}

onMounted(() => {
  if (isAllowPlay.value) {
    aniTempPlay.value = true
  }
})
</script>

<template>
  <div ref="aniBoxRef" class="ani-box" :style="aniBoxStyle" @animationiteration="iterationFn"></div>
</template>

<style scoped lang="scss">
.ani-box {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}
</style>

<style lang="scss">
@keyframes ani-box-x {
  0% {
    background-position: var(--base-width-offset) center;
  }
  100% {
    background-position: calc(var(--base-width-offset) + var(--base-width)) center;
  }
}

@keyframes ani-box-y {
  0% {
    background-position: center var(--base-height-offset);
  }
  100% {
    background-position: center calc(var(--base-height-offset) + var(--base-height));
  }
}
</style>
