import type { DirectiveBinding } from 'vue'

// 实现效果达到img的 object-fit 的效果

let resizeObserver: ResizeObserver
export default {
  mounted: (el: HTMLElement, binding: DirectiveBinding) => {
    //
    // display
    const parent = el.parentElement
    if (!parent) {
      throw new Error('parent dom not found')
    }
    const type = binding.arg || 'contain'
    const drafts = binding.value
    const draftsRatio = drafts[0] / drafts[1]

    const change = (display: [number, number]) => {
      const displayRatio = display[0] / display[1]

      if (displayRatio === draftsRatio) {
        el.style.width = '100%'
        el.style.height = '100%'
      } else {
        switch (type) {
          case 'cover':
          case 'contain':
          case 'fill':
            if (
              (displayRatio > draftsRatio && (type === 'contain' || type === 'fill'))
              || (displayRatio < draftsRatio && type === 'cover')
            ) {
              el.style.position = 'absolute'
              el.style.width = `${display[1] * draftsRatio}px`
              el.style.height = `${display[1]}px`
              el.style.left = `${(display[0] - display[1] * draftsRatio) / 2}px`
              el.style.top = '0'
            } else {
              el.style.position = 'absolute'
              el.style.width = `${display[0]}px`
              el.style.height = `${display[0] / draftsRatio}px`
              el.style.top = `${(display[1] - display[0] / draftsRatio) / 2}px`
              el.style.left = '0'
            }
            // fill, 需要计算拉伸
            if (type === 'fill') {
              let oldTransform = el.style.transform || ''
              let scaleX = 1
              let scaleY = 1
              if (displayRatio > draftsRatio) {
                scaleX = display[0] / (display[1] * draftsRatio)
                scaleY = 1
              } else {
                scaleX = 1
                scaleY = display[1] / (display[0] / draftsRatio)
              }
              if (oldTransform.includes('scale3d')) {
                oldTransform = oldTransform.replace(/scale\(-?\d+deg\)/, `scale3d(${scaleX}, ${scaleY}, 1)`)
              } else {
                oldTransform += ` scale3d(${scaleX}, ${scaleY}, 1)`
              }
              el.style.transform = oldTransform
            }

            el.style.setProperty('--width', `${Number.parseInt(el.style.width)}px`)
            el.style.setProperty('--height', `${Number.parseInt(el.style.height)}px`)
            break
          default:
            break
        }
      }
    }

    // 监控父节点尺寸变化
    resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect
        change([width, height])
      }
    })
    resizeObserver.observe(parent)
  },
  unmounted: () => {
    if (resizeObserver) {
      resizeObserver.disconnect()
    }
  },
}
