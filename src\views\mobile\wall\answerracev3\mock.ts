import type { ProAnswerracev3, ProAnswerracev3Config, ProAnswerracev3Team } from '~/types/wall/answerracev3'

export function mockConfig(): ProAnswerracev3Config {
  return {
    answerracev3Id: String(Math.floor(Math.random() * 2) + 1),
    id: '',
    createDate: new Date(),
    updateDate: new Date(),
    deleteTag: 'N',
    userId: '',
    wallId: '',
    openState: 'Y',
    answerraceThemeLimit: '',
    answerraceAdvancedLimit: '',
  }
}

export function mockRound(state: ProAnswerracev3['state'] = 'NOT_STARTED'): ProAnswerracev3 {
  return {
    id: '1',
    themeId: '1',
    title: 'answerracev3',
    wallId: '',
    userId: '',
    rule: '规则内容，轮次编辑中配置',
    state,
    joinType: 'TEAM',
    watchTime: 2,
    submitType: 'MANUAL_SUBMIT',
    answerTimeType: 'INDIVIDUAL_TIME',
    createDate: new Date(),
    updateDate: new Date(),
    deleteTag: 'N',
    ingJoinSwitch: 'Y',
    showAnswerType: 'immediate',
    showResultType: 'score',
    showCommentSwitch: 'Y',
    chooseSubjectType: 'system',
    wrongScoreType: 'fixed',
    wrongScore: 0,
    outSwitch: 'off',
    outWrongCnt: 0,
    outRange: 'all',
    outRankSwitch: 'off',
    sort: 0,
    scoreType: '',
  }
}

export function mockTeams(): ProAnswerracev3Team[] {
  return Array.from({ length: 5 }).fill({
    id: 'team-001',
    createDate: new Date(),
    updateDate: new Date(),
    deleteTag: 'N',
    userId: 'user-001',
    wallId: 'wall-001',
    answerracev3Id: 'round-001',
    teamName: '团队一',
    teamHeadImg: '',
  }).map((item, index) => {
    return {
      ...(item as any),
      id: `team-${index}`,
      teamName: `团队${index + 1}`,
    }
  })
}
