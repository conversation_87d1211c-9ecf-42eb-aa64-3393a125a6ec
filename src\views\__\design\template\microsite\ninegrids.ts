import type { IDesignGroup } from '../../layer/group/group'
import type { IDesignTemplateSetup } from '../../types'
import { layerUuid } from '../../utils'

function getItem(name: string, icon: string, module: string): IDesignGroup {
  return {
    type: 'group',
    uuid: layerUuid(),
    templateId: 'template-A24njl6uo1',
    name,
    lock: true,
    isPercent: 1,
    style: { width: '100px', height: '100px', background: 'rgba(255, 255, 255, 0.2)', borderRadius: '4px' },
    layers: [
      {
        uuid: layerUuid(),
        name: '名称',
        type: 'text',
        data: name,
        style: { width: '100px', height: '30px', left: 0, top: '66px', lineHeight: 2, textAlign: 'center', color: 'rgb(255, 255,255)' },
      },
      {
        uuid: layerUuid(),
        name: '图标',
        data: icon,
        type: 'image',
        color: 'rgb(255, 255,255)',
        style: { width: '40px', height: '40px', left: '30px', top: '18px' },
      },
      {
        uuid: layerUuid(),
        name: '背景图',
        type: 'image',
        data: 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7',
        style: { width: '64px', height: '64px', left: '20px', top: '7px' },
      },
    ],
    events: [{ event: 'click', type: 'interact', value: { module } }],
  }
}
export function setup(app: IDesignTemplateSetup) {
  app.registry({
    label: '九宫格',
    thumbnail: new URL(`./ninegrids.png`, import.meta.url).href,
    defaultData() {
      const item1 = getItem('上墙', '//res3.hixianchang.com/qn/lib/microsite/icon/msg.png', 'msg')
      const item2 = getItem('滚动抽奖', '//res3.hixianchang.com/qn/lib/microsite/icon/lottery.png', 'lottery')
      const item3 = getItem('图片', '//res3.hixianchang.com/qn/lib/microsite/icon/pic.png', 'pic')

      const item4 = getItem('红包雨', '//res3.hixianchang.com/qn/lib/microsite/icon/redpack.png', 'redpack')
      const item5 = getItem('摇一摇', '//res3.hixianchang.com/qn/lib/microsite/icon/shakev3.png', 'shakev3')
      const item6 = getItem('接金币', '//res3.hixianchang.com/qn/lib/microsite/icon/goldcoinv3.png', 'goldcoinv3')

      const item7 = getItem('数钱', '//res3.hixianchang.com/qn/lib/microsite/icon/moneyv3.png', 'moneyv3')
      const item8 = getItem('投票', '//res3.hixianchang.com/qn/lib/microsite/icon/vote.png', 'vote')
      const item9 = getItem('评分', '//res3.hixianchang.com/qn/lib/microsite/icon/mark.png', 'mark')

      const row1Uuid = layerUuid()
      const row2Uuid = layerUuid()
      const row3Uuid = layerUuid()

      return {
        type: 'group',
        uuid: layerUuid(),
        name: '九宫格',
        style: {
          width: '335px',
          height: '335px',
        },
        layout: {
          direction: 'column',
          gap: 10,
          items: { [row1Uuid]: { flex: 1 }, [row2Uuid]: { flex: 1 }, [row3Uuid]: { flex: 1 } },
        },
        layers: [
          {
            type: 'group',
            uuid: row1Uuid,
            name: '第一行',
            lock: true,
            style: {
              width: '335px',
              height: '100px',
            },
            layout: {
              direction: 'row',
              gap: 10,
              items: { [item1.uuid]: { flex: 1 }, [item2.uuid]: { flex: 1 }, [item3.uuid]: { flex: 1 } },
            },
            layers: [item1, item2, item3],
          },
          {
            type: 'group',
            uuid: row2Uuid,
            name: '第二行',
            lock: true,
            style: {
              width: '335px',
              height: '100px',
            },
            layout: {
              direction: 'row',
              gap: 10,
              items: { [item4.uuid]: { flex: 1 }, [item5.uuid]: { flex: 1 }, [item6.uuid]: { flex: 1 } },
            },
            layers: [item4, item5, item6],
          },
          {
            type: 'group',
            uuid: row3Uuid,
            name: '第三行',
            lock: true,
            style: {
              width: '335px',
              height: '100px',
            },
            layout: {
              direction: 'row',
              gap: 10,
              items: { [item7.uuid]: { flex: 1 }, [item8.uuid]: { flex: 1 }, [item9.uuid]: { flex: 1 } },
            },
            layers: [item7, item8, item9],
          },
        ],
      }
    },
  })
}
