<script setup lang="ts">
import { getDefaultMaterial, openSelectMaterial, useDataAttr } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'
import { DEFAULT_DATA, type IDesignMobileLotteryv3 } from './mobile-lotteryv3'

const layer = defineModel<IDesignMobileLotteryv3>('layer', { required: true })

const confirmBtnImgBind = useDataAttr(layer.value.data, 'confirmBtnImg', DEFAULT_DATA.confirmBtnImg)

async function updateMaterialFn(dataKey: keyof typeof layer.value.data, isReset: boolean = false) {
  if (!dataKey) {
    return
  }
  const result = isReset
    ? await getDefaultMaterial(layer.value, `data.${dataKey}`)
    : await openSelectMaterial('PIC')
  if (result) {
    layer.value.data[dataKey] = result
  } else if (isReset) {
    delete layer.value.data[dataKey]
  }
}
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">组件具体显示效果和轮次 抽奖资格获取 配置项有关。</div>
      <div class="my-10px b-1px b-#ccc b-solid"></div>
      <div class="setting-item flex-col items-start!">
        <strong class="setting-item-label my-16px!">报名按钮</strong>
        <div class="setting-item-content">
          <div class="thumbnail-box bgblank">
            <div class="bg">
              <MaterialThumbnail @select="updateMaterialFn('confirmBtnImg')" @reset="updateMaterialFn('confirmBtnImg', true)">
                <img v-if="confirmBtnImgBind" :src="confirmBtnImgBind" class="w-200px" />
              </MaterialThumbnail>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
