import { merge } from 'lodash-es'
import { BisTypes } from '../../'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './winning-list4-setting.vue'
import Comp from './winning-list4.vue'

// 类型
export const type = 'winning-list4'

// 数据类型约束
interface ContentStyle {
  fontColor: string
  fontSize: number
  fonBold: boolean
}

export interface IDesignWinningList4 extends IDesignLayer {
  type: typeof type
  data: {
    itemGap: number // 每项之间的空隙
    imageWidth: number // 图片宽度
    imageHeight: number // 图片高度
    imageMode?: 'cover' | 'contain' | undefined // 图片显示模式：cover 或 contain
    imageRadius: number // 图片圆角
    borderColor: string // 边框颜色
    borderWidth: number // 边框宽度
    innerGap: number // 每项内部元素之间的空隙
    contentStyle: ContentStyle[]
    placeholderHeadImg?: string // 占位头像
  }
}

export const DEFAULT_DATA: IDesignWinningList4['data'] = {
  itemGap: 10,
  innerGap: 10,
  imageWidth: 100,
  imageHeight: 100,
  imageRadius: 100,
  borderColor: '#fff',
  borderWidth: 2,
  placeholderHeadImg: new URL('./assets/placeholder.png', import.meta.url).href, // 默认占位头像
  contentStyle: [
    {
      fontColor: '#fff',
      fonBold: false,
      fontSize: 14,
    },
    {
      fontColor: '#fff',
      fonBold: false,
      fontSize: 14,
    },
    {
      fontColor: '#fff',
      fonBold: false,
      fontSize: 14,
    },
  ],
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.common,
    type,
    name: '中奖名单',
    Comp,
    CompSetting,
    showInteractive: [InteractiveEnum.piclotteryv3],
    showType: ['pcwall'],
    status: ['finish', 'winlist'],
    thumbnail: new URL('./winning-list4.png', import.meta.url).href,
    defaultData(options): IDesignWinningList4 {
      return merge({
        uuid: layerUuid(),
        name: '中奖名单',
        type,
        style: {
          width: '1100px',
          height: '600px',
        },
        data: {
          contentStyle: DEFAULT_DATA.contentStyle,
        },
      }, options as IDesignWinningList4)
    },
  })
}
