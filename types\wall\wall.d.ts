export interface IWall {
  id: number
  deleteTag: string
  createDate: string
  updateDate: string
  userId: number
  classId: number
  theme: string
  startDate: string
  endDate: string
  wallMode: string
  wallVersion: string
  wallLevel: string
  wallProperty: string
  wallFlag: string
  mobileFlag: string
  joinLimit: string
  province: string
  city: string
  contacts: string
  company: string
  phone: string
  qq: string
  comefrom: string
  activeState: string
  dealState: string
  publishDate: string
  maxLimit: number
  wxauthType: string
  agentId?: number
  curJoinNumber: number
  watermarkUnlockNum: number
  lotteryImportMaxLimit: number

}
