<script setup lang="ts">
const modelValue = defineModel<string>({
  default: '1px #000000',
})

// 内部状态管理
const strokeWidth = ref(0)
const strokeColor = ref('rgba(0, 0, 0, 0)')

// 解析 modelValue
function parseModelValue(value: string) {
  if (value && typeof value === 'string') {
    // 使用正则表达式匹配宽度和颜色部分
    const widthMatch = value.match(/(\d+(?:\.\d+)?px)/g)
    const colorMatch = value.match(/#[0-9a-fA-F]{3,8}|rgba?\([^)]+\)/g)
    const parts = [
      widthMatch ? widthMatch[0] : '',
      colorMatch ? colorMatch[0] : '',
    ]
    if (parts.length === 2) {
      const widthPart = Number.parseFloat(parts[0])
      if (!Number.isNaN(widthPart)) {
        strokeWidth.value = widthPart
      }
      if (parts[1] === 'px' || parts[1] === '') {
        strokeColor.value = 'rgba(0, 0, 0, 0)'
      } else {
        strokeColor.value = parts[1]
      }

      return
    }
  }
  // 解析失败时使用默认值
  strokeWidth.value = 0
  strokeColor.value = 'rgba(0, 0, 0, 0)'
}

// 同步更新 modelValue
watch([strokeWidth, strokeColor], () => {
  modelValue.value = `${strokeWidth.value}px ${strokeColor.value}`
})

onMounted(() => {
  parseModelValue(modelValue.value)
})
</script>

<template>
  <div class="text-stroke-edit">
    <div class="setting-item">
      <span>描边宽度</span>
      <el-input-number
        v-model="strokeWidth"
        v-input-number
        :min="0"
        :step="0.1"
        class="w-50"
        controls-position="right"
      />
    </div>
    <div class="setting-item">
      <span>描边颜色</span>
      <hi-color v-model="strokeColor" />
    </div>
  </div>
</template>

<style scoped>
.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
