import { BisTypes } from '../..'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './shake-ing3-setting.vue'
import Comp from './shake-ing3.vue'

// 类型
export const type = 'shake-ing3'

export const defaultCount = 10
export const defaultColor = '#fff000'
export const defaultBorderColor = '#e8ff26'
export const defaultDirection = 'up'

export interface IDesignShakeIng3 extends IDesignLayer {
  type: typeof type
  count?: number
  track: string[] // 轨道
  trackBottom: string // 轨道底部
  rankColor?: string[] // 排名颜色
  data: {
    name: string
    avatar: string
    score: number
    progress: number
  }[]
}

export function setup(app: IDesignSetup) {
  let status: string[]
  switch (app.interactive) {
    case InteractiveEnum.shakev3:
      status = ['ing', 'finish']
      break
    default:
      status = ['ing', 'finish']
      break
  }

  app.registry({
    showType: ['pcwall'],
    bisType: BisTypes.sportsIng,
    showInteractive: [InteractiveEnum.shakev3, InteractiveEnum.moneyv3, InteractiveEnum.goldcoinv3, InteractiveEnum.diglettv3, InteractiveEnum.firev3],
    type,
    name: '进行中排行',
    thumbnail: new URL('./shake-ing3.png', import.meta.url).href,
    status,
    Comp,
    CompSetting,
    defaultData() {
      return {
        uuid: layerUuid(),
        name: '进行中排行',
        type,
        track: [
          new URL(`./assets/3track_1.png`, import.meta.url).href,
          new URL(`./assets/3track_2.png`, import.meta.url).href,
          new URL(`./assets/3track_3.png`, import.meta.url).href,
          new URL(`./assets/3track_4.png`, import.meta.url).href,
        ],
        trackBottom: new URL(`./assets/3trackBottom.png`, import.meta.url).href,
        rankColor: ['#ffc000', '#acc2c6', '#f6ad91'],
        data: [],
        style: { width: '90%', height: '100%', top: '50%', left: '50%' },
      }
    },
  })
}
