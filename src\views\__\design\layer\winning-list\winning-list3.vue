<script setup lang="ts">
import { defineCustomEmits, useDesignState, useDesignTemp } from '../../index'
import { processStyle } from '../../utils'
import { DEFAULT_DATA, type IDesignWinningList3 } from './winning-list3'

const layer = defineModel<IDesignWinningList3>('layer', { required: true })

const designState = useDesignState()

const designTemp = useDesignTemp()

const customEmits = defineCustomEmits(layer)

const data = computed(() => {
  return { ...DEFAULT_DATA, ...layer.value.data }
})

// 参与人奖池
const winningList = computed(() => {
  return designState.getLayerData('winningList2') || []
})

const importformShowOption = computed(() => {
  return designState.getLayerData('importformShowOption') || []
})

const contentStyle = computed(() => {
  return data.value.contentStyle.map((item) => {
    return processStyle({
      flex: 1,
      fontSize: `${item.fontSize}px`,
      color: item.fontColor || '#000',
      fontWeight: item.fonBold ? 'bold' : 'normal',
    })
  })
})

const itemGap = computed(() => processStyle(`${data.value.itemGap}px`))

const itemStyle = computed(() => {
  return processStyle({
    width: `calc((100% - ${(data.value.itemsPerRow - 1) * data.value.itemGap}px) / ${data.value.itemsPerRow})`,
    background: data.value.bgColor,
    gap: `${data.value.innerGap}px`,
    paddingTop: `${data.value.innerGap}px`,
    paddingBottom: `${data.value.innerGap}px`,
    borderRadius: `${data.value.itemRadius}px`,
  })
})

function onDeleteItem(item: any) {
  ElMessageBox.confirm('确定要取消当前用户的中奖资格吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '再想想',
    type: 'warning',
  }).then(() => {
    if (designTemp.isEdit) {
      ElMessageBox.alert('当前中奖名单是模拟数据，无法取消中奖，请创建活动后在大屏幕中操作', '需要在大屏幕中操作')
    } else {
      customEmits('winning-delete', item.id)
    }
  }).catch(() => {
    // do nothing
  })
}
</script>

<template>
  <ul
    class="h-full flex flex-wrap content-center justify-center overflow-y-auto"
    :style="{
      gap: itemGap,
      alignContent: data.flexContentStyle,
    }"
  >
    <li
      v-for="(item, index) in winningList"
      :key="index"
      class="item relative line-clamp-1 flex list-none items-center justify-between overflow-hidden rounded-2xl px-6 py-4 text-center"
      :style="itemStyle"
    >
      <span v-if="importformShowOption[0]" :style="contentStyle[0]" class="line-clamp-1 line-height-tight"> {{ item.nameD0 }}</span>
      <span v-if="importformShowOption[1]" :style="contentStyle[1]" class="line-clamp-1 line-height-tight"> {{ item.nameD1 }} </span>
      <span v-if="importformShowOption[2]" :style="contentStyle[2]" class="line-clamp-1 line-height-tight">  {{ item.nameD2 }}</span>
      <div class="delete absolute inset-0 z-10 hidden cursor-pointer items-center justify-center bg-black bg-opacity-80 text-white" @click="onDeleteItem(item)">
        <icon-ph:trash-bold />
      </div>
    </li>
  </ul>
</template>

<style scoped lang="scss">
.item {
  &:hover {
    .delete {
      display: flex;
    }
  }
}
</style>
