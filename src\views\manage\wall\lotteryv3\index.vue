<script setup lang="ts">
import HiDesign from 'design/index.vue'
import { useDesignTemp } from '~/src/views/__/design'
import { useMobileLotteryV3 } from '~/src/views/mobile/wall/lotteryv3'
import { usePcwallLotteryv3 } from '~/src/views/pcwall/lotteryv3'

definePage({ meta: { label: '滚动抽奖' } })

const interactive = 'lotteryv3'
const designTemp = useDesignTemp()

watch(
  () => designTemp.showType,
  (v) => {
    if (v === 'mobile') {
      useMobileLotteryV3()
    } else {
      usePcwallLotteryv3()
    }
  },
  { immediate: true },
)
</script>

<template>
  <HiDesign :interactive="interactive" />
</template>
