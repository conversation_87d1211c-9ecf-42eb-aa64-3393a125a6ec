<script setup lang="ts">
import type { OnDrag } from 'react-moveable/declaration/types'
import Moveable from 'vue3-moveable'
import { provideElementSize, useDesignData, useDesignTemp } from '..'
import HiAnimate from './setting/animate/index.vue'
import HiStyle from './setting/style/index.vue'
import HiTrigger from './setting/trigger/index.vue'

provideElementSize()

const designData = useDesignData()
const designTemp = useDesignTemp()

function closeFn() {
  designTemp.reset()
}

const moveableTarget = ref<HTMLElement>()
const dragHandlerRef = ref<HTMLElement>()
const moveableRef = ref<Moveable>()

const currentLayers = computed(() => {
  return designData.getLayerByUuids(designTemp.activeList)
})

const layerName = computed(() => {
  const nameArr = []
  if (designTemp.activeList.length === 1) {
    const uuid = designTemp.activeList[0]
    const nowLayer = designData.getLayerByUuid(uuid)
    if (nowLayer) {
      if (nowLayer.$parent) {
        nameArr.push(nowLayer.$parent.name)
      }
      nameArr.push(nowLayer.name)
    }
  }
  nameArr.push('设置')
  return nameArr.filter(Boolean).join(' - ')
})

const isShow = computed(() => {
  // 有选中的图层，且图层都存在
  return currentLayers.value.length
})

const nowTab = ref(0)

watch(() => currentLayers.value.length, (v) => {
  if (v !== 1) {
    nowTab.value = 0
  }
})
// drag ///////////////////////////////////////////
const dragPosition = useLocalStorage('design-setting-temporary-position', { top: 0, left: 0 })
watch(moveableTarget, (v) => {
  if (v) {
    v.style.top = `${dragPosition.value.top}px`
    v.style.left = `${dragPosition.value.left}px`
  }
}, {
  immediate: true,
})

function onDrag(e: OnDrag) {
  if (e.bottom > 0 && e.top > 0) {
    e.target.style.top = `${e.top}px`
    dragPosition.value.top = e.top
  }
  if (e.left > 0) {
    e.target.style.left = `${e.left}px`
    dragPosition.value.left = e.left
  }
}
</script>

<template>
  <div v-if="isShow" ref="moveableTarget" class="moveable-ignore setting">
    <div ref="dragHandlerRef" class="title">
      <span>{{ layerName }}</span>
      <icon-ph-x-bold class="x-box" @click="closeFn" />
    </div>
    <ul class="tabs-box">
      <li :class="{ cur: nowTab === 0 }" @click="nowTab = 0">样式</li>
      <template v-if="designTemp.activeList?.length < 2">
        <li v-auth="['man', 'oem', 'spo.hi.vip', 'spo.ft']" :class="{ cur: nowTab === 1 }" @click="nowTab = 1">动画</li>
        <li v-auth="['man', 'oem', 'spo.hi.vip', 'spo.ft']" :class="{ cur: nowTab === 2 }" @click="nowTab = 2">触发</li>
      </template>
    </ul>
    <div class="wrap">
      <HiStyle v-if="nowTab === 0" />
      <HiAnimate v-if="nowTab === 1" />
      <HiTrigger v-if="nowTab === 2" />
    </div>
  </div>
  <Moveable
    ref="moveableRef"
    :style="{ '--moveable-color': 'transparent' }"
    :target="moveableTarget"
    :draggable="true"
    :drag-target="dragHandlerRef"
    :drag-target-self="false"
    :origin="false"
    @drag="onDrag"
  />
</template>

<style scoped lang="scss">
// 设置悬浮框统一结构
// .setting-block {
//   h2
//   .setting-wrap {
//     h3
//     .setting-item {
//       h3
//       div.content
//     }
//   }
// }

.tabs-box {
  display: flex;
  justify-content: space-around;
  background-color: #fff;

  li {
    --hover-color: #1261ff;

    flex: 1;
    cursor: pointer;
    padding: 6px 0;
    text-align: center;
    color: #666;
    font-size: 14px;
    font-weight: bold;
    transition: 0.2s;
    border-bottom: 2px solid #ccd5db;

    &.cur,
    &:hover {
      border-color: var(--hover-color);
      color: var(--hover-color);
    }
  }
}

.setting {
  --scrollbar-width: 2px;

  position: fixed;
  top: var(--top-height);
  left: var(--left-width);
  width: 260px;
  height: 90%;
  z-index: 20;
  background-color: #eef2f8;
  box-shadow: 0 0 16px rgba(0, 0, 0, 0.16);
  display: flex;
  flex-direction: column;
  border-radius: 5px;
  overflow: hidden;

  .title {
    height: 48px;
    padding: 0 16px;
    background-color: #fff;
    cursor: move;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .x-box {
      margin: 5px;
      cursor: pointer;
    }
  }

  .wrap {
    overflow: auto;
    flex: 1;
  }
}
</style>
