<script setup lang="ts">
import type { IDesignMobileChooseTeam } from './mobile-choose-team'
import { defineCustomEmits, useDesignState, useDesignTemp } from '../..'

export interface ITeamInfo {
  id: string
  teamName: string
  teamHeadImg: string
}

const layer = defineModel<IDesignMobileChooseTeam>('layer', { required: true })
const customEmits = defineCustomEmits(layer)
const designTemp = useDesignTemp()
const designState = useDesignState()

const activeTeamId = ref<string>('')

const currentTeamId = computed<string>(() => {
  return designState.getLayerData('regedit')?.teamId || ''
})

function randomTeamList(length: number = 10) {
  const arr: ITeamInfo[] = []
  for (let i = 1; i <= length; i++) {
    arr.push({
      id: `${i}`,
      teamName: `队伍${i}`,
      teamHeadImg: new URL(`./assets/default.png`, import.meta.url).href,
    })
  }
  return arr
}
const teamList = computed<ITeamInfo[]>(() => {
  const data = designState.getLayerData('teamList')
  if (data || !designTemp.isEdit) {
    return data
  }
  return randomTeamList()
})

const teamJoinType = computed(() => {
  return designState.getLayerData('teamJoinType') || ''
})

const step = computed(() => {
  if (currentTeamId.value) {
    return 'HasTeam'
  }
  return 'NoTeam'
})

watch(() => currentTeamId.value, (v) => {
  activeTeamId.value = v
}, {
  immediate: true,
})

const currentTeam = computed<ITeamInfo>(() => {
  return teamList.value?.find(item => item.id === currentTeamId.value) || {
    id: '',
    teamName: '',
    teamHeadImg: '',
  }
})

function onJoinTeam() {
  if (designTemp.isEdit) {
    designState.setLayerData({
      ...designState.layerData,
      regedit: {
        teamId: '1',
      },
    })
    return
  }
  if (activeTeamId.value) {
    customEmits('join-team', activeTeamId.value)
  }
}

function onLeaveTeam() {
  if (designTemp.isEdit) {
    designState.setLayerData({
      ...designState.layerData,
      regedit: {
        teamId: '',
      },
    })
    return
  }
  activeTeamId.value = ''
  customEmits('leave-team')
}
</script>

<template>
  <div
    v-if="teamList"
    class="mobile-choose-team relative"
    :style="{
      '--primary-color': layer.data.primaryColor || '#0667CF',
    }"
  >
    <!-- 等待选择队伍 -->
    <template v-if="step === 'NoTeam'">
      <img src="./assets/select-team.png" alt="请选择队伍" class="mx-auto mt-120px h-auto w-180px">
      <div class="teamlist mt-30px">
        <div
          v-for="item in teamList"
          :key="item.id"
          :class="{
            active: activeTeamId === item.id,
          }"
          @click="activeTeamId = item.id"
        >
          <img :src="item.teamHeadImg" :alt="item.teamName">
          <p>{{ item.teamName }}</p>
        </div>
      </div>
      <button @click="onJoinTeam">加入队伍</button>
    </template>

    <!-- 已经选择队伍 -->
    <template v-else-if="currentTeam.id">
      <div class="flex flex-col items-center justify-center mt-140px!">
        <div class="mb-20px text-center text-16px text-#ebe888 line-height-normal">
          <p class="">活动还未开始</p>
          <p>大家一起准备哦!</p>
        </div>
        <img :src="currentTeam.teamHeadImg" :alt="currentTeam.teamName" class="avatar size-100px">
        <p class="name">{{ currentTeam.teamName }}</p>
        <button v-if="teamJoinType !== 'AUTO'" @click="onLeaveTeam">重新选择队伍</button>
      </div>
    </template>
  </div>
</template>

<style scoped lang="scss">
.mobile-choose-team {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-size: 16px;
}
.teamlist {
  flex-shrink: 0;
  max-height: 42vh;
  overflow-x: hidden;
  overflow-y: auto;
  margin: 10% auto 8%;
  display: grid;
  width: 96%;
  justify-content: space-between;
  grid-template-columns: repeat(3, 30%);
  grid-gap: 10px;
  .active {
    img {
      background-image: url('./assets/team-item-border-active.png');
    }
  }
  img {
    display: block;
    aspect-ratio: 1;
    width: 90px;
    height: 90px;
    border-radius: 16px;
    padding: 10px;
    margin: 0 auto;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100%;
    background-image: url('./assets/team-item-border.png');
  }
  p {
    text-align: center;
    color: white;
    margin-top: 4px;
    padding: 6px;
    font-size: 12px;
    background: linear-gradient(
      to right,
      rgba(0, 44, 105, 0.2),
      rgba(0, 44, 105, 1) 20%,
      rgba(0, 44, 105, 1) 80%,
      rgba(0, 44, 105, 0.2)
    );
  }
}

button {
  width: 200px;
  height: 40px;
  line-height: 40px;
  font-size: 20px;
  border-radius: 30px;
  background: linear-gradient(to bottom, #1794fc, #0f82ef 20%, #0656c7, #0349bb);
  box-shadow: 0 0 2px 0 #ccc;
  color: #fff;
  margin: 20px auto;
  &:active {
    opacity: 0.9;
  }
}

.avatar {
  border-radius: 10px;
  margin: 0 auto;
  display: block;
  aspect-ratio: 1;
  padding: 6px;
  background-color: #002a64;
  border: 3px solid #02aec6;
}
.name {
  text-align: center;
  margin: 20px auto 40px;
  color: white;
  width: fit-content;
  padding: 6px 34px;
  font-size: 14px;
  background: linear-gradient(
    to right,
    rgba(0, 44, 105, 0.2),
    rgba(0, 44, 105, 1) 20%,
    rgba(0, 44, 105, 1) 80%,
    rgba(0, 44, 105, 0.2)
  );
}
</style>
