<script setup lang="ts">
import { Howl } from 'howler/dist/howler.core.min'
import { deaQiniulUrl } from '~/src/utils'

export interface AudioMaterial {
  url: string
  id: string
  name?: string
}

const props = defineProps<{
  materialList: AudioMaterial[]
  menuType?: string
  toUpWallList?: string[]
}>()

const emits = defineEmits<{
  (e: 'selection', val: AudioMaterial): void
  (e: 'delete', val: string): void
  (e: 'edit', val: AudioMaterial): void
  (e: 'upWall', val: string): void
}>()

const route = useRoute()

const isManage = computed(() => route.path.startsWith('/manage'))
const isOem = computed(() => route.path.startsWith('/oem'))
const isSpo = computed(() => route.path.startsWith('/admin'))
const isMyMaterial = computed(() => props.menuType === 'my_material')
const showUpWall = computed(() => props.menuType === 'OFF')

const musicObj = ref<Record<string, Howl>>({})
function handlePlayMusic(item: AudioMaterial) {
  const currentMusic = musicObj.value[item.id]
  const isPlaying = currentMusic?.playing()

  // 暂停所有
  Object.values(musicObj.value).forEach((music) => {
    if (music !== currentMusic) {
      music.pause()
    }
  })

  if (currentMusic) {
    if (isPlaying) {
      currentMusic.pause()
    } else {
      currentMusic.play()
    }
    return
  }

  const music = new Howl({
    src: [
      deaQiniulUrl(item.url),
    ],
    html5: true,
    onend: () => {
    },
  })
  music.play()
  musicObj.value[item.id] = music
}

function isPlaying(item: AudioMaterial) {
  return musicObj.value[item.id] && musicObj.value[item.id].playing()
}

function showSelected(item: AudioMaterial) {
  if (showUpWall.value) {
    return props.toUpWallList?.includes(item.id)
  }
}
onUnmounted(() => {
  for (const key in musicObj.value) {
    musicObj.value[key].stop()
  }
  musicObj.value = {}
})
</script>

<template>
  <div class="music-box">
    <div v-for="item in materialList" :key="item.id" class="music-item" :class="{ selected: showSelected(item) }" @click="emits('selection', item)">
      <div class="name">{{ item.name }}</div>
      <div class="flex items-center justify-around gap-10">
        <el-tooltip content="播放" placement="top">
          <div class="flex items-center justify-between" @click.stop="handlePlayMusic(item)">
            <icon-ph-pause-circle v-if="isPlaying(item)" class="play h-20 w-20" />
            <icon-ph-play-circle v-else class="play h-20 w-20" />
          </div>
        </el-tooltip>
        <el-tooltip v-if="isManage || isOem || (isSpo && isMyMaterial)" content="编辑" placement="top">
          <div class="flex items-center justify-between" @click.stop="emits('edit', item)">
            <icon-ph-note-pencil class="h-20 w-20" />
          </div>
        </el-tooltip>
        <el-tooltip v-if="isManage || isOem || (isSpo && isMyMaterial)" content="删除" placement="top">
          <div class="flex items-center justify-between" @click.stop="emits('delete', item.id)">
            <icon-ph-trash class="h-20 w-20" />
          </div>
        </el-tooltip>
        <el-tooltip v-if="showUpWall" content="上架" placement="top">
          <div class="flex items-center justify-between" @click.stop="emits('upWall', item.id)">
            <icon-ph-arrow-circle-up class="del h-20 w-20" />
          </div>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.music-box {
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  height: 80%;
  padding: 10px;

  .music-item {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: calc(22% - 12px);
    min-width: 260px;
    height: 40px;
    border-bottom: 1px solid #f6f9fa;
    margin: 0 10px 10px;
    cursor: pointer;
    padding: 0 10px;
    border-radius: 50px;
    transition: all 0.2s;
    background-color: #f6f9fa;
    &.selected {
      border: 1px solid #1e9ff2;
    }

    &:hover {
      background-color: #e6ebed;
    }

    .name {
      width: calc(100% - 70px);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .music-footer {
    position: absolute;
    left: 0;
    bottom: 0px;
    width: 100%;
    height: 60px;
    border-top: 1px solid #e6ebed;
    background-color: #fff;

    .info {
      display: flex;
      align-items: center;
      height: 60px;
      padding-left: 24px;
    }
  }
}
</style>
