{"name": "web-next", "type": "module", "version": "1.0.144", "private": true, "scripts": {"dev": "pnpm dev:pro", "dev:pro": "vite --mode dev --open", "dev:pro:iframe": "IS_OEPN_LOCAL_IFRAME_DEV=true pnpm dev:pro", "dev:oem:new:iframe": "IS_OEPN_LOCAL_IFRAME_DEV=true pnpm dev:oem:new", "dev:oem": "VITE_PROJECT=oem VITE_PRO_PROXY_URL=https://lindagold.fws.dev.hidanmu.com vite --mode dev --open", "dev:oem:new": "VITE_PROJECT=oem VITE_PRO_PROXY_URL=https://hd.dev.hidanmu.com vite --mode dev --open", "build": "vite build --mode pro", "build:dev": "vite build --mode dev", "build:uat": "vite build --mode uat", "preview": "vite build && vite preview", "preview:dev": "pnpm build:dev && vite preview --mode dev", "lint": "vue-tsc --noEmit && eslint .", "lint:fix": "eslint . --fix", "plop": "plop"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@faker-js/faker": "^9.0.3", "@floating-ui/vue": "^1.1.6", "@formkit/auto-animate": "^0.8.2", "@tweenjs/tween.js": "17.4.0", "@vueuse/components": "^11.1.0", "@vueuse/core": "^11.1.0", "@vueuse/integrations": "^11.1.0", "@wtto00/jweixin-esm": "1.6.0-5", "animate.css": "^4.1.1", "apng-js": "^1.1.4", "axios": "^1.7.7", "bezier-easing": "^2.1.0", "big.js": "^6.2.2", "bignumber.js": "^9.1.2", "color": "^4.2.3", "devtools-detector": "^2.0.22", "dompurify": "^3.1.7", "echarts": "^5.5.1", "element-plus": "^2.8.4", "events": "^3.3.0", "gsap": "^3.12.5", "howler": "^2.2.4", "lodash-es": "^4.17.21", "lz-string": "^1.5.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^2.2.2", "polyfill-object.fromentries": "^1.0.1", "postcss": "^8.4.47", "postprocessing": "^6.36.3", "qrcode": "^1.5.4", "quill": "^2.0.2", "randomcolor": "^0.6.2", "resize-observer-polyfill": "^1.5.1", "signature_pad": "^5.0.3", "sortablejs": "^1.15.3", "tdesign-vue-next": "^1.10.7", "three": "^0.169.0", "unocss": "^0.63.1", "vant": "^4.9.7", "vue": "^3.5.10", "vue-deepunref": "^1.0.1", "vue-i18n": "^10.0.3", "vue-router": "^4.4.5", "vue3-moveable": "^0.28.0"}, "devDependencies": {"@antfu/eslint-config": "^3.7.3", "@iconify-json/ic": "^1.2.0", "@iconify-json/ph": "^1.2.0", "@iconify/vue": "^4.1.2", "@playwright/test": "^1.52.0", "@types/big.js": "^6.2.2", "@types/color": "^3.0.6", "@types/dompurify": "^3.0.5", "@types/howler": "^2.2.12", "@types/lodash-es": "^4.17.12", "@types/randomcolor": "^0.5.9", "@types/sortablejs": "^1.15.8", "@types/three": "^0.169.0", "@unocss/eslint-config": "^0.63.1", "@unocss/preset-rem-to-px": "^0.63.1", "@vitejs/plugin-vue": "^5.1.4", "@vitejs/plugin-vue-jsx": "^4.0.1", "autoprefixer": "^10.4.20", "code-inspector-plugin": "^0.17.3", "eslint": "^9.11.1", "eslint-plugin-format": "^0.1.2", "lint-staged": "^15.2.10", "playwright": "^1.52.0", "plop": "^4.0.1", "postcss": "^8.4.21", "postcss-preset-env": "^10.1.1", "postcss-pxtorem": "^6.1.0", "replace-in-file": "^8.2.0", "rollup": "^4.22.5", "rollup-plugin-copy": "^3.5.0", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.79.4", "svgo": "^3.3.2", "terser": "^5.34.1", "typescript": "^5.6.2", "unplugin-auto-import": "^0.18.3", "unplugin-icons": "^0.19.3", "unplugin-vue-components": "^0.27.4", "unplugin-vue-router": "^0.10.8", "vite": "^5.4.8", "vite-plugin-compression": "^0.5.1", "vite-plugin-inspect": "^0.8.7", "vue-tsc": "^2.1.6"}, "pnpm": {"patchedDependencies": {"postcss-pxtorem@6.1.0": "patches/<EMAIL>"}, "onlyBuiltDependencies": ["@parcel/watcher", "core-js", "esbuild", "vue-demi"]}, "browserslist": ["> 1%", "iOS 7.1", "last 2 versions", "not ie <= 8"]}