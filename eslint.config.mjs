import antfu from '@antfu/eslint-config'

export default antfu({
  ignores: ['node_modules', '**/node_modules/**', 'dist', '**/dist/**', 'pnpm-lock.yaml', '**/pnpm-lock.yaml/**'],
  unocss: true,
  rules: {
    'no-unused-expressions': 'off',
    'no-console': 'off',
    'no-empty': 'off',
    'no-alert': 'off',
    'antfu/if-newline': 'off',
    'ts/ban-ts-comment': 'off',
    'no-cond-assign': 'off',
    'ts/prefer-ts-expect-error': 'off',
    'vue/max-attributes-per-line': ['error', {
      singleline: 10,
      multiline: 1,
    }],
    'vue/singleline-html-element-content-newline': 'off',
    'style/brace-style': ['error', '1tbs', { allowSingleLine: true }],
    'vue/html-self-closing': 'off',
    // https://eslint.org/docs/latest/extend/selectors#what-syntax-can-selectors-have
    'no-restricted-syntax': [
      'error',
      // parseInt 写法
      {
        selector: 'CallExpression[callee.name!=\'parseInt\'] > Identifier[name=\'parseInt\']',
        message: 'Call parseInt directly to guarantee radix param is not incorrectly provided',
      },
      // Number.parseInt 写法
      {
        selector: 'CallExpression[arguments.0.object.name="Number"][arguments.0.property.name="parseInt"]',
        message: 'Call parseInt directly to guarantee radix param is not incorrectly provided',
      },
    ],
    'regexp/no-super-linear-backtracking': 'off',
    'regexp/no-misleading-capturing-group': 'off',
  },
  formatters: {
    /**
     * Format CSS, LESS, SCSS files, also the `<style>` blocks in Vue
     * By default uses Prettier
     */
    css: true,
    prettierOptions: {
      tabWidth: 2,
      semi: false,
      singleQuote: true,
      bracketSpacing: true,
      useTabs: false,
      printWidth: 120,
      proseWrap: 'preserve',
      vueIndentScriptAndStyle: false,
      endOfLine: 'lf',
    },
  },
})
