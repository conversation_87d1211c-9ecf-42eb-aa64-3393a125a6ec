import { merge } from 'lodash-es'
import { BisTypes } from '../..'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './winning-list3-setting.vue'
import Comp from './winning-list3.vue'

// 类型
export const type = 'winning-list3'

// 数据类型约束
interface ContentStyle {
  fontColor: string
  fontSize: number
  fonBold: boolean
}

export interface IDesignWinningList3 extends IDesignLayer {
  type: typeof type
  data: {
    bgColor: string
    itemsPerRow: number // 每行显示几个
    itemGap: number // 每项之间的空隙
    itemRadius: number
    innerGap: number // 每项内部元素之间的空隙
    contentStyle: ContentStyle[]
    flexContentStyle?: 'start' | 'center'
  }
}

export const DEFAULT_DATA: IDesignWinningList3['data'] = {
  bgColor: '#333333',
  itemsPerRow: 3,
  itemGap: 10,
  itemRadius: 100,
  innerGap: 10,
  flexContentStyle: 'start',
  contentStyle: [
    {
      fontColor: '#fff',
      fonBold: false,
      fontSize: 14,
    },
    {
      fontColor: '#fff',
      fonBold: false,
      fontSize: 14,
    },
    {
      fontColor: '#fff',
      fonBold: false,
      fontSize: 14,
    },
  ],
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.sportsIng,
    type,
    name: '名单抽奖结果',
    showType: ['pcwall'],
    status: ['finish', 'winlist'],
    showInteractive: [InteractiveEnum.listlotteryv3],
    thumbnail: new URL('./winning-list3.png', import.meta.url).href,
    Comp,
    CompSetting,
    defaultData(options): IDesignWinningList3 {
      return merge({
        uuid: layerUuid(),
        name: '名单抽奖结果',
        type,
        style: {
          width: '1000px',
          height: '500px',
        },
        data: {
          contentStyle: DEFAULT_DATA.contentStyle,
        },
      }, options as IDesignWinningList3)
    },
  })
}
