import { loadImg } from '@/utils'
import { chunk } from 'lodash-es'

// 签名数据管理
interface ISignData {
  id: number
  img: string
  updateTime: number
  image: HTMLImageElement
}

const { promise: readyPromise, resolve: readyResolve } = Promise.withResolvers<void>()
const imgSize = 600
let handler: NodeJS.Timeout = null as any
let fetchIng = false
const originData: Ref<ISignData[]> = ref([])
// 查询下一条数据
let nowIndex = 0
let repeatIndex = 0
const existingIds = new Set<number>()

// 当前数据中最大的时间戳
let maxUpdateTime = 0

async function fetchData() {
  if (fetchIng) return
  try {
    fetchIng = true

    // 调用api接口查询最新数据
    const data = await api.pcwall.padsign.dataList({ where: { gtUpdateTime: maxUpdateTime } })

    // base64转图片
    const chunkArr = chunk(data, 100)
    for (const itemChunk of chunkArr) {
      const promiseArr = []
      for (const item of itemChunk) {
        if (item.updateTime > maxUpdateTime) {
          maxUpdateTime = item.updateTime
        }
        if (item.deleteTag === 'Y') {
          // 删除数据
          originData.value = originData.value.filter(i => i.id !== item.id)
          existingIds.delete(item.id) // 从Set中删除id
          continue
        }
        // 重复的数据不再添加
        if (existingIds.has(item.id)) {
          continue
        }
        promiseArr.push(
          new Promise<void>((resolve) => {
            let imgUrl = item.img!
            imgUrl = `${imgUrl}?imageView2/0/w/${imgSize}`
            loadImg(imgUrl).then((img) => {
              originData.value.push({
                id: item.id,
                img: imgUrl,
                image: img,
                updateTime: item.updateTime,
              })
              existingIds.add(item.id) // 添加新id到Set中
            }).finally(resolve)
          }),
        )
      }
      await Promise.all(promiseArr)
    }

    if (originData.value.length > 0) {
      readyResolve()
    }
  } catch {
  } finally {
    fetchIng = false
  }
}
function getNext(): ISignData | null {
  let data = originData.value[nowIndex]
  if (data) {
    nowIndex++
    return data
  }
  // 没有最新数据了，从头开始
  data = originData.value[repeatIndex]
  if (data) {
    repeatIndex++
    return data
  }
  repeatIndex = 0
  return originData.value[repeatIndex++]
}

const loopText = (() => {
  let localNowIndex = nowIndex
  let localRepeatIndex = repeatIndex
  return () => {
    if (originData.value.length === 0) {
      return null
    }
    let data = originData.value[localNowIndex]
    if (data) {
      localNowIndex++
      return data
    }
    data = originData.value[localRepeatIndex]
    localRepeatIndex = (localRepeatIndex + 1) % originData.value.length
    return data
  }
})()

export function useSignData() {
  tryOnBeforeMount(() => {
    clearInterval(handler)
    handler = setInterval(() => fetchData(), 3000)
    fetchData()
  })
  tryOnBeforeUnmount(() => {
    clearInterval(handler)
  })
  return {
    hasData: computed(() => originData.value.length > 0),
    readyPromise,
    getNext,
    loopText,
  }
}
