<script setup lang="ts">
import type { IDesignMobilePerformance, IPerformanceForm } from './mobile-performance'
import Sortable from 'sortablejs'
import { getDefaultMaterial, openSelectMaterial, useDataAttr, useDesignState } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'
import { defaultData } from './mobile-performance'

const designState = useDesignState()

const layer = defineModel<IDesignMobilePerformance>('layer', { required: true })

async function updateMaterialFn(dataKey: Exclude<keyof typeof layer.value.data, 'isAllowEdit' | 'formSort'>, isReset: boolean = false) {
  if (!dataKey) {
    return
  }
  const result = isReset
    ? await getDefaultMaterial(layer.value, `data.${dataKey}`)
    : await openSelectMaterial('PIC')
  if (result) {
    layer.value.data[dataKey] = result
  } else if (isReset) {
    delete layer.value.data[dataKey]
  }
}

const cancelButtonImgBind = useDataAttr(layer.value.data, 'cancelButtonImg', defaultData.cancelButtonImg)
const saveButtonImgBind = useDataAttr(layer.value.data, 'saveButtonImg', defaultData.saveButtonImg)
const updateButtonImgBind = useDataAttr(layer.value.data, 'updateButtonImg', defaultData.updateButtonImg)
const isAllowEditBind = useDataAttr(layer.value.data, 'isAllowEdit', defaultData.isAllowEdit)

// 表单排序
const formSortBind = useDataAttr(layer.value.data, 'formSort', {})

const sortForm = computed(() => {
  const form = (designState.getLayerData('performance-form') || []).filter((item: { type: string }) => item.type !== 'CUSTOM') as IPerformanceForm[]
  return form.sort((a, b) => {
    if (layer.value.data?.formSort?.[a.key] == null) {
      return 1
    }
    if (layer.value.data?.formSort?.[b.key] === null) {
      return -1
    }
    return layer.value.data?.formSort?.[a.key] - layer.value.data.formSort?.[b.key]
  })
})

let sortable: Sortable | null = null
const sortableTableRef = useTemplateRef('sortableTableRef')

function sortableInit() {
  if (!sortableTableRef.value) {
    watchOnce(() => sortableTableRef.value, sortableInit)
    return
  }
  if (sortable) {
    sortable.destroy()
  }
  sortable = new Sortable(sortableTableRef.value, {
    sort: true,
    dataIdAttr: 'data-key',
    handle: '.handle',
    draggable: '.sort-item',
    onEnd() {
      formSortBind.value = sortable?.toArray()?.reduce((acc, cur, index) => {
        acc[cur] = index
        return acc
      }, {} as Record<string, number>) || {}
    },
  })
}

onMounted(() => {
  sortableInit()
})

onUnmounted(() => {
  sortable?.destroy()
})
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div v-if="sortForm.length" class="setting-item">
        <div class="mr-10px flex-shrink-0">表单排序</div>
        <table class="w-full table-fixed border text-center">
          <thead>
            <tr>
              <th class="w-50px py-6px">排序</th>
              <th class="py-6px">字段</th>
            </tr>
          </thead>
          <tbody ref="sortableTableRef">
            <tr
              v-for="item in sortForm"
              :key="item.key"
              :data-key="item.key"
              class="sort-item even:bg-gray-50 hover:bg-gray-50 odd:bg-white"
            >
              <td>
                <icon-ph:list-bold class="handle mr-2 flex-shrink-0 cursor-pointer text-gray-500 hover:text-blue-500"></icon-ph:list-bold>
              </td>
              <td class="text-ellipsis">
                {{ item.name }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="setting-item">
        <div class="setting-item-label">保存按钮</div>
        <div class="setting-item-content">
          <div class="thumbnail-box bgblank">
            <MaterialThumbnail @select="updateMaterialFn('saveButtonImg')" @reset="updateMaterialFn('saveButtonImg', true)">
              <img v-if="saveButtonImgBind" :src="saveButtonImgBind" />
            </MaterialThumbnail>
          </div>
        </div>
      </div>
      <div class="setting-item">
        <div class="setting-item-label">取消按钮</div>
        <div class="setting-item-content">
          <div class="thumbnail-box bgblank">
            <MaterialThumbnail @select="updateMaterialFn('cancelButtonImg')" @reset="updateMaterialFn('cancelButtonImg', true)">
              <img v-if="cancelButtonImgBind" :src="cancelButtonImgBind" />
            </MaterialThumbnail>
          </div>
        </div>
      </div>
      <div class="setting-item">
        <div class="setting-item-label">修改按钮</div>
        <div class="setting-item-content">
          <div class="thumbnail-box bgblank">
            <MaterialThumbnail @select="updateMaterialFn('updateButtonImg')" @reset="updateMaterialFn('updateButtonImg', true)">
              <img v-if="updateButtonImgBind" :src="updateButtonImgBind" />
            </MaterialThumbnail>
          </div>
        </div>
      </div>
      <div class="setting-item">
        <div class="setting-item-label">是否可编辑</div>
        <div class="setting-item-content">
          <el-switch v-model="isAllowEditBind" active-color="#13ce66" inactive-color="#ff4949" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.setting-wrap {
  padding: 10px 0;
}
.thumbnail-box {
  width: 60px;
  height: 60px;
  outline: 1px solid #e6ebed;
  border-radius: 4px;
  position: relative;

  img {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    object-fit: contain;
  }
}

table {
  border-collapse: collapse;
  th,
  td {
    padding: 6px;
    border: 1px solid #ccc;
  }
}
</style>
