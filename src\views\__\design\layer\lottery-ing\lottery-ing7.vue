<script setup lang="ts">
import type { IDesignLotteryIng7 } from './lottery-ing7'
import useThree from '@/views/__/three/useThree'
import BezierEasing from 'bezier-easing'
import gsap from 'gsap'
import { cloneDeep, sample, throttle } from 'lodash-es'
import * as THREE from 'three'
import { useDesignState } from '../..'
import { defaultAnimateSpeed, defaultCameraY, defaultHeadSize, defaultPlaceHolderHeadImg, defaultRows } from './lottery-ing7'

const layer = defineModel<IDesignLotteryIng7>('layer', { required: true })

const designState = useDesignState()
const status = computed(() => designState.status)
// 参与人奖池
const regeditList = computed(() => {
  return designState.getLayerData('regeditList') || []
})

const wallHeadSize = computed(() => layer.value.headSize ?? defaultHeadSize)
const animateSpeed = computed(() => layer.value.animateSpeed ?? defaultAnimateSpeed)
const maskImg = computed(() => layer.value.maskImg ?? '')
const rows = computed(() => layer.value.rows ?? defaultRows)
const cameraY = computed(() => layer.value.cameraY ?? defaultCameraY)

const threeRef = ref<HTMLElement | null>(null)
const { scene, loadTexture, camera } = useThree({
  threeRef,
  hasHelp: false,
  hasControls: false,
})

const cache: Record<string, any> = {}
// 生成纹理
function createTexture(url: string) {
  let texture = cache[url]
  if (texture) {
    return texture
  }
  texture = loadTexture(url)
  cache[url] = texture
  return texture
}

class BaseShape {
  name = 'BaseShape'
  group: THREE.Group
  intervalHandler: NodeJS.Timeout | null = null
  constructor() {
    this.group = new THREE.Group()
    this.group.visible = false
    scene.add(this.group)
  }

  disposeItem(item: THREE.Object3D<THREE.Object3DEventMap>) {
    if (item instanceof THREE.Mesh || item instanceof THREE.Sprite) {
      gsap.killTweensOf(item.scale)
      item.material?.dispose()
      item.geometry?.dispose()
    } else if (item instanceof THREE.Group) {
      item.children.forEach((child) => {
        this.disposeItem(child)
      })
    }
  }

  init() {
    this.intervalHandler && clearInterval(this.intervalHandler)
    this.group.traverse((item) => {
      this.disposeItem(item)
    })
    this.group.clear()
  }

  play() {
  }

  getEase(value = [0.66, 0.2, 0.45, 0.79] as [number, number, number, number]) {
    return BezierEasing(...value)
  }

  destory() {
    this.intervalHandler && clearInterval(this.intervalHandler)
    this.group.children.forEach(child => this.disposeItem(child))
    this.group.clear()
    this.group.visible = false
  }
}

class ShapeObj extends BaseShape {
  initialScale = 1
  minScaleFactor = 0.3
  breathingDurationBase = 3

  activeTweens: gsap.core.Tween[] = []
  maskTexture: THREE.Texture | null = null // 用于存储加载的遮罩纹理

  constructor() {
    super()
    this.name = 'ShapeObj'
  }

  async init() {
    super.init()
    await this.loadMask(maskImg.value) // 加载遮罩纹理
    // 相机
    camera.fov = 40
    camera.position.x = 500
    camera.position.y = cameraY.value
    camera.position.z = 2200 // 可以根据需要微调 Z 位置
    camera.updateProjectionMatrix()
    camera.lookAt(scene.position) // 保持看向场景中心
    scene.rotation.set(0, 0, 0)
    this.initShapeWall()
  }

  async initShapeWall() {
    const radius = 1000
    const headSize = wallHeadSize.value

    // 根据目标高度和头像大小计算行数
    const rowCount = rows.value

    // 几列，周长/单个大小
    const colCount = Math.floor((2 * Math.PI * radius) / headSize)
    const itemR = 2 * Math.PI / colCount
    const geometry = new THREE.PlaneGeometry(headSize - 10, headSize - 10) // 稍微缩小以留出间隙

    for (let i = 0; i < rowCount; i++) {
      const y = (i - (rowCount - 1) / 2) * headSize
      // *** 定义当前行的中心点向量 ***
      const centerVector = new THREE.Vector3(0, y, 0)
      for (let j = 0; j < colCount; j++) {
        const x = radius * Math.sin(j * itemR)
        const z = radius * Math.cos(j * itemR)

        const itemData = getItem()
        if (!itemData) continue // 使用 continue 而不是 return

        const material = new THREE.MeshBasicMaterial({
          map: createTexture(itemData.avatar),
          fog: false,
          transparent: true,
          alphaMap: this.maskTexture,
          side: THREE.DoubleSide,
        })
        material.onBeforeCompile = (shader) => {
          shader.fragmentShader = shader.fragmentShader.replace(
            '#include <alphamap_fragment>',
            `
          #ifdef USE_ALPHAMAP
              float alpha = texture2D( alphaMap, vAlphaMapUv ).a;
              diffuseColor.a = alpha;
          #endif
        `,
          )
        }
        const threeMesh = new THREE.Mesh(geometry, material)
        threeMesh.position.set(x, y, z)
        // *** 使 Mesh 面向中心轴上的对应点 ***
        threeMesh.lookAt(centerVector)
        this.group.add(threeMesh)
      }
    }
  }

  async loadMask(url: string) {
    if (url) {
      try {
        this.maskTexture = await loadTexture(url)
        this.maskTexture.wrapS = THREE.ClampToEdgeWrapping // 根据需要设置纹理环绕方式
        this.maskTexture.wrapT = THREE.ClampToEdgeWrapping
      } catch (error) {
        console.error('Failed to load mask texture:', error)
        this.maskTexture = null // 加载失败则不使用遮罩
      }
    } else {
      this.maskTexture = null
    }
  }

  getDisplayArea(z = 0) {
    const radian = Math.PI / 180
    // 计算相机到目标 Z 平面的实际距离
    const distance = Math.abs(camera.position.z - z)
    const height = 2 * Math.tan((camera.fov / 2) * radian) * distance
    const width = camera.aspect * height
    return {
      width,
      height,
    }
  }

  play() {
    // 旋转
    if (animateSpeed.value > 0) {
      gsap.to(this.group.rotation, {
        duration: 100 / animateSpeed.value,
        ease: 'none',
        y: -Math.PI * 2,
        repeat: -1,
      })
    }
    this.intervalHandler && clearInterval(this.intervalHandler)
    if (status.value === 'ready') {
      this.intervalHandler = setInterval(() => {
        this.updateItem()
      }, 100)
    }
  }

  updateItem() {
    try {
      const arr = this.group.children.filter((item) => {
        return (item instanceof THREE.Mesh)
      })
      const object = arr[(Math.random() * arr.length) | 0]
      const itemData = getItem()
      if (!itemData) return
      object.material.map = createTexture(itemData.avatar)
    } catch (e) {
      console.log('Error in updateItem:', e)
    }
  }

  destory() {
    super.destory()
    this.maskTexture?.dispose()
    this.maskTexture = null
    if (this.activeTweens) {
      this.activeTweens.forEach(tween => tween.kill())
      this.activeTweens = []
    }
  }
}

const shapeObj = ref<BaseShape | null>(null)
async function runAnimit() {
  shapeObj.value = new ShapeObj()
  shapeObj.value.group.visible = true
  shapeObj.value.init()
  shapeObj.value.play()
}
const defaultHeadImg = computed(() => {
  return layer.value.placeHolderHeadImg ?? defaultPlaceHolderHeadImg
})
function getItem() {
  if (regeditList.value.length === 0) {
    return {
      avatar: defaultHeadImg.value,
    }
  }
  const tem = cloneDeep(sample(regeditList.value))
  if (!tem.avatar) tem.avatar = defaultHeadImg.value
  const itemData = Object.assign({}, tem)
  return itemData
}

watch(
  () => [status.value, layer.value.headSize, animateSpeed.value, layer.value.maskImg, layer.value.rows, cameraY.value],
  () => {
    throttle(() => {
      shapeObj.value?.destory()
      runAnimit()
    }, 300, { leading: false })()
  },
  { deep: true, immediate: true },
)

onUnmounted(() => {
  shapeObj.value?.destory()
})
</script>

<template>
  <div class="lottery-ing2-box">
    <div ref="threeRef" class="three-box"></div>
  </div>
</template>

<style scoped lang="scss">
.lottery-ing2-box {
  width: 100%;
  height: 100%;
  .three-box {
    width: 100%;
    height: 100%;
  }
}
</style>
