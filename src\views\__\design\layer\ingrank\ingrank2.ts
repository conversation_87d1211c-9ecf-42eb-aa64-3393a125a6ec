import type { IDesignLayer, IDesignSetup } from '../../types'
import { BisTypes } from '../..'
import { InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './ingrank2-setting.vue'
import Comp from './ingrank2.vue'

// 类型
export const type = 'ingrank2'
export const defaultWidth = 300
// 数据类型约束
export interface DefaultData {
  avatarSize?: number
  nameTextSize?: number
  nameTextColor?: string
  socreTextSize?: number
  socreTextColor?: string
  maxCount?: number
  // showTime?: boolean
  dataSource?: string
}
export interface IDesignIngRank2 extends DefaultData, IDesignLayer {
  type: typeof type
}

export const defaultData: DefaultData = {
  avatarSize: 48,
  nameTextSize: 16,
  nameTextColor: '#000',
  socreTextSize: 14,
  socreTextColor: '#555',
  maxCount: 10,
  // showTime: true,
  dataSource: 'subject',
}
// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.sportsIng,
    showType: ['pcwall'],
    status: ['ing'],
    showInteractive: [InteractiveEnum.answerracev3],
    type,
    name: '答题小排行',
    thumbnail: new URL('./ingrank2.png', import.meta.url).href,
    Comp,
    CompSetting,
    defaultData() {
      return {
        type,
        uuid: layerUuid(),
        name: '答题小排行',
        data: [],
        style: {
          left: '10px',
          top: '10px',
          width: `${defaultWidth}px`,
          height: '540px',
        },
      }
    },
  })
}
