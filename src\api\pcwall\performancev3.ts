import { HiRequest } from '../request'

export default {
  configRead: (params: any) => HiRequest.post('/pro/hxc/web/properformancev3config/read.htm', params),
  read: (params: any) => HiRequest.post('/pro/hxc/web/properformancev3/read.htm', params),
  formlist: (params: any) => HiRequest.post('/pro/hxc/web/properformancev3form/list.htm', params),
  recordquery: (params: any) => HiRequest.post('/pro/hxc/web/properformancev3record/query.htm', params),
  rulelist: (params: any) => HiRequest.post('/pro/hxc/web/properformancev3rule/list.htm', params),
}
