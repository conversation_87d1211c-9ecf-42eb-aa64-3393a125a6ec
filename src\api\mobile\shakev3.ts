import { HiRequest } from '../request'

export default {
  configRead: (params: any) => HiRequest.post('/pro/hxc/mobile/proshakev3config/read.htm', params),
  read: (params: any) => HiRequest.post('/pro/hxc/mobile/proshakev3/read.htm', params),
  ranking: (params: any) => HiRequest.post('/pro/hxc/mobile/proshakev3/ranking.htm', params),

  reportRecord: (params: any) => HiRequest.post('/pro/hxc/mobile/proshakev3record/report.htm', params),
  readRecord: (params: any) => HiRequest.post('/pro/hxc/mobile/proshakev3record/read.htm', params),

  readRegedit: (params: any) => HiRequest.post('/pro/hxc/mobile/proshakev3regedit/read.htm', params),
  insertRegedit: (params: any) => HiRequest.post('/pro/hxc/mobile/proshakev3regedit/insert.htm', params),
  quitRegedit: (params: any) => HiRequest.post('/pro/hxc/mobile/proshakev3regedit/quit.htm', params),

  listTeam: (params: any) => HiRequest.post('/pro/hxc/mobile/proshakev3team/list.htm', params),
  readTeam: (params: any) => HiRequest.post('/pro/hxc/mobile/proshakev3team/read.htm', params),
}
