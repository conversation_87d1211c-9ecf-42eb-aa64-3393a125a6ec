import { merge } from 'lodash-es'
import { BisTypes } from '../../'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './listlottery-ing-setting.vue'
import Comp from './listlottery-ing.vue'

// 类型
export const type = 'listlottery-ing'

// 数据类型约束
interface ContentStyle {
  fontColor: string
  fontSize: number
  fonBold: boolean
}

export interface IDesignListlotteryIng extends IDesignLayer {
  type: typeof type
  data: {
    direction: 'col' | 'row'
    bgColor: string
    itemsPerRow: number // 每行显示几个
    itemGap: number // 每项之间的空隙
    itemRadius: number
    innerGap: number // 每项内部元素之间的空隙
    contentStyle: ContentStyle[]
  }
}

export const DEFAULT_DATA: IDesignListlotteryIng['data'] = {
  direction: 'row',
  bgColor: '',
  itemsPerRow: 3,
  itemGap: 24,
  innerGap: 0,
  itemRadius: 0,
  contentStyle: [
    {
      fontColor: 'rgb(255, 202, 40)',
      fonBold: false,
      fontSize: 16,
    },
    {
      fontColor: 'rgb(255, 202, 40)',
      fonBold: false,
      fontSize: 16,
    },
    {
      fontColor: 'rgb(255, 202, 40)',
      fonBold: false,
      fontSize: 16,
    },
  ],
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.sportsIng,
    type,
    name: '名单抽奖动效',
    showType: ['pcwall'],
    status: ['ing', 'ready'],
    showInteractive: [InteractiveEnum.listlotteryv3],
    thumbnail: new URL('./listlottery-ing.png', import.meta.url).href,
    Comp,
    CompSetting,
    defaultData(options): IDesignListlotteryIng {
      return merge({
        uuid: layerUuid(),
        name: '名单抽奖动效',
        type,
        style: {
          width: '1000px',
          height: '500px',
        },
        data: {
          contentStyle: DEFAULT_DATA.contentStyle,
        },
      }, options as IDesignListlotteryIng)
    },
  })
}
