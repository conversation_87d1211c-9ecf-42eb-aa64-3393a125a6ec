<script setup lang="ts">
import HiDesign from 'design/index.vue'
import { useDesignTemp } from '~/src/views/__/design'
import { useMobilePerformancev3 } from '~/src/views/mobile/wall/performancev3'
import { usePcwallPerformancev3 } from '~/src/views/pcwall/performancev3'

definePage({ meta: { label: '业绩目标会' } })

const interactive = 'performancev3'
const designTemp = useDesignTemp()

watch(
  () => designTemp.showType,
  (v) => {
    if (v === 'mobile') {
      useMobilePerformancev3()
    } else {
      usePcwallPerformancev3()
    }
  },
  { immediate: true },
)
</script>

<template>
  <HiDesign :interactive="interactive" />
</template>

<style scoped lang="scss">
</style>
