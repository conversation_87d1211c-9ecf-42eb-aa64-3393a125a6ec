import type { MaybeArray } from 'rollup'
import { useInstanceRouter } from '../hooks/useInstanceRouter'
import { getInteractive } from '../views/__/design/utils'
import { useUserStore } from './../store/user'
import { envUtils } from './env'

// man 是超管，oem 是服务商
// spo 是所有主办方，spo.hi 是Hi现场的主办方，spo.ft 是梵天系统的主办方，spo.hi.vip 是Hi现场的vip主办方

export declare type AuthProps = MaybeArray<'man' | 'oem' | 'spo' | 'spo.hi' | 'spo.hi.vip' | 'spo.ft'>

export function hasAuth(value: AuthProps) {
  const router = useInstanceRouter()
  const route = router.currentRoute.value
  const isManage = route.path.startsWith('/manage/')
  const isOem = route.path.startsWith('/oem/')
  const isAdmin = route.path.startsWith('/admin/')
  const isTest = route.path.startsWith('/test/')

  if (isTest || localStorage.getItem('privilegeunlocking')) {
    return true
  }
  const auths = Array.isArray(value) ? value : [value]
  if (isManage && auths.includes('man')) {
    return true
  }
  if (isOem && auths.includes('oem')) {
    return true
  }
  if (isAdmin) {
    // 所有主办方
    if (auths.includes('spo')) {
      return true
    }
    // Hi现场的主办方
    if (auths.includes('spo.hi') && !envUtils.isOem) {
      return true
    }
    // Hi现场的vip主办方
    if (auths.includes('spo.hi.vip') && !envUtils.isOem) {
      const interactive = getInteractive()
      // 业绩目标会都有权限
      if (['performancev3'].includes(interactive)) {
        return true
      }
      // 微站且themeId不等于72（经典版），有权限
      const router = useInstanceRouter()
      const themeId = router.currentRoute.value?.query?.themeId
      if (interactive === 'microsite' && themeId && themeId !== '72') {
        return true
      }
      const userStore = useUserStore()
      return userStore.userRoleType === 'VIP'
    }
    // 梵天系统的主办方
    if (auths.includes('spo.ft')) {
      return envUtils.isOem
    }
  }

  return false
}
