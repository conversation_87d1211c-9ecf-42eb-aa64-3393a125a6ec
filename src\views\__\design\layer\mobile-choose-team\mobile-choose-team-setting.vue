<script setup lang="ts">
import type { IDesignMobileChooseTeam } from './mobile-choose-team'

const layer = defineModel<IDesignMobileChooseTeam>('layer', { required: true })
</script>

<template>
  <div v-if="layer.data" class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <span>颜色</span>
        <hi-color v-model="layer.data.primaryColor" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>
