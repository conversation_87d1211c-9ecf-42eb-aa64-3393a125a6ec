import { useImData } from '~/src/hooks/useImData'
import { timer } from '~/src/utils'
import { useDesignData, useDesignState, useDesignTemp } from '../../__/design'

interface IConfig {
  themeId: number
}

export function usePcwallMicrosite() {
  const designTemp = useDesignTemp()
  const designData = useDesignData()
  const designState = useDesignState()

  const isDesignEdit = designTemp.isEdit
  const config = ref<IConfig>()

  async function fetchConfig() {
    if (isDesignEdit) {
      await timer(200)
      config.value = { themeId: 72 }
    }
  }
  async function fetchTheme() {
    // 查询主题
    if (isDesignEdit) {
      designTemp.fetchTheme()
    } else {
      const { themeId } = config.value || {}
      if (!themeId) return
      const theme = await api.pcwall.theme.readSimple({ where: { id: themeId } })
      designTemp.theme = theme
      if (theme) {
        designData.setState(JSON.parse(theme.webContent))
      }
    }
  }

  // 状态在数据中存储
  watch(
    () => designData.status,
    (status) => {
      if (status) {
        designState.setStatusList(status)
      }
    },
    { immediate: true, deep: true },
  )

  watch(
    () => designState.statusList,
    (statusList) => {
      if (JSON.stringify(statusList) !== JSON.stringify(designData.status)) {
        designData.setStatus(statusList)
      }
    },
  )

  // designState
  //   .setStatusList([
  //     { label: '准备中', value: 'ready' },
  //     { label: '倒计时', value: '321' },
  //     { label: '进行中', value: 'ing' },
  //     { label: '排行榜', value: 'finish' },
  //   ])

  watch(() => config.value?.themeId, fetchTheme)
  // 数据同步
  useImData({
    'im:microsite:config': config,
  })

  tryOnMounted(async () => {
    if (isDesignEdit) {
      fetchConfig()
    }
  })
}
