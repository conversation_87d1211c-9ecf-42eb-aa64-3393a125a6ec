import type { AxiosRequestConfig } from 'axios'
import { HiRequest } from '../request.ts'

export default {
  read: (params: any) => HiRequest.post('/pro/hxc/mobile/proanswerracev3/read.htm', params),

  teamList: (params: any) => HiRequest.post('/pro/hxc/mobile/proanswerracev3team/list.htm', params),
  teamRead: (params: any) => HiRequest.post('/pro/hxc/mobile/proanswerracev3team/read.htm', params),

  configRead: (params: any) => HiRequest.post('/pro/hxc/mobile/proanswerracev3config/read.htm', params),

  recordReport: (params: any) => HiRequest.post('/pro/hxc/mobile/proanswerracev3record/report.htm', params),
  recordRead: (params: any, config: AxiosRequestConfig = {}) => HiRequest.post('/pro/hxc/mobile/proanswerracev3record/read.htm', params, config),
  recordResult: (params: any) => HiRequest.post('/pro/hxc/mobile/proanswerracev3record/result.htm', params),

  regeditInsert: (params: any) => HiRequest.post('/pro/hxc/mobile/proanswerracev3regedit/insert.htm', params),
  regeditRead: (params: any) => HiRequest.post('/pro/hxc/mobile/proanswerracev3regedit/read.htm', params),

  subjectList: (params: any) => HiRequest.post('/pro/hxc/mobile/proanswerracev3subject/list.htm', params),
  subjectRead: (params: any, config: AxiosRequestConfig = {}) => HiRequest.post('/pro/hxc/mobile/proanswerracev3subject/read.htm', params, config),
}
