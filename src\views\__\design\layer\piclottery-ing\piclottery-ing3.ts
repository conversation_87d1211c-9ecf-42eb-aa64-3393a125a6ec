import type { IDesignLayer, IDesignSetup } from '../../types'
import { BisTypes, useDesignData } from '../..'
import { InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './piclottery-ing3-setting.vue'
import Comp from './piclottery-ing3.vue'
// 类型
export const type = 'piclottery-ing3'
export const defaultAnimateSpeed = 5
export const defaultHeadSizeW = 120
export const defaultHeadSizeH = 120
export const defaultPlaceHolderImg = new URL('./assets/placehoder.png', import.meta.url).href
export const defultRowCount = 10
export const defaultImgMode = 'cover'
export const defaultItemBorderColor = '#fff'
export const defultColCount = 20
// 数据类型约束
export interface IDesignPiclotteryIng3 extends IDesignLayer {
  type: typeof type
  headSizeW?: number
  headSizeH?: number
  animateSameSwitch?: boolean
  placeHolderImg?: string
  animiteSpeed?: number
  rowCount?: number
  colCount?: number
  itemBorderColor?: string
  itemBorderWidth?: number
  imgMode?: 'cover' | 'contain' | 'fill'
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.sportsIng,
    showType: ['pcwall'],
    status: ['ing', 'ready'],
    showInteractive: [InteractiveEnum.piclotteryv3],
    type,
    name: '图片抽奖3d效果',
    thumbnail: new URL('./piclottery-ing3.png', import.meta.url).href,
    Comp,
    CompSetting,
    defaultData() {
      const designData = useDesignData()
      const [width, height] = designData.option.drafts
      return {
        type,
        uuid: layerUuid(),
        name: '图片抽奖3d效果',
        style: {
          width: `${width}px`,
          height: `${height}px`,
          top: 0,
          left: 0,
        },
      }
    },
  })
}
