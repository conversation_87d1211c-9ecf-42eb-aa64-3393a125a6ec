import { merge } from 'lodash-es'
import { BisTypes } from '../../'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './mobile-answerrace-ing-setting.vue'
import Comp from './mobile-answerrace-ing.vue'

// 类型
export const type = 'mobile-answerrace-ing'

export const DEFAULT_DATA = {
  /* ---------- 进度 ---------- */
  progressQuestionColor: '#fff',
  progressBarDefaultColor: 'rgba(0, 0, 0, 0.4)',
  progressBarFilledColor: '#FFF100',
  progressDecorationImage: new URL('./assets/clock.png', import.meta.url).href,
  progressTextColor: '#fff',

  /* ---------- 题目 ---------- */
  questionFontSize: 14,
  questionColor: '#fff',

  /* ---------- 选项 ---------- */
  optionSelectedColor: '#333',
  optionSelectedBackground: '#00DA9F',
  optionSelectedBorderColor: '#eee',

  optionErrorColor: '#fff',
  optionErrorBackground: '#F10600',
  optionErrorBorderColor: '#eee',

  optionUnselectedColor: '#fff',
  optionUnselectedBackground: 'rgba(0, 0, 0, 0.4)',
  optionUnselectedBorderColor: '#8E82FF',

  /* ---------- 其他 ---------- */
  answerStatusColor: '#fff',
  eliminatedText: '您被淘汰，无法继续作答',
  answerCorrectText: '回答正确',
  annotationColor: '#fff',
  submitButtonImage: new URL('./assets/confirm.png', import.meta.url).href,
}

// 数据类型约束
export interface IDesignMobileAnswerraceIng extends IDesignLayer {
  type: typeof type
  data: Partial<typeof DEFAULT_DATA>
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.sportsIng,
    type,
    name: '答题进行中',
    showInteractive: [InteractiveEnum.answerracev3],
    showType: ['mobile'],
    Comp,
    CompSetting,
    thumbnail: new URL('./mobile-answerrace-ing.png', import.meta.url).href,
    defaultData(options): IDesignMobileAnswerraceIng {
      return merge({
        uuid: layerUuid(),
        name: '答题进行中',
        type,
        style: {
          width: '375px',
          height: '820px',
        },
        isPercent: 1,
        data: {},
      }, options as IDesignMobileAnswerraceIng)
    },
  })
}
