<script setup lang="ts">
import type { IDesignLayer, IDesignLayerEventType } from '../../../types'
import { useDesignTemp } from '../../..'
import { InteractiveEnum } from '../../../types'

import { getInteractive } from '../../../utils'
import HiBusiness from './business.vue'
import HiInteract from './interact.vue'
import HiLink from './link.vue'
import HiPhone from './phone.vue'
import HiShowHide from './show-hide.vue'
import HiStatus from './status.vue'

defineProps<{
  templateId?: string
}>()

const deisgnTemp = useDesignTemp()

const layer = defineModel<IDesignLayer>('layer', { required: true })

interface IComponentValue {
  showType?: string[]
  showInteractive?: InteractiveEnum[]
  component: Component
}
const all: { [k in IDesignLayerEventType]: IComponentValue } = {
  'show-hide': {
    component: HiShowHide,
  },
  'link': {
    component: HiLink,
  },
  'phone': {
    component: HiPhone,
  },
  'business': {
    component: HiBusiness,
  },
  // 跳转互动
  'interact': {
    showType: ['mobile'],
    showInteractive: [InteractiveEnum.microsite],
    component: HiInteract,
  },
  'status': {
    showType: ['mobile'],
    showInteractive: [InteractiveEnum.microsite, InteractiveEnum.listlotteryv3, InteractiveEnum.piclotteryv3, InteractiveEnum.seglottery],
    component: HiStatus,
  },
}

function getTitle(type: IDesignLayerEventType) {
  const component = all[type].component
  return 'label' in component ? component.label : ''
}

const isMultiple = ref(true)
const events = computed({
  get() {
    const alltype = (layer.value.events || []).map(item => item.type)
    if (isMultiple.value) {
      return alltype
    }
    return alltype[0]
  },
  set(value: IDesignLayerEventType[]) {
    if (!Array.isArray(value)) {
      value = [value]
    }

    if (!layer.value.events) {
      layer.value.events = []
    }
    if (value.length === 0) {
      layer.value.events = []
      return
    }

    // diff 新增、删除
    const _events = Array.isArray(events.value) ? events.value : [events.value]
    const add = value.filter(type => !_events.includes(type))
    const del = _events?.filter(type => !value.includes(type))

    layer.value.events.filter(item => del.includes(item.type)).forEach((item) => {
      layer.value.events?.splice(layer.value.events.indexOf(item), 1)
    })

    add.forEach((type) => {
      layer.value.events?.push({ event: 'click', type, value: null })
    })
  },
})

function removeEvent(type: IDesignLayerEventType) {
  if (!isMultiple.value) {
    events.value = []
  } else if (Array.isArray(events.value)) {
    events.value = events.value.filter(item => item !== type)
  }
}

const allEvents = computed(() => {
  return Object.entries(all)
    .filter(([_type, value]) => !value.showType || value.showType.includes(deisgnTemp.showType || ''))
    .filter(([_type, value]) => !value.showInteractive || value.showInteractive.includes(getInteractive()))
    .filter(([_type]) => _type !== 'phone') // 电话暂不支持
})
</script>

<template>
  <div class="trigger-box">
    <div class="wrap">
      <h2 class="pl-6px">{{ layer.name }}</h2>
      <el-select
        v-model="events"
        :multiple="isMultiple"
        collapse-tags
        placeholder="请选择事件类型"
        clearable
        :popper-options="{ placement: 'right-start' }"
        class="w-160"
      >
        <el-option
          v-for="[type, value] of allEvents"
          :key="type"
          :label=" 'label' in value.component ? value.component.label : '' "
          :value="type"
        >
        </el-option>
      </el-select>
    </div>
    <template v-if="layer.events">
      <div v-for="item, index in layer.events" :key="item.type" class="selected-item">
        <div class="title">
          <span>{{ getTitle(item.type) }}</span>
          <icon-ph-x class="x-box" @click="removeEvent(item.type)" />
        </div>
        <component :is="all[item.type].component" v-model:layer="layer" v-model:event="layer.events[index]" />
      </div>
    </template>
  </div>
</template>

<style scoped lang="scss">
.trigger-box {
  background-color: #fff;
  margin-bottom: 10px;
}
.wrap {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
  padding: 10px;
  > h2 {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .item-box {
    width: calc((100% - 20px) / 3);
    height: 60px;
    border: 1px solid #ccd5db;
    border-radius: 5px;
    transition: 0.2s;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    background-color: #fff;

    &.selected,
    &:hover {
      background-color: var(--el-color-primary);
      color: #fff;
    }

    .icon {
      font-size: 16px;
    }
    h3 {
      font-size: 12px;
      margin: 0;
    }
  }
}
.selected-item {
  padding: 10px;

  .title {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 14px;
    padding: 0 5px;
    border-left: 2px solid var(--el-color-primary);
    color: #666;
    svg {
      color: #aaa;
      &:hover {
        color: #666;
      }
    }
  }
}
</style>
