<script setup lang="ts">
// 动画相关
const transitionAttr = computed(() => {
  return {
    enterActiveClass: 'animate__animated animate__fadeIn',
    leaveActiveClass: 'animate__animated animate__fadeOut',
  }
})
</script>

<template>
  <div class="animate-box">
    <Transition v-bind="transitionAttr" appear>
      <div class="box"></div>
    </Transition>
  </div>
</template>

<style scoped lang="scss">
.animate-box {
}
.box {
  width: 300px;
  height: 300px;
  background: red;
}
</style>
