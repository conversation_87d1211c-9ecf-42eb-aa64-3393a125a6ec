<script setup lang="ts">
import { defineCustomEmits, EMITS_EVENT_SHOW_HIDE, useDesignTemp } from '../..'
import { defaultData, type IDesignMobileGameRule } from './mobile-game-rule'

const layer = defineModel<IDesignMobileGameRule>('layer', { required: true })
const customEmits = defineCustomEmits(layer)
const designTemp = useDesignTemp()

const version = computed(() => {
  return layer.value.version || 1
})

const titleImg = computed(() => {
  return layer.value.data.titleImg || defaultData.titleImg
})

const bgImg = computed(() => {
  return layer.value.data.bgImg || defaultData.bgImg
})

const closeImg = computed(() => {
  return layer.value.data.closeImg || defaultData.closeImg
})

const textColor = computed(() => {
  return layer.value.data.textColor || defaultData.textColor
})

function close() {
  // 编辑模式不可操作
  if (designTemp.isEdit) {
    return
  }
  customEmits(EMITS_EVENT_SHOW_HIDE, 'hide')
}
</script>

<template>
  <div class="overlay">
    <div
      v-if="version >= 2"
      class="version-2 mobile-game-rule"
      :style="{
        color: textColor,
        backgroundImage: `url(${bgImg})`,
      }"
    >
      <img :src="titleImg" alt="title" class="title-img">
      <img :src="closeImg" alt="close" class="close-img" @click="close">
      <p class="text">
        {{ layer.data.text }}
      </p>
    </div>

    <div
      v-else
      class="mobile-game-rule relative"
      :style=" {
        '--close-color': layer.data.closeColor || '#B56A1C',
        '--title-color': layer.data.titleColor || '#FFE8B6',
        '--title-bg-color': layer.data.titleBgColor || '#FF5400',
        '--text-color': layer.data.textColor || '#ae5d0d',
        '--text-bg-color': layer.data.textBgColor || '#f8d69f',
        '--text-border-color': layer.data.textBorderColor || '#50492d30',
      }"
    >
      <button class="absolute-tr size-40px -top-80px" @click="close">
        <icon-ph-x-circle-duotone class="size-full text-[var(--close-color)]"></icon-ph-x-circle-duotone>
      </button>
      <div class="box">
        <div class="title">
          <p>{{ layer.data.title || '游戏规则' }}</p>
        </div>
        <p class="text">
          {{ layer.data.text }}
        </p>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
}
.mobile-game-rule {
  width: 70%;
  .box {
    background-color: var(--text-bg-color);
    border: 4px solid var(--text-border-color);
    border-radius: 10px;
    padding: 4%;
  }
  .title {
    margin-top: -32px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    p {
      font-size: 24px;
      background-color: var(--title-bg-color);
      padding: 10px 30px;
      border-radius: 40px;
      color: var(--title-color);
      font-size: 14px;
    }
  }
  .text {
    color: var(--text-color);
    white-space: pre-wrap;
    word-wrap: break-word;
    text-align: justify;
    line-height: 1.6;
    font-size: 15px;
  }

  &.version-2 {
    min-height: 150px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: top center;
    position: relative;
    padding: 0 4% 4%;
    p {
      max-height: 400px;
      overflow-y: auto;
    }
  }
  .title-img {
    display: block;
    max-width: 60%;
    object-fit: contain;
    margin: -10% auto 10px;
  }

  .close-img {
    width: 44px;
    height: 44px;
    object-fit: contain;
    position: absolute;
    top: -56px;
    right: -10px;
  }
}
</style>
