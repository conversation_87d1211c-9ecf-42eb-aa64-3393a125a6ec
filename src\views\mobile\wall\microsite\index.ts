import { randomAvatar } from '@/utils'
import { useImData } from '~/src/hooks/useImData'
import { useInstanceRouter } from '~/src/hooks/useInstanceRouter'
import { dateNormalize } from '~/src/utils/date'
import { useDesignData, useDesignState, useDesignTemp } from '~/src/views/__/design'
import { useMobileStore } from '../../stores/useMobileStore'
import { fetchTheme } from '../../utils'

interface IConfig {
  themeId: number
}
export function useMobileMicrosite() {
  const designTemp = useDesignTemp()
  const designData = useDesignData()
  const designState = useDesignState()

  designTemp.showType = 'mobile'

  const router = useInstanceRouter()
  const route = router.currentRoute
  const mobileStore = useMobileStore()
  const { wall, featureConfig, isShowAd, isWedding } = storeToRefs(mobileStore)

  // 配置
  const config = ref<IConfig>()
  const allConfig = ref<any>({})
  // 启用的功能模块
  const openActArr = ref<string[]>([])
  // 微信用户信息
  const wxuser = ref()
  // 广告
  const adResponse = ref()
  // 状态在数据中存储
  watch(
    () => designData.status,
    (dataStatus) => {
      if (!dataStatus) return
      if (JSON.stringify(dataStatus) !== JSON.stringify(designState.statusList)) {
        designState.setStatusList(dataStatus)
        designState.setStatus(dataStatus[0].value)
      }
    },
    { immediate: true, deep: true },
  )

  watch(() => designState.status, (v) => {
    window.parent.postMessage({
      type: 'iframe:set-title',
      data: {
        title: designState.statusList.find(item => item.value === v)?.label || '',
      },
    }, '*')
  })

  watchOnce(
    () => designData.$ready,
    () => {
      if (!designData.status?.length) {
        designState.setStatusList([{ label: '首页', value: 'default' }])
      }
    },
  )

  const mobContent = computed(() => JSON.parse(designTemp.theme?.mobContent || '{}'))
  watch(() => mobContent.value.status, () => {
    if (designTemp.isEdit) {
      return
    }
    // 结合路由动态决定渲染的status
    if (!designState.status || mobContent.value.status.includes(designState.status)) {
      designState.setStatus(route.value.query.status || mobContent.value.status[0].value)
    }
    designState.setStatusList(mobContent.value.status || [])
  })

  // 路由和状态绑定，前进 next -> m更新hash，后退 m -> next更新status
  const lastStatus: string[] = []

  watch(() => designState.status, (v, o) => {
    if (o && v) {
      lastStatus.push(o)
      window.parent.postMessage({ type: 'iframe:set-hash', data: { status: v } }, '*')
    }
  })

  useEventListener('message', (e: MessageEvent) => {
    if (e.data.type === 'iframe:back') {
      const last = lastStatus.pop()
      designState.setStatus(last || designState.statusList[0].value)
    }
  })

  // 主要作用是将数据暴露给图层组件
  designState
    .setLayerData({
      '#主题名称#': computed(() => designTemp.theme?.name),
      '#活动名称#': computed(() => wall.value?.theme),
      '#活动时间#': computed(() => {
        const { startDate, endDate } = wall.value
        if (startDate && endDate) {
          const format = 'YYYY/MM/DD'
          return `${dateNormalize(startDate, format)} - ${dateNormalize(endDate, format)}`
        }
      }),
      'isShowAd': computed(() => isShowAd.value),
      'adResponse': computed(() => adResponse.value),
      'isWedding': computed(() => isWedding.value),
      'openActArr': computed(() => openActArr.value),
      'wxuser': computed(() => wxuser.value),
      'wall': computed(() => wall.value),
      'featureConfig': computed(() => featureConfig.value),
      'isMsgOpen': computed(() => allConfig.value?.wallmsgConfig?.openState === 'Y'),
    })
    // 图层组件事件
    .setLayerEventList([
      {
        eventId: 'back-home',
        name: '返回首页',
        value() {
          // 微站模块本身就是首页，改成status置为第一个就行
          designState.setStatus(designState.statusList[0].value)
        },
      },
      // 广告交互
      {
        eventId: 'clickAd',
        value(data) {
          window.parent.postMessage({ type: 'iframe:clickAd', data }, '*')
        },
      },
      {
        eventId: 'SHOW_AD',
        value(data) {
          window.parent.postMessage({ type: 'iframe:SHOW_AD', data }, '*')
        },
      },
    ])

  watch(() => config.value?.themeId, themeId => fetchTheme(themeId || ''))

  const initMock = () => {
    wxuser.value = {
      nickName: '测试用户',
      imgpath: randomAvatar(),
    }
    config.value = { themeId: 72 }
  }
  async function fetchWall() {
    let data = {}
    const wallId = route.value.query.wallId
    if (designTemp.isAdmin) {
      data = await api.admin.wall.read({ where: { id: wallId } })
    } else if (designTemp.isManage) {
      data = { name: '活动标题', theme: '活动名称', startDate: '1970-01-01 00:00:00', endDate: '1970-01-01 00:00:00' }
    }
    wall.value = data
  }
  async function fetchWallConfig() {
    const data: string[] = []
    const wallId = route.value.query.wallId
    if (designTemp.isAdmin) {
      const res: any = await api.admin.wall.allConfig({ where: { wallId } })
      Object.keys(res).forEach((key: string) => {
        if (res[key].openState === 'Y' && key !== 'wallmsg') {
          data.push(key.replace('Config', ''))
        }
      })
      openActArr.value = data
      allConfig.value = res
    } else if (designTemp.isManage) {
      openActArr.value = ['showAll']
    }
  }
  async function fetchFeatureConfig() {
    let data = {}
    const wallId = route.value.query.wallId
    if (designTemp.isAdmin) {
      data = await api.admin.wall.featureconfigRead({ where: { wallId } })
    } else if (designTemp.isManage) {
      data = { phoneAdvertisementSwitch: 'Y' }
    }
    featureConfig.value = data
  }
  tryOnMounted(() => {
    // 预览也是admin但是不是edit，也需要请求数据
    if (designTemp.isEdit || designTemp.isAdmin) {
      initMock()
      fetchWall()
      fetchWallConfig()
      fetchFeatureConfig()
    }
    const themeId = route.value.query.themeId
    if (themeId) {
      fetchTheme(themeId as string)
    }
  })

  // 数据同步
  useImData({
    'im:microsite:config': config,
    'im:wall': wall,
    'im:feature:config': featureConfig,
    'im:allConfig': allConfig,
    'im:openActArr': openActArr,
    'im:wxuser': wxuser,
    'im:adResponse': adResponse,
  })
}
