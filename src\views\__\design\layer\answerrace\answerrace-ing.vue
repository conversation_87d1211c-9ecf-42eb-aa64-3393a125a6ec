<script setup lang="ts">
import { initKeybindScript, useShortcutKey } from '@/views/__/design/hooks/useShortcutKey'
import { defineCustomEmits, injectScale, useDesignState, useDesignTemp } from '../../index'
import { processStyle } from '../../utils'
import { defaultData, editTempData, type IDesignAnswerIng } from './answerrace-ing'
import rankTable from './components/ranktable.vue'
import readyDown from './components/readydown.vue'

const layer = defineModel<IDesignAnswerIng>('layer', { required: true })
const designState = useDesignState()
const customEmits = defineCustomEmits(layer)

const timestamp = useTimestamp()
const designTemp = useDesignTemp()
const isDesignEdit = designTemp.isEdit
const scale = injectScale()

const readyDownImg = computed(() => { return layer.value.readyDownImg ?? defaultData.readyDownImg })
const readyDownColor = computed(() => { return layer.value.readyDownColor ?? defaultData.readyDownColor })
// 题目
const ingDownColor = computed(() => { return layer.value.ingDownColor ?? defaultData.ingDownColor })
const subjectColor = computed(() => { return layer.value.subjectColor ?? defaultData.subjectColor })
const subjectFontSize = computed(() => { return layer.value.subjectFontSize ?? defaultData.subjectFontSize })
const optionFontSize = computed(() => { return layer.value.optionFontSize ?? defaultData.optionFontSize })
const optionColor = computed(() => { return layer.value.optionColor ?? defaultData.optionColor })
const optionBgColor = computed(() => { return layer.value.optionBgColor ?? defaultData.optionBgColor })
const optionActiveColor = computed(() => { return layer.value.optionActiveColor ?? defaultData.optionActiveColor })
const optionActiveBgColor = computed(() => { return layer.value.optionActiveBgColor ?? defaultData.optionActiveBgColor })
const optionImgAlign = computed(() => { return layer.value.optionImgAlign ?? defaultData.optionImgAlign })
// btn
const pauseBtnColor = computed(() => { return layer.value.pauseBtnColor ?? defaultData.pauseBtnColor })
const nextBtnColor = computed(() => { return layer.value.nextBtnColor ?? defaultData.nextBtnColor })
// rank
const rankTitleImg = computed(() => { return layer.value.rankTitleImg ?? defaultData.rankTitleImg })

type SubjectType = 'TEXT' | 'IMAGE' | 'VIDEO' | 'AUDIO'

const subjextState = computed(() => {
  if (isDesignEdit) {
    return editTempData.value.lookState
  }
  return designState.getLayerData('subjectStatus')
})
const nowSubject = computed(() => {
  return designState.getLayerData('nowSubject') || {}
})

const subjectTips = computed(() => {
  const obj = {
    read: '请读题',
    answering: '请答题',
    result: '等待下一题',
    rank: '等待下一题',
  }
  type StateKey = keyof typeof obj
  const key = subjextState.value as StateKey
  return obj[key] || ''
})
const answerText = computed(() => {
  if (isDesignEdit) {
    return 'D'
  }
  return nowSubject.value.answer || ''
})
const options = computed(() => {
  if (isDesignEdit) {
    return [
      { title: 'A. 能吃' },
      { title: 'B. 不能吃' },
      { title: 'C. 洗干净能吃' },
      { title: 'D. 削了芽能吃', img: new URL('@/assets/answer/answer1.png', import.meta.url).href, rightAnswer: 'Y' },
    ]
  }
  const arr = JSON.parse(nowSubject.value.options || '[]')
  const indexList = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H']
  arr.forEach((item: any, index: number) => {
    item.title = item.title ? (`${indexList[index]}、${item.title}`) : indexList[index]
  })
  return arr
})

const ingRankingsSubject = computed(() => {
  return designState.getLayerData('ingRankingsSubject') || []
})
const subjectContent = computed(() => {
  if (isDesignEdit) {
    const sourceObj: Record<SubjectType, string | null> = {
      TEXT: null,
      IMAGE: new URL('@/assets/answer/tudou.png', import.meta.url).href,
      VIDEO: 'https://res3.hixianchang.com/qn/material/0/27c777eee76f4bd6a6cdf956a823d2c6.mp4',
      AUDIO: new URL('@/assets/answer/audio.MP3', import.meta.url).href,
    }
    return {
      type: editTempData.value.lookType,
      content: '（单选题）发芽了的土豆能吃吗？',
      resource: sourceObj[editTempData.value.lookType as SubjectType],
    }
  }
  const radioType = nowSubject.value.type || 'RADIO'
  const obj = {
    RADIO: '（单选题）',
    CHECKBOX: '（多选题）',
  }
  const sub = JSON.parse(nowSubject.value.subjectContent || '{}')
  sub.content = `${obj[radioType as 'RADIO' | 'CHECKBOX']}${sub.content ? sub.content : ''}`
  return sub
})
const subjectType = computed(() => {
  if (isDesignEdit) {
    return editTempData.value.lookType
  }
  return subjectContent.value.type
})
const remark = computed(() => {
  if (isDesignEdit) {
    return '土豆发芽后，毒素会集中在芽上，吃了会中毒。'
  }
  return nowSubject.value.remark || ''
})

const showCommentSwitch = computed(() => {
  return designState.getLayerData('showCommentSwitch') && subjextState.value === 'result' && remark.value.trim() !== ''
})
const joinType = computed(() => {
  return designState.getLayerData('joinType')
})

const scoreName = computed(() => {
  return joinType.value === 'PERSONAL' ? '分数/用时' : '分数/正确率'
})

const wrapStyle = computed(() => {
  return processStyle ({
    '--subject-color': subjectColor.value,
    '--subject-font-size': `${subjectFontSize.value}px`,
    '--ready-down-color': readyDownColor.value,
    '--ing-down-color': ingDownColor.value,
    '--option-font-size': `${optionFontSize.value}px`,
    '--option-color': optionColor.value,
    '--option-bg-color': optionBgColor.value,
    '--option-active-color': optionActiveColor.value,
    '--option-active-bg-color': optionActiveBgColor.value,
    '--option-img-align': optionImgAlign.value,
    '--rank-bg-color': layer.value.rankBgColor ?? defaultData.rankBgColor,
    '--rank-border-color': layer.value.rankBorderColor ?? defaultData.rankBorderColor,
    '--pausebtn-color': pauseBtnColor.value,
    '--nextbtn-color': nextBtnColor.value,
    '--rankbtn-color': layer.value.rankBtnColor ?? defaultData.rankBtnColor,
    '--colserankbtn-color': layer.value.closerankBtnColor ?? defaultData.closerankBtnColor,
  }, scale.value)
})
const optionStyle = computed(() => {
  const obj: any = {
    left: 'flex-start',
    center: 'center',
    right: 'flex-end',
  }
  return {
    'align-items': obj[optionImgAlign.value],
  }
})
function isRight(item: any) {
  if (!['result'].includes(subjextState.value)) {
    return false
  }
  return item.rightAnswer === 'Y'
}
const downFlagTime = computed(() => {
  return designState.getLayerData('downFlagTime') || null
})
const changeSubDownTime = computed(() => {
  if (designState.getLayerData('changeSubDownTime') !== null) {
    return Math.ceil(designState.getLayerData('changeSubDownTime') / 1000)
  }
  return null
})
// 每个阶段持续事件
const planeTimes = computed(() => {
  return designState.getLayerData('planeTimes') || {}
})
const downTime = computed(() => {
  if (['result', 'rank'].includes(subjextState.value) && changeSubDownTime.value) {
    // 如果是结果页，且有变更的时间，则使用变更的时间
    return changeSubDownTime.value
  }
  if (!downFlagTime.value) return 0
  // 计算距离的秒数
  const x = downFlagTime.value - timestamp.value
  if (x < 0) {
    return designTemp.isEdit ? 0 : 0
  }
  return Math.ceil(x / 1000)
})

const progressPercentage = computed(() => {
  if (isDesignEdit) return 100
  if (!downTime.value) return 0
  if (subjextState.value === '321') return 0
  if (subjextState.value === 'read') {
    const percentage = (1 - Number(downTime.value) / planeTimes.value.watchTime) * 100
    return Math.max(0, Math.min(100, percentage))
  }
  if (subjextState.value === 'answering') {
    const percentage = (1 - Number(downTime.value) / planeTimes.value.answerTime) * 100
    return Math.max(0, Math.min(100, percentage))
  }
  return 100
})
const showNextBtn = computed(() => {
  if (['result', 'rank'].includes(subjextState.value)) return true
  return false
})

const nextBtnText = computed(() => {
  if (isDesignEdit) return '下一题'
  return changeSubDownTime.value ? `下一题（${changeSubDownTime.value}）` : '下一题'
})

const rankConfig = computed(() => {
  return {
    headerSize: layer.value.rankHeaderSize ?? defaultData.rankHeaderSize,
    sortFontSize: layer.value.rankSortFontSize ?? defaultData.rankSortFontSize,
    sortColor: layer.value.rankSortColor ?? defaultData.rankSortColor,
    rankBgColor: layer.value.rankBgColor ?? defaultData.rankBgColor,
    rankBorderColor: layer.value.rankBorderColor ?? defaultData.rankBorderColor,
    titleFontSize: layer.value.rankTitleFontSize ?? defaultData.rankTitleFontSize,
    titleColor: layer.value.rankTitleColor ?? defaultData.rankTitleColor,
    contentTextAlign: layer.value.rankContentTextAlign ?? defaultData.rankContentTextAlign,
    contentColor: layer.value.rankContentColor ?? defaultData.rankContentColor,
    contentFontSize: layer.value.rankContentFontSize ?? defaultData.rankContentFontSize,
  }
})

const pauseBtnText = computed(() => {
  if (isDesignEdit) return '暂停'
  const isPause = designState.getLayerData('isPause') || false
  return isPause ? '继续' : '暂停'
})

function nextSubject() {
  if (isDesignEdit) return
  if (['321', 'read', 'answering'].includes(subjextState.value)) {
    return
  }
  customEmits('nextSubject')
}
const hidePauseBtn = computed(() => {
  return designState.getLayerData('hidePauseBtn')
})
function pauseAnswer() {
  if (isDesignEdit) return
  if (hidePauseBtn.value) return
  if (['321', 'read', 'answering'].includes(subjextState.value)) {
    return
  }
  customEmits('pause')
}

function changeRank() {
  if (isDesignEdit) return
  if (subjextState.value === 'rank') {
    customEmits('setTemState', 'result')
  } else {
    customEmits('setTemState', 'rank')
  }
}

function initKeybind() {
  // 快捷键
  if (!isDesignEdit) {
    try {
      initKeybindScript()
    } catch (err) {
      console.error(err)
    }
    const keyboard: Record<string, {
      checkIsWork: () => boolean
      callback: () => void
    }> = {}
    const keybindEvents = [
      { keyboard: ' ', fn: pauseAnswer },
      { keyboard: 'arrowRight', fn: nextSubject },
      { keyboard: 'arrowLeft', fn: changeRank },
    ]
    for (const item of keybindEvents) {
      keyboard[item.keyboard] = {
        checkIsWork: () => true,
        callback: item.fn,
      }
    }
    if (Object.keys(keyboard).length) {
      useShortcutKey(keyboard)
    }
  }
}
initKeybind() // 初始化快捷键，立即执行

// const subjectStatus = computed(() => {
//   if (isDesignEdit) {
//     return editTempData.value.lookState
//   }
//   return designState.getLayerData('subjectStatus')
// })
watch(
  () => editTempData.value.lookState,
  () => {
    if (isDesignEdit) {
      customEmits('setEditLookState', editTempData.value.lookState)
    }
  },
  { immediate: true },
)

const rankLoading = computed(() => {
  return designState.getLayerData('subjectrankLoading') || false
})
onBeforeMount(() => {
})
</script>

<template>
  <div class="asnwerrace-ing" :style="wrapStyle">
    <ready-down v-if="subjextState === '321'" :down-time="downFlagTime" class="ready-down" :bg-img="readyDownImg" :color="readyDownColor"></ready-down>
    <div v-if="['read', 'answering', 'result'].includes(subjextState) " class="sub-answer-box w-full flex">
      <div class="center-content relative">
        <div class="time">
          <icon-ph:alarm-bold class="text-current" />
          <!-- <div class="progress-diy"><div class="inner"></div></div> -->
          <el-progress class="progress" :stroke-width="15" :percentage="100 - progressPercentage" :show-text="false" :color=" ingDownColor" />
          <div class="num">{{ downTime }}</div>
          <!-- <div>{{ `调试：${subjectStatus}` }}</div> -->
        </div>
        <div class="tips">{{ subjectTips }}</div>
        <div class="subject">
          <div v-if="subjectType !== 'TEXT'" class="subject-media">
            <img v-if="subjectType === 'IMAGE'" :src="subjectContent.resource" alt="">
            <video v-if="subjectType === 'VIDEO'" :src="subjectContent.resource" controls></video>
            <div v-if="subjectType === 'AUDIO'" class="audio flex">
              <audio preload="auto" controls :src="subjectContent.resource" />
            </div>
          </div>
          <div class="subject-text">{{ subjectContent.content }}</div>
          <div v-if="subjextState === 'result'" class="subject-answer">正确答案：{{ answerText }}</div>
        </div>
        <div class="options flex-w-w flex">
          <div v-for="(item, index) in options" :key="index" class="option-item" :class="[{ active: isRight(item) }]" :style="optionStyle">
            <div v-if="item.img" class="pic"> <img :src="item.img" alt=""></div>
            <div class="text">{{ item.title }}</div>
          </div>
        </div>
      </div>
      <div v-if="showCommentSwitch" class="notes h-360 w-160 p-5">
        <div class="note-title">注释：</div>
        <div>{{ remark }}</div>
      </div>
    </div>
    <div v-if="subjextState === 'rank'" class="rank-box">
      <div class="rank-title"><img :src="rankTitleImg" alt=""></div>
      <rank-table :loading="rankLoading" :score-name="scoreName" :rank-data="ingRankingsSubject" :config="rankConfig"> </rank-table>
    </div>
    <div v-if="['read', 'answering', 'result', 'rank'].includes(subjextState) " class="btn-box">
      <el-tooltip v-if="!hidePauseBtn" class="box-item" content="快捷键空格" placement="top-start">
        <div class="pause btn" @click="pauseAnswer">{{ pauseBtnText }}</div>
      </el-tooltip>
      <el-tooltip v-if="showNextBtn" class="box-item" content="快捷键➡" placement="top-start">
        <div class="btn next" @click="nextSubject">{{ nextBtnText }}</div>
      </el-tooltip>
      <el-tooltip v-if="subjextState === 'result'" class="box-item" content="快捷键⬅" placement="top-start">
        <div class="rank btn" @click="changeRank">查看本题排行</div>
      </el-tooltip>
      <el-tooltip v-if="subjextState === 'rank'" class="box-item" content="快捷键⬅" placement="top-start">
        <div class="close-rank btn" @click="changeRank">关闭本题排行</div>
      </el-tooltip>
    </div>
  </div>
</template>

<style scoped lang="scss">
.asnwerrace-ing {
  position: relative;
  width: 100%;
  height: 100%;
  min-width: 600px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}
.ready-down {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60%;
  height: 60%;
  transform: translate(-50%, -50%);
}
.sub-answer-box {
  height: calc(100% - 50px);
}
.center-content {
  width: calc(100% - 160px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}
.time {
  width: 85%;
  height: 50px;
  text-align: center;
  font-size: 24px;
  color: var(--ing-down-color);
  position: relative;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: space-between;
  .progress {
    width: calc(100% - 100px);
  }
  .num {
    width: 40px;
    height: 40px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    text-align: center;
    line-height: 40px;
    font-size: 20px;
    font-weight: bold;
    position: absolute;
    right: 0;
    top: 20px;
  }
}
.tips {
  width: 100%;
  height: 30px;
  text-align: center;
  font-size: 20px;
  color: #fff;
  line-height: 30px;
  margin: 8px 0;
}
.subject {
  width: 100%;
  min-height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  .subject-media {
    width: 256px;
    max-height: 144px;
    min-height: 100px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    img,
    video {
      width: 256px;
      height: 144px;
      object-fit: contain;
      object-position: center;
      border-radius: 5px;
    }
    .audio {
      overflow: hidden;
      height: 35px;
      border-radius: 100px;
      background-color: #f1f3f4;
      width: 100%;
      audio {
        width: 100%;
        height: 100%;
        padding-right: 2px;
        border-radius: 50px;
        -webkit-user-drag: none;

        &::-webkit-media-controls {
          overflow: hidden !important;
        }
        &::-webkit-media-controls-enclosure {
          width: calc(100%);
          margin-left: auto;
        }
      }
    }
  }
  .subject-text {
    width: 100%;
    text-align: center;
    font-size: 20px;
    color: #fff;
    margin-top: 10px;
  }
  .subject-answer {
    width: 100%;
    text-align: center;
    font-size: 18px;
    color: #fff;
    margin-top: 5px;
  }
}
.options {
  flex: 1;
  width: 100%;
  flex-wrap: wrap;
  min-width: 500px;
  width: 95%;
  justify-content: flex-start;
  align-content: flex-start;
  margin-top: 20px;
  overflow-y: auto;
  gap: 0 20px;
  .option-item {
    width: calc(50% - 10px);
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    flex-direction: column;
    margin-bottom: 10px;
    &.active {
      .pic {
        border: 1px solid var(--option-active-bg-color);
      }
      .text {
        background: var(--option-active-bg-color);
        color: var(--option-active-color);
        word-break: break-all;
      }
    }
    .pic {
      max-width: 100%;
      max-height: 80px;
      border: 1px solid #ccc;
      border-radius: 5px;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .text {
      width: 100%;
      font-size: var(--option-font-size);
      color: var(--option-color);
      border-radius: 5px;
      background: var(--option-bg-color);
      border: #ccc 1px solid;
      height: min-content;
      min-height: 35px;
      margin-top: 8px;
      line-height: 1.5;
      padding: 3px 5px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
    }
  }
}
.notes {
  border: #999 1px solid;
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.6);
  overflow: auto;
  color: #fff;
  position: absolute;
  right: 0;
  top: 65px;
  word-break: break-all;
  .note-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
  }
}
.btn-box {
  width: 100%;
  height: 35px;
  display: flex;
  justify-content: center;
  margin-top: 15px;
  gap: 0 15px;
  .btn {
    height: 35px;
    padding: 0 20px;
    line-height: 33px;
    min-width: 110px;
    border-radius: 20px;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    color: #fff;
    border: #ddd 1px solid;
    cursor: pointer;
    &:hover {
      filter: brightness(0.9);
    }
  }
  .pause {
    background: var(--pausebtn-color);
  }
  .next {
    background: var(--nextbtn-color);
  }
  .rank {
    background: var(--rankbtn-color);
  }
  .close-rank {
    background: var(--colserankbtn-color);
  }
}
.rank-box {
  width: 100%;
  height: calc(100% - 79px);
  border-radius: 15px;
  border: 3px solid var(--rank-border-color);
  padding: 60px 15px 15px 15px;
  position: relative;
  margin-top: 30px;
  font-size: 18px;
  background: var(--rank-bg-color);
  .rank-title {
    position: absolute;
    left: 50%;
    top: 0;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 100%;
      max-height: 80px;
      object-fit: contain;
    }
  }
}
</style>
