<script setup lang="ts">
import { cloneDeep, sampleSize } from 'lodash-es'
import { injectScale, useDesignState, useDesignTemp } from '../../index'
import { processStyle } from '../../utils'
import { DEFAULT_DATA, type IDesignPiclotteryIng1 } from './piclottery-ing1'

const layer = defineModel<IDesignPiclotteryIng1>('layer', { required: true })

const designTemp = useDesignTemp()
const designState = useDesignState()

const scale = injectScale()

const data = computed(() => {
  return { ...DEFAULT_DATA, ...layer.value.data }
})

// 参与人奖池
const regeditList = computed(() => {
  return designState.getLayerData('regeditList') || []
})

const importformShowOption = computed(() => {
  return designState.getLayerData('importformShowOption') || []
})

const peopleCount = computed(() => {
  return designState.getLayerData('#每次抽取人数#')
})

interface IEstimateMaxVisibleRowsParams {
  containerHeight: number
  imageHeight: number
  innerGap: number
  itemGap: number
  fontSizes: number[]
  enabledTextLines: boolean[]
  fontLineHeightRatio?: number
  conservativeOffset?: number
}

function estimateMaxVisibleRows({
  containerHeight,
  imageHeight,
  innerGap,
  itemGap,
  fontSizes,
  enabledTextLines,
  fontLineHeightRatio = 1,
  conservativeOffset = 0,
}: IEstimateMaxVisibleRowsParams): number {
  const lineCount = enabledTextLines.filter(Boolean).length
  const maxFontSize = Math.max(...fontSizes.slice(0, lineCount), 0)
  const fontHeight = maxFontSize * fontLineHeightRatio
  const difference = 10 // 预留差异值，避免计算误差导致的显示问题
  const itemHeight = imageHeight + innerGap + fontHeight * lineCount + itemGap + difference
  const rawCount = Math.floor(containerHeight / itemHeight)
  const safeCount = Math.max(0, rawCount - conservativeOffset)
  return safeCount
}

interface IEstimateMaxPerRowParams {
  containerWidth: number
  imageWidth: number
  itemGap: number
  conservativeOffset?: number
  buffer?: number
}

function estimateMaxPerRow({
  containerWidth,
  imageWidth,
  itemGap,
  conservativeOffset = 0,
  buffer = 4,
}: IEstimateMaxPerRowParams): number {
  // 每个 item 占宽：图片 + 间距（不加最后一个 item 的间距）
  const itemTotalWidth = imageWidth + itemGap

  // 有效宽度需要减去 buffer
  const availableWidth = containerWidth - buffer

  // 最多能放几个（向下取整），再保守减去 conservativeOffset
  const rawCount = Math.floor(availableWidth / itemTotalWidth)
  const safeCount = Math.max(0, rawCount - conservativeOffset)

  return safeCount
}

const maxDisplayCount = computed(() =>
  estimateMaxVisibleRows({
    containerHeight: Number.parseFloat(layer.value.style.height as string) || 0,
    imageHeight: data.value.imageHeight,
    innerGap: data.value.innerGap,
    itemGap: data.value.itemGap,
    fontSizes: data.value.contentStyle?.map(i => i.fontSize || 0) || [],
    enabledTextLines: importformShowOption.value,
  }) * estimateMaxPerRow({
    containerWidth: Number.parseFloat(layer.value.style.width as string) || 0,
    imageWidth: data.value.imageWidth,
    itemGap: data.value.itemGap,
  }),
)

const ingCount = computed(() => {
  return Math.min(designTemp.isEdit ? 20 : peopleCount.value, regeditList.value.length, maxDisplayCount.value)
})

const loadedImageCache = new Set<string>()

function preloadImages(urls: string[]) {
  urls.forEach((url) => {
    if (!url || loadedImageCache.has(url)) return
    const img = new window.Image()
    img.src = url
    img.onload = () => {
      loadedImageCache.add(url)
    }
  })
}

const randomSubset = shallowRef<any[]>([])
const lastRandomSubset = shallowRef<any[]>([])

function sampleNoRepeat(arr: any[], prevArr: any[], count: number, key = 'avatar') {
  // arr: 采样源，prevArr: 上一轮已用，count: 数量，key: 比较的唯一键
  if (!Array.isArray(arr) || arr.length === 0) return []
  if (!prevArr || !prevArr.length) return sampleSize(arr, Math.min(count, arr.length))
  // 1. 先随机采样
  const res = sampleSize(arr, count)
  // 2. 找出哪些位置跟上次一样
  for (let i = 0; i < res.length; i++) {
    if (!prevArr[i]) continue
    if (res[i][key] === prevArr[i][key]) {
      // 3. 从还没用过的里面找一个，换掉
      const candidate = arr.find(
        item =>
          !res.some(r => r[key] === item[key])
          && item[key] !== prevArr[i][key],
      )
      if (candidate) {
        res[i] = candidate
      }
    }
  }
  return res
}

function updateRandomSubset() {
  const cached = regeditList.value.filter((item: any) => !item.avatar || loadedImageCache.has(item.avatar))
  const notCached = regeditList.value.filter((item: any) => item.avatar && !loadedImageCache.has(item.avatar))
  const nextPreload = sampleSize(notCached, Math.min(Math.max(Math.round(ingCount.value / 2), ingCount.value - cached.length, 3), notCached.length))
  lastRandomSubset.value = cloneDeep(randomSubset.value)

  if (cached.length > ingCount.value) {
    randomSubset.value = sampleNoRepeat(cached, lastRandomSubset.value, ingCount.value)
  } else {
    randomSubset.value = sampleNoRepeat([...cached, ...nextPreload], lastRandomSubset.value, ingCount.value)
  }

  const urlsToPreload = nextPreload.map((item: any) => item.avatar).filter(Boolean)
  preloadImages(urlsToPreload)
}

// 每 300ms 自动刷新
useIntervalFn(updateRandomSubset, 120, {
  immediate: true,
})

const imageStyle = computed(() => {
  return processStyle({
    width: `${data.value.imageWidth}px`,
    height: `${data.value.imageHeight}px`,
    borderRadius: `${data.value.imageRadius}px`,
    border: `${data.value.borderWidth}px solid ${data.value.borderColor}`,
  }, scale.value)
})

const itemGap = computed(() => processStyle(`${data.value.itemGap}px`, scale.value))

const innerGap = computed(() => processStyle(`${data.value.innerGap}px`, scale.value))

const contentStyle = computed(() => {
  return data.value?.contentStyle?.map((item) => {
    return processStyle({
      flex: 1,
      fontSize: `${item.fontSize}px`,
      color: item.fontColor || '#000',
      fontWeight: item.fonBold ? 'bold' : 'normal',
    }, scale.value)
  })
})
</script>

<template>
  <div class="piclottery-ing1-box h-full w-full">
    <div
      class="h-full w-full flex flex-wrap items-center justify-around overflow-hidden text-white"
      :style="{
        gap: itemGap,
      }"
    >
      <div
        v-for="(item, index) in randomSubset"
        :key="item.id"
        class="flex flex-col items-center justify-center"
        :style="{
          gap: innerGap,
        }"
      >
        <div class="relative overflow-hidden" :style="imageStyle">
          <img
            v-if="lastRandomSubset[index]?.avatar"
            class="absolute z-0 h-full w-full"
            :src="lastRandomSubset[index]?.avatar"
            :style="{
              objectFit: data.imageMode,
            }"
          >
          <img
            class="absolute z-1 h-full w-full"
            :src="item?.avatar || data.placeholderHeadImg"
            :style="{
              objectFit: data.imageMode,
            }"
          >
        </div>
        <p
          v-if="importformShowOption?.[0]"
          :style="contentStyle?.[0]"
          class="line-clamp-1"
          :class="{
            invisible: !item.nameD0,
          }"
        >
          {{ item.nameD0 || '-' }}
        </p>
        <p
          v-if="importformShowOption?.[1]"
          :style="contentStyle?.[1]"
          class="line-clamp-1"
          :class="{
            invisible: !item.nameD1,
          }"
        >
          {{ item.nameD1 || '-' }}
        </p>
        <p
          v-if="importformShowOption?.[2]"
          :style="contentStyle?.[2]"
          class="line-clamp-1"
          :class="{
            invisible: !item.nameD2,
          }"
        >
          {{ item.nameD2 || '-' }}
        </p>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
