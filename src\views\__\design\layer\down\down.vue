<script setup lang="ts">
import type { IDesignDown } from './down'
import { envUtils } from '~/src/utils/env'
import { useDesignState, useDesignTemp } from '../..'

const layer = defineModel<IDesignDown>('layer', { required: true })
const timestamp = useTimestamp()
const designTemp = useDesignTemp()
const designState = useDesignState()

const downRef = ref<HTMLElement>()
const boxSize = useElementSize(downRef)
const boxStyle = computed(() => {
  const color = layer.value.color || '#fec500'
  const shadowColor = layer.value.shadowColor || 'rgba(0, 0, 0, 0)'

  const fontSize = Math.min(boxSize.width.value, boxSize.height.value)

  let textShadow = ''
  for (let i = 1; i <= Math.max(fontSize / 50, 6); i++) {
    textShadow += `,${i}px ${i}px ${shadowColor}`
  }

  return {
    fontSize: `${fontSize}px`,
    color,
    textShadow: textShadow.slice(1),
  }
})

const data = computed(() => {
  const downFlagTime = designState.getLayerData('downFlagTime')
  if (!downFlagTime) return ''
  // 计算距离的秒数
  const x = downFlagTime - (envUtils.isPlayWright ? Date.now() : timestamp.value)
  if (x < 0) {
    return designTemp.isEdit ? '0' : ''
  }
  return Math.ceil(x / 1000)
})
</script>

<template>
  <div v-if="data" ref="downRef" class="design-down-box" :style="boxStyle">
    <Transition>
      <span :key="data">{{ data }}</span>
    </Transition>
  </div>
</template>

<style scoped lang="scss">
.design-down-box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
}

// 组件内动画
.v-enter-active,
.v-leave-active {
  transition: opacity 0.8s ease;
  position: absolute;
}
.v-enter-from,
.v-leave-to {
  opacity: 0;
}
</style>
