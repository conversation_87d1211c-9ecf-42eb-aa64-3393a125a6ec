import { HiRequest } from '../request'

export default {
  list: (params: any) => HiRequest.post('/manage/admin/promateriallabel/list.htm', params),
  add: (params: any) => HiRequest.post('/manage/admin/promateriallabel/add.htm', params),
  update: (params: any) => HiRequest.post('/manage/admin/promateriallabel/update.htm', params),
  delete: (params: any) => HiRequest.post('/manage/admin/promateriallabel/delete.htm', params),
  batch: (params: any) => HiRequest.post('/manage/admin/promateriallabel/batch.htm', params),
  read: (params: any) => HiRequest.post('/manage/admin/promateriallabel/read.htm', params),
}
