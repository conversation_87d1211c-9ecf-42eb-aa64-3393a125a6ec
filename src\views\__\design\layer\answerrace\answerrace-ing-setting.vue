<script setup lang="ts">
import type { IDesignAnswerIng } from './answerrace-ing'
import { getDefaultMaterial, openSelectMaterial, useDataAttr } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'
import { defaultData, editTempData } from './answerrace-ing'

const layer = defineModel<IDesignAnswerIng>('layer', { required: true })

const stateOptions = [
  { label: '倒计时', value: '321' },
  { label: '看题', value: 'read' },
  { label: '答题中', value: 'answering' },
  { label: '结束', value: 'result' },
  { label: '排行榜', value: 'rank' },
]
const topicTypeOptions = [
  { label: '图', value: 'IMAGE' },
  { label: '文本', value: 'TEXT' },
  { label: '视频', value: 'VIDEO' },
  { label: '音频', value: 'AUDIO' },
]
const aglinOptions = [
  { label: '左对齐', value: 'left' },
  { label: '居中对齐', value: 'center' },
  { label: '右对齐', value: 'right' },
]
const subjectFontSizeBind = useDataAttr(layer.value, 'subjectFontSize', 24)
const readyDownColorBind = useDataAttr(layer.value, 'readyDownColor', defaultData.readyDownColor)
const readyDownImgBind = useDataAttr(layer.value, 'readyDownImg', defaultData.readyDownImg)
// rank
const rankTitleFontSizeBind = useDataAttr(layer.value, 'rankTitleFontSize', defaultData.rankTitleFontSize)
const rankTitleColorBind = useDataAttr(layer.value, 'rankTitleColor', defaultData.rankTitleColor)
const rankContentTextAlignBind = useDataAttr(layer.value, 'rankContentTextAlign', defaultData.rankContentTextAlign)
const rankSortColorBind = useDataAttr(layer.value, 'rankSortColor', defaultData.rankSortColor)
const rankSortFontSizeBind = useDataAttr(layer.value, 'rankSortFontSize', defaultData.rankSortFontSize)
const rankHeaderSizeBind = useDataAttr(layer.value, 'rankHeaderSize', defaultData.rankHeaderSize)
const rankContentFontSizeBind = useDataAttr(layer.value, 'rankContentFontSize', defaultData.rankContentFontSize)
const rankContentColorBind = useDataAttr(layer.value, 'rankContentColor', defaultData.rankContentColor)
const rankTitleImgBind = useDataAttr(layer.value, 'rankTitleImg', defaultData.rankTitleImg)
const rankBgColorBind = useDataAttr(layer.value, 'rankBgColor', defaultData.rankBgColor)
const rankBorderColorBind = useDataAttr(layer.value, 'rankBorderColor', defaultData.rankBorderColor)
// btn
const pauseBtnColorBind = useDataAttr(layer.value, 'pauseBtnColor', defaultData.pauseBtnColor)
const nextBtnColorBind = useDataAttr(layer.value, 'nextBtnColor', defaultData.nextBtnColor)
const rankBtnColorBind = useDataAttr(layer.value, 'rankBtnColor', defaultData.rankBtnColor)
const closerankBtnColorBind = useDataAttr(layer.value, 'closerankBtnColor', defaultData.closerankBtnColor)
const ingDownColorBind = useDataAttr(layer.value, 'ingDownColor', defaultData.ingDownColor)
const subjectColorBind = useDataAttr(layer.value, 'subjectColor', defaultData.subjectColor)
const optionFontSizeBind = useDataAttr(layer.value, 'optionFontSize', defaultData.optionFontSize)
const optionColorBind = useDataAttr(layer.value, 'optionColor', defaultData.optionColor)
const optionActiveColorBind = useDataAttr(layer.value, 'optionActiveColor', defaultData.optionActiveColor)
const optionBgColorBind = useDataAttr(layer.value, 'optionBgColor', defaultData.optionBgColor)
const optionActiveBgColorBind = useDataAttr(layer.value, 'optionActiveBgColor', defaultData.optionActiveBgColor)
const optionImgAlignBind = useDataAttr(layer.value, 'optionImgAlign', defaultData.optionImgAlign)

type IType = 'readyDownImg' | 'rankTitleImg'
async function updateMaterialFn(name: IType, isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, name)
    : await openSelectMaterial('PIC')
  if (result) {
    layer.value[name] = result
  } else if (isReset) {
    delete layer.value[name]
  }
}

const collList: Ref<string[]> = ref(['sub'])
watch(
  () => editTempData.value.lookState,
  (newVal) => {
    if (newVal === '321') {
      collList.value = ['321']
    } else if (newVal === 'read') {
      collList.value = ['sub']
    } else if (newVal === 'answering') {
      collList.value = ['sub']
    } else if (newVal === 'result') {
      collList.value = ['btn']
    } else if (newVal === 'rank') {
      collList.value = ['btn', 'rank']
    }
  },
  { immediate: true },
)
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <el-collapse v-model="collList">
        <div class="setting-group">
          <h3>预览切换</h3>
          <div class="setting-item">
            <h3>题目状态</h3>
            <el-select v-model="editTempData.lookState" placeholder="请选择预览状态" style="width: 120px">
              <el-option v-for="item in stateOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
          <div class="setting-item">
            <h3>题目类型</h3>
            <el-select v-model="editTempData.lookType" placeholder="请选择题目类型" style="width: 120px">
              <el-option v-for="item in topicTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
        </div>
        <el-collapse-item title="准备倒计时" name="321">
          <div class="setting-group">
            <div class="setting-item">
              <h3>背景</h3>
              <div class="h-80 w-80">
                <MaterialThumbnail @select="updateMaterialFn('readyDownImg')" @reset="updateMaterialFn('readyDownImg', true)">
                  <img :src="readyDownImgBind" class="bgblank object-contain">
                </MaterialThumbnail>
              </div>
            </div>
            <div class="setting-item">
              <h3>颜色</h3>
              <hi-color v-model="readyDownColorBind" />
            </div>
          </div>
        </el-collapse-item>
        <el-collapse-item title="题目选项" name="sub">
          <div class="setting-item">
            <h3>倒计时颜色</h3>
            <hi-color v-model="ingDownColorBind" />
          </div>
          <div class="setting-item">
            <h3>题目字号</h3>
            <el-input-number v-model="subjectFontSizeBind" v-input-number controls-position="right" :min="1" :step="1" />
          </div>
          <div class="setting-item">
            <h3>题目字色</h3>
            <hi-color v-model="subjectColorBind" />
          </div>
          <div class="setting-item">
            <h3>选项图对齐</h3>
            <el-select v-model="optionImgAlignBind" placeholder="选择选项图对齐" style="width: 120px">
              <el-option v-for="item in aglinOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
          <div class="setting-item">
            <h3>选项字号</h3>
            <el-input-number v-model="optionFontSizeBind" v-input-number controls-position="right" :min="1" :step="1" />
          </div>
          <div class="setting-item">
            <h3>选项字色</h3>
            <hi-color v-model="optionColorBind" />
          </div>
          <div class="setting-item">
            <h3>选项背景</h3>
            <hi-color v-model="optionBgColorBind" />
          </div>
          <div class="setting-item">
            <h3>选项正确背景</h3>
            <hi-color v-model="optionActiveBgColorBind" />
          </div>
          <div class="setting-item">
            <h3>选项正确字色</h3>
            <hi-color v-model="optionActiveColorBind" />
          </div>
        </el-collapse-item>
        <el-collapse-item title="操作按钮" name="btn">
          <div class="setting-item">
            <h3>暂停按钮颜色</h3>
            <hi-color v-model="pauseBtnColorBind" />
          </div>
          <div class="setting-item">
            <h3>下一题</h3>
            <hi-color v-model="nextBtnColorBind" />
          </div>
          <div class="setting-item">
            <h3>本题排行</h3>
            <hi-color v-model="rankBtnColorBind" />
          </div>
          <div class="setting-item">
            <h3>关闭排行</h3>
            <hi-color v-model="closerankBtnColorBind" />
          </div>
        </el-collapse-item>
        <el-collapse-item title="本题排行榜" name="rank">
          <div class="setting-group">
            <div class="setting-item">
              <h3>标题</h3>
              <div class="h-40 w-80">
                <MaterialThumbnail @select="updateMaterialFn('rankTitleImg')" @reset="updateMaterialFn('rankTitleImg', true)">
                  <img :src="rankTitleImgBind" class="bgblank object-contain">
                </MaterialThumbnail>
              </div>
            </div>
            <div class="setting-item">
              <h3>标题字号</h3>
              <el-input-number v-model="rankTitleFontSizeBind" v-input-number controls-position="right" :min="1" :step="1" />
            </div>
            <div class="setting-item">
              <h3>标题字色</h3>
              <hi-color v-model="rankTitleColorBind" />
            </div>
            <div class="setting-item">
              <h3>内容字号</h3>
              <el-input-number v-model="rankContentFontSizeBind" v-input-number controls-position="right" :min="1" :step="1" />
            </div>
            <div class="setting-item">
              <h3>内容对齐</h3>
              <el-select v-model="rankContentTextAlignBind" placeholder="请选择题目对齐" style="width: 120px">
                <el-option v-for="item in aglinOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>
            <div class="setting-item">
              <h3>题目字色</h3>
              <hi-color v-model="rankContentColorBind" />
            </div>
            <div class="setting-item">
              <h3>头像尺寸</h3>
              <el-input-number v-model="rankHeaderSizeBind" v-input-number controls-position="right" :min="10" :step="1" />
            </div>
            <div class="setting-item">
              <h3>序号字号</h3>
              <el-input-number v-model="rankSortFontSizeBind" v-input-number controls-position="right" :min="10" :step="1" />
            </div>
            <div class="setting-item">
              <h3>序号字色</h3>
              <hi-color v-model="rankSortColorBind" />
            </div>
            <div class="setting-item">
              <h3>边框颜色</h3>
              <hi-color v-model="rankBorderColorBind" />
            </div>
            <div class="setting-item">
              <h3>背景颜色</h3>
              <hi-color v-model="rankBgColorBind" />
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<style scoped lang="scss">
.setting-group {
  margin-top: 8px;
  margin-bottom: 8px;
  .setting-item {
    margin-left: 15px;
  }
}
</style>
