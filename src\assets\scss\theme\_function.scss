@use 'sass:math';
@use 'sass:map';
@use 'sass:color';

// @name: Hi现场主题色生成工具，基于element-plus/theme-chalk 修改简化
// @author: heshuaitao

$color-white: #ffffff !default;
$color-black: #000000 !default;

// join var name
// joinVarName(('button', 'text-color')) => '--el-button-text-color'
$namespace: 'el' !default;

@function joinVarName($list) {
  $name: --#{$namespace};

  @each $item in $list {
    @if $item != '' {
      $name: #{$name}-#{$item};
    }
  }

  @return $name;
}

@mixin set-css-var-value($name, $value) {
  #{joinVarName($name)}: #{$value};
}

@mixin set-css-color-type($color) {
  @include set-css-var-value(('color', 'primary'), $color);

  $colors: ();

  @for $i from 1 through 9 {
    $colors: map.deep-merge(
      (
        'light-#{$i}': mix($color-white, $color, math.percentage(math.div($i, 10))),
      ),
      $colors
    );
  }

  @each $i in (3, 5, 7, 8, 9) {
    @include set-css-var-value(('color', 'primary', 'light', $i), map.get($colors, 'light-#{$i}'));
  }

  // --el-color-primary-dark-2
  $colors: map.deep-merge(
    (
      'dark-2': mix($color-white, $color, math.percentage(math.div(2, 10))),
    ),
    $colors
  );

  @include set-css-var-value(('color', 'primary', 'dark-2'), map.get($colors, 'dark-2'));
}
