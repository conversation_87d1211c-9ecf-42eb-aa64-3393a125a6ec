import type { ProxyOptions } from 'vite'
import { resolve } from 'node:path'
import process from 'node:process'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import autoprefixer from 'autoprefixer'
import { codeInspectorPlugin } from 'code-inspector-plugin'
import postcssPresetEnv from 'postcss-preset-env'
import { visualizer } from 'rollup-plugin-visualizer'
import { optimize } from 'svgo'
import Unocss from 'unocss/vite'
import AutoImport from 'unplugin-auto-import/vite'
import { FileSystemIconLoader } from 'unplugin-icons/loaders'
import IconsResolver from 'unplugin-icons/resolver'
import Icons from 'unplugin-icons/vite'
import {
  ElementPlusResolver,
  TDesignResolver,
  VantResolver,
  VueUseComponentsResolver,
  VueUseDirectiveResolver,
} from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'
import { VueRouterAutoImports } from 'unplugin-vue-router'
import VueRouter from 'unplugin-vue-router/vite'
import { defineConfig, loadEnv } from 'vite'
import Compression from 'vite-plugin-compression'

import { PostCssPxToDesign } from './plugins/postcss-px-design'
import { HiResolver } from './plugins/resolver'
import { templateUnocssPxDesign } from './plugins/template-unocss-px-design'

// https://vitejs.dev/config/
const IS_OEPN_LOCAL_IFRAME_DEV = !!process.env.IS_OEPN_LOCAL_IFRAME_DEV

function proxyTarget(target: string): ProxyOptions {
  return {
    target,
    changeOrigin: true,
    ws: true,
    followRedirects: true,
    // secure: true,
    configure: (proxy) => {
      // proxy 是http-proxy -> createProxyServer 返回的实例，可用于监听事件
      proxy.on('proxyReq', (proxyReq, req) => {
        const host = target.split('/')[2]
        proxyReq.setHeader('Host', host)
        const { referer } = req.headers
        if (referer) {
          const arr = referer.split('/')
          arr[2] = host
          const result = arr.join('/')
          proxyReq.setHeader('referer', result)
        }
      })
      // 修改响应头，cookie SameSite=None; 允许跨域写入cookie，只能在https下使用，用于本地开发
      proxy.on('proxyRes', (proxyRes, req) => {
        // 从 proxyRes 中获取请求的 referer
        const { referer } = req.headers
        const isHttps = referer?.startsWith('https')

        const cookies = proxyRes.headers['set-cookie']
        if (cookies) {
          if (isHttps) {
            const newCookies = cookies.map((cookie: string) => {
              return `${cookie.replace(/SameSite=Lax/g, '')}SameSite=None; Secure`
            })
            proxyRes.headers['set-cookie'] = newCookies
          } else {
            // 非https时删除同源设置
            const newCookies = cookies.map((cookie: string) => {
              return `${cookie.replace(/Secure; SameSite=None;/g, '')}`
            })
            proxyRes.headers['set-cookie'] = newCookies
          }
        }
      })
    },
  }
}

const sslConfig = {
  https: {
    key: resolve(__dirname, './cert/test.hixianchang.com-key.pem'),
    cert: resolve(__dirname, './cert/test.hixianchang.com.pem'),
  },
}

export default ({ mode }: { mode: string }) => {
  const isDev = mode === 'dev'
  // 获取.env文件里定义的环境变量
  const env = loadEnv(mode, process.cwd(), '')

  let base = `${[env.VITE_APP_RESDOMAIN, env.VITE_APP_PREFIX].filter(i => i).join('/')}/`
  if (!base.startsWith('/')) {
    base = `/${base}`
  }

  const onDemandPlugins = []
  if (isDev) {
    // 定位DOM源码
    onDemandPlugins.push(codeInspectorPlugin({
      bundler: 'vite',
      hideDomPathAttr: true,
    }))
  }

  return defineConfig({
    base,
    optimizeDeps: {
      include: [
        'vue',
        '@vue/compiler-sfc',
        '@faker-js/faker',
        '@tweenjs/tween.js',
        '@vueuse/components',
        '@vueuse/core',
        '@vueuse/integrations',
        '@element-plus/icons-vue',
        'axios',
        'vue-router',
        'pinia',
        'element-plus',
        'tdesign-vue-next',
        'vant',
        'echarts',
        'lodash-es',
        'quill',
      ],
    },
    resolve: {
      alias: [
        { find: '~', replacement: resolve(__dirname, './') },
        { find: '@', replacement: resolve(__dirname, './src') },
        { find: 'design', replacement: resolve(__dirname, './src/views/__/design') },
        { find: 'pcwall', replacement: resolve(__dirname, './src/views/pcwall') },
        { find: 'mobile', replacement: resolve(__dirname, './src/views/mobile') },
      ],
    },
    plugins: [
      vue({
        script: {
          defineModel: true,
        },
        template: {
          transformAssetUrls: {
            video: ['src', 'poster'],
            source: ['src'],
            img: ['src'],
            image: ['xlink:href', 'href'],
            use: ['xlink:href', 'href'],
          },
        },
      }),
      vueJsx({
        // options are passed on to @vue/babel-plugin-jsx
        mergeProps: false,
        enableObjectSlots: false,
      }),
      // https://github.com/posva/unplugin-vue-router
      VueRouter({
        routesFolder: 'src/views',
        dts: './types/typed-router.d.ts',
        exclude: ['**/components/**', '**/__*', '**/__*/**/*'],
      }),
      templateUnocssPxDesign(),
      Unocss({}),
      AutoImport({
        include: [
          /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
          /\.vue$/,
          /\.vue\?vue/, // .vue
          /\.md$/, // .md
        ],
        imports: [
          'vue',
          '@vueuse/core',
          'pinia',
          VueRouterAutoImports,
          {
            'axios': [['default', 'axios']],
            '@/api': [['default', 'api']],
          },
        ],
        resolvers: [
          //
          ElementPlusResolver({ importStyle: false }),
          VantResolver({}),
          TDesignResolver({ library: 'vue-next' }),
          IconsResolver({ prefix: 'Icon' }),
          HiResolver(),
        ],
        dts: './types/auto-imports.d.ts',
      }),
      Components({
        dirs: ['src/components'],
        extensions: ['vue', 'tsx'],
        directoryAsNamespace: true,
        globalNamespaces: ['', 'test'],
        collapseSamePrefixes: true,
        dts: './types/components.d.ts',
        resolvers: [
          ElementPlusResolver({ importStyle: false }),
          VantResolver({}),
          TDesignResolver({ library: 'vue-next' }),
          IconsResolver({
            customCollections: ['custom'],
            componentPrefix: 'icon',
            enabledCollections: ['ph', 'ic'],
          }),
          VueUseComponentsResolver(),
          VueUseDirectiveResolver(),
          HiResolver(),
        ],
      }),
      // https://github.com/antfu/unplugin-icons
      Icons({
        compiler: 'vue3',
        autoInstall: true,
        customCollections: {
          custom: FileSystemIconLoader('./src/assets/icons', (svg) => {
            // 通过svgo对svg进行压缩、处理，也可以每次放svg时手动处理
            // https://github.com/svg/svgo#built-in-plugins
            const result = optimize(svg, {
              plugins: [
                {
                  name: 'preset-default',
                  params: {
                    overrides: {
                      inlineStyles: {
                        onlyMatchedOnce: false,
                      },
                    },
                  },
                },
              ],
            })
            return result.data || svg
          }),
        },
        defaultClass: 'inline-block',
      }),
      // gzip压缩 生产环境生成 .gz 文件
      Compression({
        verbose: true,
        disable: false,
        threshold: 10240,
        algorithm: 'gzip',
        ext: '.gz',
      }),
      // 打包分析
      visualizer({
        open: false,
        gzipSize: true,
        brotliSize: true,
      }),
      ...onDemandPlugins,
    ],
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern',
        },
      },
      postcss: {
        plugins: [
          PostCssPxToDesign(),
          postcssPresetEnv({
            features: {},
          }),
          autoprefixer({}),
        ],
      },
    },
    server: {
      host: IS_OEPN_LOCAL_IFRAME_DEV ? 'test.hixianchang.com' : '0.0.0.0',
      port: 5173,
      open: true,
      hmr: true,
      proxy: {
        '^/pro/hxc/web/polling/': proxyTarget('https://www.dev.hixianchang.com'),
        // '^/pro': proxyTarget('http://***************:8080/app-api/'),
        '^/pro': proxyTarget(process.env.VITE_PRO_PROXY_URL || 'https://www.dev.hixianchang.com'),
        '^/oem': proxyTarget('https://hd.dev.hidanmu.com'),
        '^/manage': proxyTarget('https://manage.dev.hidanmu.com'),
      },
      watch: {
        ignored: ['**/node_modules/**', '**/dist/**'],
      },
      ...(IS_OEPN_LOCAL_IFRAME_DEV ? sslConfig : {}),
    },
    define: {
      // 弃用options api,打包体积会缩小
      // 编译后启用devtools
      __VUE_PROD_DEVTOOLS__: ['dev'].includes(mode),
    },
    build: {
      target: 'es2017',
      sourcemap: false,
      modulePreload: {
        polyfill: false,
      },
      rollupOptions: {
        output: {
          // dynamicImportFunction: 'myImport',
          chunkFileNames: 'static/js/[name]-[hash].js',
          entryFileNames: 'static/js/[name]-[hash].js',
          assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
          manualChunks: {
            'vendor': [
              'vue',
              'vue-router',
              'pinia',
              '@vueuse/core',
              '@vueuse/components',
              '@vueuse/integrations',
              'lodash-es',
              'axios',
              'devtools-detector',
              'nprogress',
            ],
            'element-plus': ['element-plus'],
            'tdesign': ['tdesign-vue-next'],
            'vant': ['vant'],
            'echarts': ['echarts'],
            'quill': ['quill'],
            'color': ['color'],
            'faker': ['@faker-js/faker'],
            'qrcode': ['qrcode'],
            'three': ['three'],
          },
        },
      },
      assetsInlineLimit(filePath, content) {
        if (filePath.includes('/__/design/')) {
          return false
        }
        return content.length < 4096
      },
    },
  })
}
