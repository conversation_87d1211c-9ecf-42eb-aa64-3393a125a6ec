<script setup lang="ts">
import type { IDesignLotteryPrize } from './lottery-prize'
import { useDataAttr } from '../..'
import { defaultCountColor, defaultCoutFontSize, defaultNameColor, defaultNameFontSize, defaultPrizeImgHeight, defaultPrizeImgWidth, defaultShowPrizeCount, defaultShowPrizeName } from './lottery-prize'

const layer = defineModel<IDesignLotteryPrize>('layer', { required: true })

const prizeImgWidthBind = useDataAttr(layer.value, 'prizeImgWidth', defaultPrizeImgWidth)
const prizeImgHeightBind = useDataAttr(layer.value, 'prizeImgHeight', defaultPrizeImgHeight)
const nameColorBind = useDataAttr(layer.value, 'nameColor', defaultNameColor)
const countColorBind = useDataAttr(layer.value, 'countColor', defaultCountColor)
const coutFontSizeBind = useDataAttr(layer.value, 'coutFontSize', defaultCoutFontSize)
const nameFontSizeBind = useDataAttr(layer.value, 'nameFontSize', defaultNameFontSize)
const showPrizeNameBind = useDataAttr(layer.value, 'showPrizeName', defaultShowPrizeName)
const showPrizeCountBind = useDataAttr(layer.value, 'showPrizeCount', defaultShowPrizeCount)
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        实际大屏幕展示时将使用奖品设置-奖品图片。
      </div>
      <div class="setting-item">
        <h3>奖品图宽度</h3>
        <el-slider v-model="prizeImgWidthBind" :min="1" :max="100" :step="1" class="mx-10 flex-1" />
      </div>
      <div class="setting-item">
        <h3>奖品图高度</h3>
        <el-slider v-model="prizeImgHeightBind" :min="1" :max="100" :step="1" class="mx-10 flex-1" />
      </div>
      <div class="setting-item">
        <h3>奖品名颜色</h3>
        <hi-color v-model="nameColorBind" />
      </div>
      <div class="setting-item">
        <h3>奖品名字号</h3>
        <el-input-number v-model="nameFontSizeBind" v-input-number :max="50" :min="12" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>剩余数量颜色</h3>
        <hi-color v-model="countColorBind" />
      </div>
      <div class="setting-item">
        <h3>剩余数量字号</h3>
        <el-input-number v-model="coutFontSizeBind" v-input-number :max="50" :min="12" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>显示奖品名称</h3>
        <el-switch v-model="showPrizeNameBind" />
      </div>
      <div class="setting-item">
        <h3>显示奖品数量</h3>
        <el-switch v-model="showPrizeCountBind" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
</style>
