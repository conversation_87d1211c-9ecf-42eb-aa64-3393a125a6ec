import type { IDesignLayer, IDesignSetup } from '../../types'
import { BisTypes } from '../..'
import { InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './seglottery-ing-setting.vue'
import Comp from './seglottery-ing.vue'
// 类型
export const type = 'seglottery-ing'
export const defaultColor = '#fff'
export const defaultData = {
  itemBgImg: new URL('./assets/laohuji.png', import.meta.url).href,
  noStartImg: new URL('./assets/mask.png', import.meta.url).href,
  roolBgcolor: '#fff',
  roolBorderColor: '#03a9f4',
  roolWidth: 76,
  roolHeight: 45,
  roolLeft: 8,
  roolTop: 37,
  roolFontcolor: '#334400',
  roolFontSize: 24,
  itemCount: 1,
  roolGap: 5,
  roolLabelcolor: '#333',
  roolLabelSize: 14,
  roolLabelPostion: 'right',
  showLabel: true,
  animationMode: 'slotMachine',
}

// 数据类型约束
export interface IDesignSeglotteryIng extends IDesignLayer {
  type: typeof type
  animationMode?: 'default' | 'slotMachine'
  itemBgImg?: string
  noStartImg?: string
  itemCount?: number
  roolBgcolor?: string
  roolBorderColor?: string
  roolWidth?: number
  roolHeight?: number
  roolLeft?: number
  roolTop?: number
  roolFontcolor?: string
  roolFontSize?: number
  roolLabelcolor?: string
  roolLabelSize?: number
  roolGap?: number
  roolLabelPostion?: 'right' | 'bottom'
  showLabel?: boolean
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.sportsIng,
    type,
    name: '老虎机抽奖',
    showInteractive: [InteractiveEnum.seglottery],
    thumbnail: new URL('./seglottery-ing.png', import.meta.url).href,
    Comp,
    CompSetting,
    defaultData() {
      return {
        type,
        uuid: layerUuid(),
        name: '老虎机抽奖',
        style: {
          left: '0px',
          top: '100px',
          width: '600px',
          height: '400px',
        },
      }
    },
  })
}
