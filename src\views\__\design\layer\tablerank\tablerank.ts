import type { IDesignLayer, IDesignSetup } from '../../types'
import { BisTypes } from '../..'
import { InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './tablerank-setting.vue'
import Comp from './tablerank.vue'

// 类型
export const type = 'tablerank'
export const defaultColor = '#fff'
export const defaultShowTh = true
export const defaultShowBorder = true
export const defaultPadding = [20, 20, 20, 20]
export interface TableItemStyle {
  fontSize: number
  fontColor: string
  fontBold: boolean
  align: 'left' | 'center' | 'right'
  width: number
}
// 数据类型约束
export interface IDesignIngTablerank extends IDesignLayer {
  type: typeof type
  showTh?: boolean
  showBorder?: boolean
  avatarSize?: number
  titleFontSize: number
  titleFontColor: string
  contentStyle: TableItemStyle[]
  dataSource?: string
  paddingLeft?: number
  paddingRight?: number
  paddingTop?: number
  paddingBottom?: number
  data: {
    name: string
    avatar: string
    score: number
  }[]
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.sportsIng,
    showType: ['pcwall'],
    status: ['ing'],
    showInteractive: [InteractiveEnum.performancev3],
    type,
    name: '表格统计',
    thumbnail: new URL('./tablerank.png', import.meta.url).href,
    Comp,
    CompSetting,
    defaultData() {
      return {
        type,
        uuid: layerUuid(),
        name: '表格统计',
        avatarSize: 35,
        titleFontSize: 16,
        titleFontColor: '#fff',
        contentFontSize: 14,
        contentFontColor: '#fff',
        contentStyle: [],
        data: [],
        style: {
          right: '10px',
          top: '10px',
          width: '300px',
          height: '500px',
          background: '#0088ff',
        },
      }
    },
  })
}
