import type { IDesignLayer, IDesignSetup } from '../../types'
import { merge } from 'lodash-es'
import { BisTypes, openSelectMaterial, ResourceUtil } from '../..'

import { layerUuid } from '../../utils'
import CompSetting from './img-video-setting.vue'
import Comp from './img-video.vue'

export const type = 'img-video'

export type IMode = 'none' | 'contain' | 'fill' | 'cover' | 'auto'
export type IRepeat = 'no-repeat' | 'repeat-x' | 'repeat-y' | 'repeat'

export const defaultMode = 'contain'
export const defaultRepeat = 'no-repeat'
export const defaultSize = 100
export const defaultPosition = 50
export const defaultBgType = 'img' // img | video
export const defaultVideoObjectFit = 'fill' // 对应视频组件objectFit
export const defaultMuted = true
export const defaultLoop = true
export const defaultAutoplay = true
export const defaultControls = false

export interface IDesignImgVideo extends IDesignLayer {
  type: typeof type
  bgType?: 'img' | 'video' // 背景类型
  imgData: string// 对应图片组件data
  mode?: IMode
  imgRepeat?: IRepeat
  sizeX?: number // 百分比, 0 为 auto
  sizeY?: number // 百分比, 0 为 auto
  posX?: number // x 位置百分数
  posY?: number // y 位置百分数
  color?: string // 背景颜色，如果设置了颜色渲染时转为mask的方式，部分属性会失效
  hasQrcode?: boolean // 是否有二维码
  clickPreviewSwitch?: boolean // 点击放大预览
  // 下面是视频相关属性
  videoData: string // 视频地址 ,对应视频组件data
  videoRepeat?: '' | 'x' | 'y' // 对应视频组件repeat
  muted?: boolean
  loop?: boolean
  autoplay?: boolean
  controls?: boolean
  videoObjectFit?: 'none' | 'fill' | 'contain' | 'cover' // 对应视频组件objectFit
}

export function setup(app: IDesignSetup) {
  app.registry({
    type,
    base: true,
    // bisType: BisTypes.common,
    name: '图片/视频',
    Comp,
    CompSetting,
    async defaultData(options) {
      const imgData = (options as IDesignImgVideo)?.imgData || await openSelectMaterial()
      if (!imgData) return
      const { width, height } = await ResourceUtil.loadImage(imgData, { draftScale: 0.2 })

      return merge({
        uuid: layerUuid(),
        name: '图片/视频',
        imgData,
        type,
        style: {
          width: `${width}px`,
          height: `${height}px`,
        },
        ratio: Number.parseFloat((width / height).toFixed(2)),
      }, options as unknown as IDesignImgVideo)
    },
  })
}
