<script lang="ts" setup>
import { fixiOSInput } from './mobile/utils/fix'
import { sign } from './mobile/utils/jssdk'
import '@wtto00/jweixin-esm'

definePage({ meta: { label: '移动端' } })
const matchs = location.href.match(/mobileFlag=([^&]+)/) || []
const mobileFlag = matchs[1]
const customFlag = mobileFlag === 'QgjoIbzZ'

onMounted(async () => {
  if (window.self === window.parent && customFlag) {
    try {
      await sign()
      const imgUrl = new URL('./mobile/assets/QgjoIbzZ.jpg', import.meta.url).href
      window.wx.ready(() => {
        const v: WX.AppMessageShareData = {
          title: '看竞赛|马上嗨', // 分享标题
          imgUrl, // 分享图标
          link: window.location.href, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
          desc: '上林马术中央公园|赛事一站式服务平台',
        }
        window.wx.hideAllNonBaseMenuItem()
        window.wx.showMenuItems({
          menuList: ['menuItem:share:appMessage', 'menuItem:share:timeline'],
        })
        window.wx.updateAppMessageShareData(v)
        window.wx.updateTimelineShareData(v)
      })
    } catch {}
  }
})

if (customFlag) {
  const scriptDom = document.createElement('script')
  scriptDom.type = 'text/javascript'
  const innerHTML = `
      //百度统计
      var _hmt = _hmt || []
      ;(function () {
        var hm = document.createElement('script')
        hm.src = '//hm.baidu.com/hm.js?510471bd3fbcaab8fb95546678d1adbf'
        var s = document.getElementsByTagName('script')[0]
        s.parentNode.insertBefore(hm, s)
      })()
      ;(window._hmt = window._hmt || []).push(['_trackEvent', '202506live', 'microsite', 'visit'])
  `
  scriptDom.innerHTML = innerHTML
  document.body.appendChild(scriptDom)
}

fixiOSInput()
</script>

<template>
  <hi-mobile-layout />
</template>

<style scoped lang="scss"></style>
