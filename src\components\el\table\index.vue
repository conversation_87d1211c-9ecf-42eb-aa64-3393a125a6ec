<script setup lang="ts">
import { ElTable } from 'element-plus'

defineOptions({
  name: 'HiElTable',
})

const attrs = useAttrs()
const tableRef = ref()

defineExpose({
  data: computed(() => tableRef.value.data),
  store: computed(() => tableRef.value.store),
  clearSelection: () => tableRef.value.clearSelection(),
  getSelectionRows: () => tableRef.value.getSelectionRows(),
  toggleRowSelection: (row: any, selected: boolean) => tableRef.value.toggleRowSelection(row, selected),
  toggleAllSelection: () => tableRef.value.toggleAllSelection(),
  toggleRowExpansion: (row: any, expanded?: boolean) => tableRef.value.toggleRowExpansion(row, expanded),
  setCurrentRow: (row: any) => tableRef.value.setCurrentRow(row),
  clearSort: () => tableRef.value.clearSort(),
  clearFilter: (columnKeys: string[]) => tableRef.value.clearFilter(columnKeys),
  doLayout: () => tableRef.value.doLayout(),
  sort: (prop: string, order: string) => tableRef.value.sort(prop, order),
  scrollTo: (options: ScrollToOptions | number, yCoord?: number) => tableRef.value.scrollTo(options, yCoord),
  setScrollTop: (top?: number) => tableRef.value.setScrollTop(top),
  setScrollLeft: (left?: number) => tableRef.value.setScrollLeft(left),
})
</script>

<template>
  <ElTable ref="tableRef" v-bind="{ 'row-key': 'id', 'hi-table': 1, 'stripe': true, ...attrs }">
    <slot />
  </ElTable>
</template>

<style scoped lang="scss"></style>
