<script setup lang="ts">
import { defineCustomEmits, useDesignState, useDesignTemp } from '../../index'
import { onEnd, onStart } from './game-logic'
import { DEFAULT_DATA, type IDesignMobileGoldcoinIng } from './mobile-goldcoin-ing'

const layer = defineModel<IDesignMobileGoldcoinIng>('layer', { required: true })

const designTemp = useDesignTemp()
const designState = useDesignState()
const customEmits = defineCustomEmits(layer)

const data = computed(() => {
  return {
    ...DEFAULT_DATA,
    ...layer.value.data,
  }
})

const isSplitMoneyMode = computed(() => designState.getLayerData('$游戏模式-分钱$'))

const gameContainerRef = useTemplateRef<HTMLDivElement>('gameContainerRef')

const bombMusic = computed(() => data.value.bombMusic)
const bombImage = computed(() => data.value.bombImage)
const successBombImage = computed(() => data.value.successBombImage)
const successMusic = computed(() => data.value.successMusic)
const receiver = computed(() => data.value.receiver)
const fallings = computed(() => {
  // 分钱模式不显示减分元素
  return data.value.fallings.filter(item => item.score >= 0 || !isSplitMoneyMode.value)
})

const mobileGoldcoinIngBoxRef = ref<HTMLElement>()

const { height } = useElementSize(mobileGoldcoinIngBoxRef)

function restart() {
  onEnd()
  if (gameContainerRef.value!) {
    onStart({
      el: gameContainerRef.value!,
      auto: designTemp.isEdit, // 禁止手势操作
      bombMusic: bombMusic.value,
      bombImage: bombImage.value,
      successBombImage: successBombImage.value,
      successMusic: successMusic.value,
      receiver: receiver.value,
      fallings: fallings.value,
      showChangeTips: !isSplitMoneyMode.value, // 根据是否分钱模式决定
      layerHeight: height.value,
      fallingsDensity: data.value.fallingsDensity,
      fallingSpeed: data.value.fallingSpeed,
      onUpdateScore: (score: number) => {
        customEmits('updateScore', score)
      },
    })
  }
}

onMounted(async () => {
  // 初始化游戏逻辑
  if (gameContainerRef.value) {
    onStart({
      el: gameContainerRef.value,
      auto: designTemp.isEdit, // 禁止手势操作
      bombMusic: bombMusic.value,
      bombImage: bombImage.value,
      successBombImage: successBombImage.value,
      successMusic: successMusic.value,
      receiver: receiver.value,
      fallings: fallings.value,
      showChangeTips: !isSplitMoneyMode.value, // 根据是否分钱模式决定
      layerHeight: height.value,
      fallingsDensity: data.value.fallingsDensity,
      fallingSpeed: data.value.fallingSpeed,
      onUpdateScore: (score: number) => {
        customEmits('updateScore', score)
      },
    })
  }
})

onUnmounted(() => {
  // 销毁游戏逻辑
  onEnd()
})

// 配置项变化，重新初始化
watch(() => data.value, () => restart(), {
  deep: true,
})

// 图层大小变化，重新初始化
watch(() => height.value, () => restart(), {
  deep: true,
})

// 分钱模式变化，重新初始化
watch(() => isSplitMoneyMode.value, () => restart())
</script>

<template>
  <div ref="mobileGoldcoinIngBoxRef" class="mobile-goldcoin-ing-box" @touchstart.stop.prevent>
    <div ref="gameContainerRef" class="game-container h-full w-full"></div>
  </div>
</template>

<style scoped lang="scss">
.mobile-goldcoin-ing-box {
  width: 100%;
  height: 100%;
  .game-container {
    position: relative;
    overflow: hidden;
  }
  :deep() {
    img {
      max-width: 100%;
      height: 100%;
    }
    .game-el {
      position: absolute;
      &.toleft {
        img {
          transform: scaleX(-1);
        }
      }
    }
    .bomb-score {
      position: absolute;
      top: -4px;
      right: 0;
      color: #fed965;
      text-shadow:
        0 0 2px #e7750c,
        0 0 3px #ffffff;
      font-size: 24px;
      font-weight: bold;
      font-style: italic;
      border: 1px white;
      animation: opacityshow 0.3s;
    }
  }
}
@keyframes opacityshow {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}
</style>
