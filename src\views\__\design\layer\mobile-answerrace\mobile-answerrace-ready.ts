import { merge } from 'lodash-es'
import { BisTypes } from '../../'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './mobile-answerrace-ready-setting.vue'
import Comp from './mobile-answerrace-ready.vue'

// 类型
export const type = 'mobile-answerrace-ready'

export const DEFAULT_DATA = {
  decorationImage: new URL('./assets/ready.png', import.meta.url).href,
  teamSelectionBackground: '#ffffff',
  teamSelectionText: '选择队伍',
  teamSelectionTextColor: '#333333',
  rulesBackground: 'rgba(0,0,0,0.5)',
  rulesTextFontSize: 18,
  rulesTextColor: '#fff',

  statusTextColor: '#fff',
  statusTextFontSize: 18,

  roundStartedText: '轮次已开始\n无法参与',
  waitingToStartText: '等待开始',
}

// 数据类型约束
export interface IDesignMobileAnswerraceReady extends IDesignLayer {
  type: typeof type
  data: Partial<typeof DEFAULT_DATA>
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.ready,
    showInteractive: [InteractiveEnum.answerracev3],
    showType: ['mobile'],
    type,
    name: '答题等待页',
    Comp,
    CompSetting,
    thumbnail: new URL('./mobile-answerrace-ready.png', import.meta.url).href,
    defaultData(options): IDesignMobileAnswerraceReady {
      return merge({
        uuid: layerUuid(),
        name: '答题等待页',
        type,
        style: {
          width: '375px',
          height: '820px',
        },
        isPercent: 1,
        data: {},
      }, options as IDesignMobileAnswerraceReady)
    },
  })
}
