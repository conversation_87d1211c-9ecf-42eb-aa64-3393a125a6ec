<script setup lang="ts">
import type { IDesignSeglotteryIng1 } from './seglottery-ing1'
import { useDataAttr } from '../..'
import { DEFAULT_DATA } from './seglottery-ing1'

const layer = defineModel<IDesignSeglotteryIng1>('layer', { required: true })

const animationModeBind = useDataAttr(layer.value.data, 'animationMode', DEFAULT_DATA.animationMode) // 动画模式

const maxRenderCountBind = useDataAttr(layer.value.data, 'maxRenderCount', DEFAULT_DATA.maxRenderCount) // 列数
const gapBind = useDataAttr(layer.value.data, 'gap', DEFAULT_DATA.gap) // 间距

const backgroundColorBind = useDataAttr(layer.value.data, 'backgroundColor', DEFAULT_DATA.backgroundColor) // 卡片背景
const borderRadiusBind = useDataAttr(layer.value.data, 'borderRadius', DEFAULT_DATA.borderRadius) // 圆角
const cardGapBind = useDataAttr(layer.value.data, 'cardGap', DEFAULT_DATA.cardGap) // 卡片间距

const labelColorBind = useDataAttr(layer.value.data, 'labelColor', DEFAULT_DATA.labelColor) // 标签颜色
const labelFontSizeBind = useDataAttr(layer.value.data, 'labelFontSize', DEFAULT_DATA.labelFontSize) // 标签字号
const labelFontWeightBind = useDataAttr(layer.value.data, 'labelFontWeight', DEFAULT_DATA.labelFontWeight) // 标签加粗
const labelLeftMarginBind = useDataAttr(layer.value.data, 'labelLeftMargin', DEFAULT_DATA.labelLeftMargin) // 标签左侧边距
const labelShowBind = useDataAttr(layer.value.data, 'labelShow', DEFAULT_DATA.labelShow)
const labelWidthBind = useDataAttr(layer.value.data, 'labelWidth', DEFAULT_DATA.labelWidth) // 标签宽度

const numColorBind = useDataAttr(layer.value.data, 'numColor', DEFAULT_DATA.numColor) // 数字颜色
const numFontSizeBind = useDataAttr(layer.value.data, 'numFontSize', DEFAULT_DATA.numFontSize) // 数字字号
const numFontWeightBind = useDataAttr(layer.value.data, 'numFontWeight', DEFAULT_DATA.numFontWeight) // 数字加粗

const numWidthBind = useDataAttr(layer.value.data, 'numWidth', DEFAULT_DATA.numWidth) // 数字宽度
const numHeightBind = useDataAttr(layer.value.data, 'numHeight', DEFAULT_DATA.numHeight) // 数字高度

const numBorderShowBind = useDataAttr(layer.value.data, 'numBorderShow', DEFAULT_DATA.numBorderShow) // 是否显示数字边框
const numBorderColorBind = useDataAttr(layer.value.data, 'numBorderColor', DEFAULT_DATA.numBorderColor) // 边框颜色
const numBorderWidthBind = useDataAttr(layer.value.data, 'numBorderWidth', DEFAULT_DATA.numBorderWidth) // 边框粗细
const numBorderRadiusBind = useDataAttr(layer.value.data, 'numBorderRadius', DEFAULT_DATA.numBorderRadius) // 边框圆角
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <!-- 布局相关 -->
      <div class="setting-item">
        <h3>最大展示数量</h3>
        <el-input-number v-model="maxRenderCountBind" v-input-number :min="1" :max="20" controls-position="right" />
      </div>

      <div class="setting-item">
        <h3>间距 </h3>
        <el-input-number v-model="gapBind" v-input-number :min="0" :max="100" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>动画模式</h3>
        <el-select v-model="animationModeBind" placeholder="请选择动画模式" class="w-100px">
          <el-option label="数字变化" value="default" />
          <el-option label="向上滑动" value="slotMachine" />
        </el-select>
      </div>

      <!-- 卡片外观 -->
      <h2>卡片</h2>
      <div class="setting-item">
        <h3>背景色</h3>
        <hi-color v-model="backgroundColorBind" />
      </div>
      <div class="setting-item">
        <h3>圆角</h3>
        <el-input-number v-model="borderRadiusBind" v-input-number :min="0" :max="50" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>边距</h3>
        <el-input-number v-model="cardGapBind" v-input-number :min="0" :max="100" controls-position="right" />
      </div>

      <!-- 标签文字 -->

      <h2>标签</h2>
      <div class="setting-item">
        <h3>标签展示</h3>
        <el-switch v-model="labelShowBind" />
      </div>
      <template v-if="labelShowBind">
        <div class="setting-item">
          <h3>标签宽度</h3>
          <el-input-number v-model="labelWidthBind" v-input-number :min="0" controls-position="right" />
        </div>
        <div class="setting-item">
          <h3>标签颜色</h3>
          <hi-color v-model="labelColorBind" />
        </div>
        <div class="setting-item">
          <h3>标签字号</h3>
          <el-input-number v-model="labelFontSizeBind" v-input-number :min="10" :max="80" controls-position="right" />
        </div>
        <div class="setting-item">
          <h3>标签加粗</h3>
          <el-switch v-model="labelFontWeightBind" />
        </div>
        <div class="setting-item">
          <h3>标签距数字距离</h3>
          <el-input-number v-model="labelLeftMarginBind" v-input-number :min="0" controls-position="right" />
        </div>
      </template>

      <!-- 数字文字 -->
      <h2>数字</h2>
      <div class="setting-item">
        <h3>数字颜色</h3>
        <hi-color v-model="numColorBind" />
      </div>
      <div class="setting-item">
        <h3>数字字号</h3>
        <el-input-number v-model="numFontSizeBind" v-input-number :min="10" :max="80" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>数字加粗</h3>
        <el-switch v-model="numFontWeightBind" />
      </div>
      <div class="setting-item">
        <h3>数字宽度</h3>
        <el-input-number v-model="numWidthBind" v-input-number :min="1" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>数字高度</h3>
        <el-input-number v-model="numHeightBind" v-input-number :min="1" controls-position="right" />
      </div>

      <!-- 数字边框 -->
      <div class="setting-item">
        <h3>数字边框</h3>
        <el-switch v-model="numBorderShowBind" active-text="显示" inactive-text="隐藏" />
      </div>

      <template v-if="numBorderShowBind">
        <h2>边框</h2>
        <div class="setting-item">
          <h3>边框颜色</h3>
          <hi-color v-model="numBorderColorBind" />
        </div>
        <div class="setting-item">
          <h3>边框宽度</h3>
          <el-input-number v-model="numBorderWidthBind" v-input-number :min="1" :max="10" controls-position="right" />
        </div>
        <div class="setting-item">
          <h3>边框圆角 </h3>
          <el-input-number v-model="numBorderRadiusBind" v-input-number :min="0" :max="20" controls-position="right" />
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped lang="scss">
.setting-block {
  padding: 12px;
}
.setting-item {
  margin-bottom: 18px;
  h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 6px;
  }
}
</style>
