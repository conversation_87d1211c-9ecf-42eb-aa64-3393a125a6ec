import { Locale } from 'vant'
import vantZhCn from 'vant/es/locale/lang/zh-CN'
import vantZhTw from 'vant/es/locale/lang/zh-TW'
import { createI18n } from 'vue-i18n'
import zhCN from './lang/zh-CN.ts'
import zhTW from './lang/zh-TW.ts'

function getLocale(): supportedLocales {
  const language = navigator.language
  const supportedLocales = ['zh-CN', 'zh-TW']
  const traditionalChineseLocales = ['zh-TW', 'zh-HK', 'zh-MO']

  if (supportedLocales.includes(language)) {
    return language as supportedLocales
  }

  if (traditionalChineseLocales.includes(language as supportedLocales)) {
    return 'zh-TW'
  }

  return 'zh-CN'
}

const messages = {
  'zh-CN': zhCN,
  'zh-TW': zhTW,
  'zh-HK': zhTW, // 香港繁体
  'zh-MO': zhTW, // 澳门繁体
}

type supportedLocales = keyof typeof messages

const i18n = createI18n({
  locale: getLocale(),
  messages,
  fallbackLocale: 'zh-CN',
  legacy: false,
})

const vantI18n: Record<supportedLocales, any> = {
  'zh-CN': vantZhCn,
  'zh-TW': vantZhTw,
  'zh-HK': vantZhTw,
  'zh-MO': vantZhTw,
}
Locale.use(getLocale(), vantI18n[getLocale()])

window.addEventListener('languagechange', async () => {
  // 注意：监控语言变化。但部分内容不会立即变化，比如脱离Vue的内容、data(){}中初始化的内容
  i18n.global.locale.value = getLocale()
  Locale.use(getLocale(), vantI18n[getLocale()])
})

export default i18n
