<script setup lang="ts">
import { getDefaultMaterial, openSelectMaterial, useDataAttr } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'
import { defaultBorderColor, defaultColor, defaultCount, defaultDirection, type IDesignShakeIng2 } from './shake-ing2'

const layer = defineModel<IDesignShakeIng2>('layer', { required: true })

type IType = 'trackTop' | 'track' | 'contestant'
async function updateMaterialFn(name: IType, index?: number, isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, `${name}${index !== undefined ? `.${index}` : ''}`)
    : await openSelectMaterial('PIC')
  if (result || (isReset && index === undefined)) {
    if (name === 'contestant') {
      const arr = layer.value[name] as string[]
      if (index !== undefined) {
        arr[index] = result
      }
    } else {
      layer.value[name] = result
    }
  }
}

function remove(name: IType, index: number) {
  const arr = layer.value[name] as string[]
  arr.splice(index, 1)
}
function add(name: IType, index: number, defaultValue: string) {
  const arr = layer.value[name] as string[]
  arr.splice(index + 1, 0, defaultValue)
}

const countBind = useDataAttr(layer.value, 'count', defaultCount)
const colorBind = useDataAttr(layer.value.style, 'color', defaultColor)
const borderColorBind = useDataAttr(layer.value.style, 'borderColor', defaultBorderColor)
const directionBind = useDataAttr(layer.value, 'direction', defaultDirection)
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>数量</h3>
        <el-slider v-model.number="countBind" class="mx-10" :min="1" :max="50" :step="1"></el-slider>
      </div>
      <div class="setting-item">
        <h3>字体颜色</h3>
        <hi-color v-model="colorBind" />
      </div>
      <div class="setting-item">
        <h3>边框颜色</h3>
        <hi-color v-model="borderColorBind" />
      </div>
      <div class="setting-item">
        <h3>轨道顶部</h3>
        <div class="bgblank relative h-50 w-64">
          <MaterialThumbnail @select="updateMaterialFn('trackTop')" @reset="updateMaterialFn('trackTop', undefined, true)">
            <img v-if="layer.trackTop" :src="layer.trackTop">
          </MaterialThumbnail>
        </div>
      </div>
      <div class="setting-item">
        <h3>轨道</h3>
        <div class="bgblank relative h-50 w-64">
          <MaterialThumbnail @select="updateMaterialFn('track')" @reset="updateMaterialFn('track', undefined, true)">
            <img v-if="layer.track" :src="layer.track">
          </MaterialThumbnail>
        </div>
      </div>
      <div class="setting-item">
        <h3>方向</h3>
        <el-radio-group v-model="directionBind">
          <el-radio-button label="上" value="up" />
          <el-radio-button label="下" value="down" />
        </el-radio-group>
      </div>
      <div class="setting-item flex-items-start!">
        <h3>参赛物</h3>
        <div>
          <div
            v-for="(item, index) in layer.contestant"
            :key="index"
            class="relative mb-10 h-60 w-120 flex"
            @click="updateMaterialFn('contestant', index)"
          >
            <MaterialThumbnail @select="updateMaterialFn('contestant', index)" @reset="updateMaterialFn('contestant', index, true)">
              <img :src="item" class="bgblank w-90 object-contain">
            </MaterialThumbnail>
            <div class="ml-6 w-20 pt-10">
              <icon-ph:minus-bold v-if="layer.contestant.length > 1" @click.stop="remove('contestant', index)" />
              <icon-ph:plus-bold @click.stop="add('contestant', index, item)" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
