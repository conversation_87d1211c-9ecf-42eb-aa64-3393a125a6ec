<script setup lang="ts">
import api from '@/api'

definePage({ meta: { label: '管理员登录' } })

const userInfo = useStorage('dev:manage:user:login', {
  username: '',
  passwd: '',
})

const route = useRoute()
const router = useRouter()

async function loginFn() {
  if (!userInfo.value.username || !userInfo.value.passwd) {
    return ElMessage.error('请输入用户名和密码')
  }
  // 调用接口
  // await api
  await api.man.user.devLogin(userInfo.value)

  ElMessage.success('登录成功')

  const redirect = route.query.redirect
  setTimeout(() => {
    if (redirect) {
      router.push(decodeURIComponent(redirect as string))
    } else {
      router.push('/manage/wall/shakev3')
    }
  }, 1000)
}
</script>

<template>
  <div class="content-box">
    <div class="w-400">
      <h1 class="text-center">管理员</h1>
      <el-form :label-width="100">
        <el-form-item label="用户名">
          <el-input id="username" v-model="userInfo.username" />
        </el-form-item>
        <el-form-item label="密码">
          <el-input id="password" v-model="userInfo.passwd" type="password" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loginFn">登录</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<style scoped lang="scss">
.content-box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
