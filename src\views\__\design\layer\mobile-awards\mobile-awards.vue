<script setup lang="ts">
import type { IDesignMobileAwards } from './mobile-awards'
import { fenToYuan } from '@/utils/math'
import { defineCustomEmits, EMITS_EVENT_SHOW_HIDE, useDesignData, useDesignState, useDesignTemp } from '../..'

export interface IMobileAwardsRecord {
  awardsId?: number
  redpackAmount?: string
  status?: string
  count?: number
  score?: number
  teamRanking?: number
  ranking: number
  teamId?: number
  isTeam?: boolean
  isOut?: boolean
}

export interface IMobileAwardsAwards {
  name: string
  img: string
  type: 'EMPTY' | 'REDPACK' | 'KIND'
  redpackAmount?: number
  singleAmount?: number
}

const designTemp = useDesignTemp()
const designData = useDesignData()
const layer = defineModel<IDesignMobileAwards>('layer', { required: true })
const customEmits = defineCustomEmits(layer)
const designState = useDesignState()

const isWedding = designState.getLayerData('isWedding')

// 查找排行榜图层
const rankingListLayer = designData.findOneLayerByType('mobile-rankinglist')

// 当前组件需要的业务数据
const record = computed<IMobileAwardsRecord>(() => {
  const data = designState.getLayerData('record')
  if (data) {
    return data
  }
  return {
    awardsId: 0,
    redpackAmount: '0',
    status: '',
    count: 0,
    score: 0,
    teamRanking: 0,
    ranking: 0,
  }
})
const awards = computed<IMobileAwardsAwards>(() => {
  const data = designState.getLayerData('awards')
  if (data) {
    return data
  }
  return {
    name: '',
    img: '',
    type: '',
  }
})
const awardCount = computed(() => designState.getLayerData('awardCount'))
const isShowAd = computed(() => designState.getLayerData('isShowAd'))
const scoreUnit = computed(() => designState.getLayerData('scoreUnit') || '次')

const defaultAwardImg = new URL(`./assets/prize.png`, import.meta.url).href

const type = computed(() => {
  if (designTemp.isEdit) {
    // 随机返回一个类型
    return ['KIND', 'REDPACK', 'EMPTY', 'NOT_WINNING'][Math.floor(Math.random() * 4)]
  }
  if (!awardCount.value && !awards.value.type) return 'EMPTY'
  if (!awards.value || !record.value.awardsId || record.value?.isOut) return 'NOT_WINNING'
  return awards.value.type
})

// @ts-ignore
const isMiniProgram = () => (window.__wxjs_environment === 'miniprogram' || window.navigator.userAgent.includes('miniProgram'))

const adslotId = computed(() => {
  if (isMiniProgram()) {
    return 411821
  }
  if (isWedding) {
    return 440416
  }
  return 410966
})

function getStaticImageUrl(name: string) {
  return new URL(`./assets/${name}.png`, import.meta.url).href
}

const titleImg = computed(() => {
  const typeImg = {
    KIND: getStaticImageUrl(`title-gxhj`),
    REDPACK: getStaticImageUrl(`title-gxhj`),
    EMPTY: getStaticImageUrl(`title-blcj`),
    NOT_WINNING: getStaticImageUrl(`title-zjzl`),
  }
  return typeImg[type.value as keyof typeof typeImg] || typeImg.EMPTY
})

function onEmitBack() {
  customEmits(EMITS_EVENT_SHOW_HIDE, 'hide')
}

function onEmitClickAd() {
  customEmits('clickAd', { adslotId: adslotId.value, adSource: 'ad_tuia', adFlag: 'flow', adStatus: 'click' })
}

function onEmitRanking() {
  if (rankingListLayer) {
    customEmits(EMITS_EVENT_SHOW_HIDE, {
      uuid: rankingListLayer.uuid,
      show: 'show',
    })
  }
}

function onEmitAwards() {
  customEmits('clickAwards')
}

onMounted(() => {
  if (isShowAd.value) {
    customEmits('SHOW_AD', { adslotId: adslotId.value, adFlag: 'flow' })
  }
})
</script>

<template>
  <div class="design-mobile-awards">
    <div class="shine-container" :class="{ 'not-winning': type === 'NOT_WINNING' }">
      <div class="award-box" :class="{ 'not-winning': type === 'NOT_WINNING' }">
        <img :src="titleImg" class="award-box__title" alt="获奖状态" />
        <div v-if="type === 'EMPTY'">
          <!-- 需要换掉 没有奖品这里显示一个图标 -->
          <img
            class="mt-10px w-180px"
            src="./assets/icon-medal.png"
          />
        </div>
        <!-- 成绩信息 -->
        <div class="score-container" :class="{ border: type === 'EMPTY' }">
          <div class="score-item">
            <p class="score-title">本轮成绩</p>
            <p>{{ record.count || record.score || 0 }}{{ scoreUnit }}</p>
          </div>
          <div class="border"></div>
          <div class="score-item">
            <p class="score-title">本轮排名</p>
            <template v-if="record.isOut">
              <p>淘汰</p>
            </template>
            <template v-else-if="record.teamId || record.isTeam">
              <p class="sm">团队：{{ record.teamRanking || 0 }}</p>
              <p class="sm">个人：{{ record.ranking || 0 }}</p>
            </template>
            <template v-else>
              <p>NO.{{ record.ranking }}</p>
            </template>
          </div>
        </div>
        <div class="award-img-box">
          <!-- 实物奖品 -->
          <template v-if="type === 'KIND'">
            <img :src="awards.img || defaultAwardImg" class="awardImg" />
            <h4 class="line-clamp-1">{{ awards.name }}</h4>
            <div class="btn-ef374e" @click="onEmitBack">返回</div>
            <p class="tips">领奖说明：请及时联系主办方兑奖</p>
          </template>
          <!-- 红包奖品 -->
          <template v-else-if="type === 'REDPACK'">
            <div class="redpack-bg flex items-center justify-center">
              <p class="text-10px">
                <span class="money">{{ fenToYuan(record.redpackAmount || '') }}</span>
                元
              </p>
            </div>
            <div class="btn-ef374e" @click="onEmitBack">返回</div>
            <p class="tips">48小时内到微信零钱，请注意查收</p>
          </template>
          <!-- 没有奖品 -->
          <div v-else-if="type === 'EMPTY'" class="noaward">
            <template v-if="isShowAd">
              <p class="tips">您有1次抽奖机会</p>
              <button class="btn-ef374e" @click="onEmitClickAd">赶紧去抽奖</button>
            </template>
            <button v-else class="btn-ef374e" @click="onEmitBack">返回</button>
          </div>
          <!-- 没有中奖 -->
          <div v-else class="noaward">
            <img class="noaward-img" src="./assets/not-winning.png" />
            <div class="noaward-text">
              <p>您跟奖品插肩而过~</p>
              <p>大奖还在等着你哦~</p>
            </div>
            <button v-if="isShowAd" class="btn-ef374e" @click="onEmitClickAd">领个安慰奖</button>
            <div v-else class="btn-ef374e" @click="onEmitBack">返回</div>
          </div>
        </div>
      </div>
      <div class="flex">
        <div v-if="rankingListLayer" class="btn-fea416" @click="onEmitRanking">排行榜</div>
        <div class="btn-fea416" @click="onEmitAwards">中奖记录</div>
      </div>
      <icon-ph-x-bold class="btn-close" @click="onEmitBack" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.design-mobile-awards {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  padding-top: 40px;
  font-size: 14px;
  .shine-container {
    background-image: url('./assets/bg-shine.png');
    background-position: top center;
    background-repeat: no-repeat;
    background-size: 82%;
    padding-top: 90px;
    &.not-winning {
      background-image: url('./assets/bg-shine--not-winning.png');
    }
  }
  .award-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    margin-top: 8px;
    width: 300px;
    padding-bottom: 20px;
    color: rgb(255, 148, 14);
    background: url('./assets/bg.png') 0% 0% / 100% 100% no-repeat;
    &.not-winning {
      background: url('./assets/bg--not-winning.png') 0% 0% / 100% 100% no-repeat;
    }
    &__title {
      width: 240px;
      height: 51px;
      margin-top: -24px;
    }
    .score {
      &-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        text-align: center;
        width: 80%;
        margin-top: 10px;
        &.border {
          border-top: 1px solid #eeebe9;
          border-bottom: 1px solid #eeebe9;
          padding-top: 20px;
          padding-bottom: 20px;
          margin-bottom: 24px;
        }

        .border {
          width: 1px;
          height: 60%;
          background-color: #eeebe9;
        }
      }

      &-item {
        flex: 1;
        font-size: 20px;
        font-weight: bold;
        .sm {
          font-size: 10px;
        }
      }
      &-title {
        color: #ef374e;
        font-size: 15px;
        font-weight: initial;
        margin-bottom: 15px;
      }
    }
    .award-img-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 80%;
      h4 {
        margin-top: 10px;
        font-size: 15px;
      }

      .awardImg {
        margin-top: 10px;
        width: 100px;
        height: 100px;
        object-fit: contain;
        object-position: center;
      }
      .redpack-bg {
        width: 130px;
        height: 130px;
        margin-top: 20px;
        background: url('./assets/redpack-bg.png') 0% 0% / 100% 100% no-repeat;
      }
      .money {
        font-size: 20px;
        font-weight: bolder;
      }
    }

    .btn-fea416 {
      margin-top: 15px;
    }
  }
}

.noaward {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #888;
  width: 100%;
  &-img {
    margin-top: 10px;
    width: 90px;
    height: 90px;
  }

  &-text {
    margin-top: 15px;
    margin-bottom: 5px;
    font-size: 15px;
  }
}

.tips {
  color: #000;
  font-size: 12px;
}

.btn-ef374e,
.btn-fea416 {
  color: white;
  border-radius: 20px;
  display: block;
  width: 100%;
  text-align: center;
  padding: 10px;
  margin: 14px;
  cursor: pointer;
}
.btn-ef374e {
  background-color: #ef374e;
}
.btn-fea416 {
  background-color: #fea416;
}
.btn-close {
  display: block;
  margin: 10px auto;
  cursor: pointer;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.4);
  border-radius: 50px;
  padding: 10px;
  font-size: 30px;
}
</style>
