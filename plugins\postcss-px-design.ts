// postcss 的插件 vite内置了postCss插件 无需安装
import type { Plugin } from 'postcss'

const reg = /(-?\d+)px/g

export function PostCssPxToDesign(): Plugin {
  return {
    postcssPlugin: 'postcss-px-to-design',
    Once(root, param) {
      const mainFile = param.result.opts.from
      if (!mainFile) return

      if (mainFile.includes('/design/layer/')) {
        if (
          mainFile.includes('-setting.vue')
          || mainFile.includes('-attachment.vue')
          || mainFile.includes('/design/layer/index.vue')
          || mainFile.includes('/design/layer/moveable.vue')
        ) {
        } else {
          root.walkDecls((decl) => {
            if (decl.value.includes('px')) {
              decl.value = decl.value.replace(reg, (match, p1) => {
                return `calc(var(--design-scale) * ${p1}px)`
              })
            }
          })
        }
      }
    },

  }
}
