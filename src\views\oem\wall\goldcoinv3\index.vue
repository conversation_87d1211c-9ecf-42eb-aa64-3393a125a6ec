<script setup lang="ts">
import HiDesign from 'design/index.vue'
import { useDesignTemp } from '~/src/views/__/design'
import { useMobileGoldcoinv3 } from '~/src/views/mobile/wall/goldcoinv3'
import { usePcwallGoldcoinv3 } from '~/src/views/pcwall/goldcoinv3'

definePage({ meta: { label: '接金币' } })

const interactive = 'goldcoinv3'
const designTemp = useDesignTemp()

watch(
  () => designTemp.showType,
  (v) => {
    if (v === 'mobile') {
      useMobileGoldcoinv3()
    } else {
      usePcwallGoldcoinv3()
    }
  },
  { immediate: true },
)
</script>

<template>
  <HiDesign :interactive="interactive" />
</template>

<style scoped lang="scss">
</style>
