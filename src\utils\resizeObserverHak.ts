function debounce(fn: any, delay: number) {
  let timer: NodeJS.Timeout
  return function () {
    // @ts-ignore
    // eslint-disable-next-line ts/no-this-alias
    const context = this
    // eslint-disable-next-line prefer-rest-params
    const args = arguments
    clearTimeout(timer)
    timer = setTimeout(() => {
      try {
        fn.apply(context, args)
        // eslint-disable-next-line unused-imports/no-unused-vars
      } catch (e) { }
    }, delay)
  }
}

const _ResizeObserver = window.ResizeObserver

if (_ResizeObserver) {
  window.ResizeObserver = class ResizeObserver extends _ResizeObserver {
    constructor(callback: any) {
      callback = debounce(callback, 16)
      super(callback)
    }
  }
}
