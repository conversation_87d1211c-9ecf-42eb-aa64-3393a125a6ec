diff --git a/index.js b/index.js
index eee218a2b5e7850a36d99d19684d1ff110eba350..4794e3468ac444bf1aad9e2589014a86cc672104 100644
--- a/index.js
+++ b/index.js
@@ -117,6 +117,12 @@ function createPropListMatcher(propList) {
   };
 }
 
+const __optsObj = {}
+const __satisfyPropListObj = {}
+const __excludeObj = {}
+const __isExcludeFileObj = {}
+const __pxReplaceObj = {}
+
 module.exports = (options = {}) => {
   convertLegacyOptions(options);
   const opts = Object.assign({}, defaults, options);
@@ -148,10 +154,20 @@ module.exports = (options = {}) => {
         opts.unitPrecision,
         opts.minPixelValue,
       );
+
+      __optsObj[filePath] = opts
+      __satisfyPropListObj[filePath] = satisfyPropList
+      __excludeObj[filePath] = exclude
+      __isExcludeFileObj[filePath] = isExcludeFile
+      __pxReplaceObj[filePath] = pxReplace
     },
     Declaration(decl) {
+      isExcludeFile = __isExcludeFileObj[decl.source.input.file]
       if (isExcludeFile) return;
 
+      // opts = __optsObj[decl.source.input.file]
+      // satisfyPropList = __satisfyPropListObj[decl.source.input.file]
+
       if (
         decl.value.indexOf(opts.unit) === -1 ||
         !satisfyPropList(decl.prop) ||
@@ -159,6 +175,8 @@ module.exports = (options = {}) => {
       )
         return;
 
+      pxReplace = __pxReplaceObj[decl.source.input.file]
+
       const value = decl.value.replace(pxRegex(opts.unit), pxReplace);
 
       // if rem unit already exists, do not add or replace
