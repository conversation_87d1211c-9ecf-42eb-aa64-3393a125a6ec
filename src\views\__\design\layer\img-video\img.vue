<script setup lang="ts">
import type { CSSProperties } from 'vue'
import type { IDesignImgVideo } from './img-video'
import { isIOS } from '@vueuse/core'
import { hasAuth } from '~/src/utils/auth'
import { openSelectMaterial, useDesignTemp } from '../..'
import { defaultMode, defaultPosition, defaultRepeat, defaultSize } from './img-video'

const layer = defineModel<IDesignImgVideo>('layer', { required: true })
const designTemp = useDesignTemp()
const bgRef = ref<HTMLElement>()

async function selectMaterialFn() {
  if (designTemp.isPreview) return
  if (!hasAuth(['man', 'oem', 'spo.hi.vip', 'spo.ft']) && !layer.value.spoVisible) return
  const result = await openSelectMaterial('PIC')
  if (result) {
    layer.value.imgData = result
  }
}

const imgPath = computed(() => {
  const url = layer.value.imgData
  return url
  // const width = layer.value.style.width
  // const height = layer.value.style.height
  // if (!width || !height) return url
  // return `${url}?bgView2/2/w/${Number.parseInt(`${width}`)}/h/${Number.parseInt(`${height}`)}`
})

const backgroundStyle = computed(() => {
  const mode = layer.value.mode || defaultMode
  const repeat = layer.value.imgRepeat

  const style: CSSProperties = {
    backgroundImage: `url(${imgPath.value})`,
    backgroundPosition: `${layer.value.posX ?? defaultPosition}% ${layer.value.posY ?? defaultPosition}%`,
  }

  if (mode === 'contain' || mode === 'cover' || mode === 'auto') {
    style.backgroundSize = mode
  } else if (mode === 'fill') {
    style.backgroundSize = '100% 100%'
  } else if (mode === 'none') {
    const sizeX = layer.value.sizeX ?? defaultSize
    const sizeY = layer.value.sizeY ?? defaultSize
    const resultX = sizeX ? `${sizeX}%` : 'auto'
    const resultY = sizeY ? `${sizeY}%` : 'auto'
    style.backgroundSize = `${resultX} ${resultY}`
  }
  if (repeat) {
    style.backgroundRepeat = repeat
  } else {
    style.backgroundRepeat = defaultRepeat
  }
  return style
})

const maskStyle = computed(() => {
  const mode = layer.value.mode || defaultMode
  const repeat = layer.value.imgRepeat

  const style: CSSProperties = {
    background: layer.value.color,
    maskImage: `url(${imgPath.value})`,
    maskPosition: `${layer.value.posX ?? defaultPosition}% ${layer.value.posY ?? defaultPosition}%`,
  }

  if (mode === 'contain' || mode === 'cover' || mode === 'auto') {
    style.maskSize = mode
  } else if (mode === 'fill') {
    style.maskSize = '100% 100%'
  } else if (mode === 'none') {
    const sizeX = layer.value.sizeX ?? defaultSize
    const sizeY = layer.value.sizeY ?? defaultSize
    const resultX = sizeX ? `${sizeX}%` : 'auto'
    const resultY = sizeY ? `${sizeY}%` : 'auto'
    style.maskSize = `${resultX} ${resultY}`
  }
  if (repeat) {
    style.maskRepeat = repeat
  } else {
    style.maskRepeat = defaultRepeat
  }
  return style
})

const bgStyle = computed(() => {
  return layer.value.color ? maskStyle.value : backgroundStyle.value
})

////
// 如果是适应、拉伸、裁剪时使用 img 标签显示
const isImgTag = computed(() => {
  if (layer.value.color) return false
  const mode = layer.value.mode || defaultMode
  if (mode === 'fill') {
    return true
  }
  return false
})
const imgStyle = computed(() => {
  const style: CSSProperties = {}
  const mode = layer.value.mode || defaultMode
  if (mode === 'fill') {
    style.objectFit = mode
  }
  return style
})
/// ///////// 二维码长按识别兼容
let tmpId: string | null = null
function removeQrcode() {
  const topDocument = window.top?.document
  if (!topDocument) return
  topDocument.body.querySelector(`#${tmpId}`)?.remove()
  document.removeEventListener('touchend', removeQrcode)
  document.removeEventListener('touchcancel', removeQrcode)
}
function qrcodeTouch(e: TouchEvent) {
  e.stopPropagation()
  e.preventDefault()

  const dom = bgRef.value
  if (!dom) return

  const topDocument = window.top?.document
  if (!topDocument) return
  tmpId = `tmp_${Math.random().toString(36).slice(2)}`

  const rect = dom.getBoundingClientRect()
  const img = document.createElement('img')
  img.setAttribute('id', tmpId)
  img.src = layer.value.imgData
  Object.assign(img.style, {
    position: 'absolute',
    width: `${rect.width}px`,
    height: `${rect.height}px`,
    left: `${rect.left}px`,
    top: `${rect.top}px`,
    zIndex: `9999`,
    maxWidth: 'none',
    maxHeight: 'none',
  })
  topDocument.body.appendChild(img)

  document.addEventListener('touchend', removeQrcode)
  document.addEventListener('touchcancel', removeQrcode)
}

onMounted(() => {
  // 只有iphone需要处理
  const inIframe = window.top !== window.self
  if (layer.value.hasQrcode && inIframe && isIOS) {
    bgRef.value?.addEventListener('touchstart', qrcodeTouch)
  }
})
</script>

<template>
  <img
    v-if="isImgTag"
    ref="bgRef"
    :src="imgPath"
    class="design-bg"
    :style="imgStyle"
    @dblclick="selectMaterialFn"
  />
  <div
    v-else
    class="design-bg"
    :style="bgStyle"
    @dblclick="selectMaterialFn"
  ></div>
</template>

<style scoped lang="scss">
.design-bg {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
