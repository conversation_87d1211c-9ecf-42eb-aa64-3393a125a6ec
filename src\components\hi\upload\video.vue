<script lang="ts" setup>
import type { UploadFile, UploadFiles, UploadProps } from 'element-plus'
import { ElMessage } from 'element-plus'

// 修改为视频类型
const props = defineProps({
  moduleValue: {
    type: String,
    default: '',
  },
  maxSize: {
    type: [String, Number],
    default: 100, // 修改为视频最大大小
  },
  maxWidth: {
    type: [String],
    default: '',
  },
  accept: {
    type: String,
    default: 'video/mp4', // 修改为视频类型
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  // 是否需要显示loding状态
  showLoading: {
    type: Boolean,
    default: false,
  },
  use: {
    type: String,
    default: 'video', // 修改为视频类型
  },

})
const emits = defineEmits<{
  (e: 'update:modelValue', v: string): void
  (event: 'beforeUpload'): void
  (event: 'success', url: string, uploadFile: UploadFile, uploadFiles: UploadFiles): void
}>()

const acceptType = ['video/*']
const attrs = useAttrs()
const upload = ref<any>()
const loading = ref(false)
const verifyVideoSize: UploadProps['beforeUpload'] = (file) => {
  console.log('🚀 ~ file: video.vue:56 ~ constverifyVideoSize:UploadProps[\'beforeUpload\']= ~ file:', file)
  console.log(attrs, '----attrs', props, '----props')
  const isLt100M = Number(file.size / 1024 / 1024) > Number(props.maxSize) // 修改为视频最大大小
  const pattern = file.type

  emits('beforeUpload')

  if (!/video/.test(pattern)) {
    // 修改为视频类型
    ElMessage.error({ message: `只支持视频` })
    return false
  }
  if (isLt100M) {
    // 修改为视频最大大小
    let message = `上传视频大小不能超过${props.maxSize}M`
    if (Number(props.maxSize) < 1) {
      // 之前maxSize是以M为单位的，但提示的时候如果提示0.2M这种效果可能不好，判断一下小于1M的时候换成k提示
      message = `上传视频大小不能超过${Math.trunc((Number(props.maxSize) * 1024) / 100) * 100}k`
    }
    ElMessage.error({ message })
    return false
  }
  if (props.showLoading) {
    loading.value = true
  }
  if (!props.maxWidth) {
    return true
  }
  // 视频尺寸检查
  return new Promise((resolve, reject) => {
    const video = document.createElement('video')
    video.src = window.URL.createObjectURL(file)
    video.onloadedmetadata = function () {
      const width = video.videoWidth
      const height = video.videoHeight
      const [maxWidth, maxHeight] = props.maxWidth.split('x')
      if (width > Number(maxWidth) || height > Number(maxHeight)) {
        ElMessage.error({ message: `上传的视频尺寸不能超过${maxWidth}x${maxHeight}` })
        reject(
          new Error(`上传的视频尺寸不能超过${props.maxWidth}`),
        )
      } else {
        resolve()
      }
    }
  })
}

const uploadSuccess: UploadProps['onSuccess'] = async (res, file, fileList) => {
  emits('success', res.data, file, fileList)
  emits('update:modelValue', res.data)
}
</script>

<template>
  <el-upload
    ref="upload"
    v-bind="$attrs"
    :action="`/api/spo/commonFile/uploadVideo?use=${use}`"
    :before-upload="verifyVideoSize"
    :accept-type="acceptType"
    :accept="accept"
    :disabled="disabled"
    :on-success="uploadSuccess"
  >
    <slot></slot>
  </el-upload>
</template>

<style scope lang="scss">
:deep(.el-upload--picture-card) {
  width: 100%;
  height: 100%;
}
</style>
