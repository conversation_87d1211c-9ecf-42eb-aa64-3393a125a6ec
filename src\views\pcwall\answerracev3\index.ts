import { randomAvatar, timer } from '@/utils'
import { useDesignData, useDesignState, useDesignTemp } from '@/views/__/design'

import { faker } from '@faker-js/faker/locale/zh_CN'
import { useImData } from '~/src/hooks/useImData'
import { envUtils } from '~/src/utils/env'
import { Toast } from '~/src/utils/toast'
import { useParticipantStore, usePcwallStore } from '..'

// 类型
interface IPage<T> {
  total: number
  totalPages: number
  pageIndex: number
  pageSize: number
  dataList: T[]
}
interface IConfig {
  answerracev3Id: number
  answerraceThemeLimit: 'N' | 'Y'
  answerraceAdvancedLimit: 'N' | 'Y'
}
interface IRound {
  id: number
  joinType: 'PERSONAL' | 'TEAM'
  themeId: number
  title: string
  state?: 'NOT_STARTED' | 'WIN_LIST' | 'IN' | 'FINISH'
  rule?: string
  [key: string]: any
}
interface IRegedit {
  name: string
  avatar: string
}
interface ITeam {
  id: number
  name: string
  avatar: string
  count: number
}
interface IRanking {
  id: number
  name: string
  avatar: string
  score: number | string
}

interface IApiRanking {
  id: number
  teamId?: string
  wxUserId?: number
  totalScore: number
  totalTime?: number
  [key: string]: any
}
interface ISunject {
  id: number
  startTime: number
  [key: string]: any
}

export function usePcwallAnswerracev3() {
  const timestamp = useTimestamp()
  // 逻辑 /////////////////////
  const designTemp = useDesignTemp()
  const designData = useDesignData()
  const designState = useDesignState()

  const pcwallStore = usePcwallStore()
  const participantStore = useParticipantStore()

  const isDesignEdit = designTemp.isEdit

  const config = ref<IConfig>()
  const round = ref<IRound>()
  const currentSubjectUpdateId = ref(0)

  // 参与人
  let regeditTimer: NodeJS.Timeout | undefined
  const regeditPage = ref<IPage<IRegedit>>()
  const regeditCount = ref(10)

  // 团队
  const teamList = ref<ITeam[]>([])
  // 当前题目
  const nowSubject = ref<ISunject>()

  // 排行榜
  const subjectrankLoading = ref(false) // 进行中排行最终结算标志
  const finishRankingsLoading = ref(false) // 结束后排行最终结算标志
  let rankingsTimer: NodeJS.Timeout | undefined
  const rankingCount = ref({
    subject: 50,
    round: 200,
  })
  const ingRankingsSubject = ref<IRanking[]>()
  const ingRankingsRound = ref<IRanking[]>()
  const finishRankings = ref<IRanking[]>()
  const allRankings = ref<IRanking[]>()

  // 名字
  const getName = (wxUserId: number) => {
    if (pcwallStore.isSignName) {
      return participantStore.getSignName(wxUserId)
    }
    return participantStore.getWxName(wxUserId)
  }
  const status = computed(() => {
    const statusObj = {
      NOT_STARTED: 'ready',
      WIN_LIST: 'winlist',
      IN: 'ing',
      FINISH: 'finish',
    }
    if (!round.value) return ''

    if (isDesignEdit) {
      return designState.status
    }
    return round.value?.state ? statusObj[round.value.state] : ''
  })
  // 是否暂停
  const isPause = computed(() => { return nowSubject.value?.state === 'PAUSE' })
  // 答题时间
  const answerTime = computed(() => {
    let answerTime = round.value?.answerTime || 0
    if (round.value?.answerTimeType === 'INDIVIDUAL_TIME') {
      answerTime = nowSubject.value?.answerTime || 0
    }
    return answerTime
  })
  // 题目运行过的总时间
  const runTime = computed(() => {
    const resumeTime = nowSubject.value?.resumeTime
    const runTime = nowSubject.value?.runTime || 0
    if (nowSubject.value?.state === 'PAUSE') {
      return runTime
    }
    return Math.max(timestamp.value - resumeTime + runTime, 0)
  })

  const temState = ref<'result' | 'rank' | null>(null) // 临时状态，用于手动切换
  const subjectStatusReal = computed(() => {
    if (!nowSubject.value) return
    if (isDesignEdit) return
    const now = timestamp.value
    const startTime = nowSubject.value?.startTime
    const watchTime = round.value?.watchTime || 0
    if (now < startTime) return '321'
    if (runTime.value < watchTime * 1000) return 'read'
    if (runTime.value < ((watchTime + answerTime.value) * 1000)) return 'answering'
    // 自动展示排行
    if (round.value?.showResultType === 'END_SHOW_ANSWER') {
      const showAnswerTime = round.value?.showAnswerTime || 0
      if (runTime.value < (watchTime + answerTime.value + showAnswerTime) * 1000) {
        return 'result'
      } else {
        return 'rank'
      }
    } else {
      // 手动展示答案
      return 'result'
    }
  })
  const subjectStatus = computed(() => {
    if (subjectStatusReal.value && ['result', 'rank'].includes(subjectStatusReal.value)) {
      return temState.value || subjectStatusReal.value
    }
    return subjectStatusReal.value
  })

  // 切题剩余时间,毫秒
  const changeSubDownTime = computed(() => {
    if (!nowSubject.value || !subjectStatus.value) return null
    const showAnswerTime = round.value?.showAnswerTime
    const showResultTime = round.value?.showResultTime
    const watchTime = round.value?.watchTime || 0
    if (['result', 'rank'].includes(subjectStatus.value ?? '') && round.value?.showAnswerType === 'TIMED_ANSWER') {
      let totleTime = showAnswerTime + answerTime.value + watchTime
      // 如果自动展示rank，则需要加上showResultTime
      if (round.value?.showResultType === 'END_SHOW_ANSWER') {
        totleTime += showResultTime
      }
      return Math.max(totleTime * 1000 - runTime.value, 0)
    }
    return null
  })
  // 倒计时时间
  const downFlagTime = computed(() => {
    if (!nowSubject.value || !subjectStatus.value) return null
    if (subjectStatus.value === '321') {
      return nowSubject.value?.startTime
    }
    if (subjectStatus.value === 'read') {
      return nowSubject.value?.startTime + round.value?.watchTime * 1000
    }
    if (subjectStatus.value === 'answering') {
      return nowSubject.value?.startTime + (round.value?.watchTime + answerTime.value) * 1000
    }
    if (['result', 'rank'].includes(subjectStatus.value)) {
      if (round.value?.showAnswerType === 'MANUAL_ANSWER' || nowSubject.value.state === 'PAUSE') {
        return null
      }
      // 自动展示答案
      if (round.value?.showAnswerType === 'TIMED_ANSWER') {
        return timestamp.value + (changeSubDownTime.value || 0)
      }
    }
    return null
  })
  // 自动处理时间
  const planeTimes = computed(() => {
    return {
      watchTime: round.value?.watchTime,
      answerTime: answerTime.value,
      showAnswerTime: round.value?.showAnswerTime,
      showResultTime: round.value?.showResultTime,
    }
  })
  // 接口定义
  async function fetchConfig() {
    if (isDesignEdit) {
      await timer(200)
      config.value = { answerracev3Id: 1, answerraceThemeLimit: 'Y', answerraceAdvancedLimit: 'Y' }
    }
  }
  async function fetchRound() {
    round.value = undefined
    if (isDesignEdit) {
      await timer(200)
      round.value = {
        id: 1,
        themeId: 1,
        title: '答题',
        joinType: 'PERSONAL',
        rule: '答题规则\n这是游戏规则',
        subjectId: 1,
        showCommentSwitch: 'Y',
      }
    }
  }
  async function fetchTheme() {
    // 查询主题
    if (isDesignEdit) {
      designTemp.fetchTheme()
    } else {
      const { themeId } = round.value || {}
      if (!themeId) return
      const theme = await api.pcwall.theme.readSimple({ where: { id: themeId } })
      designTemp.theme = theme
      if (theme) {
        designData.setState(JSON.parse(theme.webContent))
      }
    }
  }
  async function fetchTeam() {
    // 查询队伍列表
    if (!isDesignEdit) {
      const answerracev3Id = round.value?.id
      if (!answerracev3Id) return
      if (round.value?.joinType === 'PERSONAL') return
      teamList.value = []
      const res: { id: number, teamName: string, teamHeadImg: string }[] = await api.pcwall.answerracev3.teamList({ where: { answerracev3Id } })
      teamList.value = res.map(({ id, teamName, teamHeadImg }) => ({ id, name: teamName, avatar: teamHeadImg, count: 0 }))
    }
  }
  async function fetchRegedit() {
    // 查询参与人列表
    if (isDesignEdit) {
      await timer(200)
      if ((regeditPage.value?.total || 0) > 16) return
      if (envUtils.isPlayWright) {
        faker.seed(1)
      }
      const random = Math.random() * 10 | 0
      const total = envUtils.isPlayWright ? 16 : (regeditPage.value?.total || 0) + random
      const pageSize = regeditCount.value

      // 生成列表
      const dataList = []
      if (envUtils.isPlayWright) {
        faker.seed(1)
      }
      for (let i = 0; i < Math.max(total, pageSize); i++) {
        dataList.push({ name: faker.person.fullName(), avatar: randomAvatar(i) })
      }

      regeditPage.value = {
        total,
        totalPages: Math.ceil(total / pageSize),
        pageIndex: 1,
        pageSize,
        dataList,
      }
    } else {
      // 查询参与人
      const answerracev3Id = round.value?.id
      if (!answerracev3Id) return
      const page = await api.pcwall.answerracev3.regeditPage({
        pageIndex: 1,
        pageSize: regeditCount.value,
        where: { answerracev3Id },
      })
      // 团队赛
      if (round.value?.joinType === 'TEAM') {
        const list: any = []
        const res: Record<string, string> = await api.pcwall.answerracev3.regeditMembersCnt({ answerracev3Id })
        teamList.value.forEach((item) => {
          list.push({
            name: `${Number.parseInt(res[item.id] || '0')}人`,
            avatar: item.avatar,
          })
        })
        page.dataList = list
      } else {
        await participantStore.relationParticipant(page.dataList)

        const list: any = []
        page.dataList.forEach(({ wxUserId }: { wxUserId: number }) => {
          const name = getName(wxUserId)
          const avatar = participantStore.getAvatar(wxUserId)
          list.push({ name, avatar })
        })
        page.dataList = list
      }
      // 出现定时调用的地方最好有判断，防止数据被重复覆盖触发页面重新渲染
      if (JSON.stringify(regeditPage.value) !== JSON.stringify(page)) {
        regeditPage.value = page
      }
    }
  }

  async function fetchSubject() {
    if (!isDesignEdit) {
      const id = round.value?.subjectId
      if (!id) return
      // 查询题目
      const subject = await api.pcwall.answerracev3.subjectRead({ where: { id } })
      if (!subject) {
        Toast.message('题目不存在')
      }
      nowSubject.value = subject
    }
  }
  async function startFn() {
    try {
      if (!round.value) return
      // 开始
      if (!isDesignEdit) {
        // 判断参与人数
        if (!regeditPage.value?.total) {
          await Toast.message('参与人数不足')
          return
        }
        // 接口
        await api.pcwall.answerracev3.go()
      }
    } catch (error: any) {
      Toast.message(error?.message || error?.data?.msg || '操作失败')
      console.error(error)
    }
  }
  // 暂停
  const notAuto = computed(() => {
    return round.value?.showAnswerType === 'MANUAL_ANSWER'
  })
  const pauseIng = ref(false)
  async function pauseFn() {
    try {
      if (!round.value) return
      if (isDesignEdit) return
      if (!nowSubject.value) return
      if (pauseIng.value) return
      if (notAuto.value || !['rank', 'result'].includes(subjectStatus.value ?? '')) {
        return
      }
      pauseIng.value = true
      if (isPause.value) {
        await api.pcwall.answerracev3.resume({
          answerracev3Id: round.value.id,
          subjectId: nowSubject.value?.id,
        })
      } else {
        await api.pcwall.answerracev3.pause({
          answerracev3Id: round.value.id,
          subjectId: nowSubject.value?.id,
        })
      }
    } catch (error: any) {
      Toast.message(error?.message || error?.data?.msg || '操作失败')
      console.error(error)
    }
    fetchSubject()
    await timer(500)
    pauseIng.value = false
  }
  // 切题
  const nextSubIng = ref(false)
  async function nextSubFn() {
    try {
      if (!round.value) return
      if (isDesignEdit) return
      if (nextSubIng.value) return
      nextSubIng.value = true
      await api.pcwall.answerracev3.nextSubject({ answerracev3Id: round.value.id, subjectId: round.value.subjectId })
    } catch (error: any) {
      Toast.message(error?.message || error?.data?.msg || '操作失败')
      console.log(error)
    }
    await timer(1000)
    nextSubIng.value = false
  }

  async function fetchRankings(params?: any) {
    if (isDesignEdit) {
      await timer(200)
      const list = []
      if (envUtils.isPlayWright) {
        faker.seed(1)
      }
      for (let i = 0; i < 50; i++) {
        list.push({
          id: i + 1,
          name: faker.person.fullName(),
          avatar: randomAvatar(),
          score: envUtils.isPlayWright ? i * 10 : `${(Math.random() + i).toFixed(2)}秒`,
        })
      }
      return list
    } else {
      let result: IApiRanking[] = []
      const where: Record<string, any> = params || {}
      result = await api.pcwall.answerracev3.ranking(where)
      // name, avatar, score
      const list: IRanking[] = []
      // 团队模式
      if (round.value?.joinType === 'TEAM') {
        for (const { teamId, score: totalScore, accuracy } of result) {
          if (!teamId) continue
          const team = teamList.value.find(item => item.id === Number.parseInt(teamId))
          if (!team) continue
          const id = team.id
          const name = team.name
          const avatar = team.avatar
          const score = `${totalScore}分/${(accuracy * 100).toFixed(1)}%`
          list.push({ id, name, avatar, score })
        }
      } else {
        await participantStore.relationParticipant(result as { wxUserId: number }[])
        for (const { wxUserId, totalScore, totalTime } of result) {
          if (!wxUserId) continue
          const id = wxUserId
          const name = getName(wxUserId)
          const avatar = participantStore.getAvatar(wxUserId)
          const score = `${totalScore}分/${((totalTime ?? 0) / 1000).toFixed(2)}秒`
          list.push({ id, name, avatar, score })
        }
      }
      return list
    }
  }
  // 本轮排名
  async function fetchRoundRankings(type?: string) {
    const status = type || round.value?.state
    const showNum = status === 'IN' ? rankingCount.value.round : 200
    if (!showNum || !['IN', 'FINISH'].includes(status as string)) return
    const isFinalData = status === 'FINISH' ? true : null
    const data = await fetchRankings({ answerracev3Id: round.value?.id, showNum, isFinalData })
    if (status === 'IN') {
      ingRankingsRound.value = data || []
    } else {
      finishRankings.value = data || []
    }
  }
  // 本题排名
  async function fetchSubjectRankings(isFinalData?: boolean) {
    if (!round.value?.subjectId) return
    if (!rankingCount.value.subject) return
    const data = await fetchRankings({
      answerracev3Id: round.value?.id,
      showNum: rankingCount.value.subject,
      subjectId: round.value?.subjectId,
      isFinalData,
    })
    ingRankingsSubject.value = data || []
  }
  // 全部排名
  async function fetchAllRankings() {
    const data = await fetchRankings({
      showNum: 200,
    })
    allRankings.value = data || []
  }
  function resetData() {
    // 重置数据
    regeditPage.value = undefined
    regeditCount.value = 3
    ingRankingsRound.value = []
    ingRankingsSubject.value = []
    finishRankings.value = []
    // rankingCount.value = { subject: 0, round: 0 }
  }
  function setTemState(state: 'result' | 'rank' | null) {
    // 设置临时状态
    temState.value = state
  }
  const editLookState = ref<'321' | 'read' | 'answering' | 'result' | 'rank' | null>(null)
  function showWinlistFn() {
    if (!isDesignEdit) {
      updateRemoteState('WIN_LIST')
    }
  }
  function hideWinFn() {
    if (!isDesignEdit) {
      updateRemoteState('NOT_STARTED')
    }
  }
  // 更新状态
  async function updateRemoteState(state: 'NOT_STARTED' | 'WIN_LIST') {
    try {
      await api.pcwall.answerracev3.updateState({ id: round.value?.id, state })
    } catch (error: any) {
      Toast.message(error?.message || error?.msg || '操作失败')
      console.error(error)
    }
  }

  const current = ref()
  // 更新状态
  async function fetchCurrent() {
    if (isDesignEdit) return
    const id = round.value?.subjectId
    if (!id) return
    const data = await api.pcwall.answerracev3.current({ answerracev3Id: round.value?.id, subjectId: round.value?.subjectId })
    current.value = data || {}
  }
  // 主要作用是将数据暴露给图层组件
  designState
    .setStatusList([
      { label: '准备中', value: 'ready' },
      { label: '总排行', value: 'winlist' },
      { label: '进行中', value: 'ing' },
      { label: '排行榜', value: 'finish' },
    ])
    .setLayerData({
      '#轮次标题#': computed(() => {
        return round.value?.title || ''
      }),
      '#参与人数#': computed(() => {
        return regeditPage.value?.total || 0
      }),
      '#游戏规则#': computed(() => {
        return round.value?.rule || ''
      }),
      '#题目进度#': computed(() => {
        if (isDesignEdit) return '1/10题'
        return `${current.value?.current || 0}/${current.value?.total || 0}题`
      }),
      '$规则存在$': computed(() => {
        return !!round.value?.rule
      }),
      'planeTimes': computed(() => {
        return planeTimes.value
      }),
      'joinType': computed(() => {
        return round.value?.joinType || 'PERSONAL'
      }),
      'regeditList': computed(() => {
        return regeditPage.value?.dataList || []
      }),
      // 'ingRankingOptions': computed(() => {
      //   return [{ label: '本题排名', value: 'subject' }, { label: '本轮排名', value: 'round' }]
      // }),
      'ingRankingsSubject': computed(() => {
        return ingRankingsSubject.value
      }),
      'ingRankingsRound': computed(() => {
        return ingRankingsRound.value
      }),
      'isPause': computed(() => {
        return isPause.value
      }),
      'endRankings': computed(() => {
        return finishRankings.value
      }),
      'allRankings': computed(() => {
        return allRankings.value
      }),
      'downFlagTime': computed(() => {
        return downFlagTime.value
      }),
      'subjectStatus': computed(() => {
        return subjectStatus.value
      }),
      'nowSubject': computed(() => {
        return nowSubject.value
      }),
      'showCommentSwitch': computed(() => {
        return round.value?.showCommentSwitch === 'Y'
      }),
      'changeSubDownTime': computed(() => {
        return changeSubDownTime.value
      }),
      'hidePauseBtn': computed(() => {
        if (subjectStatus.value && ['rank', 'result'].includes(subjectStatus.value)) {
          return notAuto.value
        } else {
          return true
        }
      }),
      'subjectrankLoading': computed(() => {
        if (isDesignEdit) return false
        return subjectrankLoading.value
      }),
      'finishRankingsLoading': computed(() => {
        if (isDesignEdit) return false
        return finishRankingsLoading.value
      }),
    })
    // 图层组件事件
    .setLayerEventList([
      // 数量
      {
        eventId: 'regedit-count',
        value(data: number) {
          regeditCount.value = Math.max(regeditCount.value, data)
        },
      },
      {
        eventId: 'setEditLookState',
        value(data: '321' | 'read' | 'answering' | 'result' | 'rank' | null) {
          editLookState.value = data
        },
      },
      // 事件
      {
        eventId: 'start',
        name: '开始',
        value: startFn,
        status: ['ready'],
      },
      {
        eventId: 'showWinlist',
        name: '打开中奖名单',
        value: showWinlistFn,
        status: ['ready'],
      },
      {
        eventId: 'hideWinlist',
        name: '关闭中奖名单',
        value: hideWinFn,
        status: ['winlist'],
      },
      {
        eventId: 'pause',
        value: pauseFn,
      },
      {
        eventId: 'nextSubject',
        value: nextSubFn,
      },
      {
        eventId: 'setTemState',
        value: setTemState,
      },
      {
        eventId: 'resetData',
        value: resetData,
      },
    ])

  // 变化监控
  watch(() => config.value?.answerracev3Id, fetchRound)
  watch(() => round.value?.themeId, fetchTheme)
  watch(() => round.value?.subjectId, () => {
    temState.value = null
    ingRankingsSubject.value = []
    fetchSubject()
    fetchCurrent()
  })
  watch(
    () => ({ status: status.value, id: round.value?.id }),
    async (newValues, oldValues = { status: '', id: undefined }) => {
      if (!round.value?.id) return
      const newStatus = newValues.status
      const newId = newValues.id
      const oldStatus = oldValues.status
      const oldId = oldValues.id
      if (oldStatus === newStatus && newId === oldId) return
      clearInterval(rankingsTimer)
      rankingsTimer = undefined
      // 切换轮次
      if (newId !== oldId) {
        resetData()
        await Promise.all([fetchTeam(), fetchSubject()])
      }
      if (newStatus === 'ready') {
        clearInterval(regeditTimer)
        regeditTimer = setInterval(fetchRegedit, 2000)
        fetchRegedit()
      } else {
        clearInterval(regeditTimer)
        regeditTimer = undefined
      }
      if (newStatus === 'winlist') {
        fetchAllRankings()
      }
      if (newStatus === 'ing') {
        if (isDesignEdit) {
          fetchSubjectRankings()
          fetchRoundRankings('IN')
        }
      }
      designState.setStatus(status.value)
      if (newStatus === 'finish') {
        finishRankingsLoading.value = true
        await fetchRoundRankings('FINISH')
        finishRankingsLoading.value = false
      }
    },
    { immediate: true },
  )
  watch(() => subjectStatus.value, async (n) => {
    if (!round.value || isDesignEdit) return
    if (n === 'result') {
      clearInterval(rankingsTimer)
      subjectrankLoading.value = true
      await fetchSubjectRankings(true)
      subjectrankLoading.value = false
      fetchRoundRankings('IN')
    }
    if (n === 'rank') {
      clearInterval(rankingsTimer)
      await fetchSubjectRankings(true)
      fetchRoundRankings('IN')
    }
    if (n === 'answering') {
      fetchRoundRankings('IN')
      fetchSubjectRankings()
      clearInterval(rankingsTimer)
      rankingsTimer = setInterval(() => {
        fetchRoundRankings('IN')
        fetchSubjectRankings()
      }, 2000)
    }
    if (n === '321') {
      clearInterval(rankingsTimer)
      ingRankingsSubject.value = []
    }
  }, {
    immediate: true,
  })
  // 暂停or恢复时，重新获取题目
  watch(() => currentSubjectUpdateId.value, () => {
    fetchSubject()
  })

  // 自动切题
  watch(() => changeSubDownTime.value, async (n) => {
    if (!subjectStatus.value) return
    if (!round.value) return
    if (status.value !== 'ing') return
    if (['result', 'rank'].includes(subjectStatus.value) && round.value?.showAnswerType === 'TIMED_ANSWER' && n === 0) {
      nextSubFn()
    }
  })

  // 数据同步
  useImData({
    'im:answerracev3:config': config,
    'im:answerracev3:currentSubjectUpdateId': currentSubjectUpdateId,
    'im:answerracev3': computed({
      get() { return round.value },
      async set(v) {
        if (v?.id !== round.value?.id) {
          round.value = undefined
          await nextTick()
        }
        round.value = v
      },
    }),
  })
  ///////////////////////
  tryOnMounted(async () => {
    await pcwallStore.fetchApplySignConfig()
    if (isDesignEdit) {
      fetchConfig()
    }
  })
  tryOnBeforeUnmount(() => {
    clearInterval(regeditTimer)
    clearInterval(rankingsTimer)
  })
}
