<script setup lang="ts">
import BezierEasing from 'bezier-easing'
import gsap from 'gsap'
import * as THREE from 'three'
import { Toast } from '~/src/utils/toast'
import { AnimationTexture } from '../../__/three/AnimationTexture'
import useThree from '../../__/three/useThree'
import { defaultConfig } from '../../admin/wall/padsign'
import { useSignData } from './useSignData'

definePage({ meta: { label: 'iPad 签字' } })

// 配置数据
const padsignConfig = ref<{
  bgVideo: string
  bgImage: string
  shapes: { name: string, logo?: string }[]
  animationEffectSwitch: 'Y' | 'N'
}>({
  bgVideo: '', // 背景视频
  bgImage: '', // 背景图片
  shapes: [],
  animationEffectSwitch: 'N',
})

// 查配置
async function fetchConfig() {
  let config = await api.pcwall.padsign.configRead({})
  if (config) {
    config = Object.assign({}, defaultConfig, config)
  } else {
    config = defaultConfig
  }

  const resource = config.bgPc || defaultConfig.bgPc
  const endFlag = `${resource.split('.').reverse()[0]}`.toLocaleLowerCase()
  if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'bpm'].includes(endFlag)) {
    padsignConfig.value.bgImage = resource
  } else if (['mp4', 'mov', 'avi', 'mpg', 'mpeg', 'webm'].includes(endFlag)) {
    padsignConfig.value.bgVideo = resource
  } else {
    Toast.message(`未知的背景资源类型：${endFlag}`)
  }
  // 形状
  padsignConfig.value.shapes = JSON.parse(config.specialEffect)

  padsignConfig.value.animationEffectSwitch = config.animationEffectSwitch
}

const { hasData, readyPromise, getNext, loopText } = useSignData()
const threeRef = ref<HTMLElement | null>(null)
const { scene, loadTexture, drawImageToCanvasTextureByUV, drawImageToCanvas, generateUVs, getViewSizeAtZ, arrangeSize, loadImg } = useThree({
  threeRef,
  hasHelp: false,
  hasControls: false,
})

const params = {
  paused: false,
  itemWidth: 10,
  timeScale: 1,
  direction: 0, // 方向
  backGroundColor: '',
  backVideo: '蓝色背景',
}

class BaseShape {
  name = 'BaseShape'
  group: THREE.Group
  timeline: gsap.core.Timeline
  constructor(config?: { defaults?: gsap.TweenVars }) {
    this.group = new THREE.Group()
    this.group.visible = false
    scene.add(this.group)
    this.timeline = gsap.timeline({
      defaults: Object.assign({}, config?.defaults || {}, {
        duration: 3,
        ease: 'none',
      }),
      paused: true,
    })
    this.timeline.addLabel('start')
  }

  disposeItem(item: THREE.Object3D<THREE.Object3DEventMap>) {
    if (item instanceof THREE.Mesh || item instanceof THREE.Sprite) {
      item.material?.dispose()
      item.geometry?.dispose()
    } else if (item instanceof THREE.Group) {
      item.children.forEach((child) => {
        this.disposeItem(child)
      })
    }
  }

  init() {
    this.timeline.clear()
    this.group.traverse((item) => {
      this.disposeItem(item)
    })
    this.group.clear()
  }

  play() {
    return new Promise((resolve) => {
      this.timeline.restart().then(resolve)
    })
  }

  getEase(value = [0.66, 0.2, 0.45, 0.79] as [number, number, number, number]) {
    return BezierEasing(...value)
  }
}

// LOGO 效果
class Shape1 extends BaseShape {
  logo: string
  constructor(logo: string) {
    super()
    this.name = 'Shape1'
    this.logo = logo
  }

  async init() {
    super.init()

    // 可视区大小
    const size = getViewSizeAtZ(0)
    const logoImg = await loadImg(this.logo)
    // 单个图片大小
    const itemWidth = params.itemWidth
    const itemHeight = itemWidth / 1.8
    // 增加全局放大，因为不放大文字模糊的厉害
    const glableScale = 3
    const { width, height } = arrangeSize(logoImg, size.width, size.height)
    // 生成切割好的uv和透明通道
    const { uvs, alphaMap } = generateUVs(logoImg, width, height, itemWidth, itemHeight)

    // 生成canvas，往上面画文字图片
    const wordsCanvas = document.createElement('canvas')
    wordsCanvas.width = width * glableScale
    wordsCanvas.height = height * glableScale
    const canvasTexture = new THREE.CanvasTexture(wordsCanvas)
    canvasTexture.colorSpace = THREE.SRGBColorSpace
    canvasTexture.needsUpdate = true

    // 填充每个区域
    const geometry = new THREE.PlaneGeometry(itemWidth, itemHeight)

    for (const item of uvs) {
      if (!item.count) continue

      const canvasMaterial = new THREE.MeshBasicMaterial({
        map: canvasTexture,
        alphaMap,
        side: THREE.DoubleSide,
        transparent: true,
        alphaTest: 0.2,
      })

      canvasMaterial.blending = THREE.CustomBlending
      canvasMaterial.blendSrc = THREE.SrcAlphaFactor
      canvasMaterial.blendDst = THREE.DstAlphaFactor
      canvasMaterial.blendEquation = THREE.AddEquation

      const mesh = new THREE.Mesh(geometry.clone().setAttribute('uv', item.uv), canvasMaterial)

      // 相对自己有个左右幅度，z轴为统一平面
      const vector3 = new THREE.Vector3().random().subScalar(0.5).multiplyScalar(800)
      vector3.setZ(1200)
      mesh.position.copy(vector3)

      mesh.userData = item
      this.group.add(mesh)

      this.timeline.to(
        mesh.position,
        {
          x: item.x,
          y: item.y,
          z: 0,
          delay: (item.rowIndex / item.row) * 3,
          duration: Math.random() / 2 + 1,
          ease: this.getEase(),
          onUpdate() {
            const process = this.progress()
            if (process > 0.8) {
              mesh.material.alphaMap = alphaMap
            } else {
              mesh.material.alphaMap = null
            }
          },
        },
        'start',
      )
    }

    for (const item of uvs) {
      if (!item.count) continue
      // 画文字
      const itemData = getNext()
      if (!itemData) return
      drawImageToCanvasTextureByUV(wordsCanvas, canvasTexture, itemData.image, item, glableScale)
    }
  }

  play() {
    return new Promise((resolve) => {
      this.timeline
        .restart()
        .then(() => new Promise(r => setTimeout(r, 10000)))
        .then(() => this.timeline.reverse())
        .then(resolve)
    })
  }
}
// 球体效果
class Shape2 extends BaseShape {
  constructor() {
    super()
    this.name = 'Shape2'
  }

  async init() {
    super.init()

    // 可视区大小
    const size = getViewSizeAtZ(0)
    const count = 150
    const sphere = []

    const vector = new THREE.Vector3()
    for (let i = 0; i < count; i++) {
      const phi = Math.acos(-1 + (2 * i) / count)
      const theta = Math.sqrt(count * Math.PI) * phi

      const object = new THREE.Object3D()

      // 半径
      object.position.setFromSphericalCoords(Math.min(size.width, size.height) * 0.35, phi, theta)

      vector.copy(object.position).multiplyScalar(2)
      object.lookAt(vector)

      // 获取世界坐标系下的位置
      object.updateWorldMatrix(true, false)

      const vector3 = new THREE.Vector3()
      object.getWorldPosition(vector3)
      vector3.multiplyScalar(5).setY(-1000)

      object.userData = {
        position: object.position,
        randomPosition: vector3,
      }

      sphere.push(object)
    }

    // 遍历
    for (const item of sphere) {
      // 生成文字
      const itemData = getNext()
      if (!itemData) return
      const image = itemData.image

      const scale = (50 / image.width) * 0.5
      const geometry = new THREE.PlaneGeometry(image.width * scale, image.height * scale)
      const material = new THREE.SpriteMaterial({
        // color: 0xffffff,
        map: loadTexture(image),
        fog: false,
        transparent: true,
      })
      material.blending = THREE.CustomBlending
      material.blendSrc = THREE.SrcAlphaFactor
      material.blendDst = THREE.DstAlphaFactor
      material.blendEquation = THREE.AddEquation
      const sprite = new THREE.Sprite(material)
      sprite.geometry = geometry
      sprite.position.copy(item.userData.randomPosition)
      this.group.add(sprite)

      // 给每个添加动画
      this.timeline.to(
        sprite.position,
        {
          ...item.userData.position,
          delay: Math.random() * 2,
          duration: 2,
          ease: this.getEase([0.32, 0.66, 0.45, 0.79]),
        },
        'start',
      )
    }
    this.timeline.addLabel('rotation')

    this.timeline.to(
      this.group.rotation,
      {
        x: -Math.PI * 2,
        y: Math.PI * 1.3,
        ease: this.getEase([0.46, 0.4, 0.66, 0.8]),
        duration: 8,
      },
      'rotation-=2',
    )
    this.timeline.to(
      this.group.rotation,
      {
        z: Math.PI * 1.3,
        repeat: 1,
        yoyo: true,
        duration: 8,
      },
      'rotation-=2',
    )
  }

  play() {
    return new Promise((resolve) => {
      this.timeline
        .restart()
        .then(() => this.timeline.reverse())
        .then(resolve)
    })
  }
}
// 柱子效果
class Shape3 extends BaseShape {
  constructor() {
    super({
      defaults: {
        duration: 3,
      },
    })
    this.name = 'Shape3'
  }

  async init() {
    super.init()

    const groupOdd = new THREE.Group()
    const groupEven = new THREE.Group()
    this.group.add(groupOdd, groupEven)

    const allMesh: THREE.Mesh[] = []

    // 可视区大小
    const size = getViewSizeAtZ(0)
    const raduis = Math.min(size.width, size.height) * 0.3
    // 画圆
    const circleGeometry = new THREE.CircleGeometry(raduis * 1.3, 20)
    const position = circleGeometry.getAttribute('position')

    for (let i = 0; i < position.count - 1; i++) {
      const x = position.getX(i)
      let y = position.getZ(i) // 圆环放倒
      const z = position.getY(i)
      if (x === 0 && y === 0 && z === 0) continue

      // 每个点在z轴累加即可
      for (let j = -8; j <= 8; j++) {
        const itemData = getNext()
        if (!itemData) return
        const image = itemData.image.cloneNode() as HTMLImageElement

        const scale = 30 / image.width

        y = j * 30
        //
        const geometry = new THREE.PlaneGeometry(image.width * scale, image.height * scale)
        const material = new THREE.MeshBasicMaterial({
          map: loadTexture(image),
          transparent: true,
          side: THREE.DoubleSide,
        })
        material.blending = THREE.CustomBlending
        material.blendSrc = THREE.SrcAlphaFactor
        material.blendDst = THREE.DstAlphaFactor
        material.blendEquation = THREE.AddEquation
        const mesh = new THREE.Mesh(geometry, material)
        mesh.position.set(x, y, z)
        mesh.lookAt(new THREE.Vector3(0, y, 0))
        mesh.rotation.y += Math.PI

        // 生成随机位置
        const vector3 = new THREE.Vector3()
          .random()
          .subScalar(0.5)
          .multiplyScalar(raduis / 2)
        vector3.z = 500
        mesh.userData.randomPosition = vector3
        mesh.userData.randomRotation = new THREE.Euler(0, 0, 0)

        // 记录形状位置
        const shapPosition = mesh.position.clone()
        const shapRotation = mesh.rotation.clone()
        mesh.userData.shapPosition = { x: shapPosition.x, y: shapPosition.y, z: shapPosition.z }
        mesh.userData.shapRotation = { x: shapRotation.x, y: shapRotation.y, z: shapRotation.z }

        // 初始位置
        mesh.position.copy(mesh.userData.randomPosition)
        mesh.rotation.copy(mesh.userData.randomRotation)

        if (Math.abs(j) % 2) {
          groupEven.add(mesh)
        } else {
          groupOdd.add(mesh)
        }
        allMesh.push(mesh)
      }
    }

    // 从随机位置飞入屏幕组成形状
    for (const mesh of allMesh) {
      this.timeline.to(mesh.position, { ...mesh.userData.shapPosition, delay: Math.random() * 3 + 0.5 }, 'start')
      this.timeline.to(mesh.rotation, { ...mesh.userData.shapRotation, delay: Math.random() * 3 + 0.5 }, 'start')
    }
    // 开始奇偶旋转
    this.timeline.addLabel('step1')
    this.timeline.to(groupEven.rotation, { y: Math.PI * 1, duration: 6, repeat: 2 }, 'step1-=1')
    this.timeline.to(groupOdd.rotation, { y: -Math.PI * 1, duration: 6, repeat: 2 }, 'step1-=1')
    // 圆柱拉进、拉远
    this.timeline.to(this.group.position, { z: raduis * 0.6, ease: this.getEase([0.66, 0.2, 0.45, 0.79]) }, 'step1+=4')
    // this.timeline.to(this.group.position, { z: raduis * 0.2, ease: this.getEase([0.66, 0.2, 0.45, 0.79]) }, 'step1+=8')
    // 形状x轴旋转九十度
    // this.timeline.addLabel('step3', 'step1+=20')
    // this.timeline.to(groupEven.rotation, { x: Math.PI / 2, duration: 6 }, 'step3')
    // this.timeline.to(groupOdd.rotation, { x: Math.PI / 2, duration: 6 }, 'step3')
    // this.timeline.to(this.group.position, { z: -raduis * 0.3, duration: 6 }, 'step3')
    // 圆柱拉进、拉远
    // this.timeline.to(this.group.position, { z: raduis * 1.5, duration: 6 }, 'step3+=6')
  }

  play() {
    return new Promise((resolve) => {
      this.timeline
        .restart()
        .then(() => this.timeline.reverse())
        .then(resolve)
    })
  }
}

// 头像飞出
const AllShapes = computed(() => {
  const arr: BaseShape[] = []
  padsignConfig.value.shapes.forEach((item) => {
    switch (item.name) {
      case 'Shape1':
        if (item.logo) {
          arr.push(new Shape1(item.logo))
        }
        break
      case 'Shape2':
        arr.push(new Shape2())
        break
      case 'Shape3':
        arr.push(new Shape3())
        break
    }
  })
  return arr
})

const shapeIndex = ref(0)
// 播放签名动画
async function runNext() {
  const next = AllShapes.value[shapeIndex.value]
  if (next) {
    shapeIndex.value++
  } else {
    shapeIndex.value = 0
    runNext()
    return
  }

  next.timeline.timeScale(params.timeScale)
  next.group.visible = true
  next.init()
  next.play().then(() => {
    next.group.visible = false
    setTimeout(runNext, 0)
  })
}

// 火焰效果
const effectTexture = new AnimationTexture({
  url: new URL(`./assets/huoyan.png`, import.meta.url).href,
  autoPlay: false,
  loop: 1,
})
const huoyanMaterial = new THREE.MeshBasicMaterial({
  map: effectTexture,
  transparent: true,
})
{
  const effectMesh = new THREE.Mesh(
    new THREE.PlaneGeometry(1, 1),
    huoyanMaterial,
  )
  effectMesh.scale.set(260, 260, 260)
  effectMesh.position.set(0, 0, 100)
  scene.add(effectMesh)
}
// 文字
const wordsSize = 100
const wordsCanvas = document.createElement('canvas')
wordsCanvas.width = wordsSize * 3
wordsCanvas.height = wordsSize * 3
const wordsTexture = new THREE.CanvasTexture(wordsCanvas)
wordsTexture.colorSpace = THREE.SRGBColorSpace
const wordsMesh = new THREE.Mesh(
  new THREE.PlaneGeometry(1, 1),
  new THREE.MeshBasicMaterial({
    map: wordsTexture,
    // color: 0xff00ff,
    transparent: true,
  }),
)
wordsMesh.position.set(0, 0, 130)
wordsMesh.scale.set(0, 0, 0)
scene.add(wordsMesh)

const wordsTimeline = gsap.timeline({
  defaults: {
    duration: 1,
    ease: 'power3.inOut',
  },
  paused: true,
})
wordsTimeline.addLabel('start')
wordsTimeline.to(
  {},
  {
    onStart() {
      effectTexture.player?.play()
    },
  },
  'start+=.15',
)
wordsTimeline.fromTo(wordsMesh.scale, { x: 0, y: 0, z: 0 }, { x: wordsSize, y: wordsSize, z: wordsSize }, 'start')
wordsTimeline.to(wordsMesh.scale, { x: 0, y: 0, z: 0 }, 'start+=4')

async function playNext() {
  try {
    const next = loopText()
    if (!next) return
    if (!next.img) return

    const img = await loadImg(next.img)

    // clear canvas
    const ctx = wordsCanvas.getContext('2d')!
    ctx.clearRect(0, 0, wordsCanvas.width, wordsCanvas.height)
    drawImageToCanvas(img, wordsCanvas, 'contain')
    wordsTexture.needsUpdate = true
    // 动画开始播放
    wordsTimeline.restart()
    await new Promise(resolve => wordsTimeline.then(resolve))
  } finally {
    setTimeout(() => {
      playNext()
    }, 3000)
  }
}

//////////////
onMounted(async () => {
  try {
    // 优先加载火焰图片,确保效果播放不会丢失
    const huoyanImg = new Image()
    huoyanImg.src = new URL(`./assets/huoyan.png`, import.meta.url).href
    await new Promise((resolve) => {
      huoyanImg.onload = resolve
    })

    // 查配置信息
    await fetchConfig()

    if (AllShapes.value.length === 0) {
      Toast.messageSync('没有配置签字动画')
      return
    }

    // 初始化签字
    let hideFn = () => {}
    if (!hasData.value) {
      hideFn = Toast.messageSync('没有签字数据，请先签字')
    }
    await readyPromise
    hideFn()

    if (padsignConfig.value.animationEffectSwitch === 'N') {
      huoyanMaterial.visible = false
    }

    runNext()
    playNext()
  } catch (error) {
    console.error(error)
    Toast.message('加载失败，请刷新重试')
  }
})
</script>

<template>
  <div class="padsign-box">
    <video
      v-if="padsignConfig.bgVideo"
      class="bg-box"
      :src="padsignConfig.bgVideo"

      autoplay
      loop
      muted
      disablepictureinpicture
      disableremoteplayback
      playsinline
    ></video>
    <img v-else-if="padsignConfig.bgImage" class="bg-box" :src="padsignConfig.bgImage" alt="" />
    <div ref="threeRef" class="three-box"></div>
  </div>
</template>

<style scoped lang="scss">
.three-box,
.bg-box {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.bg-box {
  object-fit: fill;
  z-index: 1;
}
.three-box {
  z-index: 2;
  background-color: transparent;
}
</style>
