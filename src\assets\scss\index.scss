@use './nprogress.scss';

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  --webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

html,
body,
#app {
  --el-color-primary: #3c55ff;
  --el-color-primary-dark-2: #1d3aff;
  --el-color-primary-light-3: #7f8ffd;
  --scrollbar-width: 6px;

  height: 100%;
  overflow: hidden;
  font-family: 'Microsoft YaHei', Arial, Helvetica, 'Avenir', sans-serif;
  font-size: 14px;
  color: #333;

  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

/*全局滚动条样式*/
::-webkit-scrollbar {
  width: var(--scrollbar-width);
  height: 8px;
}

/* 滚动槽 */
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.15);
  border-radius: 0;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.3);
}

body {
  margin: 0;
}

/* 统一字体颜色定义 */
/* 公共链接样式 */
a,
a:link,
a:visited,
a:hover {
  color: var(--el-color-primary);
  text-decoration: none;
}

button {
  cursor: pointer;
  border: none;
  outline: none;
  background: none;
}

ul,
li {
  margin: 0;
  padding: 0;
  list-style: none;
}

p {
  margin: 0;
  padding: 0;
}

// 标题
h1 {
  font-size: 32px;
  margin: 12px 0;
}

h2 {
  font-size: 24px;
  margin: 10px 0;
}

h3 {
  font-size: 18px;
  margin: 8px 0;
}

h4 {
  font-size: 16px;
  margin: 6px 0;
}

h5 {
  font-size: 14px;
  margin: 4px 0;
}

h6 {
  font-size: 12px;
  margin: 2px 0;
}

input[type='number'] {
  appearance: textfield;

  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }
}

.el-pagination .el-select .el-input {
  width: 100px !important;
}

/* fade */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease-in-out;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 进入后和离开前保持原位
.fade-right-enter-to,
.fade-right-leave-from {
  opacity: 1;
  transform: none;
}

// 设置进入和离开过程中的动画时长0.5s
.fade-right-enter-active,
.fade-right-leave-active {
  transition: all 0.3s;
}

// 进入前和离开后为透明，并在右侧20px位置
.fade-right-enter-from,
.fade-right-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

// 重置el-avatar的样式，因为此组件在el-dropdown中时会出现边框
.el-avatar[tabindex] {
  outline: none;
}

/* 文字单行显示，多余部分省略号 */
.limit {
  white-space: nowrap;
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
}

// 部分原子化无法生效
.overflow-hidden {
  overflow: hidden;
}

// 去掉el按钮的边框
.el-button.is-text:not(.is-disabled):focus-visible {
  outline: none !important;
}

// fix animate.css delay，repeat失效
.animate__animated {
  animation-delay: var(--animate-delay);
  animation-iteration-count: var(--animate-repeat);
}

.animate__rotate360 {
  animation-name: rotate360;
  animation-timing-function: linear;
}

@keyframes rotate360 {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

// 图片背景
.bgblank {
  background-size: 9px;
  background-image: url(@/assets/bgblank.svg);

  >img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

// tabs full
.el-tabs-full {
  width: 100%;
  height: 100%;

  .el-tabs__content {
    height: calc(100% - 39px);
  }
}

.pointer-lock {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  pointer-events: none;

  &::after {
    content: '';
    display: block;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAQAAAAAYLlVAAADgUlEQVR42u2Yy2sTURTGv3nPZKaxmTxrMzXYGENRUIKPugoIrlwIMuBWUNCNiFtdZFORirjRautKXFjc6MY/wSpSBFuDYpFW4xMX2mpTLWmu3GmbTtupbnqvKDlDAsnm+8137j33nAs0oxnNaEYz/tUgAgT6gQj6zT2osFTJ1m7W71Wy/CEESJBf5eoThBBSn/iwC7IHwS1EKMN5Kj9KBinC1/E9UAhNCSfzlZH83GsqnyUJH0JJ5CJfWCaf8CPQRDBf+8rwCvklhLG9bNfCKvMTnBECzF+NML6noLBJhAA5o68tT59+D+FWG2QWHkhd6ug+uu+vk96FZx6k0PjdS94QQj4egbb+HgiuBB12jSo0ouABHPL/ReYm7+xIG67EovYZiFzpLve87Cv3PxuoTfkBZt6P3ij3v7hW7rncjWjaWP8kCCUxq6EVKT2DPLZj58x7P8CXYexQtyGvbUYbItAZOFASCwpMRJAyHHQi/+OdH2DyMfLYbKTNJOy4BZXBViQ0CUraQDhsI4XMz7d+gG+P9E1IwkYLQl2qK7GpBIIrFWWoSROtaJ+t+AGmH2IjWhGCWlAgsayF1Ac1biG1EsBKwCrQ40hgfxrIMJFc5UC8LQQJHIICWCsBqkOIgw8AdSAZ5EAMIchcAIqBKbBoCmRODiDYAZMLAAIBqkMt/FIQ7IAHwGsRwjL/3iLEGrvA4rUNgWIAQHWI2y6AUFBWp2ChEHEBED2AoLPAxPxZILBbfuJw7Pv+6WOfTo6dnpv0A8xWxk99PjF9rHr4UyfDthzq3NM/9YT1iSdxNmeiN5AS8pXcbjyLXfHSP5OEkKnjXkfEoCvWLjnU+IE1poIE6fU8uL8dWnH9m1JXyuiw7x6g3fDF38g/P+N1xQy6IjoXROD0HwpGmJcvn0cHbBgMSlJJhIYwUsgOHqVSFwPkX/Zhi9GODRkdDAYT2geEwjbakXtwbjlCQ34rHETbQkWZzV2JCCVpImo4yD08u4SwTD4GOhUwOhO8jhgWonCWXGjI59HhNSXMpoJlCEZaW0BYfHu9A7H4/NszbcxpPVBhtcSQ1nJjVwmpTY1c0Dx5WF0quFxTUQQTUaS1rJo/uFvLwmlhmvsgBLoc6YzowEEqbC/knutNqYKQHUZkQwRhOhO6EufLWleC3KVmdOi08vO4ogy4MYAEyZVcCX9BvhnN+H/iF+0u596u9fOoAAAAAElFTkSuQmCC) 50% no-repeat;
    width: 30px;
    height: 30px;
    margin-left: -15px;
    margin-top: -15px;
    background-size: contain;
    transform: rotateZ(90deg);
  }
}
