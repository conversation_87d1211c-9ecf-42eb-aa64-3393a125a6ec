<script setup lang="ts">
import { getDefaultMaterial, openSelectMaterial, useDataAttr } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'
import { defaultData, type IDesignSeglotteryIng } from './seglottery-ing'

const layer = defineModel<IDesignSeglotteryIng>('layer', { required: true })

const itemCountBlind = useDataAttr(layer.value, 'itemCount', defaultData.itemCount)
const roolBgcolorBlind = useDataAttr(layer.value, 'roolBgcolor', defaultData.roolBgcolor)
const roolBorderColorBind = useDataAttr(layer.value, 'roolBorderColor', defaultData.roolBorderColor)
const roolWidthBind = useDataAttr(layer.value, 'roolWidth', defaultData.roolWidth)
const roolHeightBlind = useDataAttr(layer.value, 'roolHeight', defaultData.roolHeight)
const roolLeftBlind = useDataAttr(layer.value, 'roolLeft', defaultData.roolLeft)
const roolTopBlind = useDataAttr(layer.value, 'roolTop', defaultData.roolTop)
const itemBgImgBlind = useDataAttr(layer.value, 'itemBgImg', defaultData.itemBgImg)
const noStartImgBlind = useDataAttr(layer.value, 'noStartImg', defaultData.noStartImg)
const roolFontcolorBlind = useDataAttr(layer.value, 'roolFontcolor', defaultData.roolFontcolor)
const roolFontSizeSizeBlind = useDataAttr(layer.value, 'roolFontSize', defaultData.roolFontSize)
const roolGapBlind = useDataAttr(layer.value, 'roolGap', defaultData.roolGap)
const roolLabelPostionBlind = useDataAttr(layer.value, 'roolLabelPostion', defaultData.roolLabelPostion)
const roolLabelcolorBlind = useDataAttr(layer.value, 'roolLabelcolor', defaultData.roolLabelcolor)
const roolLabelSizeBlind = useDataAttr(layer.value, 'roolLabelSize', defaultData.roolLabelSize)
const showLabelBlind = useDataAttr(layer.value, 'showLabel', defaultData.showLabel)
const animationModeBlind = useDataAttr(layer.value, 'animationMode', defaultData.animationMode)

async function updateMaterialFn(name: 'itemBgImg' | 'noStartImg', isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, `${name}`)
    : await openSelectMaterial('PIC')
  if (result) {
    layer.value[name] = result
  } else if (isReset) {
    delete layer.value[name]
  }
}

const posList = [
  { label: '右侧', value: 'right' },
  { label: '下方', value: 'bottom' },
]
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item mt-5!">
        <h3>背景图</h3>
        <div class="flex items-end">
          <div class="bgblank relative h-50 w-64">
            <MaterialThumbnail @select="updateMaterialFn('itemBgImg')" @reset="updateMaterialFn('itemBgImg', true)">
              <img v-if="itemBgImgBlind" :src="itemBgImgBlind" alt="" class="h-full w-full object-cover" />
            </MaterialThumbnail>
          </div>
        </div>
      </div>
      <div class="setting-item">
        <h3>动画模式</h3>
        <el-select v-model="animationModeBlind" placeholder="请选择动画模式" class="w-100px">
          <el-option label="数字变化" value="default" />
          <el-option label="向上滑动" value="slotMachine" />
        </el-select>
      </div>

      <div class="setting-item">
        <h3>元素总数</h3>
        <el-input-number v-model="itemCountBlind" v-input-number :max="30" :min="1" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>滚动区域</h3>
      </div>
      <div class="setting-item">
        <h3></h3>
        <div class="flex flex-a-c">
          <p>宽：</p>
          <el-input-number v-model="roolWidthBind" v-input-number :min="0" :max="100" controls-position="right" />
          <p class="ml-10">高：</p>
          <el-input-number v-model="roolHeightBlind" v-input-number :min="0" :max="100" controls-position="right" />
        </div>
      </div>
      <div class="setting-item">
        <h3></h3>
        <div class="flex flex-a-c">
          <p>左：</p>
          <el-input-number v-model="roolLeftBlind" :min="0" :max="100" controls-position="right" />
          <p class="ml-10">上：</p>
          <el-input-number v-model="roolTopBlind" :min="0" :max="100" controls-position="right" />
        </div>
      </div>
      <div class="setting-item">
        <h3>卡片背景</h3>
        <hi-color v-model="roolBgcolorBlind" />
      </div>
      <div class="setting-item">
        <h3>卡片间隔</h3>
        <el-input-number v-model="roolGapBlind" :min="0" :max="100" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>卡片边框色</h3>
        <hi-color v-model="roolBorderColorBind" />
      </div>
      <div class="setting-item">
        <h3>卡片值颜色</h3>
        <hi-color v-model="roolFontcolorBlind" />
      </div>
      <div class="setting-item">
        <h3>卡片值字号</h3>
        <el-input-number v-model="roolFontSizeSizeBlind" v-input-number :max="100" :min="10" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>卡片标签颜色</h3>
        <hi-color v-model="roolLabelcolorBlind" />
      </div>
      <div class="setting-item">
        <h3>卡片标签字号</h3>
        <el-input-number v-model="roolLabelSizeBlind" v-input-number :max="100" :min="10" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>标签位置</h3>
        <el-select v-model="roolLabelPostionBlind" placeholder="选择标签位置" class="w-100">
          <el-option v-for="item in posList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="setting-item mt-5!">
        <h3>未开始封面</h3>
        <div class="flex items-end">
          <div class="bgblank relative h-50 w-64">
            <MaterialThumbnail @select="updateMaterialFn('noStartImg')" @reset="updateMaterialFn('noStartImg', true)">
              <img v-if="noStartImgBlind" :src="noStartImgBlind" alt="" class="h-full w-full object-cover" />
            </MaterialThumbnail>
          </div>
        </div>
      </div>
      <div class="setting-item">
        <h3>标签展示</h3>
        <el-switch v-model="showLabelBlind" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>
