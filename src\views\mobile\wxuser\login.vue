<script setup lang="ts">
import api from '@/api'
import VantButton from 'vant/lib/button'
import VantCellGroup from 'vant/lib/cell-group'
import Vant<PERSON>ield from 'vant/lib/field'
import { envUtils } from '~/src/utils/env'
import 'vant/lib/button/style'
import 'vant/lib/field/style'
import 'vant/lib/cell-group/style'

definePage({ meta: { label: '参与者登录' } })

const isShow = ref(envUtils.isDev)
const route = useRoute()
const router = useRouter()
const redirect = ref<string>()

const userInfo = ref({
  wxUserId: localStorage.wxuserId || 45066,
  mobileFlag: route.query.mobileFlag || localStorage.mobileFlag || 'kuugZBbF',
  secret: 'hxc@2025',
})

async function loginFn() {
  await api.mobile.wxuser.hlogin({ where: userInfo.value })

  ElMessage.success('登录成功')

  setTimeout(() => {
    if (redirect.value) {
      router.push(decodeURIComponent(redirect.value))
    } else {
      router.push('/mobile/wall/shakev3')
    }
  }, 1000)
}

onMounted(() => {
  const _redirect = decodeURIComponent(route.query.redirect as string)
  if (_redirect) {
    redirect.value = _redirect
    if (_redirect.includes('mobileFlag')) {
      const mobileFlag = _redirect.match(/mobileFlag=(\w+)/)
      if (mobileFlag) {
        userInfo.value.mobileFlag = mobileFlag[1]
      }
    }
  }
})
</script>

<template>
  <div v-if="isShow" class="mx-auto my-10% w-300 text-center">
    <h3>参与者登录</h3>
    <VantCellGroup inset>
      <VantField v-model.number="userInfo.wxUserId" required label="用户Id" placeholder="请输入用户Id" />
      <VantField v-model.trim="userInfo.mobileFlag" required label="mobileFlag" placeholder="请输入mobileFlag" />
    </VantCellGroup>
    <VantButton type="primary" block class="mt-20!" @click="loginFn">登录</VantButton>
  </div>
</template>

<style scoped lang="scss">
</style>
