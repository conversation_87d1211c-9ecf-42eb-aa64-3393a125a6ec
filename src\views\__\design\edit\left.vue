<script setup lang="ts">
import component from '@/assets/design/layout/component.svg'
import image from '@/assets/design/layout/image.svg'
import Media from '@/assets/design/layout/media.svg'
import textRich from '@/assets/design/layout/text-rich.svg'
import text from '@/assets/design/layout/text.svg'
import video from '@/assets/design/layout/video.svg'
import { createComponent } from './createLayer'

const leftLayoutOptions = [
  { type: 'component', name: '组件', icon: component },
  { type: 'text', name: '文本', icon: text },
  { type: 'text-rich', name: '富文本', icon: textRich },
  { type: 'image', name: '图片', icon: image },
  // { type: 'shape', name: '形状', icon: shape },
  { type: 'video', name: '视频', icon: video },
  { type: 'img-video', name: '图片/视频', icon: Media },
]
</script>

<template>
  <div class="left">
    <div
      v-for="item in leftLayoutOptions"
      :key="item.type"
      class="moveable-ignore item-box"
      :class="[item.type === 'component' ? 'cur' : '']"
      @click="createComponent(item.type)"
    >
      <img :src="item.icon">
      <span> {{ item.name }}</span>
    </div>
  </div>
</template>

<style scoped lang="scss">
.left {
  width: var(--left-width);
  height: 100%;
  overflow: auto;
  background-color: #fff;
  border-right: 1px solid var(--border-color);
  z-index: 10;
}
.item-box {
  width: 100%;
  height: 64px;
  text-align: center;
  transition: 0.2s;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  cursor: pointer;
  span {
    font-size: 12px;
    line-height: 20px;
    text-align: center;
    font-weight: 400;
  }

  &.cur,
  &:hover {
    color: #fff;
    background-color: var(--el-color-primary);
    img {
      filter: invert(1);
    }
  }
}
</style>
