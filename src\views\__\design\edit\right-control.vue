<script setup lang="ts">
import { hasAuth } from '~/src/utils/auth'
import { designDataHistory, provideElementSize, useDesignData, useDesignTemp } from '..'
import { initShortcuts } from '../shortcuts'
import selectPhone from './select-phone.vue'

provideElementSize()

const designData = useDesignData()
const designTemp = useDesignTemp()
const { trashFn, pasteFn, copyFn } = initShortcuts()

const isMobileMode = computed(() => designTemp.showType === 'mobile')

function copy() {
  if (!designTemp.activeList.length) {
    return
  }
  copyFn()
  pasteFn()
}

function onScalePlus() {
  designTemp.scale += 10
}

function onScaleMinus() {
  designTemp.scale -= 10
}

function onDraftsBlur() {
  designData.option.drafts.forEach((_item, index) => {
    if (!designData.option.drafts[index]) {
      designData.option.drafts[index] = 300
    }
  })
}
</script>

<template>
  <div class="moveable-ignore right-control flex flex-col items-center">
    <template v-if="hasAuth(['man', 'oem', 'spo.hi.vip', 'spo.ft'])">
      <el-button title="撤销" text class="mx-0 my-2 w-90% py-14" :disabled="!designDataHistory.canUndo" @click="designDataHistory.undo">
        <icon-ph:arrow-bend-up-left-bold />
      </el-button>
      <el-button title="前进" text class="mx-0 my-2 w-90% py-14" :disabled="!designDataHistory.canRedo" @click="designDataHistory.redo">
        <icon-ph:arrow-bend-up-right-bold />
      </el-button>

      <div class="mx-auto my-6 h-1px w-50% bg-#ccc"></div>

      <el-button title="复制" text class="mx-0 my-2 w-90% py-14" @click="copy">
        <icon-ph:copy-bold />
      </el-button>

      <el-button title="删除" text class="mx-0 my-2 w-90% py-14" @click="trashFn">
        <icon-ph:trash-bold />
      </el-button>
    </template>

    <div class="mx-auto my-6 h-1px w-50% bg-#ccc"></div>

    <el-popover
      v-if="hasAuth(['man', 'oem', 'spo.hi.vip', 'spo.ft'])"
      placement="left-start"
      :width="200"
      trigger="click"
    >
      <template #reference>
        <el-button title="设计稿" text class="mx-0 my-2 w-90% py-14">
          <icon-ph:resize-bold />
        </el-button>
      </template>
      <template #default>
        <div>
          <div class="mb-10 flex items-center justify-between font-bold">
            设计稿
          </div>
          <p v-if="isMobileMode" class="my-10px text-12px text-red">手机端设计稿尺寸不支持修改</p>
          <div class="mb-10 flex items-center justify-between">
            <span class="text-14 text-#666">宽度</span>
            <div>
              <el-input-number
                v-model="designData.option.drafts[0]"
                :controls="false"
                :min="isMobileMode ? 300 : 751"
                :disabled="isMobileMode"
                :precision="0"
                class="mr-6 w-100"
                @blur="onDraftsBlur"
              />
              <span>px</span>
            </div>
          </div>

          <div class="mb-10 flex items-center justify-between">
            <span class="text-14 text-#666">高度</span>
            <div>
              <el-input-number
                v-model="designData.option.drafts[1]"
                :controls="false"
                :min="300"
                :disabled="isMobileMode"
                :precision="0"
                class="mr-6 w-100"
                @blur="onDraftsBlur"
              />
              <span>px</span>
            </div>
          </div>
        </div>
      </template>
    </el-popover>

    <el-popover
      v-if="isMobileMode"
      placement="left-start"
      :width="260"
      trigger="click"
    >
      <template #reference>
        <el-button title="手机预览" text class="mx-0 my-2 w-90% py-14">
          <icon-ph:device-mobile-camera class="text-14px" />
        </el-button>
      </template>
      <template #default>
        <div>
          <select-phone v-if="isMobileMode" />
        </div>
      </template>
    </el-popover>

    <!-- 放大 缩小 -->
    <div class="flex flex-col items-center justify-center">
      <el-button title="缩小" text class="mx-0 my-2 w-90% py-14" @click="onScaleMinus">
        <icon-ph:minus-circle-bold />
      </el-button>
      <p class="my-5px text-12px">{{ designTemp.scale }}%</p>
      <el-button title="放大" text class="mx-0 my-2 w-90% py-14" @click="onScalePlus">
        <icon-ph:plus-circle-bold />
      </el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.right-control {
  --el-fill-color: #3c55ff;
  --el-fill-color-light: #3c55ff;

  width: var(--right-control-width);
  background-color: #fff;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.08);
  z-index: 10;
  :deep() {
    .el-button {
      margin: 0;
    }
    .el-button.is-text:not(.is-disabled):hover {
      svg {
        color: #fff;
      }
    }
    .el-input-number.is-without-controls .el-input__wrapper {
      padding: 0;
    }
  }
}
:deep() {
  .right-control--el-color-picker {
    .el-color-picker__trigger {
      border: none;
      width: 40px;
    }
  }
}
</style>
