import type { IDesignLayer, IDesignSetup } from '../../types'
import { BisTypes, useDesignData } from '../..'
import { InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './lottery-ing4-setting.vue'
import Comp from './lottery-ing4.vue'
// 类型
export const type = 'lottery-ing4'
export const defaultHeadSize = 200
export const defaultGapRow = 10
export const defaultGapColumn = 10
export const defaultDecorateAnimation = true
// 数据类型约束
export interface IDesignLotteryIng4 extends IDesignLayer {
  type: typeof type
  headSize?: number
  decorateImg?: string
  gapRow?: number
  gapColumn?: number
  decorateAnimation?: boolean
}
export const maxCout: Record<string, number> = reactive({})
// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.sportsIng,
    showType: ['pcwall'],
    status: ['ing'],
    showInteractive: [InteractiveEnum.lotteryv3],
    type,
    name: '抽奖进行中',
    thumbnail: new URL('./lottery-ing4.png', import.meta.url).href,
    Comp,
    CompSetting,
    defaultData() {
      const designData = useDesignData()
      const [width, height] = designData.option.drafts
      return {
        type,
        uuid: layerUuid(),
        name: '抽奖进行中',
        decorateImg: new URL('./assets/head-light.png', import.meta.url).href,
        style: {
          width: `${width}px`,
          height: `${height}px`,
          top: 0,
          left: 0,
        },
      }
    },
  })
}
