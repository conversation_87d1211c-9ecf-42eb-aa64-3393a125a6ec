import type { AxiosResponse } from 'axios'
import { useInstanceRouter } from '~/src/hooks/useInstanceRouter'

export async function mobileOnFulfilledResInterceptors(_res: AxiosResponse) {
  // return res
}

export async function mobileOnRejectedResInterceptors(res: AxiosResponse) {
  const router = useInstanceRouter()
  // console.log('🚀 ~ mobileOnRejectedInterceptors ~ res:', res)
  const state = res.data.state
  // ecode是 GROUPLIMIT 分组限制，跳转到错误页
  if (state === 'GROUPLIMIT') {
    router.push('/mobile/wall/common/error')
    res.data._msg = '无法参与'
    return Promise.reject(res)
  }
}
