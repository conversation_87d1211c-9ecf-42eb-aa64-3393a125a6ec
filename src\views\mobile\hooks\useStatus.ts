import { useDesignState, useDesignTemp } from '../../__/design'

const designTemp = useDesignTemp()
const designState = useDesignState()

// 活动状态
const timestamp = useTimestamp()
interface UseStatus {
  round: Ref<any>
  regedit: Ref<any>
}
export function useStatus({
  round,
  regedit,
}: UseStatus) {
  // 活动状态
  const status = computed(() => {
    if (designTemp.isEdit) {
      return designState.status
    }

    // ready
    if (!round.value?.startTime) {
      return 'ready'
    }

    if (round.value?.endTime) {
      const offsetEnd = timestamp.value - round.value.endTime
      if (offsetEnd > 0) {
        return 'finish'
      }
    }

    if (!regedit.value) {
      return 'ready'
    }

    const offsetStart = timestamp.value - round.value.startTime
    if (offsetStart < -3000) {
      return 'ready'
    }
    // 321
    if (offsetStart > -3000 && offsetStart < 0) {
      return '321'
    }
    // ing
    if (!round.value.endTime) {
      console.warn('活动进行中结束时间未设置')
      return 'ing'
    }
    const offsetEnd = timestamp.value - round.value.endTime
    if (offsetEnd < 0) {
      return 'ing'
    }
    // finish
    return 'finish'
  }) as ComputedRef<'ready' | '321' | 'ing' | 'finish'>
  return status
}
