<script setup lang="ts">
import type { IDesignLoopMovingImage } from './loop-moving-image'
import { openSelectMaterial, useDataAttr } from '../..'
import { defalutPlayMode } from './loop-moving-image'

const layer = defineModel<IDesignLoopMovingImage>('layer', { required: true })

async function updateMaterialFn() {
  const result = await openSelectMaterial('PIC')
  if (result) {
    layer.value.data.src = result
  }
}

const playModeBind = useDataAttr(layer.value, 'playMode', defalutPlayMode)
const playpOtions = [
  { label: '左右循环', value: 'loop' },
  { label: '从右到左', value: 'rightLeft' },
  { label: '从左到右', value: 'leftRight' },
]
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <div class="thumbnail-box bgblank" @click="updateMaterialFn">
          <img :src="layer.data.src">
        </div>
      </div>
      <div class="setting-item">
        <div class="setting-item-label">持续时间</div>
        <div class="setting-item-content">
          <el-input-number v-model="layer.data.duration" v-input-number :min="1" :max="60" :controls="false"></el-input-number>
        </div>
      </div>
      <div class="setting-item">
        <div class="setting-item-label">运动方式</div>
        <div class="setting-item-content">
          <el-select v-model="playModeBind" class="w-100">
            <el-option
              v-for="item in playpOtions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </div>
      <div class="setting-item">
        <div class="setting-item-label">自动旋转</div>
        <div class="setting-item-content">
          <el-switch v-model="layer.data.autoRotate"></el-switch>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.thumbnail-box {
  width: 120px;
  height: 120px;
  outline: 1px solid #e6ebed;
  border-radius: 4px;
  position: relative;

  img {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }

  > img {
    object-fit: contain;
  }
}
</style>
