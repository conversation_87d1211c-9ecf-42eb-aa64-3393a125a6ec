<script setup lang="ts">
import HiDesign from 'design/index.vue'
import { useMobileMicrosite } from '~/src/views/mobile/wall/microsite'

definePage({ meta: { label: '微站' } })

const interactive = 'microsite'

useMobileMicrosite()

const matchs = location.href.match(/mobileFlag=([^&]+)/) || []
const mobileFlag = matchs[1]
const customFlag = mobileFlag === 'QgjoIbzZ'
if (customFlag) {
  useTitle('看竞赛|马上嗨')
}
</script>

<template>
  <HiDesign :interactive="interactive" />
</template>

<style scoped lang="scss">
</style>
