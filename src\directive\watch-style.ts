import type { DirectiveBinding } from 'vue'

let observer: MutationObserver
export default {
  mounted: (el: HTMLElement, binding: DirectiveBinding) => {
    const option: MutationObserverInit = {
      attributes: true,
      attributeFilter: ['style'],
      attributeOldValue: true,
    }

    observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
          binding.value(el.style, mutation.oldValue)
        }
      })
    })
    observer.observe(el, option)
  },
  unmounted: () => {
    if (observer) {
      observer.disconnect()
    }
  },
}
