<script setup lang="ts">
import { Delete, Edit, Plus } from '@element-plus/icons-vue'
import { computed, ref, watch } from 'vue'
import { openSelectMaterial } from '..'

interface ColorStop {
  id: number
  color: string
  position: number
}
interface GradientData {
  angle: number
  stops: ColorStop[]
}

const modelValue = defineModel<string>()
const isValueUrl = computed(() => !!modelValue.value?.startsWith('url('))
const isValueGradient = computed(() => !!modelValue.value?.startsWith('linear-gradient('))

const defaultAvatarBind = computed({
  get: () => {
    if (isValueUrl.value) {
      return modelValue.value!.replace(/^url\((.*)\)$/, '$1')
    }
    return 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'
  },
  set: (value: string) => {
    modelValue.value = value ? `url(${value})` : ''
  },
})

async function selectMaterialFn() {
  try {
    if (isValueGradient.value) {
      return
    }
    const result = await openSelectMaterial('PIC')
    if (result) {
      defaultAvatarBind.value = result
    }
  } catch (error) {
    console.error('选择素材失败:', error)
  }
}

// --- 渐变编辑逻辑 ---
const editableGradient = ref<GradientData | null>(null)
const popoverVisible = ref(false)
const nextStopId = ref(0) // 用于生成唯一 ID

function parseGradient(gradientString: string | undefined): GradientData | null {
  if (!gradientString || !gradientString.startsWith('linear-gradient(')) {
    return null
  }
  try {
    const content = gradientString.substring('linear-gradient('.length, gradientString.length - 1)
    const parts = content.split(/,(?![^(]*\))/).map(part => part.trim())

    let angle = 180
    const stops: ColorStop[] = []

    // 处理角度
    const angleMatch = parts[0].match(/^(\d+(\.\d+)?)deg$/)

    if (angleMatch) {
      angle = Number.parseFloat(angleMatch[1])
      parts.shift()
    } else if (parts[0].includes(' to ')) {
      // 处理 to 方向语法,转换为角度
      const direction = parts[0].split(' to ')[1].trim()
      switch (direction) {
        case 'top': angle = 0; break
        case 'right': angle = 90; break
        case 'bottom': angle = 180; break
        case 'left': angle = 270; break
        default: angle = 180
      }
      parts.shift()
    }

    for (const part of parts) {
      const stopMatch = part.trim().match(/^(#[0-9a-fA-F]{3,8}|rgba?\([^)]+\))\s*(\d+(\.\d+)?)?%?$/)
      if (stopMatch) {
        const color = stopMatch[1]
        const positionStr = stopMatch[2]
        const position = positionStr ? Number.parseFloat(positionStr) : (stops.length === 0 ? 0 : 100)
        stops.push({ id: nextStopId.value++, color, position })
      }
    }

    if (stops.length < 2) {
      if (stops.length === 0) stops.push({ id: nextStopId.value++, color: '#000000', position: 0 })
      if (stops.length === 1) stops.push({ id: nextStopId.value++, color: '#ffffff', position: 100 })
    }
    stops.sort((a: ColorStop, b: ColorStop) => a.position - b.position)

    return { angle, stops }
  } catch (e) {
    console.error('解析渐变失败:', e)
    return null
  }
}

function serializeGradient(gradientData: GradientData | null): string {
  if (!gradientData) return ''
  const sortedStops = [...gradientData.stops].sort((a: ColorStop, b: ColorStop) => a.position - b.position)
  const stopsString = sortedStops.map(stop => `${stop.color} ${stop.position}%`).join(', ')
  return `linear-gradient(${gradientData.angle}deg, ${stopsString})`
}

function openGradientEditor() {
  const parsed = parseGradient(modelValue.value)
  console.log('🚀 ~ openGradientEditor ~ parsed:', parsed)
  if (parsed) {
    editableGradient.value = parsed
  } else {
    nextStopId.value = 0
    editableGradient.value = {
      angle: 180,
      stops: [
        { id: nextStopId.value++, color: '#ff0000', position: 0 },
        { id: nextStopId.value++, color: '#0000ff', position: 100 },
      ],
    }
  }
  popoverVisible.value = true
}

function addStop() {
  if (!editableGradient.value) return
  const newPosition = 50
  const newColor = '#ffffff'
  editableGradient.value.stops.push({
    id: nextStopId.value++,
    color: newColor,
    position: newPosition,
  })
  editableGradient.value.stops.sort((a: ColorStop, b: ColorStop) => a.position - b.position)
}

function removeStop(id: number) {
  if (!editableGradient.value || editableGradient.value.stops.length <= 2) {
    return
  }
  editableGradient.value.stops = editableGradient.value.stops.filter((stop: ColorStop) => stop.id !== id)
}

function updateModelValueFromEditor() {
  if (editableGradient.value) {
    modelValue.value = serializeGradient(editableGradient.value)
  }
}

const serializedGradientPreview = computed(() => {
  return editableGradient.value ? serializeGradient(editableGradient.value) : ''
})

watch(editableGradient, (newValue) => {
  if (newValue) {
    modelValue.value = serializeGradient(newValue)
  }
}, { deep: true })
</script>

<template>
  <div class="text-image-edit">
    <h3>{{ isValueGradient ? '文字渐变' : '文字贴图' }}</h3>
    <div class="preview-container relative">
      <div
        class="bgblank relative h-100 w-100 cursor-pointer"
        @click="selectMaterialFn"
      >
        <div
          v-if="isValueGradient"
          class="gradient-preview h-full w-full"
          :style="{ background: modelValue }"
        />
        <img
          v-else
          :src="defaultAvatarBind"
          alt="文字贴图"
          class="max-h-full max-w-full object-contain"
        >
      </div>

      <el-popover
        v-if="isValueGradient"
        :visible="popoverVisible"
        placement="right"
        :width="300"
        popper-class="gradient-editor-popover"
        @hide="updateModelValueFromEditor"
      >
        <template #reference>
          <el-button
            class="edit-gradient-btn"
            circle
            size="small"
            @click.stop="openGradientEditor"
          >
            <el-icon :size="12">
              <Edit />
            </el-icon>
          </el-button>
        </template>
        <div v-if="editableGradient" class="gradient-editor-content">
          <div class="editor-preview" :style="{ background: serializedGradientPreview }">预览</div>

          <div class="editor-row">
            <span class="editor-label">角度</span>
            <el-slider v-model="editableGradient.angle" :min="0" :max="360" show-input size="small" />
          </div>

          <div class="editor-row stops-header">
            <span class="editor-label">颜色停靠点</span>
            <el-button size="small" circle @click="addStop">
              <el-icon :size="12">
                <Plus />
              </el-icon>
            </el-button>
          </div>
          <div
            v-for="stop in editableGradient.stops"
            :key="stop.id"
            class="editor-row stop-item"
          >
            <el-color-picker v-model="stop.color" size="small" show-alpha />
            <el-input-number
              v-model="stop.position"
              :min="0"
              :max="100"
              :step="1"
              size="small"
              controls-position="right"
              class="stop-position-input"
            />
            <span>%</span>
            <el-button
              size="small"
              circle
              type="danger"
              plain
              :disabled="editableGradient.stops.length <= 2"
              @click="removeStop(stop.id)"
            >
              <el-icon :size="12">
                <Delete />
              </el-icon>
            </el-button>
          </div>
        </div>
      </el-popover>
    </div>
  </div>
</template>

<style scoped lang="scss">
.text-image-edit {
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: self-start;

  h3 {
    margin: 0;
    font-size: 14px;
    margin-right: 10px; // Add some space
  }
}

.preview-container {
  width: 100px;
  height: 100px;
  position: relative;
}

.bgblank {
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.edit-gradient-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  z-index: 10;
}

.gradient-editor-content {
  .editor-preview {
    height: 30px;
    border-radius: 4px;
    margin-bottom: 15px;
    border: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.5);
    background-clip: padding-box;
    background-color: #fff;
  }
  .editor-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
  .editor-label {
    font-size: 13px;
    color: #606266;
    margin-right: 10px;
    width: 70px;
    flex-shrink: 0;
  }
  .el-slider {
    flex-grow: 1;
  }
  .stops-header {
    justify-content: space-between;
    margin-bottom: 5px;
    .editor-label {
      width: auto;
    }
  }
  .stop-item {
    justify-content: space-between;
    .el-color-picker {
      margin-right: 8px;
    }
    .stop-position-input {
      width: 80px;
      margin: 0 4px 0 8px;
    }
    .el-button {
      margin-left: 8px;
    }
  }
}
</style>
