<script setup lang="ts">
import type { IDesignLayerAnimate, IDesignLayerAnimateType } from '../../../types'
import { cloneDeep } from 'lodash-es'
import IconPlay from '~icons/ph/play-bold'
import IconTrash from '~icons/ph/trash-bold'
import { useDesignData, useDesignTemp } from '../../..'

const designData = useDesignData()
const designTemp = useDesignTemp()

const currentLayers = computed(() => {
  return designData.getLayerByUuids(designTemp.activeList)
})

const layer = computed(() => {
  return currentLayers.value[0]
})

// 定义动画列表
interface IAnimate { type: IDesignLayerAnimateType, name: string, animate: string }
const animateList: IAnimate[] = [
  { type: 'Empha', name: '弹跳', animate: 'animate__bounce' },
  { type: 'Empha', name: '闪烁', animate: 'animate__flash' },
  { type: 'Empha', name: '脉冲', animate: 'animate__pulse' },
  { type: 'Empha', name: '橡皮筋', animate: 'animate__rubberBand' },
  { type: 'Empha', name: '水平抖动', animate: 'animate__shakeX' },
  { type: 'Empha', name: '垂直抖动', animate: 'animate__shakeY' },
  { type: 'Empha', name: '头部抖动', animate: 'animate__headShake' },
  { type: 'Empha', name: '摇摆', animate: 'animate__swing' },
  { type: 'Empha', name: '振铃', animate: 'animate__tada' },
  { type: 'Empha', name: '摇晃', animate: 'animate__wobble' },
  { type: 'Empha', name: '抖动', animate: 'animate__jello' },
  { type: 'Empha', name: '心跳', animate: 'animate__heartBeat' },
  { type: 'Empha', name: '翻转', animate: 'animate__flip' },
  { type: 'Empha', name: '悬挂', animate: 'animate__hinge' },
  { type: 'Empha', name: '魔盒', animate: 'animate__jackInTheBox' },
  { type: 'Empha', name: '旋转', animate: 'animate__rotate360' },

  { type: 'In', name: '横向翻转', animate: 'animate__flipInX' },
  { type: 'Out', name: '横向翻出', animate: 'animate__flipOutX' },
  { type: 'In', name: '纵向翻转', animate: 'animate__flipInY' },
  { type: 'Out', name: '纵向翻出', animate: 'animate__flipOutY' },

  { type: 'In', name: '加速向左', animate: 'animate__lightSpeedInRight' },
  { type: 'Out', name: '加速向左', animate: 'animate__lightSpeedOutRight' },
  { type: 'In', name: '加速向右', animate: 'animate__lightSpeedInLeft' },
  { type: 'Out', name: '加速向右', animate: 'animate__lightSpeedOutLeft' },

  { type: 'In', name: '淡入', animate: 'animate__fadeIn' },
  { type: 'Out', name: '淡出', animate: 'animate__fadeOut' },
  { type: 'In', name: '向下淡入', animate: 'animate__fadeInDown' },
  { type: 'Out', name: '向下淡出', animate: 'animate__fadeOutDown' },
  { type: 'In', name: '(长)向下淡入', animate: 'animate__fadeInDownBig' },
  { type: 'Out', name: '(长)向下淡出', animate: 'animate__fadeOutDownBig' },
  { type: 'In', name: '向右淡入', animate: 'animate__fadeInLeft' },
  { type: 'Out', name: '向左淡出', animate: 'animate__fadeOutLeft' },
  { type: 'In', name: '(长)向右淡入', animate: 'animate__fadeInLeftBig' },
  { type: 'Out', name: '(长)向左淡出', animate: 'animate__fadeOutLeftBig' },
  { type: 'In', name: '向左淡入', animate: 'animate__fadeInRight' },
  { type: 'Out', name: '向右淡出', animate: 'animate__fadeOutRight' },
  { type: 'In', name: '(长)向左淡入', animate: 'animate__fadeInRightBig' },
  { type: 'Out', name: '(长)向右淡出', animate: 'animate__fadeOutRightBig' },
  { type: 'In', name: '向上淡入', animate: 'animate__fadeInUp' },
  { type: 'Out', name: '向上淡出', animate: 'animate__fadeOutUp' },
  { type: 'In', name: '(长)向上淡入', animate: 'animate__fadeInUpBig' },
  { type: 'Out', name: '(长)向上淡出', animate: 'animate__fadeOutUpBig' },
  { type: 'In', name: '淡入右下', animate: 'animate__fadeInTopLeft' },
  { type: 'Out', name: '淡出左上', animate: 'animate__fadeOutTopLeft' },
  { type: 'In', name: '淡入左下', animate: 'animate__fadeInTopRight' },
  { type: 'Out', name: '淡出右上', animate: 'animate__fadeOutTopRight' },
  { type: 'In', name: '淡入右上', animate: 'animate__fadeInBottomLeft' },
  { type: 'Out', name: '淡出右下', animate: 'animate__fadeOutBottomRight' },
  { type: 'In', name: '淡入左上', animate: 'animate__fadeInBottomRight' },
  { type: 'Out', name: '淡出左下', animate: 'animate__fadeOutBottomLeft' },

  { type: 'In', name: '向下滑入', animate: 'animate__slideInDown' },
  { type: 'Out', name: '向下滑出', animate: 'animate__slideOutDown' },
  { type: 'In', name: '向右滑入', animate: 'animate__slideInLeft' },
  { type: 'Out', name: '向左滑出', animate: 'animate__slideOutLeft' },
  { type: 'In', name: '向左滑入', animate: 'animate__slideInRight' },
  { type: 'Out', name: '向右滑出', animate: 'animate__slideOutRight' },
  { type: 'In', name: '向上滑入', animate: 'animate__slideInUp' },
  { type: 'Out', name: '向上滑出', animate: 'animate__slideOutUp' },

  { type: 'In', name: '滚入', animate: 'animate__rollIn' },
  { type: 'Out', name: '翻滚', animate: 'animate__rollOut' },
  { type: 'In', name: '放大进入', animate: 'animate__zoomIn' },
  { type: 'Out', name: '缩小离开', animate: 'animate__zoomOut' },
  { type: 'In', name: '向下放大', animate: 'animate__zoomInDown' },
  { type: 'Out', name: '缩小向下', animate: 'animate__zoomOutDown' },
  { type: 'In', name: '向右放大', animate: 'animate__zoomInLeft' },
  { type: 'Out', name: '缩小向左', animate: 'animate__zoomOutLeft' },
  { type: 'In', name: '向左放大', animate: 'animate__zoomInRight' },
  { type: 'Out', name: '缩小向右', animate: 'animate__zoomOutRight' },
  { type: 'In', name: '向上放大', animate: 'animate__zoomInUp' },
  { type: 'Out', name: '缩小向上', animate: 'animate__zoomOutUp' },

  { type: 'In', name: '向下后退', animate: 'animate__backInDown' },
  { type: 'Out', name: '后退向下', animate: 'animate__backOutDown' },
  { type: 'In', name: '向右后退', animate: 'animate__backInLeft' },
  { type: 'Out', name: '后退向左', animate: 'animate__backOutLeft' },
  { type: 'In', name: '向左后退', animate: 'animate__backInRight' },
  { type: 'Out', name: '后退向右', animate: 'animate__backOutRight' },
  { type: 'In', name: '向上后退', animate: 'animate__backInUp' },
  { type: 'Out', name: '后退向上', animate: 'animate__backOutUp' },

  { type: 'In', name: '弹跳进入', animate: 'animate__bounceIn' },
  { type: 'Out', name: '弹跳离开', animate: 'animate__bounceOut' },
  { type: 'In', name: '向下弹跳', animate: 'animate__bounceInDown' },
  { type: 'Out', name: '弹跳向下', animate: 'animate__bounceOutDown' },
  { type: 'In', name: '向右弹跳', animate: 'animate__bounceInLeft' },
  { type: 'Out', name: '弹跳向左', animate: 'animate__bounceOutLeft' },
  { type: 'In', name: '向左弹跳', animate: 'animate__bounceInRight' },
  { type: 'Out', name: '弹跳向右', animate: 'animate__bounceOutRight' },
  { type: 'In', name: '向上弹跳', animate: 'animate__bounceInUp' },
  { type: 'Out', name: '弹跳向上', animate: 'animate__bounceOutUp' },

  { type: 'In', name: '旋转进入', animate: 'animate__rotateIn' },
  { type: 'Out', name: '旋转离开', animate: 'animate__rotateOut' },
  { type: 'In', name: '旋入右下', animate: 'animate__rotateInDownLeft' },
  { type: 'Out', name: '旋出左下', animate: 'animate__rotateOutDownLeft' },
  { type: 'In', name: '旋入左下', animate: 'animate__rotateInDownRight' },
  { type: 'Out', name: '旋出右下', animate: 'animate__rotateOutDownRight' },
  { type: 'In', name: '旋入右上', animate: 'animate__rotateInUpLeft' },
  { type: 'Out', name: '旋出左上', animate: 'animate__rotateOutUpLeft' },
  { type: 'In', name: '旋入左上', animate: 'animate__rotateInUpRight' },
  { type: 'Out', name: '旋出右上', animate: 'animate__rotateOutUpRight' },
]
const animateObj = computed(() => {
  const obj: Record<string, IAnimate> = {}
  animateList.forEach((item) => {
    obj[item.animate] = item
  })
  return obj
})

// 扫描所有图标
const svgObj: Record<string, any> = {}
const svgList = import.meta.glob('./assets/*/*.svg', { query: '?raw', import: 'default', eager: true })
for (const [key, value] of Object.entries(svgList)) {
  const arr = key.split(/\/|\./)
  const name = `${arr[3]}_${arr[4]}`
  svgObj[name] = value
}
{
  // 检查是否所有的动画都有图标，防止内容提供的有问题
  const tmpArr: string[] = []
  animateList.forEach((item) => {
    const name = `${item.type}_${item.name}`
    if (!svgObj[name]) {
      console.error('缺少图标', item)
    }
    tmpArr.push(name)
  })
  // 哪个多了
  Object.keys(svgObj).forEach((item) => {
    if (!tmpArr.includes(item)) {
      console.error('多余图标', item)
    }
  })
}

/////
const animateTypes = ['In', 'Empha', 'Out']
const layerAnimate = ref<Record<string, IDesignLayerAnimate>>()

const defaultAnimateData: Record<string, IDesignLayerAnimate> = {
  In: { type: 'In', animate: '', duration: 1, delay: 0, repeat: 1 },
  Empha: { type: 'Empha', animate: '', duration: 1, delay: 0, repeat: 0 },
  Out: { type: 'Out', animate: '', duration: 1, delay: 0, repeat: 1 },
}
watch(
  () => layer.value?.animate,
  () => {
    if (!layer.value) return
    const result = cloneDeep(defaultAnimateData)
    const animate = layer.value.animate
    if (animate) {
      for (const item of animate) {
        const type = item.type
        if (animateTypes.includes(type)) {
          result[type] = Object.assign(result[type], cloneDeep(item))
        }
      }
    }
    layerAnimate.value = result
  },
  { immediate: true, deep: true },
)

watch(
  layerAnimate,
  () => {
    if (!layerAnimate.value) return
    // 设置回去
    const copyData = cloneDeep(layerAnimate.value)
    const tmp = Object.values(copyData).filter(i => i.animate)
    // 处理默认值
    tmp.forEach((item) => {
      if (item.duration === 1) {
        delete item.duration
      }
      if (item.delay === 0) {
        delete item.delay
      }
      if (item.repeat === 1) {
        delete item.repeat
      }
    })
    if (JSON.stringify(tmp) !== JSON.stringify(layer.value.animate)) {
      layer.value.animate = tmp
    }
  },
  { deep: true },
)

function getNameByType(type: string) {
  if (type === 'In') return '进入'
  if (type === 'Empha') return '强调'
  if (type === 'Out') return '退出'
  return ''
}

const selectType = ref<string>()
const listByType = computed(() => {
  const type = selectType.value
  if (!type) return animateList
  return animateList.filter(i => i.type === type)
})

function clearAnimate(value: IDesignLayerAnimate) {
  Object.assign(value, defaultAnimateData[value.type])
}

let timer: NodeJS.Timeout
let timer1: NodeJS.Timeout
function playAnimateFn(animate: string | IDesignLayerAnimate, isInfinite = false) {
  const emitFn = () => {
    const detail = {
      animate: '',
      duration: 1,
      delay: 0,
      repeat: 1,
    }
    if (typeof animate === 'string') {
      detail.animate = animate
    } else {
      detail.animate = animate.animate
      detail.duration = animate.duration || 1
      detail.delay = animate.delay || 0
      detail.repeat = animate.repeat || 1
    }
    designTemp.bus.emit(`${layer.value.uuid}:animate:preview`, { detail })
  }
  if (isInfinite) {
    clearInterval(timer)
    timer = setInterval(emitFn, 1600)
  }
  clearTimeout(timer1)
  timer1 = setTimeout(emitFn, 500)
}
function stopAnimateFn() {
  clearInterval(timer)
  clearTimeout(timer1)
}

function setAnimateFn(animate: string) {
  stopAnimateFn()
  const type = selectType.value
  if (!type) return
  if (layerAnimate.value && type) {
    layerAnimate.value[type].animate = animate

    selectType.value = undefined
  }
}
</script>

<template>
  <div class="animation-box">
    <ul v-if="layerAnimate">
      <li v-for="[type, item] of Object.entries(layerAnimate)" :key="type">
        <div class="title">
          <h3>{{ getNameByType(type) }}</h3>
          <span class="name-box" @click="selectType = type">{{ animateObj[item.animate]?.name || '无' }} ►</span>
          <el-tooltip content="播放动画" placement="top">
            <IconPlay class="cursor-pointer" @click="playAnimateFn(item)" />
          </el-tooltip>
          <el-tooltip content="删除动画" placement="top">
            <IconTrash class="cursor-pointer" @click="clearAnimate(item)" />
          </el-tooltip>
        </div>
        <div class="content" :class="{ disable: !item.animate }">
          <hi-input-number v-model="item.duration" :min="0" :readonly="!item.animate" prepend="时间" append="秒" />
          <hi-input-number v-model="item.delay" :min="0" :readonly="!item.animate" prepend="延迟" append="秒" />
          <hi-input-number v-if="type === 'Empha'" v-model="item.repeat" :min="1" :readonly="!item.animate" prepend="次数" append="次" />
        </div>
      </li>
    </ul>
    <div v-if="selectType" class="animate-select">
      <div class="title">
        <h3>动画选择</h3>
        <icon-ph-x-bold class="x-box" @click="selectType = undefined" />
      </div>
      <ul>
        <li
          v-for="item of listByType"
          :key="item.animate"
          @click="setAnimateFn(item.animate)"
          @mouseenter="playAnimateFn(item.animate, true)"
          @mouseleave="stopAnimateFn"
        >
          <div class="icon" v-html="svgObj[`${selectType}_${item.name}`]"></div>
          <div class="name">{{ item.name }}</div>
        </li>
      </ul>
    </div>
  </div>
</template>

<style scoped lang="scss">
.animation-box {
  background-color: #fff;
  height: 100%;
  position: relative;
}
.title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  background-color: #f1f5f9;
  border: 1px solid #f0f0f0;
  h3 {
    font-size: 14px;
    color: #333;
  }
  .name-box {
    background-color: #fff;
    padding: 0px 14px;
    border-radius: 20px;
    margin: 6px;
    line-height: 24px;
  }
}
.content {
  --gap: 16px;
  --line-count: 2;
  padding: 10px var(--gap);
  display: flex;
  flex-wrap: wrap;
  gap: var(--gap);
  background-color: #fff;

  > * {
    width: calc((100% - var(--gap) * var(--line-count)) / var(--line-count));
  }

  &.disable {
    opacity: 0.5;
    color: #999;
    cursor: not-allowed;
  }
}

.animate-select {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #fff;
  z-index: 10;
  padding-bottom: 10px;
  display: flex;
  flex-direction: column;

  .title {
    height: 32px;
    h3 {
      font-size: 12px;
    }
  }
  ul {
    flex: 1;
    display: flex;
    align-content: flex-start;
    flex-wrap: wrap;
    gap: 10px;
    padding: 12px;
    overflow: auto;
    li {
      cursor: pointer;
      width: 70px;
      transition: 0.2s;
      text-align: center;

      .icon {
        width: 70px;
        height: 70px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f5f7fa;
        color: #cedaeb;
        transition: 0.2s;
      }
      .name {
        white-space: nowrap;
        margin-top: 4px;
      }

      &:hover {
        .icon {
          background-color: var(--el-color-primary);
          color: #fff;
        }
      }
    }
  }
}
</style>
