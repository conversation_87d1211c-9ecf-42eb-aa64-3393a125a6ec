import { merge } from 'lodash-es'
import { BisTypes } from '../../'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './3d-rotating-cube-setting.vue'
import Comp from './3d-rotating-cube.vue'

// 类型
export const type = '3d-rotating-cube'

export const DEFAULT_DATA = {
  cubeInitPositionX: 0,
  cubeInitPositionY: 0,
  cubeInitPositionZ: 0,
  cubeRotationPositionX: 0,
  cubeRotationPositionY: 0.2,
  cubeRotationPositionZ: 0,
}

// 数据类型约束
export interface IDesign3dRotatingCube extends IDesignLayer {
  type: typeof type
  data: {
    cubeInitPositionX?: number
    cubeInitPositionY?: number
    cubeInitPositionZ?: number
    cubeRotationPositionX?: number
    cubeRotationPositionY?: number
    cubeRotationPositionZ?: number
  }
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.sportsIng,
    type,
    showInteractive: [InteractiveEnum.lotteryv3, InteractiveEnum.listlotteryv3, InteractiveEnum.piclotteryv3],
    showType: ['pcwall'],
    status: ['ing', 'ready'],
    name: '3D旋转立方体',
    Comp,
    CompSetting,
    thumbnail: new URL('./3d-rotating-cube.png', import.meta.url).href,
    defaultData(options): IDesign3dRotatingCube {
      return merge({
        uuid: layerUuid(),
        name: '3D旋转立方体',
        type,
        style: {
          width: '300px',
          height: '300px',
        },
        data: {},
        ratio: 1,
      }, options as IDesign3dRotatingCube)
    },
  })
}
