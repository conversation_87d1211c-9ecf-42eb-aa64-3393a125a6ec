import { useDesignData, useDesignTemp } from '../../__/design'

const designData = useDesignData()
const designTemp = useDesignTemp()

// 查询主题
export async function fetchTheme(themeId: string | number) {
  if (designTemp.isEdit || designTemp.isAdmin) {
    designTemp.fetchTheme()
  } else {
    if (!themeId) return
    const theme = await api.mobile.theme.readSimple({ where: { id: themeId } })
    designTemp.theme = theme
    if (theme) {
      designData.setState(JSON.parse(theme.mobContent))
    }
  }
}
