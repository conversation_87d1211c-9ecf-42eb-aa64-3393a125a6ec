import type { IDesignLayer, IDesignSetup, ILayerTypes } from '../../types'
import { merge } from 'lodash-es'
import { layerUuid } from '../../utils'
import CompSetting from './group-setting.vue'
import Comp from './group.vue'

export const type = 'group'
export const defaultOverflow = 'hidden'
export const defaultLayoutDirection = 'column'

export interface IDesignGroup extends IDesignLayer {
  type: 'group'
  layers: ILayerTypes[]
  // 布局, 有layout时子节点默认转为 relative
  layout?: {
    direction: 'column' | 'row'
    // 间隙
    gap: number
    // 默认模式是固定， 有值时为适应
    items: Record<string, {
      flex?: number
    }>
  }
  // 是否为模板
  // 1. 影响图层管理是否能管理子图层
  // 2. 设置中自动展开子图层的基础设置项
  templateId?: string
}

export function setup(app: IDesignSetup) {
  app.registry({
    type,
    base: true,
    name: '分组',
    Comp,
    CompSetting,
    defaultData(options) {
      return merge({
        uuid: layerUuid(),
        name: '分组',
        type,
        layers: [],
        style: {},
      }, options as IDesignGroup)
    },
  })
}
