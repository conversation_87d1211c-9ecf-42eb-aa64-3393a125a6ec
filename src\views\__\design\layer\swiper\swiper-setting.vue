<script setup lang="ts">
import type { IDesignSwiper, SpecialModule, SwiperItem } from './swiper'
import { openSelectMaterial, useDataAttr, useDesignTemp } from '../..'
import { actList } from '../../data/actList'
import { defaultDelay, defaultDirection, defaultShowPagination, specialModuleList } from './swiper'

const layer = defineModel<IDesignSwiper>('layer', { required: true })
const designTemp = useDesignTemp()
const showPaginationBlind = useDataAttr(layer.value, 'showPagination', defaultShowPagination)
const directionBind = useDataAttr(layer.value, 'direction', defaultDirection)
const delayBind = useDataAttr(layer.value, 'delay', defaultDelay)

// const itemIndex = ref(0)
// const nowAct = ref('')
// const nowActId = ref('')
const moduleActList = ref<{ [key: string]: any[] }>({
  ninegrids: [],
  wheelsurf: [],
  packetwall: [],
  mysterybox: [],
})
// const isOpenAct = ref(false)

const showRoundSelect = computed(() => (item: SwiperItem) => specialModuleList.includes(item.linkPage))

async function fetchAdminActList(module: string) {
  if (!designTemp.isAdmin) return
  const act = module as SpecialModule
  let list: any[] = await api.admin[act]?.list()
  if (['packetwall', 'mysterybox'].includes(module)) {
    // 过滤掉不需要手机端参与的轮次
    list = list.filter(item => item.gameMode === 'MOBILE')
  }
  if (['ninegrids', 'wheelsurf'].includes(module)) {
    // 过滤掉不需要手机端参与的轮次
    list = list.filter((item) => {
      return item.screenstatusLimit === 'N' || (item.screenstatusLimit === 'Y' && item.gameMode === 'SAMETIME')
    })
  }
  moduleActList.value[module] = list
}

async function onActChange(module: string, index: number) {
  // itemIndex.value = index
  // nowActId.value = ''
  layer.value.swiperData[index].linkPageRoundId = ''
  if (specialModuleList.includes(module)) {
    // 需要关联轮次。只有主办方才能关联，其他角色无法操作
    if (designTemp.isAdmin) {
      // nowAct.value = module
      // fetchAdminActList(module)
      // isOpenAct.value = true
    } else {
      ElMessageBox.alert('当前互动需要关联轮次才能使用，需要主办方身份操作。请更换其他互动', '提示', {
        type: 'warning',
      })
    }
  }
}
const actionList = computed(() => {
  return actList.filter(item => item.actType)
})
type IType = 'swiperData'
async function updateMaterialFn(name: IType | 'swiperData', index?: number) {
  const result = await openSelectMaterial('PIC')

  if (result) {
    if (name === 'swiperData' && index !== undefined) {
      const item = layer.value.swiperData[index]
      item.img = result
    }
  }
}
function remove(index: number) {
  const arr = layer.value.swiperData
  arr.splice(index, 1)
}
function add(index: number, value: SwiperItem) {
  const arr = layer.value.swiperData
  arr.splice(index + 1, 0, JSON.parse(JSON.stringify(value)))
}
// function queryRound() {
//   isOpenAct.value = false
//   layer.value.swiperData[itemIndex.value].linkPageRoundId = nowActId.value
// }

const roundList = computed(() => (item: SwiperItem) => {
  if (!item.linkPage) return []
  const act = moduleActList.value[item.linkPage]
  return act || []
})

onMounted(() => {
  specialModuleList.forEach((module) => {
    fetchAdminActList(module)
  })
})
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item flex-items-start!">
        <h3>轮播图</h3>
      </div>
      <div v-for="(item, index) in layer.swiperData" :key="index" class="setting-item flex-items-start!">
        <h3></h3>
        <div class="flex">
          <div>
            <div class="swiper-set-item flex-items-start!">
              <h3>图片</h3>
              <div class="relative mb-10 h-60 w-120 flex" @click="updateMaterialFn('swiperData', index)">
                <img :src="item.img" class="bgblank w-90 object-contain">
              </div>
              <div class="ml-6 w-20 pt-10">
                <icon-ph:minus-bold v-if="layer.swiperData.length > 1" @click.stop="remove(index)" />
                <icon-ph:plus-bold @click.stop="add(index, item)" />
              </div>
            </div>
            <div class="swiper-set-item flex flex-items-start!">
              <h3 class="line-height-30">触发</h3>
              <div class="w-150">
                <el-select v-model="item.linkType" class="mt-5 w-150">
                  <el-option
                    v-for="child in [{ label: '放大', value: 'big' }, { label: '页面跳转', value: 'page' }, { label: '外链', value: 'url' }]"
                    :key="child.value"
                    :label="child.label"
                    :value="child.value"
                  />
                </el-select>
                <template v-if="item.linkType === 'page'">
                  <div class="flex flex-a-c">
                    <p>互动：</p>
                    <el-select v-model="item.linkPage" class="mt-5 w-106" placeholder="请选择" @change="onActChange(item.linkPage, index)">
                      <el-option
                        v-for="child in actionList"
                        :key="child.actType"
                        :label="child.actName"
                        :value="child.actType"
                      >
                      </el-option>
                    </el-select>
                  </div>
                  <div v-if="showRoundSelect(item)" class="flex flex-a-c">
                    <p>轮次：</p>
                    <el-select v-model="item.linkPageRoundId" class="mt-5 w-106" placeholder="请选择">
                      <el-option
                        v-for="round in roundList(item)"
                        :key="round.id"
                        :label="round.title"
                        :value="round.id"
                      >
                      </el-option>
                    </el-select>
                  </div>
                  <p v-if="showRoundSelect(item) && !item.linkPageRoundId" class="color-red">此互动必须绑定轮次</p>
                </template>
                <el-input v-if="item.linkType === 'url'" v-model="item.linkUrl" class="mt-5 w-130px!" placeholder="请输入链接"></el-input>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="setting-item">
        <h3>分页器</h3>
        <el-switch v-model="showPaginationBlind" />
      </div>
      <div class="setting-item">
        <h3>方向</h3>
        <hi-tabs v-model="directionBind" :tabs="[{ label: '横向', value: 'horizontal' }, { label: '纵向', value: 'vertical' }]" />
      </div>
      <!-- <div class="setting-item">
        <h3>自动翻页</h3>
        <el-switch v-model="autoPlayBind" />
      </div> -->
      <div class="setting-item">
        <h3>自动翻页间隔</h3>
        <el-input-number v-model="delayBind" v-input-number :max="60" :min="1" controls-position="right" />
      </div>
    </div>
  </div>
  <!-- <el-dialog v-model="isOpenAct" title="选择轮次" width="400">
    <p>选择的互动需要关联轮次才能使用</p>
    <div class="my-20 flex items-center">
      <el-select v-model="nowActId" :placeholder="!moduleActList[nowAct]?.length ? '未创建轮次' : '请选择轮次'" class="w-200">
        <el-option v-if="!moduleActList[nowAct]?.length" label="未创建轮次" value=""> </el-option>
        <el-option v-for="item in moduleActList[nowAct]" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
    </div>
    <footer class="flex items-center justify-end">
      <el-button type="primary" :disabled="!nowActId" @click="queryRound">确 定</el-button>
    </footer>
  </el-dialog> -->
</template>

<style scoped lang="scss">
.swiper-set-item {
  display: flex;
}
</style>
