import type { Maybe<PERSON>rom<PERSON> } from 'rollup'
import type { CSSProperties, MaybeRef } from 'vue'
import type { IDesign3dRotatingCube } from './layer/3d-rotating-cube/3d-rotating-cube'
import type { IDesignAnswerIng } from './layer/answerrace/answerrace-ing'
import type { IDesignAnswerraceRank } from './layer/answerrace/answerrace-rank'
import type { IDesignAudience } from './layer/audience/audience'
import type { IDesignBarChart } from './layer/bar-chart/bar-chart'
import type { IDesignDiglettIng } from './layer/diglett-ing/diglett-ing'
import type { IDesignDown } from './layer/down/down'
import type { IDesignDynamicBackground } from './layer/dynamic-background/dynamic-background'
import type { IDesignEmpty } from './layer/empty/empty'
import type { IDesignFireIng } from './layer/fire-ing/fire-ing'
import type { IDesignGoldcoinIng } from './layer/goldcoin-ing/goldcoin-ing'
import type { IDesignGroup } from './layer/group/group'
import type { IDesignImage } from './layer/image/image'
import type { IDesignImage2 } from './layer/image/image2'
import type { IDesignImageRoll } from './layer/image/image-roll'
import type { IDesignImgVideo } from './layer/img-video/img-video'
import type { IDesignIngRank } from './layer/ingrank/ingrank'
import type { IDesignIngRank1 } from './layer/ingrank/ingrank1'
import type { IDesignIngRank2 } from './layer/ingrank/ingrank2'
import type { IDesignListlotteryIng } from './layer/listlottery-ing/listlottery-ing'
import type { IDesignListlotteryIng1 } from './layer/listlottery-ing/listlottery-ing1'
import type { IDesignListlotteryIng2 } from './layer/listlottery-ing/listlottery-ing2'
import type { IDesignListlotteryIng3 } from './layer/listlottery-ing/listlottery-ing3'
import type { IDesignListlotteryIng4 } from './layer/listlottery-ing/listlottery-ing4'
import type { IDesignLoopMovingImage } from './layer/loop-moving-image/loop-moving-image'
import type { IDesignLotteryIng } from './layer/lottery-ing/lottery-ing'
import type { IDesignLotteryIng1 } from './layer/lottery-ing/lottery-ing1'
import type { IDesignLotteryIng2 } from './layer/lottery-ing/lottery-ing2'
import type { IDesignLotteryIng3 } from './layer/lottery-ing/lottery-ing3'
import type { IDesignLotteryIng4 } from './layer/lottery-ing/lottery-ing4'
import type { IDesignLotteryIng5 } from './layer/lottery-ing/lottery-ing5'
import type { IDesignLotteryIng6 } from './layer/lottery-ing/lottery-ing6'
import type { IDesignLotteryIng7 } from './layer/lottery-ing/lottery-ing7'
import type { IDesignLotteryPrize } from './layer/lottery-prize/lottery-prize'
import type { IDesignLotteryAreadyAvatar } from './layer/lottery-ready-avatar/lottery-ready-avatar'
import type { IDesignMicrositeClassic } from './layer/microsite-classic/microsite-classic'
import type { IDesignMobileAd } from './layer/mobile-ad/mobile-ad'
import type { IDesignMobileAnswerraceIng } from './layer/mobile-answerrace/mobile-answerrace-ing'
import type { IDesignMobileAnswerraceReady } from './layer/mobile-answerrace/mobile-answerrace-ready'
import type { IDesignMobileAwardsSimple } from './layer/mobile-awards-simple/mobile-awards-simple'
import type { IDesignMobileAwards } from './layer/mobile-awards/mobile-awards'
import type { IDesignMobileChooseTeam } from './layer/mobile-choose-team/mobile-choose-team'
import type { IDesignMobileGameRule } from './layer/mobile-game-rule/mobile-game-rule'
import type { IDesignMobileGoldcoinIng } from './layer/mobile-goldcoin-ing/mobile-goldcoin-ing'
import type { IDesignMobileLotteryResult } from './layer/mobile-lottery-result/mobile-lottery-result'
import type { IDesignMobileLotteryv3 } from './layer/mobile-lotteryv3/mobile-lotteryv3'
import type { IDesignMobileMoneyIng } from './layer/mobile-money-ing/mobile-money-ing'
import type { IDesignMobilePerformance } from './layer/mobile-performance/mobile-performance'
import type { IDesignMobileRankinglist } from './layer/mobile-rankinglist/mobile-rankinglist'
import type { IDesignParticle } from './layer/particle/particle'
import type { IDesignPiclotteryIng } from './layer/piclottery-ing/piclottery-ing'
import type { IDesignPiclotteryIng1 } from './layer/piclottery-ing/piclottery-ing1'
import type { IDesignPiclotteryIng2 } from './layer/piclottery-ing/piclottery-ing2'
import type { IDesignPiclotteryIng3 } from './layer/piclottery-ing/piclottery-ing3'
import type { IDesignRankings } from './layer/rankings/rankings'
import type { IDesignRankings1 } from './layer/rankings/rankings1'
import type { IDesignRankings2 } from './layer/rankings/rankings2'
import type { IDesignRankings3 } from './layer/rankings/rankings3'
import type { IDesignRegedit } from './layer/regedit/regedit'
import type { IDesignRotate } from './layer/rotate/rotate'
import type { IDesignSeglotteryIng } from './layer/seglottery-ing/seglottery-ing'
import type { IDesignSeglotteryIng1 } from './layer/seglottery-ing/seglottery-ing1'
import type { IDesignSelect } from './layer/select/select'
import type { IDesignShakeIng } from './layer/shake-ing/shake-ing'
import type { IDesignShakeIng1 } from './layer/shake-ing/shake-ing1'
import type { IDesignShakeIng2 } from './layer/shake-ing/shake-ing2'
import type { IDesignShakeIng3 } from './layer/shake-ing/shake-ing3'
import type { IDesignShape } from './layer/shape/shape'
import type { IDesignSwiper } from './layer/swiper/swiper'
import type { IDesignIngTablerank } from './layer/tablerank/tablerank'
import type { IDesignText } from './layer/text/text'
import type { IDesignTextRich } from './layer/text/text-rich'
import type { IDesignVideo } from './layer/video/video'
import type { IDesignWallqrcode } from './layer/wallqrcode/wallqrcode'
import type { IDesignWinningList } from './layer/winning-list/winning-list'
import type { IDesignWinningList2 } from './layer/winning-list/winning-list2'
import type { IDesignWinningList3 } from './layer/winning-list/winning-list3'
import type { IDesignWinningList4 } from './layer/winning-list/winning-list4'
import type { IDesignWinningList5 } from './layer/winning-list/winning-list5'
// Import Layer Types Flag End

export type ValueRef<T> = Ref<T> | ComputedRef<T>

// 设计器模式，编辑、预览、导出
export enum ModeEnum {
  edit = 'edit',
  preview = 'preview',
  export = 'export',
}

export enum InteractiveEnum {
  shakev3 = 'shakev3',
  moneyv3 = 'moneyv3',
  goldcoinv3 = 'goldcoinv3',
  diglettv3 = 'diglettv3',
  firev3 = 'firev3',
  performancev3 = 'performancev3',
  microsite = 'microsite',
  lotteryv3 = 'lotteryv3',
  listlotteryv3 = 'listlotteryv3',
  piclotteryv3 = 'piclotteryv3',
  seglottery = 'seglottery',
  answerracev3 = 'answerracev3',
  // ...
}

export interface IDesignDisplayInfo {
  ready: boolean
  width: number
  height: number
  top: number
  left: number
  scale: number
  scaleX: number
  scaleY: number

  rootStyle: CSSProperties
  pageStyle: CSSProperties
}

// ILayerTypes start, generated by watch
export type ILayerTypes =
  | IDesignEmpty
  | IDesignText
  | IDesignTextRich
  | IDesignImage
  | IDesignImage2
  | IDesignImageRoll
  | IDesignGroup
  | IDesignShape
  | IDesignRegedit
  | IDesignDynamicBackground
  | IDesignDown
  | IDesignShakeIng
  | IDesignShakeIng1
  | IDesignShakeIng2
  | IDesignShakeIng3
  | IDesignAudience
  | IDesignRankings
  | IDesignRankings1
  | IDesignRankings2
  | IDesignRankings3
  | IDesignMobileGameRule
  | IDesignMobileChooseTeam
  | IDesignMobileRankinglist
  | IDesignMobileAwards
  | IDesignVideo
  | IDesignParticle
  | IDesignLoopMovingImage
  | IDesignIngRank
  | IDesignFireIng
  | IDesignGoldcoinIng
  | IDesignMobileGoldcoinIng
  | IDesignIngRank1
  | IDesignMobileMoneyIng
  | IDesignDiglettIng
  | IDesignRotate
  | IDesignMobileAwardsSimple
  | IDesignIngTablerank
  | IDesignMobilePerformance
  | IDesignBarChart
  | IDesignMicrositeClassic
  | IDesignSwiper
  | IDesignLotteryIng
  | IDesignLotteryIng1
  | IDesignLotteryIng2
  | IDesignLotteryIng3
  | IDesignLotteryIng4
  | IDesignSelect
  | IDesignWinningList
  | IDesignWinningList2
  | IDesignMobileLotteryv3
  | IDesignLotteryPrize
  | IDesignMobileAd
  | IDesignWallqrcode
  | IDesignLotteryAreadyAvatar
  | IDesignLotteryIng5
  | IDesignLotteryIng6
  | IDesign3dRotatingCube
  | IDesignLotteryIng7
  | IDesignListlotteryIng1
  | IDesignImgVideo
  | IDesignListlotteryIng
  | IDesignListlotteryIng2
  | IDesignListlotteryIng3
  | IDesignListlotteryIng4
  | IDesignWinningList3
  | IDesignPiclotteryIng2
  | IDesignPiclotteryIng
  | IDesignPiclotteryIng3
  | IDesignPiclotteryIng1
  | IDesignWinningList4
  | IDesignSeglotteryIng
  | IDesignSeglotteryIng1
  | IDesignWinningList5
  | IDesignAnswerIng
  | IDesignMobileAnswerraceIng
  | IDesignMobileAnswerraceReady
  | IDesignIngRank2
  | IDesignAnswerraceRank
  | IDesignMobileLotteryResult
// ILayerTypes end

// 事件中类型定义
export type IDesignLayerEventType = 'business' | 'link' | 'phone' | 'show-hide' | 'interact' | 'status'
export type IDesignLayerEvent = IDesignLayerEventLink | IDesignLayerEventPhone | IDesignLayerEventBusiness | IDesignLayerEventShowHide | IDesignLayerEventInteract | IDesignLayerEventStatus

export interface IDesignLayerEventStatus {
  event: 'click'
  type: 'status'
  value: string | null
}

export interface IDesignLayerEventInteract {
  event: 'click'
  type: 'interact'
  value: {
    module: string
    moduleId?: string
  } | null
}

export interface IDesignLayerEventLink {
  event: 'click'
  type: 'link'
  value: string | null
}
export interface IDesignLayerEventPhone {
  event: 'click'
  type: 'phone'
  value: string | null
}
export interface IDesignLayerEventBusiness {
  event: 'click'
  type: 'business'
  value: string | null
  keyboard?: string
}
export interface IDesignLayerEventShowHide {
  event: 'click'
  type: 'show-hide'
  value: {
    uuid: string
    action: 'show' | 'hide' | 'auto'
  }[] | null
}

/// ///// 事件中类型定义

export type IDesignLayerAnimateType = 'In' | 'Empha' | 'Out'
export interface IDesignLayerAnimate {
  type: 'In' | 'Empha' | 'Out'
  animate: string
  duration?: number
  delay?: number
  repeat?: number
  easing?: string // 后续可以增强为贝塞尔曲线运动
}

export interface IDsignLayerAnimateEvent extends Event {
  animate: string
}

export interface IDesignLayer {
  uuid: string // 唯一标识
  name: string // 名称
  lock?: boolean // 锁定
  show?: string[] // show字段不存在时表示显示，否则看当前状态是否在显示组中决定，空数组表示所有都不显示
  display?: string // 业务中控制显示状态，值为 layerData 中的某个响应式key值, 名称必须是驼峰，display后面的值为驼峰首字母大写
  spoVisible?: boolean // 超管配置普通用户是否可操作该图层
  type: string // 类型
  style: CSSProperties // 图层样式
  ratio?: number // 宽高比, 需要保持宽高比
  isPercent?: (0 | 1) // 标识图层尺寸和位置是否为百分比, window, height, left, top
  events?: IDesignLayerEvent[] // 事件
  animate?: IDesignLayerAnimate[] // 动画
  $parent?: IDesignLayer // 父级引用
  $dom?: HTMLElement // dom 引用
}

export type IObjectFit = 'none' | 'fill' | 'contain' | 'cover'
export interface IDesignOption {
  // 设计稿尺寸
  drafts: [number, number]
  // 显示区域
  display?: [number, number]
  objectFit?: IObjectFit
}
export interface IDesignTemp {
  // 选中的元素的uuid
  activeList: string[]
  inputing?: string
  scale: number
  clear: () => void
}

export interface IDesignMusic {
  state: string
  list: {
    uuid: string // 唯一标识
    url: string // 音乐地址
    loop?: boolean // 是否循环
    playCount?: number // 播放次数
    offset?: number // 偏移量 (毫秒)
    duration?: number // 持续时间（毫秒）
    delay?: number // 延迟时间毫秒
    rate?: number // 播放速度
    volume?: number // 音量
  }[]
}

export interface IDesignBackground {
  color?: string // 背景颜色 backgroundColor
  type?: 'background' | 'mask' // 模式 background | mask
  image?: string // 背景图片 maskImage
  showType?: 'repeat' | 'fill' | 'contain' // 显示模式 maskShowType
  size?: number // 背景大小  maskSize
  blur?: number // 模糊度 maskBlur
  bgColor?: string // 背景图的背景色
}

export type IDesignType = 'pcwall' | 'mobile'
export interface IDesignData {
  version: number // 设计器版本号，方便后续做升级兼容
  option: IDesignOption // 设计器配置，目前配置的为设计稿尺寸、显示区域
  style?: CSSProperties // 设计器最外层样式
  background?: IDesignBackground // 设计器背景
  status?: IDesignStateStatus[] // 设计器的状态列表，默认情况状态是各个逻辑中编写的，目前微站为当前页面中的状态
  layers: ILayerTypes[] // 设计器图层
  music?: IDesignMusic[] // 设计器相关音效
  $ready?: boolean // 设计器是否准备就绪
}
export interface IDesignComponentSetupOption {
  dev?: boolean // 开发模式只有在开发模式下才可用
  bisType?: string // 业务类型(用于分组)
  showType?: IDesignType[] // 暂定名，显示在手机端、大屏幕端，不传都显示 'mobile' | 'pcwall'
  showInteractive?: InteractiveEnum[] // 支持的互动类型
  thumbnail?: string // 图标
  type: string // 组件类型
  base?: boolean // 是否基础组件
  name: string // 组件名称
  status?: string[] // 可以显示的状态列表，空表示一直显示，否则根据状态显示
  Comp: Component // 渲染组件
  mountedCondition?: ComputedRef<boolean> // 渲染条件，前置条件一般是数据层面的依赖，部分数据又存在图层组件的依赖，为解决循环依赖的问题会进行图层组件的预渲染，所以带有前置条件的组件会进行两次渲染，第一次视觉上看不见，第二次才是真正的显示渲染
  CompSetting: Component // 组件设置
  defaultData: (options?: Partial<ILayerTypes>) => MaybePromise<ILayerTypes | undefined> // 组件默认数据，内部需要通过互动名称决定返回的数据
}

export interface IDesignTemplateSetupOption {
  label?: string // 模板名称
  thumbnail?: string // 图标
  defaultData: () => IDesignGroup // 组件默认数据，内部需要通过互动名称决定返回的数据
}

export interface IDesignSetup {
  interactive: string
  registry: (option: IDesignComponentSetupOption) => void
}

export interface IDesignTemplateSetup {
  interactive: string
  registry: (option: IDesignTemplateSetupOption) => void
}

// 活动状态
export interface IDesignStateStatus {
  label: string
  value: string
}
export interface IDesignStateEvent {
  eventId: string
  name?: string // name 不存在的为自定义事件，无需注册到触发事件列表中
  value: (data?: any) => MaybePromise<void>
  status?: MaybeRef<string[]> // 可以绑定的状态列表，空表示一直显示，否则根据状态显示
}

export interface IPage<T> {
  pageSize: number //
  pageIndex: number // 页码
  totalPages: number // 总页数
  dataList: T[] // 数据
  total: number // 总条数
}

// 素材类型
export type IDesignMaterialType = 'PIC' | 'VIDEO' | 'MUSIC' | 'SHAPE'
