<script setup lang="ts">
import type { IDesignBar<PERSON>hart } from './bar-chart'
import { useDataAttr, useDesignState } from '../..'
import { defalutLableLenth, defalutLableRotate, defaultBarColor, defaultBarColorsType, defaultDirection } from './bar-chart'

const layer = defineModel<IDesignBarChart>('layer', { required: true })
const designState = useDesignState()

const directionBind = useDataAttr(layer.value, 'direction', defaultDirection)
const barColorsTypeBlind = useDataAttr(layer.value, 'barColorsType', defaultBarColorsType)
const lableLenth = useDataAttr(layer.value, 'lableLenth', defalutLableLenth)
const lableRotate = useDataAttr(layer.value, 'lableRotate', defalutLableRotate)

const dataSourceOptions = computed(() => {
  return designState.getLayerData('listDataSourceList') || []
})

const blindData = computed(() => {
  const defaultData = { title: [], data: [] }
  if (!layer.value.dataSource) {
    return defaultData
  }
  const data = designState.getLayerData('allTableStatistics') || {}
  return data[layer.value.dataSource]
})
// 获取所有值为数字的key
const valueNumberKeys = computed(() => {
  const title = blindData.value?.title || []
  const _arr: any = []
  const nameArr: string[] = []
  title.forEach((item: { key: string, type: string, name: string }) => {
    if (item.type === 'NUM' || item.type === 'CUSTOM') {
      _arr.push(item.key)
      nameArr.push(item.name)
    }
  })
  return {
    keys: _arr,
    nameArr,
  }
})

watch(() => layer.value.dataSource, () => {
  const keys = valueNumberKeys.value.keys
  layer.value.barColorsAll = layer.value.barColorsAll?.slice(0, keys.length) || Array.from({ length: keys.length }).fill(defaultBarColor)
  for (let i = 0; i < keys.length; i++) {
    if (!layer.value.barColorsAll || !layer.value.barColorsAll[i]) {
      layer.value.barColorsAll[i] = defaultBarColor
    }
  }
}, {
  immediate: true,
})

function addOnesColor(index: number) {
  layer.value.barColorsOnes.splice(index + 1, 0, defaultBarColor)
}
function removeOnesColor(index: number) {
  layer.value.barColorsOnes.splice(index, 1)
}
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>数据来源</h3>
        <el-select v-model="layer.dataSource" placeholder="请选择" style="width: 140px">
          <el-option v-for="item in dataSourceOptions" :key="item.key" :label="item.name" :value="item.key" />
        </el-select>
      </div>
      <div class="setting-item">
        <h3>label颜色</h3>
        <hi-color v-model="layer.labelFontColor" />
      </div>
      <div class="setting-item">
        <h3>label大小</h3>
        <el-input-number v-model="layer.labelFontSize" v-input-number :max="150" :min="1" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>label旋转角度</h3>
        <el-input-number v-model="lableRotate" v-input-number :max="180" :min="0" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>label最多显示字数</h3>
        <el-input-number v-model="lableLenth" v-input-number :max="30" :min="1" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>value颜色</h3>
        <hi-color v-model="layer.valueFontColor" />
      </div>
      <div class="setting-item">
        <h3>value大小</h3>
        <el-input-number v-model="layer.valueFontSize" v-input-number :max="150" :min="1" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>方向</h3>
        <hi-tabs v-model="directionBind" :tabs="[{ label: '横向', value: 'horizontal' }, { label: '纵向', value: 'vertical' }]" />
      </div>
      <div class="setting-item">
        <h3>柱子宽度</h3>
        <el-input-number v-model="layer.barWidth" v-input-number :max="150" :min="1" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>柱子圆角</h3>
        <el-input-number v-model="layer.barRadius" v-input-number :max="200" :min="0" controls-position="right" />
      </div>
      <div class="setting-item">
        <h3>柱子颜色</h3>
        <hi-tabs v-model="barColorsTypeBlind" :tabs="[{ label: '统一', value: 'all' }, { label: '单项', value: 'ones' }]" />
      </div>
      <div v-if="barColorsTypeBlind === 'all'" class="setting-item">
        <h3></h3>
        <div>
          <div v-for="(color, index) in layer.barColorsAll" :key="index" class="setting-item">
            <h3>{{ valueNumberKeys.nameArr[index] }}</h3>
            <hi-color v-model="layer.barColorsAll[index]" />
          </div>
        </div>
      </div>
      <div v-if="barColorsTypeBlind === 'ones'" class="setting-item">
        <h3></h3>
        <div>
          <div v-for="(color, index) in layer.barColorsOnes" :key="index" class="setting-item">
            <h3>{{ `颜色${index + 1}` }}</h3>
            <hi-color v-model="layer.barColorsOnes[index]" />
            <div class="w-40 flex cursor-pointer flex-a-c justify-end!">
              <icon-ph:minus-bold v-if="layer.barColorsOnes.length > 1" @click.stop="removeOnesColor(index)" />
              <icon-ph:plus-bold @click.stop="addOnesColor(index)" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
ul.style-box {
  display: flex;
  font-size: 16px;

  li {
    padding: 4px 6px 0;
    margin: 1px;
    text-align: center;
    background-color: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    background-color: #f5f5f5;
    transition: 0.2s;
    position: relative;
    &.cur {
      background-color: var(--el-color-primary);
      color: #fff;
    }
    &:hover {
      background-color: var(--el-color-primary-dark-2);
      color: #fff;
      &::before,
      &::after {
        display: block;
      }
    }
    &::before,
    &::after {
      position: absolute;
      z-index: 100;
      left: 50%;
      transform: translateX(-50%);
    }
    &::before {
      // 三角
      content: '';
      display: none;
      top: -6px;
      border: 6px solid transparent;
      border-top-color: rgba(0, 0, 0, 0.8);
    }
    &::after {
      content: attr(data-tip);
      display: none;
      top: -34px;
      padding: 8px 10px;
      color: #fff;
      font-size: 12px;
      line-height: 12px;
      border-radius: 4px;
      white-space: nowrap;
      background-color: rgba(0, 0, 0, 0.8);
    }
  }
}
</style>
