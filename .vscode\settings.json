{
  "editor.tabSize": 2,
  "editor.renderWhitespace": "all",
  "editor.renderControlCharacters": true,
  "editor.rulers": [
    120,
    160
  ],
  "files.eol": "\n",
  "css.lint.unknownAtRules": "ignore",
  "scss.lint.unknownAtRules": "ignore",
  "typescript.tsdk": "node_modules/typescript/lib",
  // 这将确保 VSCode 不会建议 vue-router/auto 进行导入，因为这种写法已经不再建议，且可能被删除
  "typescript.preferences.autoImportFileExcludePatterns": [
    "vue-router/auto$"
  ],
  "workbench.iconTheme": "material-icon-theme",
  "editor.quickSuggestions": {
    "other": true,
    "comments": true,
    "strings": true
  },
  // Disable the default formatter, use eslint instead
  "prettier.enable": false,
  "editor.formatOnSave": true,
  // Auto fix
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "never"
  },
  // Silent the stylistic rules in you IDE, but still auto fix them
  "eslint.rules.customizations": [
    {
      "rule": "style/*",
      "severity": "off"
    },
    {
      "rule": "format/*",
      "severity": "off"
    },
    {
      "rule": "*-indent",
      "severity": "off"
    },
    {
      "rule": "*-spacing",
      "severity": "off"
    },
    {
      "rule": "*-spaces",
      "severity": "off"
    },
    {
      "rule": "*-order",
      "severity": "off"
    },
    {
      "rule": "*-dangle",
      "severity": "off"
    },
    {
      "rule": "*-newline",
      "severity": "off"
    },
    {
      "rule": "*quotes",
      "severity": "off"
    },
    {
      "rule": "*semi",
      "severity": "off"
    }
  ],
  // Enable eslint for all supported languages
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue",
    "html",
    "markdown",
    "json",
    "jsonc",
    "yaml",
    "toml",
    "xml",
    "gql",
    "graphql",
    "astro",
    "css",
    "less",
    "scss",
    "pcss",
    "postcss"
  ],
  "[vue]": {
    "editor.defaultFormatter": "vscode.typescript-language-features"
  },
  "todo-tree.general.tags": [
    "BUG",
    "HACK",
    "FIXME",
    "TODO",
    "XXX",
    "[ ]",
    "[x]",
    "todo"
  ],
  "common-intellisense.ui": [],
  "i18n-ally.localesPaths": [
    "src/locale/lang"
  ],
  "i18n-ally.keystyle": "nested",
  "i18n-ally.translate.engines": [
    "google",
    "google-cn"
  ],
  "i18n-ally.displayLanguage": "zh-CN",
  "i18n-ally.sourceLanguage": "zh-CN"
}
