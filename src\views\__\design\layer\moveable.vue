<script setup lang="ts">
import type { <PERSON><PERSON>rag, <PERSON><PERSON><PERSON><PERSON>, OnRotate, Ren<PERSON><PERSON> } from 'react-moveable/declaration/types'
import type { <PERSON>esign<PERSON>ayer, ILayerTypes } from '../types'
import type { IDesignGroup } from './group/group'
import Moveable, { makeAble } from 'vue3-moveable'
import { injectScale, isLockedInHierarchy, useDesignData, useDesignTemp } from '..'

const designData = useDesignData()
const designTemp = useDesignTemp()
const layer = defineModel<IDesignLayer>('layer', { required: true })
const scale = injectScale()

const snapDirections = { top: true, left: true, bottom: true, right: true, center: true, middle: true }
const elementSnapDirections = { top: true, left: true, bottom: true, right: true, center: true, middle: true }

const moveableRef = ref<Moveable>()

const isLock = computed(() => {
  // 预览模式直接禁用
  if (designTemp.isPreview) return true
  return isLockedInHierarchy(layer.value)
})

const isActived = computed(() => {
  return designTemp.activeList.includes(layer.value.uuid)
})

const inLayout = computed(() => {
  const { layout } = layer.value.$parent as IDesignGroup || {}
  return !!layout
})

const disableResize = computed(() => {
  const { layout } = layer.value.$parent as IDesignGroup || {}
  if (!layout) return false
  return !!layout.items[layer.value.uuid]
})

const disableAll = computed(() => {
  if (isLock.value) {
    return true
  }
  // 父级是模板时
  const parent = layer.value.$parent as IDesignGroup
  if (!parent) return false
  return parent.templateId
})

watch(
  () => [isActived.value, layer.value.style] as const,
  async ([v]) => {
    if (!v) return
    await nextTick()
    moveableRef.value?.updateRect()
  },
  { deep: true },
)

function roundToNDecimals(num: number, n: number = 2) {
  const factor = 10 ** n
  return Math.round(num * factor) / factor
}

function cssUnitToNumber(str: string | number | undefined) {
  return Number.parseFloat(`${str || 0}`)
}

function cssUnitToScaleNDecimalsNumber(str: string | number | undefined) {
  return roundToNDecimals(cssUnitToNumber(str) * scale.value)
}

const direction = computed<['left' | 'right', 'top' | 'bottom']>(() => {
  const { left, right, top, bottom } = layer.value.style
  return [right && !left ? 'right' : 'left', bottom && !top ? 'bottom' : 'top']
})
const lockDragDirection = computed(() => {
  const { layout } = layer.value.$parent as IDesignGroup || {}
  if (!layout) return []
  const { direction } = layout
  if (direction === 'row') {
    return ['left', 'right']
  } else {
    return ['top', 'bottom']
  }
})

const verticalGuidelines = computed(() => {
  const arr = []
  const [width] = designData.option.drafts
  for (let i = 0; i < Math.ceil(width / 100); i++) {
    arr.push(i * 100)
  }
  return arr
})
const horizontalGuidelines = computed(() => {
  const arr = []
  const [_w, height] = designData.option.drafts
  for (let i = 0; i < Math.ceil(height / 100); i++) {
    arr.push(i * 100)
  }
  return arr
})

const renderDirections = computed(() => {
  if (layer.value.type === 'text') {
    if (layer.value.style.writingMode) {
      return ['n', 's']
    } else {
      return ['e', 'w']
    }
  }
  return ['n', 'nw', 'ne', 's', 'se', 'sw', 'e', 'w']
})

// 防止出现尺寸太小时拖拽体验变差
const paddingStyle = computed(() => {
  const size = 20
  const width = Number.parseFloat(`${layer.value.style.width}`) * scale.value
  const height = Number.parseFloat(`${layer.value.style.height}`) * scale.value
  let x = 1
  let y = 1

  if (width < size) {
    x = (size - width) / 2
  }

  if (height < size) {
    y = (size - height) / 2
  }
  return { left: x, top: y, right: x, bottom: y }
})

const tmpMoveable = { width: 0, height: 0, top: 0, left: 0, right: 0, bottom: 0, rotation: 0, borderRadius: '', effective: false }
const moveableType = ref<'dragIng' | 'resizeIng' | 'rotateIng' | 'roundIng' | null>(null)
// drag ///////////////////////////////////////////
let currentLayers: ILayerTypes[] = []
function resetCurrentLayers() {
  // 优先通过designTemp.activeList获取图层(多选图层时可批量操作)，但实测发现moveable会早于赋值activeList的逻辑选中。当activeList获取不到可操作的图层时，默认选中当前图层
  const idList = [layer.value.uuid, ...designTemp.activeList]
  currentLayers = designData.getLayerByUuids(idList).filter(item => !isLockedInHierarchy(item))
}

const onDrag = {
  start() {
    moveableType.value = 'dragIng'
    Object.assign(tmpMoveable, { top: 0, left: 0, right: 0, bottom: 0, effective: false })
    resetCurrentLayers()
  },
  ing(e: OnDrag) {
    if (!isActived.value) return
    // 计算当前元素移动距离
    for (const dir of direction.value) {
      if (lockDragDirection.value.includes(dir)) continue
      const value = e[dir] || 0
      tmpMoveable[dir] = value - cssUnitToScaleNDecimalsNumber(layer.value.style[dir])
    }

    // 对所有选中的元素进行移动
    for (const layer of currentLayers) {
      const target = layer.$dom
      if (target) {
        for (const dir of direction.value) {
          if (lockDragDirection.value.includes(dir)) continue
          const styleDir = cssUnitToScaleNDecimalsNumber(layer.style[dir])
          const value = `${styleDir + tmpMoveable[dir]}px`
          target.style[dir] = value
          // todo 想要实现实时变化，但是会出现抖动
          // item.style[dir] = value === '0px' ? 0 : value
        }
      }
    }
    tmpMoveable.effective = true
  },
  end() {
    moveableType.value = null
    if (!tmpMoveable.effective) return
    for (const layer of currentLayers) {
      if (!layer.style) {
        layer.style = {}
      }
      for (const dir of direction.value) {
        if (lockDragDirection.value.includes(dir)) continue
        const styleDir = cssUnitToNumber(layer.style[dir])
        const value = `${((styleDir + tmpMoveable[dir] / scale.value)).toFixed(0)}px`
        layer.style[dir] = value === '0px' ? 0 : value
      }
    }
  },
}

// resize ///////////////////////////////////////////
const resizeTmp: Record<string, { w: number, h: number }> = {}
const onResize = {
  start() {
    moveableType.value = 'resizeIng'
    // 记录原始宽高
    Object.assign(tmpMoveable, { width: 0, height: 0, top: 0, left: 0, right: 0, bottom: 0, effective: false })
    resetCurrentLayers()
    for (const layer of currentLayers) {
      if (!layer.$dom) continue
      const rect = layer.$dom.getBoundingClientRect()
      resizeTmp[layer.uuid] = { w: rect.width, h: rect.height }
    }
  },
  ing(e: OnResize) {
    if (layer.value.style.width !== 'auto') {
      e.target.style.width = `${e.width}px`
    }
    if (layer.value.style.height !== 'auto') {
      e.target.style.height = `${e.height}px`
    }

    // 计算当前元素移动距离
    // TODO 按缩放比例计算更合适 ?
    tmpMoveable.width = e.width - cssUnitToScaleNDecimalsNumber(layer.value.style.width)
    tmpMoveable.height = e.height - cssUnitToScaleNDecimalsNumber(layer.value.style.height)

    for (const dir of direction.value) {
      if (lockDragDirection.value.includes(dir)) continue
      const value = e.drag[dir] || 0
      tmpMoveable[dir] = value - cssUnitToScaleNDecimalsNumber(layer.value.style[dir])
    }

    // 对所有选中的元素进行移动
    for (const layer of currentLayers) {
      const target = layer.$dom
      if (target) {
        if (layer.style.width !== 'auto') {
          target.style.width = `${(cssUnitToScaleNDecimalsNumber(layer.style.width) + tmpMoveable.width).toFixed(0)}px`
        }
        if (layer.style.height !== 'auto') {
          target.style.height = `${(cssUnitToScaleNDecimalsNumber(layer.style.height) + tmpMoveable.height).toFixed(0)}px`
        }
        for (const dir of direction.value) {
          if (lockDragDirection.value.includes(dir)) continue
          target.style[dir] = `${(cssUnitToScaleNDecimalsNumber(layer.style[dir]) + tmpMoveable[dir]).toFixed(0)}px`
        }
      }
    }

    tmpMoveable.effective = true
  },
  end() {
    moveableType.value = null
    if (!tmpMoveable.effective) return
    // 获取最终的宽高，计算出百分比
    for (const layer of currentLayers) {
      if (!layer.style) {
        layer.style = {}
      }
      if (layer.style.width !== 'auto') {
        const value = `${(cssUnitToNumber(layer.style.width) + tmpMoveable.width / scale.value).toFixed(0)}px`
        layer.style.width = value === '0px' ? 0 : value
      }
      if (layer.style.height !== 'auto') {
        const value = `${(cssUnitToNumber(layer.style.height) + tmpMoveable.height / scale.value).toFixed(0)}px`
        layer.style.height = value === '0px' ? 0 : value
      }
      for (const dir of direction.value) {
        if (lockDragDirection.value.includes(dir)) continue
        const value = `${(cssUnitToNumber(layer.style[dir]) + tmpMoveable[dir] / scale.value).toFixed(0)}px`
        layer.style[dir] = value === '0px' ? 0 : value
      }

      // 如果组的尺寸变换，组中的百分比的图层要重新计算尺寸和位置
      if (!layer.$dom) continue
      const rect = layer.$dom.getBoundingClientRect()
      const old = resizeTmp[layer.uuid]
      if (old) {
        const sw = rect.width / old.w
        const sh = rect.height / old.h
        // 遍历线下递归进行尺寸还原
        const walk = (layers: IDesignLayer[]) => {
          for (const layer of layers) {
            const { isPercent = 0 } = layer
            if (isPercent) {
              if (layer.style.width) {
                layer.style.width = `${(Number.parseFloat(`${layer.style.width}`) * sw).toFixed()}px`
              }
              if (layer.style.left) {
                layer.style.left = `${(Number.parseFloat(`${layer.style.left}`) * sw).toFixed()}px`
              }
              if (layer.style.height) {
                layer.style.height = `${(Number.parseFloat(`${layer.style.height}`) * sh).toFixed()}px`
              }
              if (layer.style.top) {
                layer.style.top = `${(Number.parseFloat(`${layer.style.top}`) * sh).toFixed()}px`
              }
            }
            if (layer.type === 'group') {
              walk((layer as IDesignGroup).layers)
            }
          }
        }
        if (layer.type === 'group') {
          walk(layer.layers)
        }
      }
    }
  },
}
// rotate ///////////////////////////////////////////
// 转成0-360度的结果
function normalRotation(num: number) {
  if (!num) return 0
  const rotate = Number(num) % 360
  return rotate < 0 ? 360 + rotate : rotate
}
const onRotate = {
  start() {
    moveableType.value = 'rotateIng'
    tmpMoveable.rotation = 0
    tmpMoveable.effective = false
    resetCurrentLayers()
  },
  ing(e: OnRotate) {
    const changeRotate = e.rotation

    // NOTE 预期应该是基于当前图层新角度-原始角度=实际旋转角度。由于 Moveable 不识别rotateZ，每次旋转它都是根据rotate(?deg)计算的，这里直接当做 e.rotation 是实际旋转角度。

    tmpMoveable.rotation = changeRotate

    for (const layer of currentLayers) {
      const target = layer.$dom
      if (target) {
        let oldTransform = layer.style.transform || ''
        if (oldTransform.includes('rotate(') || oldTransform.includes('rotateZ(')) {
          const oldRotation = oldTransform.match(/(rotate|rotateZ)\((-?\d+)deg\)/i)?.[2]
          const rotation = normalRotation(Number(oldRotation) + changeRotate)
          oldTransform = oldTransform.replace(/(rotate|rotateZ)\(-?\d+deg\)/, `rotateZ(${rotation}deg)`)
        } else {
          oldTransform += ` rotateZ(${normalRotation(changeRotate)}deg)`
        }
        target.style.transform = oldTransform.trim()
      }
    }

    tmpMoveable.effective = true
  },
  end() {
    moveableType.value = null
    if (!tmpMoveable.effective) return
    const changeRotate = normalRotation(tmpMoveable.rotation)
    for (const layer of currentLayers) {
      if (!layer.style) {
        layer.style = {}
      }
      let oldTransform = layer.style.transform || ''
      if (oldTransform.includes('rotate(') || oldTransform.includes('rotateZ(')) {
        const oldRotation = oldTransform.match(/(rotate|rotateZ)\((-?\d+)deg\)/i)?.[2]
        const rotation = normalRotation(Number(oldRotation) + changeRotate)
        oldTransform = oldTransform.replace(/(rotate|rotateZ)\(-?\d+deg\)/, `rotateZ(${rotation}deg)`)
      } else {
        oldTransform += ` rotateZ(${normalRotation(changeRotate)}deg)`
      }

      oldTransform = oldTransform.replace(/(rotate|rotateZ)\(0deg\)/, '')

      const resultTransform = oldTransform.trim()
      if (resultTransform) {
        layer.style.transform = resultTransform
      } else {
        delete layer.style.transform
      }
    }
  },
}

const DimensionViewable = makeAble('dimensionViewable', {
  render(moveable: any, React: Renderer) {
    const { rotatable } = moveable.props
    if (!rotatable) return null

    const rect = moveable.getRect()

    // 显示信息
    const info = []
    switch (moveableType.value) {
      case 'dragIng':
        {
          const { style } = moveable.state
          const arr = []

          for (const dir of direction.value) {
            if (lockDragDirection.value.includes(dir)) continue
            const label = { left: '左', right: '右', top: '上', bottom: '下' }[dir]
            arr.push(`${label}: ${(Number.parseFloat(style[dir]) / scale.value).toFixed(0)}`)
          }

          info.push(arr.join(' '))
        }
        break
      case 'resizeIng':
        info.push(`宽: ${Math.round(rect.width / scale.value)}  高: ${Math.round(rect.height / scale.value)}`)
        break
      case 'rotateIng':
        info.push(`${Math.round(rect.rotation)}°`)
        break
    }
    if (info.length === 0) return null

    return React.createElement('div', {
      key: 'dimension-viewer',
      className: 'moveable-dimension',
      style: {
        left: `${rect.width}px`,
        top: `${rect.height}px`,
      },
    }, info)
  },
})

function updateRect() {
  moveableRef.value?.updateRect()
}
onMounted(() => {
  designTemp.bus.on(`${layer.value.uuid}:moveable:updateRect`, updateRect)
})
onBeforeUnmount(() => {
  designTemp.bus.off(`${layer.value.uuid}:moveable:updateRect`, updateRect)
})
</script>

<!-- 控制resize句柄 :render-directions="['e', 'w']" -->
<template>
  <Moveable
    v-if="layer.$dom && !disableAll"
    ref="moveableRef"
    :style="{ '--moveable-color': isActived ? 'var(--el-color-primary)' : 'transparent' }"
    :target="layer.$dom"

    :ables="[DimensionViewable]"
    :props="{ dimensionViewable: true }"

    :draggable="!isLock"
    :throttle-drag="1"
    :rotatable="isActived && !isLock && !inLayout"
    :throttle-rotate="1"
    :resizable="isActived && !isLock && !disableResize"
    :throttle-resize="1"
    :origin="false"
    :round-padding="15"
    :render-directions="renderDirections"

    :padding="paddingStyle"
    :keep-ratio="!!layer.ratio"

    is-display-shadow-round-controls="horizontal"

    :snappable="true"
    :snap-directions="snapDirections"
    :element-snap-directions="elementSnapDirections"
    :element-guidelines="[{ element: '.guideline' }]"
    :vertical-guidelines="verticalGuidelines"
    :horizontal-guidelines="horizontalGuidelines"

    :data-uuid="layer.uuid"
    @drag-start="onDrag.start"
    @drag="onDrag.ing"
    @drag-end="onDrag.end"
    @resize-start="onResize.start"
    @resize="onResize.ing"
    @resize-end="onResize.end"
    @rotate-start="onRotate.start"
    @rotate="onRotate.ing"
    @rotate-end="onRotate.end"
  />
</template>

<style scoped lang="scss">
</style>
