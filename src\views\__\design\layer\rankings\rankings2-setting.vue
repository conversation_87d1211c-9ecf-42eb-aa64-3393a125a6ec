<script setup lang="ts">
// const layer = defineModel<IDesignRankings2>('layer', { required: true })
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>起始名次</h3>
        <el-input-number v-input-number controls-position="right" :step="1" />
      </div>
      <div class="setting-item">
        <span>尺寸</span>
        <el-slider :min="60" :max="200" :step="1" class="ml-20 flex-1" />
        <span class="w-30 text-right"></span>
      </div>
      <div class="setting-item">
        <span>间隔</span>
        <el-slider :min="0" :max="50" :step="1" class="ml-20 flex-1" />
        <span class="w-30 text-right"></span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
