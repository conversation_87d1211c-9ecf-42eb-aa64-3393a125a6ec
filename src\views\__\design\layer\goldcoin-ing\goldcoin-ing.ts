import { BisTypes, useDesignData } from '../..'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './goldcoin-ing-setting.vue'
import Comp from './goldcoin-ing.vue'

// 类型
export const type = 'goldcoin-ing'

export const defaultAvatarX = 50
export const defaultAvatarY = 50
export const defaultAvatarSize = 67
export const defaultColor = '#ff6b6b'
export const defaultBorderColor = '#ff6b6b'
export const defaultBackground = '#FFF5E6'
export const defaultAvatarZIndex = 1
export const defaultBgImg = new URL('./assets/bg.png', import.meta.url).href
export const defaultBgImgWidth = 100
export const defaultBgImgHeight = 100
export const defaultBgImgX = 0
export const defaultBgImgY = 0
export const defaultHeightPadding = 0 // 最大高度的预留高度

export interface IDesignGoldcoinIng extends IDesignLayer {
  type: typeof type
  drops?: string
  trackTop?: string
  track?: string
  trackBottom?: string
  decoration?: string
  avatarX?: number
  avatarY?: number
  avatarSize?: number
  color?: string
  borderColor?: string
  background?: string
  avatarZIndex?: number
  bgImg?: string
  bgImgWidth?: number
  bgImgHeight?: number
  bgImgX?: number
  bgImgY?: number
  heightPadding?: number
  data: {
    name: string
    avatar: string
    score: number
    progress: number
  }[]
  svgColor?: string
}

export function setup(app: IDesignSetup) {
  let status: string[]
  switch (app.interactive) {
    case InteractiveEnum.shakev3:
      status = ['ing']
      break
    default:
      status = ['ing']
      break
  }

  app.registry({
    showType: ['pcwall'],
    bisType: BisTypes.sportsIng,
    showInteractive: [InteractiveEnum.shakev3, InteractiveEnum.moneyv3, InteractiveEnum.goldcoinv3, InteractiveEnum.diglettv3, InteractiveEnum.firev3],
    type,
    name: '进行中排行',
    thumbnail: new URL('./goldcoin-ing.png', import.meta.url).href,
    status,
    Comp,
    CompSetting,
    defaultData() {
      const designData = useDesignData()
      const [width, height] = designData.option.drafts

      return {
        uuid: layerUuid(),
        name: '进行中排行',
        type,
        decoration: new URL(`./assets/head-bg.png`, import.meta.url).href,
        trackTop: new URL(`./assets/gold-top.png`, import.meta.url).href,
        track: new URL(`./assets/gold-bg.png`, import.meta.url).href,
        trackBottom: new URL(`./assets/gold-bt.png`, import.meta.url).href,
        data: [],
        style: {
          width: `${width}px`,
          height: `${height}px`,
          top: 0,
          left: 0,
        },
      }
    },
  })
}
