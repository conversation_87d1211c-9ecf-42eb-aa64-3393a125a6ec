import type { IMobileAwardsAwards, IMobileAwardsRecord } from 'design/layer/mobile-awards/mobile-awards.vue'
import type { ITeamInfo } from 'design/layer/mobile-choose-team/mobile-choose-team.vue'
import type { IRankItem } from 'design/layer/mobile-rankinglist/mobile-rankinglist.vue'
import { timer } from '@/utils'
import { useDesignState, useDesignTemp } from 'design/index'
import { closeDialog } from 'vant'
import { useImData } from '~/src/hooks/useImData'
import { useBindShake } from '../../hooks/useBindShake'
import { useLimit } from '../../hooks/useLimit'
import { useScore } from '../../hooks/useScore'
import { useStatus } from '../../hooks/useStatus'
import { useMobileStore } from '../../stores/useMobileStore'
import { useParticipantStore } from '../../stores/useParticipantStore'
import { fetchTheme } from '../../utils'
import { mockAwards, mockFinishRankings, mockRecord, mockRegedit, mockTeamList } from '../../utils/mock'
import { mockConfig, mockRound } from './mock'
import 'vant/lib/dialog/style'

// 类型
interface IConfig {
  shakev3Id: number
}
interface IRound {
  id: number
  themeId: number
  title: string
  time: number
  startTime?: number
  endTime?: number
  gameType?: 'PERSONAL' | 'TEAM'
  teamJoinType?: 'AUTO' | 'MANUAL'
}
interface IRegedit {
  name: string
  avatar: string
  teamId?: number
}

export function useMobileShakev3() {
  const timestamp = useTimestamp()
  // 逻辑 /////////////////////
  const designTemp = useDesignTemp()
  const designState = useDesignState()

  designTemp.showType = 'mobile'

  const mobileStore = useMobileStore()
  const { wall, featureConfig, isShowAd, isWedding } = storeToRefs(mobileStore)

  const participantStore = useParticipantStore()

  // 摇一摇配置
  const config = ref<IConfig>()
  // 轮次配置
  const round = ref<IRound>()
  const roundId = computed(() => round.value?.id || '')
  // 团队列表
  const teamList = ref<ITeamInfo[]>()
  // 报名信息
  const regedit = ref<IRegedit | null>()
  // 结果排行榜
  const finishRankings = ref<{ teamId?: number, count: number, wxUserId?: number, id: number }[]>()
  // 奖品信息
  const awards = ref<IMobileAwardsAwards>()
  const awardCount = ref()
  // 名次信息
  const record = ref<IMobileAwardsRecord>()

  const isTeamMode = computed(() => {
    return designState.gameMode === '$游戏模式-团队$' || round.value?.gameType === 'TEAM'
  })
  const isPersonalMode = computed(() => {
    if (isTeamMode.value) return false
    return designState.gameMode === '$游戏模式-单人$' || round.value?.gameType === 'PERSONAL'
  })

  const getName = (wxUserId: number) => {
    if (mobileStore.isSignName) {
      return participantStore.getSignName(wxUserId)
    }
    return participantStore.getWxName(wxUserId)
  }

  // 活动状态
  const status = useStatus({
    round,
    regedit,
  })

  // 活动限制
  const { limit, dealLimit } = useLimit()

  let regeditCount = 0
  async function regeditFn(teamId = 0) {
    // 报名 & 更换团队
    if (designTemp.isEdit) {
      await timer(200)
      regedit.value = mockRegedit(teamId)
      return
    }
    const shakev3Id = roundId.value
    if (!shakev3Id) return
    if (status.value === 'finish') return
    const res = await api.mobile.shakev3.readRegedit({ where: { shakev3Id } })
    if (res) {
      regedit.value = res
    } else {
      // 个人赛或团队随机加入直接报名
      if (!isTeamMode.value || round.value?.teamJoinType === 'AUTO') {
        if (regeditCount > 5) return
        if (regeditCount) {
          await timer(1500)
        }
        regeditCount++
        await api.mobile.shakev3.insertRegedit({ shakev3Id, teamId: 0 })
        regeditFn()
      } else {
        regedit.value = null
      }
    }
  }

  async function joinTeam(teamId: number) {
    if (!teamId) return
    if (designTemp.isEdit) return
    await api.mobile.shakev3.insertRegedit({ shakev3Id: roundId.value, teamId })
    regeditFn()
  }

  async function leaveTeam() {
    if (designTemp.isEdit) return
    await api.mobile.shakev3.quitRegedit({ shakev3Id: roundId.value })
    regeditFn()
  }

  async function fetchTeamList() {
    if (designTemp.isEdit) {
      await timer(200)
      // 如果不需要团队信息直接返回即可
      teamList.value = mockTeamList()
      return
    }
    if (!isTeamMode.value) {
      teamList.value = undefined
      return
    }
    if (!roundId.value) {
      return
    }
    teamList.value = await api.mobile.shakev3.listTeam({ where: { shakev3Id: roundId.value } })
  }

  // 结果上报
  const { score, scoreCount, startReport, stopReport, getScore } = useScore({
    reportFn: async (oldScore) => {
      if (designTemp.isEdit || !round.value) return Math.max(0, oldScore + scoreCount.value)
      const data = await api.mobile.shakev3.reportRecord({ shakev3Id: round.value.id, count: oldScore })
      dealLimit(data)
      // 返回值需要是最终的分数
      return data.score
    },
    // getCurScoreFn: async () => {
    // },
  })

  // 绑定摇动次数
  const nullFn = () => { }
  const { start, stop } = designTemp.isEdit ? { start: nullFn, stop: nullFn } : useBindShake(score)

  watch(limit, (v) => {
    if (v) {
      stop()
    }
  })

  let errorCount = 0
  async function fetchRecord() {
    if (designTemp.isEdit || !round.value) {
      await timer(200)
      record.value = mockRecord()
      // 查询奖品信息
      await fetchAwards()
    } else {
      try {
        const [tempRecord, awardsrecord] = await Promise.all([
          api.mobile.shakev3.readRecord({ where: { shakev3Id: round.value.id } }),
          api.mobile.awards.recordRead({ where: { module: 'shakev3', moduleId: round.value.id } }),
        ])
        if (awardsrecord) {
          tempRecord.awardsId = awardsrecord.awardsId
          tempRecord.redpackAmount = awardsrecord.redpackAmount
          tempRecord.status = awardsrecord.status
        }
        record.value = tempRecord || { count: 0, ranking: 0, redpackAmount: 0 }
        if (record.value?.awardsId) {
          await fetchAwards(record.value?.awardsId)
        } else {
          awards.value = undefined
          awardCount.value = await api.mobile.awards.count({ where: { module: 'shakev3', moduleId: round.value.id } }) || 0
        }
      } catch {
        // 为了避免活动状态是“活动处理中”不显示结果弹窗，多查询几次
        errorCount++
        if (errorCount < 5) {
          setTimeout(() => {
            fetchRecord()
          }, 1000)
        }
      }
    }
  }

  async function fetchAwards(awardsId = 0) {
    if (designTemp.isEdit) {
      await timer(200)
      awards.value = mockAwards()
    }
    if (!awardsId) {
      awards.value = undefined
      return
    }
    awards.value = await api.mobile.awards.read({ where: { id: awardsId } })
  }

  async function fetchFinishRankings() {
    // 结果排行榜
    if (designTemp.isEdit) {
      await timer(200)
      finishRankings.value = mockFinishRankings()
      return
    }
    if (isTeamMode.value && !teamList.value?.length) {
      await fetchTeamList()
    }
    finishRankings.value = await api.mobile.shakev3.ranking({
      where: {
        shakev3Id: roundId.value,
        showNum: 10,
        state: 'FINISH',
      },
    })
    if (!isTeamMode.value) {
      await participantStore.relationParticipant(finishRankings.value as { wxUserId: number }[])
    }
  }

  const finishTeamListAllRankings = computed(() => {
    if (!finishRankings.value) return finishRankings.value

    const list: IRankItem[] = []
    for (const { teamId, count: ranking, wxUserId } of (finishRankings.value || [])) {
      // 团队模式
      if (round.value?.gameType === 'TEAM') {
        if (!teamId) continue
        const team = teamList.value?.find(item => Number(item.id) === teamId)
        if (!team) continue
        const nickName = team.teamName
        const avatarUrl = team.teamHeadImg
        list.push({ nickName, avatarUrl, ranking, id: teamId })
      } else {
        if (!wxUserId) continue
        const nickName = getName(wxUserId)
        const avatarUrl = participantStore.getAvatar(wxUserId)
        list.push({ nickName, avatarUrl, ranking, id: wxUserId })
      }
    }
    // 排序
    list.sort((a, b) => b.ranking - a.ranking)
    return list
  })

  // 主要作用是将数据暴露给图层组件
  designState
    .setStatusList([
      { label: '准备中', value: 'ready' },
      { label: '倒计时', value: '321' },
      { label: '进行中', value: 'ing' },
      { label: '排行榜', value: 'finish' },
    ])
    .setLayerData({
      '#主题名称#': computed(() => designTemp.theme?.name),
      '#轮次标题#': computed(() => round.value?.title),
      '$游戏模式-单人$': computed(() => isPersonalMode.value),
      '$游戏模式-团队$': computed(() => isTeamMode.value),
      '$进行中显示3秒后隐藏$': computed(() => {
        if (designState.status === 'ing') {
          if (!round.value?.startTime) {
            return false
          }
          const offsetStart = timestamp.value - round.value.startTime
          if (offsetStart < 3000) {
            return true
          }
        }
        return false
      }),
      '#当前成绩#': computed(() => {
        return `${scoreCount.value}`
      }),
      'isShowAd': isShowAd,
      'isWedding': isWedding,
      // 团队信息
      'isTeam': computed(() => isTeamMode.value),
      'teamJoinType': computed(() => round.value?.teamJoinType),
      teamList,
      // 报名信息
      regedit,
      // 排行榜
      'finishRankings': computed(() => finishTeamListAllRankings.value),
      'ranking': computed(() => record.value?.teamRanking || record.value?.ranking),
      // 成绩信息
      record,
      // 奖品信息
      awards,
      'awardCount': computed(() => awardCount.value),
      // 轮次相关配置
      'downFlagTime': computed(() => {
        if (designState.status === '321') {
          return round.value?.startTime
        } else if (designState.status === 'ing') {
          return round.value?.endTime
        } else {
          return 0
        }
      }),
    })
    // 图层组件事件
    .setLayerEventList([
      {
        eventId: 'back-home',
        name: '返回首页',
        value() {
          window.parent.postMessage({ type: 'iframe:backHome' }, '*')
        },
      },
      // 报名或者更换团队
      {
        eventId: 'join-team',
        value(teamId) {
          joinTeam(teamId)
        },
      },
      {
        eventId: 'leave-team',
        value() {
          leaveTeam()
        },
      },
      // 查询排行榜
      { eventId: 'fetchRankings', value: fetchFinishRankings },
      {
        eventId: 'clickAd',
        value(data) {
          console.log('点击广告', data)
          window.parent.postMessage({ type: 'iframe:clickAd', data }, '*')
        },
      },
      {
        eventId: 'SHOW_AD',
        value(data) {
          console.log('SHOW_AD', data)
          window.parent.postMessage({ type: 'iframe:SHOW_AD', data }, '*')
        },
      },
      {
        eventId: 'clickAwards',
        name: '中奖记录',
        value() {
          console.log('查看中奖记录')
          window.parent.postMessage({ type: 'iframe:clickAwards' }, '*')
        },
      },
    ])

  // 变化监控
  watch(
    () => [status.value, roundId.value || 0] as const,
    async ([v], old) => {
      if (!round.value) return
      const [oStatus, oRoundId] = old || []

      if (v === 'ready') {
        fetchTeamList()
        regeditFn()
        // 重置
        limit.value = false
        errorCount = 0
      }
      if (v === '321') {
        if (designTemp.isEdit) {
          round.value.startTime = timestamp.value + 3000
          round.value.endTime = timestamp.value + 3000 + 30 * 1000
        }
      }

      if (!designTemp.isEdit && v === 'ing' && !['321'].includes(`${oStatus}`)) {
        const data = await api.mobile.wxtag.getLimitInfo({
          where: { module: 'shakev3', actId: oRoundId },
        })
        if (data && data.limitLevel && !data.state) {
          data.state = 'limit'
        }
        dealLimit(data)
      }

      // 开始监控摇一摇并进行上报
      if (v === 'ing') {
        if (designTemp.isEdit) {
          round.value.startTime = timestamp.value - 3000
          round.value.endTime = timestamp.value - 3000 + 30 * 1000
        }
        score.value = 0
        scoreCount.value = 0
        await getScore()
        start()
        startReport()
      } else {
        stop()
        stopReport(v === 'finish')
      }
      if (v === 'finish') {
        // 查询最终结果
        if (limit.value) {
          closeDialog()
        }
        await fetchRecord()
      }
      designState.setStatus(status.value)
    },
    { immediate: true, deep: true },
  )

  watch(() => round.value?.themeId, themeId => fetchTheme(themeId || ''))
  watch(() => roundId.value, () => {
    if (designTemp.isEdit) {
      return
    }
    // 重置数据
    designTemp.renderKey = Date.now()
    record.value = undefined
    finishRankings.value = undefined
    teamList.value = undefined
    awards.value = undefined
    awardCount.value = undefined
    regedit.value = undefined
    regeditCount = 0
  })
  ///////////////////////

  tryOnMounted(() => {
    if (designTemp.isEdit) {
      // 设计模式mock数据
      config.value = mockConfig()
      round.value = mockRound()
    }
  })

  // 数据同步
  useImData({
    'im:shakev3:config': config,
    'im:shakev3': round,
    'im:wall': wall,
    'im:feature:config': featureConfig,
  })
}
