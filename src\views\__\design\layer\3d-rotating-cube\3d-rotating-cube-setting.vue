<script setup lang="ts">
import { useDataAttr } from '../..'
import { DEFAULT_DATA, type IDesign3dRotatingCube } from './3d-rotating-cube'

const layer = defineModel<IDesign3dRotatingCube>('layer', { required: true })

const cubeInitPositionXBind = useDataAttr(layer.value.data, 'cubeInitPositionX', DEFAULT_DATA.cubeInitPositionX)
const cubeInitPositionYBind = useDataAttr(layer.value.data, 'cubeInitPositionY', DEFAULT_DATA.cubeInitPositionY)
const cubeInitPositionZBind = useDataAttr(layer.value.data, 'cubeInitPositionZ', DEFAULT_DATA.cubeInitPositionZ)

const cubeRotationPositionXBind = useDataAttr(layer.value.data, 'cubeRotationPositionX', DEFAULT_DATA.cubeRotationPositionX)
const cubeRotationPositionYBind = useDataAttr(layer.value.data, 'cubeRotationPositionY', DEFAULT_DATA.cubeRotationPositionY)
const cubeRotationPositionZBind = useDataAttr(layer.value.data, 'cubeRotationPositionZ', DEFAULT_DATA.cubeRotationPositionZ)
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        实际大屏幕展示时将使用奖品设置-奖品图片。
      </div>
      <el-divider />
      <div class="setting-item">
        <strong>
          初始角度
        </strong>
        <div class="ml-10px flex-1">
          <div class="setting-item">
            <h3>X</h3>
            <div class="flex-1">
              <el-slider v-model="cubeInitPositionXBind" class="mx-10" :min="0" :max="10" :step="0.1"></el-slider>
            </div>
          </div>
          <div class="setting-item">
            <h3>Y</h3>
            <div class="flex-1">
              <el-slider v-model="cubeInitPositionYBind" class="mx-10" :min="0" :max="10" :step="0.1"></el-slider>
            </div>
          </div>
          <div class="setting-item">
            <h3>Z</h3>
            <div class="flex-1">
              <el-slider v-model="cubeInitPositionZBind" class="mx-10" :min="0" :max="10" :step="0.1"></el-slider>
            </div>
          </div>
        </div>
      </div>

      <div class="setting-item">
        <strong>
          旋转变化
        </strong>
        <div class="ml-10px flex-1">
          <div class="setting-item">
            <h3>X</h3>
            <div class="flex-1">
              <el-slider v-model="cubeRotationPositionXBind" class="mx-10" :min="0" :max="1" :step="0.1"></el-slider>
            </div>
          </div>
          <div class="setting-item">
            <h3>Y</h3>
            <div class="flex-1">
              <el-slider v-model="cubeRotationPositionYBind" class="mx-10" :min="0" :max="1" :step="0.1"></el-slider>
            </div>
          </div>
          <div class="setting-item">
            <h3>Z</h3>
            <div class="flex-1">
              <el-slider v-model="cubeRotationPositionZBind" class="mx-10" :min="0" :max="1" :step="0.1"></el-slider>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
