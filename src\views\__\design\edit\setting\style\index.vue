<script setup lang="ts">
import type { IDesignGroup } from '../../../layer/group/group'
import type { IDesignData, IDesignLayer } from '../../../types'
import { cloneDeep } from 'lodash-es'
import { hasAuth } from '~/src/utils/auth'
import { useDesignData, useDesignSetup, useDesignTemp } from '../../..'
import HiStyleBase from './base.vue'
import HiStyleBorder from './border.vue'
import HiStyleFilter from './filter.vue'
import HiStyleShadow from './shadow.vue'

const key = ref('')
const designData = useDesignData()
const designTemp = useDesignTemp()
const openIndex = ref<number>(0)

function findLayer(uuid: string, layers: IDesignLayer[] = []): { parent: IDesignLayer[], index: number } | undefined {
  for (let i = 0; i < layers.length; i++) {
    const layer = layers[i]
    if (layer.uuid === uuid) {
      return { parent: layers, index: i }
    }
    if (layer.type === 'group') {
      const group = layer as IDesignGroup
      const result = findLayer(uuid, group.layers)
      if (result) return result
    }
  }
}
const oldCurrentLayer = computed(() => {
  const uuids = designTemp.activeList
  if (uuids.length > 1) return null
  // 查询uuid对应的父级
  if (!designTemp.parentTheme) return null

  let _parentDesign: IDesignData
  if (designTemp.showType === 'pcwall') {
    _parentDesign = JSON.parse(designTemp.parentTheme.webContent)
  } else if (designTemp.showType === 'mobile') {
    _parentDesign = JSON.parse(designTemp.parentTheme.mobContent)
  } else {
    return null
  }

  const result = findLayer(uuids[0], _parentDesign.layers)
  if (!result) return null
  return result.parent[result.index]
})
const currentLayer = computed(() => {
  const uuids = designTemp.activeList
  if (uuids.length > 1) return null

  return designData.getLayerByUuid(uuids[0])
})
const currentLayers = computed(() => {
  return designData.getLayerByUuids(designTemp.activeList)
})

const designSetup = useDesignSetup()
const setupOption = computed(() => {
  const layer = currentLayers.value[0]
  if (!layer) return
  return designSetup.getComponentSetupOption(layer.type)
})
// setting ///////////////////////////////////////////
const auth = hasAuth(['man', 'oem', 'spo.hi.vip', 'spo.ft'])
const commonSetting = computed(() => {
  const resultArr = []

  if (currentLayers.value.length === 1) {
    resultArr.push(HiStyleBase)
    if (auth) {
      resultArr.push(HiStyleBorder)
      resultArr.push(HiStyleShadow)
      resultArr.push(HiStyleFilter)
    }
  } else {
    resultArr.push(HiStyleBase)
  }
  return resultArr
})
watch(
  () => currentLayers.value.length,
  (v) => {
    if (v !== 1) {
      openIndex.value = 0
    }
  },
)

// reset
const stringifyFn = (key: string, value: any) => key.startsWith('$') ? undefined : value
const canReset = computed(() => {
  const check = designTemp.activeList.length === 1
    && !!designTemp.parentTheme
    && !!designTemp.theme?.pId
  if (!check) return false
  if (!oldCurrentLayer.value) return false
  return JSON.stringify(toValue(currentLayer), stringifyFn) !== JSON.stringify(toValue(oldCurrentLayer), stringifyFn)
})

async function resetFn() {
  if (currentLayer.value && oldCurrentLayer.value) {
    if (currentLayer.value.uuid !== oldCurrentLayer.value.uuid) return
    const result = findLayer(currentLayer.value.uuid, designData.layers)
    if (!result) return
    const { parent, index } = result
    parent.splice(index, 1, cloneDeep(oldCurrentLayer.value))

    key.value = Math.random().toString(36).slice(2)
  }
}
</script>

<template>
  <el-popconfirm
    v-if="canReset"
    title="该操作会将当前图层重置为原有设置，是否继续?"
    cancel-button-text="取消"
    confirm-button-text="继续"
    :width="260"
    @confirm="resetFn"
  >
    <template #reference>
      <el-button class="reset" type="warning" plain>重置</el-button>
    </template>
  </el-popconfirm>
  <div v-if="setupOption" :key="key" class="style-box">
    <component
      :is="setupOption.CompSetting"
      v-if="currentLayers.length === 1 && setupOption.CompSetting"
      v-model:layer="currentLayers[0]"
    />
    <template v-if="hasAuth(['man', 'oem', 'spo.hi.vip', 'spo.ft'])">
      <div
        v-for="(item, index) of commonSetting"
        :key="index"
        class="setting-block"
        :class="{ open: openIndex === index }"
        @click="openIndex = index"
      >
        <h2>{{ 'label' in item ? item.label : '' }}</h2>
        <component :is="item" v-model:layer="currentLayers[0]" />
      </div>
    </template>
    <!-- 没有附加配置项组件添加提示 -->
    <template v-else-if="currentLayers.length > 1 || ['empty', 'group'].includes(currentLayer?.type || '')">
      <div class="my-40px text-center text-#666">
        <p>暂无可用配置项，开通会员配置更灵活</p>
      </div>
    </template>
  </div>
</template>

<style scoped lang="scss">
.reset {
  margin: 6px 16px;
  width: calc(100% - 32px);
}

.style-box {
  :deep() {
    // 通用设置样式
    .setting-block {
      background-color: #fff;
      overflow: hidden;
      cursor: pointer;

      &:not(:first-child) {
        height: 40px;

        &:has(h2) {
          margin-top: 4px;
        }
      }

      &.open {
        height: auto;

        > h2 {
          color: #fff;
          background-color: var(--el-color-primary);
        }
      }

      > h2 {
        height: 40px;
        line-height: 40px;
        font-size: 12px;
        font-weight: 700;
        padding: 0 16px;
        color: #111;
        transition: 0.2s;
        margin: 0;

        &:hover {
          color: #fff;
          background-color: var(--el-color-primary);
        }
      }

      .setting-wrap {
        margin-left: 16px;
        margin-right: 16px;
        padding: 10px 0;

        h3 {
          font-weight: 400;
          font-size: 14px;
          margin-right: 6px;
          white-space: nowrap;
        }
      }

      .setting-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 8px;
        margin-bottom: 8px;
      }
    }
  }
}
</style>
