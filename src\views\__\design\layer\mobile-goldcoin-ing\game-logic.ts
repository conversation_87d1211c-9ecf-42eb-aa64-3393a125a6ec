import gsap from 'gsap'
import { MotionPathPlugin } from 'gsap/MotionPathPlugin'
import { timer } from '~/src/utils'

gsap.registerPlugin(MotionPathPlugin)

let gameContainerWidth = 0
let gameContainerHeight = document.body.offsetHeight

let gameContainerRef: HTMLDivElement | null = null

const visibility = useDocumentVisibility()

const howlCache: { [url: string]: Howl } = {}
let currentAudioHowl: Howl | null = null
function playAudio(url: string) {
  if (!url) return
  if (currentAudioHowl) {
    currentAudioHowl.stop()
  }
  if (howlCache[url]) {
    currentAudioHowl = howlCache[url]
  } else {
    currentAudioHowl = new Howl({
      src: [url],
    })
    howlCache[url] = currentAudioHowl
  }
  currentAudioHowl.play()
}

function unloadAllAudio() {
  Object.keys(howlCache).forEach((url) => {
    howlCache[url].unload()
  })
  for (const url in howlCache) {
    delete howlCache[url]
  }
  currentAudioHowl = null
}

// 碰撞检测
interface Rect {
  position: { x: number, y: number }
  size: { width: number, height: number }
}
function checkCollision(rect1: Rect, rect2: Rect) {
  const l1 = { x: rect1.position.x, y: rect1.position.y }
  const r1 = { x: rect1.position.x + rect1.size.width, y: rect1.position.y + rect1.size.height }
  const l2 = { x: rect2.position.x, y: rect2.position.y }
  const r2 = { x: rect2.position.x + rect2.size.width, y: rect2.position.y + rect2.size.height }
  if (l1.x > r2.x || l2.x > r1.x || l1.y > r2.y || l2.y > r1.y) return false
  return true
}

// 函数：播放音效

// 函数：计算金币起始点和终点，以及轨迹
function random(m: number, n: number, b = false) {
  const result = Math.ceil(Math.random() * (n - m + 1) + m - 1)
  return b ? (Math.random() > 0.5 ? result : -result) : result
}

function getPath(offset = 0) {
  // 基础尺寸
  const stepY = 80
  const count = Math.ceil(gameContainerWidth / stepY)
  const safe = gameContainerWidth / 10
  // 位置基线
  const baseX = random(safe, gameContainerWidth - safe)
  const x0 = baseX + random(safe / 2, safe, true)
  const y0 = -offset
  const xA = []
  const yA = []
  // 偏移量范围
  for (let i = 1; i < count; i++) {
    yA.push(Math.min(i * stepY, gameContainerHeight))
  }
  // 结尾
  xA.push(Math.min(baseX + random(safe / 2, safe, true), gameContainerWidth))
  yA.push(gameContainerHeight + offset)
  return {
    x0,
    y0,
    xA,
    yA,
  }
}

// 类：el、size、position、speed、type(用于检索比较)、修改位置、修改大小、销毁（类内部所有）、分数、死亡（碰撞/修改DOM内容、下一步是销毁）
interface Size {
  width: number
  height: number
}
interface Position {
  x: number
  y: number
}

interface CacheObjects {
  falling: (GlodcoinFalling | GoldcoinBase)[]
  receiver: (GlodcoinReceiver | GoldcoinBase)[]
}

const cacheObjects: CacheObjects = {
  falling: [],
  receiver: [],
}

type CacheObjectsKey = keyof CacheObjects

class GoldcoinBase {
  el = document.createElement('div')
  size: Size = { width: 50, height: 50 }
  position: Position = { x: 0, y: 0 }
  speed: number = 0
  type: CacheObjectsKey | null = null
  isDestoryed: boolean = false

  constructor(type: CacheObjectsKey) {
    this.type = type
  }

  // 统一记录到缓存
  onMounted() {
    this.el.classList.add('game-el', this.type!)
    gameContainerRef?.appendChild(this.el)
    const _cache = cacheObjects[this.type!]
    if (!_cache.length) {
      cacheObjects[this.type!] = []
    }
    cacheObjects[this.type!].push(this)
  }

  // 统一移除DOM、清理缓存
  onDestroy() {
    if (this.isDestoryed) return
    this.isDestoryed = true
    try {
      gameContainerRef?.removeChild(this.el)
    } catch {
    }
    const _cache = cacheObjects[this.type!]
    if (_cache.length) {
      const index = _cache.findIndex(item => item === this)
      if (index > -1) {
        _cache.splice(index, 1)
      }
    }
  }

  setContent(content: string) {
    this.el.innerHTML = content
  }

  setSize(size: Partial<Size>) {
    if (size.width) {
      this.size.width = size.width
      this.el.style.width = `${size.width}px`
    }
    if (size.height) {
      this.size.height = size.height
      this.el.style.height = `${size.height}px`
    }
  }

  setPosition(position: Partial<Position>, updateDom = true) {
    if (position.x) {
      this.position.x = position.x
    }
    if (position.y) {
      this.position.y = position.y
    }
    if (updateDom) {
      const transform = `translateX(${position.x || this.position.x}px) translateY(${position.y || this.position.y}px)`
      this.el.style.transform = transform
    }
  }
}
// 移动人物，接收者
interface GlodcoinReceiverOptions {
  imgUrl: string
  width: number
  height: number
  rotate: boolean
  auto: boolean
}
class GlodcoinReceiver extends GoldcoinBase {
  rotate: boolean = false
  imgUrl: string = ''
  auto: boolean = false // 编辑模式下，自动移动、取消手势监听

  constructor(options: GlodcoinReceiverOptions) {
    super('receiver')
    this.rotate = options.rotate
    this.imgUrl = options.imgUrl
    this.auto = options.auto
    // 计算显示宽高，初始位置
    let width = options.width
    let height = options.height
    const maxWidth = gameContainerWidth / 2
    if (width > maxWidth) {
      height = (height * maxWidth) / width
      width = maxWidth
    }

    this.setSize({ width: width * 0.7, height: height * 0.7 })
    this.onBeforeMounted()
  }

  onBeforeMounted(): void {
    this.setContent(`<img src="${this.imgUrl}"/>`)
    const x = gameContainerWidth / 2 - this.size.width / 2
    const y = gameContainerHeight - this.size.height

    this.setPosition({
      x,
      y,
    })
    this.onMounted()
  }

  onMounted(): void {
    super.onMounted()
    if (this.auto) {
      this.autoMove()
    } else {
      // 根据手势移动
      useDraggable(this.el, {
        preventDefault: true,
        onStart: () => { },
        onMove: position => this.move(position),
        onEnd: () => { },
      })
    }
  }

  autoMove() {
    const maxX = gameContainerWidth - this.size.width
    // Set the initial position and orientation of the this.el
    gsap.set(this.el, { x: 0, scaleX: 1 })
    // Create a timeline that repeats infinitely
    const tl = gsap.timeline({
      repeat: -1,
      onUpdate: () => {
        const currentX = gsap.getProperty(this.el, 'x') as number
        this.setPosition({
          x: currentX,
        }, false)
      },
    })
    // Move from x = 0 to x = maxX
    tl.to(this.el, {
      x: maxX,
      duration: 2,
      ease: 'power1.inOut',
    })
    if (this.rotate) {
      // Flip horizontally before moving back
      tl.set(this.el, { scaleX: -1 })
    }
    // Move from x = maxX back to x = 0
    tl.to(this.el, {
      x: 0,
      duration: 2,
      ease: 'power1.inOut',
    })
    if (this.rotate) {
      // Flip horizontally before moving back
      tl.set(this.el, { scaleX: 1 })
    }
  }

  move(position: { x: number, y: number }) {
    // 根据手势方向旋转元素
    if (this.rotate) {
      if (position.x < this.position.x) {
        this.el.classList.add('toleft')
      } else {
        this.el.classList.remove('toleft')
      }
    }
    // 计算位置，不允许超出屏幕可见区域
    let l = Math.max(-this.size.width / 2 - 1, position.x)
    if (l + this.size.width / 2 > gameContainerWidth) {
      l = gameContainerWidth - this.size.width / 2
    }
    this.setPosition({ x: l })
  }
}

// 掉落物
interface GoldcoinFallingOptions {
  score: number
  imgUrl: string
  width: number
  height: number
  showChangeTips: boolean
  bombMusic: string
  bombImage: string
  successBombImage: string
  successMusic: string
  onUpdateScore: (score: number) => void
  fallingSpeed: number
}
class GlodcoinFalling extends GoldcoinBase {
  score: number = 0
  imgUrl: string = ''
  showChangeTips: boolean = false
  isDead: boolean = false
  tween: gsap.core.Tween | null = null
  // 爆炸特效、音效
  bombMusic: string = ''
  bombImage: string = ''
  successBombImage: string = ''
  successMusic: string = ''
  // 回调函数：金币事件
  onUpdateScore: (score: number) => void
  fallingSpeed: number = 3

  constructor(options: GoldcoinFallingOptions) {
    super('falling')
    this.score = options.score
    this.imgUrl = options.imgUrl
    this.showChangeTips = options.showChangeTips
    this.bombMusic = options.bombMusic
    this.bombImage = options.bombImage
    this.successBombImage = options.successBombImage
    this.successMusic = options.successMusic
    this.onUpdateScore = options.onUpdateScore
    this.fallingSpeed = options.fallingSpeed

    // 计算显示宽高，初始位置
    let width = options.width
    let height = options.height
    const maxWidth = gameContainerWidth / 8
    if (width > maxWidth) {
      height = (height * maxWidth) / width
      width = maxWidth
    }
    this.setSize({ width, height })
    this.onBeforeMounted()
  }

  onBeforeMounted(): void {
    this.setContent(`<img src="${this.imgUrl}">`)
    this.setPosition({
      x: Math.random() * (gameContainerWidth - this.size.width),
      y: -this.size.height,
    })
    this.onMounted()
  }

  onMounted(): void {
    super.onMounted()
    // 更新位置
    this.updatePosition()
  }

  updatePosition(): void {
    // 更新位置
    const { x0: startX, y0: startY, xA: targetX, yA: targetY } = getPath(this.size.height)
    const baseSpeed = (10 - this.fallingSpeed) * 1000
    const speed = random(Math.max(1000, baseSpeed - 2000), baseSpeed + 2000)
    const xPosition = Math.random() > 0.5 && targetX[0] * targetX.length > gameContainerWidth - this.size.width ? -2 : 2
    const pathPoints = targetY.map((y, i) => ({ x: targetX[0] + i * xPosition, y }))
    this.tween = gsap.to(this.el, {
      startAt: {
        x: startX,
        y: startY,
      },
      motionPath: {
        path: pathPoints,
        autoRotate: false, // Set to true if you want the element to rotate along the path
        alignOrigin: [0.5, 0.5], // Adjust the alignment origin if necessary
        curviness: 1, // Adjust the curviness of the path
        relative: false,
      },
      ease: 'power1.out', // Choose an easing function that suits your animation
      duration: speed / 1000,
      onUpdate: () => {
        const currentX = gsap.getProperty(this.el, 'x') as number
        const currentY = gsap.getProperty(this.el, 'y') as number
        this.setPosition({
          x: currentX,
          y: currentY,
        }, false)
        this.checkCollision({
          x: currentX,
          y: currentY,
        })
      },
      onComplete: () => {
        gsap.killTweensOf(this.el)
        this.onDestroy()
      },
    })
  }

  checkCollision({ x, y }: Position): void {
    const fallingRect = {
      position: { x, y },
      size: this.size,
    }
    cacheObjects.receiver.forEach((receiver) => {
      if (!receiver.isDestoryed && !this.isDead && checkCollision(fallingRect, receiver)) {
        this.onDead()
      }
    })
  }

  async onDead(): Promise<void> {
    gsap.killTweensOf(this.el)
    this.isDead = true

    // 爆炸特效、音效，判断是哪个
    const curMusic = this.score < 0 ? this.bombMusic : this.successMusic
    const curImage = this.score < 0 ? this.bombImage : this.successBombImage
    // 播放音效
    playAudio(curMusic)
    // 播放特效
    this.setContent(`<img src="${curImage}"> `)
    if (this.showChangeTips) {
      // 分数变化提示
      await timer(500)
      const scoreStr = `${this.score >= 0 ? '+' : ''}${this.score}`
      this.setContent(`<div class="bomb-score"> ${scoreStr} </div>`)
    }
    // 更新分数
    this.onUpdateScore(this.score)
    // 销毁
    await timer(this.showChangeTips ? 800 : 200)
    this.onDestroy()
  }
}

// 函数：主函数，初始化游戏

let createArmyInterval: NodeJS.Timeout | undefined
interface Falling {
  score: number
  imgUrl: string
  width: number
  height: number
  showChangeTips?: boolean
}
export interface GlodcoinGameOptions {
  el: HTMLDivElement
  bombMusic: string
  bombImage: string
  successBombImage: string
  successMusic: string
  showChangeTips?: boolean // 是否显示分数变化提示
  receiver: {
    imgUrl: string
    width: number
    height: number
    rotate: boolean
  }
  fallings: Falling[]
  onUpdateScore: (score: number) => void
  auto: boolean
  layerHeight: number // 游戏区域高度，经过计算后的高度
  fallingsDensity: number // 单位秒
  fallingSpeed: number // 单位秒
}

export function onStart(options: GlodcoinGameOptions) {
  // 全局配置
  gameContainerRef = options.el
  gameContainerWidth = options.el.offsetWidth
  gameContainerHeight = options.layerHeight

  // 创建接收者
  // eslint-disable-next-line no-new
  new GlodcoinReceiver({
    imgUrl: options.receiver.imgUrl,
    width: options.receiver.width,
    height: options.receiver.height,
    rotate: options.receiver.rotate,
    auto: options.auto,
  })
  // 创建掉落物
  // 计算敌人出现概率
  const enemyArr: Falling[] = []
  for (let i = 0; i < options.fallings.length; i++) {
    const enemy = options.fallings[i]
    for (let j = 0; j < (enemy.score > 0 ? 2 : 1); j++) {
      enemyArr.push(enemy)
    }
  }

  function createFalling() {
    const info = enemyArr[random(0, enemyArr.length - 1)]
    if (!info) return
    // eslint-disable-next-line no-new
    new GlodcoinFalling({
      score: info.score,
      imgUrl: info.imgUrl,
      width: info.width,
      height: info.height,
      showChangeTips: options.showChangeTips ?? false,
      bombMusic: options.bombMusic,
      bombImage: options.bombImage,
      successBombImage: options.successBombImage,
      successMusic: options.successMusic,
      onUpdateScore: options.onUpdateScore,
      fallingSpeed: options.fallingSpeed,
    })
  }

  createFalling()
  const density = 11 - options.fallingsDensity
  createArmyInterval = setInterval(() => {
    if (visibility.value === 'hidden') return
    createFalling()
  }, density * 100)
}

// 函数：游戏结束，清理全部内容
export function onEnd() {
  clearInterval(createArmyInterval)
  unloadAllAudio()
  Object.keys(cacheObjects).forEach((type) => {
    cacheObjects[type as CacheObjectsKey].forEach(item => item.onDestroy())
    cacheObjects[type as CacheObjectsKey] = []
  })
}
