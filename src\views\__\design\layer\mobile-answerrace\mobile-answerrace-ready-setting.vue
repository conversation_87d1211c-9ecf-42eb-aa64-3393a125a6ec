<script setup lang="ts">
import { getDefaultMaterial, openSelectMaterial, useDataAttr } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'
import { DEFAULT_DATA, type IDesignMobileAnswerraceReady } from './mobile-answerrace-ready'
import { readyMockConfig, useReadyMockConfig } from './mock'

const layer = defineModel<IDesignMobileAnswerraceReady>('layer', { required: true })

async function updateMaterialFn(name: 'decorationImage', isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, `${name}`)
    : await openSelectMaterial('PIC')
  if (result) {
    layer.value.data[name] = result
  } else if (isReset) {
    delete layer.value.data[name]
  }
}

const decorationImageBind = useDataAttr(layer.value.data, 'decorationImage', DEFAULT_DATA.decorationImage)
const teamSelectionBackgroundBind = useDataAttr(layer.value.data, 'teamSelectionBackground', DEFAULT_DATA.teamSelectionBackground)
const rulesBackgroundBind = useDataAttr(layer.value.data, 'rulesBackground', DEFAULT_DATA.rulesBackground)
const teamSelectionTextBind = useDataAttr(layer.value.data, 'teamSelectionText', DEFAULT_DATA.teamSelectionText)
const teamSelectionTextColorBind = useDataAttr(layer.value.data, 'teamSelectionTextColor', DEFAULT_DATA.teamSelectionTextColor)
const rulesTextFontSizeBind = useDataAttr(layer.value.data, 'rulesTextFontSize', DEFAULT_DATA.rulesTextFontSize)
const rulesTextColorBind = useDataAttr(layer.value.data, 'rulesTextColor', DEFAULT_DATA.rulesTextColor)
const roundStartedTextBind = useDataAttr(layer.value.data, 'roundStartedText', DEFAULT_DATA.roundStartedText)
const waitingToStartTextBind = useDataAttr(layer.value.data, 'waitingToStartText', DEFAULT_DATA.waitingToStartText)

const statusTextFontSizeBind = useDataAttr(layer.value.data, 'statusTextFontSize', DEFAULT_DATA.statusTextFontSize)
const statusTextColorBind = useDataAttr(layer.value.data, 'statusTextColor', DEFAULT_DATA.statusTextColor)

const PREVIEW_JOIN_TYPE = [
  { label: '个人', value: 'PERSONAL' },
  { label: '团队', value: 'TEAM' },
]

const PREVIEW_STATE = [
  { label: '准备中', value: 'waitingToStart' },
  { label: '进行中未报名', value: 'roundStarted' },
]

</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <h2>预览切换</h2>
      <div class="setting-item">
        <h3>模式</h3>
        <el-select v-model="readyMockConfig.mockJoinType" placeholder="请选择预览模式" style="width: 120px">
          <el-option v-for="item in PREVIEW_JOIN_TYPE" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="setting-item">
        <h3>状态</h3>
        <el-select v-model="readyMockConfig.mockState" placeholder="请选择预览状态" style="width: 120px">
          <el-option v-for="item in PREVIEW_STATE" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>

      <el-divider />

      <h2>配置项</h2>
      <div class="setting-item">
        <h3>装饰图</h3>
        <div class="flex items-end">
          <div class="bgblank relative h-70 w-120">
            <MaterialThumbnail @select="updateMaterialFn('decorationImage')" @reset="updateMaterialFn('decorationImage', true)">
              <img v-if="decorationImageBind" :src="decorationImageBind" alt="" class="h-full w-full object-cover" />
            </MaterialThumbnail>
          </div>
        </div>
      </div>

      <el-divider />

      <h2>选择队伍</h2>
      <div class="setting-item">
        <h3>背景色</h3>
        <hi-color v-model="teamSelectionBackgroundBind" />
      </div>

      <div class="setting-item">
        <h3>标题文案</h3>
        <el-input v-model="teamSelectionTextBind" class="ml-40px" :maxlength="10" placeholder="请输入文案" />
      </div>

      <div class="setting-item">
        <h3>标题颜色</h3>
        <hi-color v-model="teamSelectionTextColorBind" />
      </div>

      <el-divider />

      <h2>活动规则</h2>

      <div class="setting-item">
        <h3>背景色</h3>
        <hi-color v-model="rulesBackgroundBind" />
      </div>

      <div class="setting-item">
        <h3>文字颜色</h3>
        <hi-color v-model="rulesTextColorBind" />
      </div>

      <div class="setting-item">
        <h3>文字字号</h3>
        <el-input-number v-model="rulesTextFontSizeBind" v-input-number size="small" controls-position="right" class="control-input" :max="30" :min="10" />
      </div>

      <el-divider />

      <h2>状态文案</h2>

      <div class="setting-item">
        <h3>字号</h3>
        <el-input-number v-model="statusTextFontSizeBind" v-input-number size="small" controls-position="right" class="control-input" :max="30" :min="10" />
      </div>

      <div class="setting-item">
        <h3>颜色</h3>
        <hi-color v-model="statusTextColorBind" />
      </div>

      <div class="setting-item">
        <h3>等待开始文案</h3>
        <el-input v-model="waitingToStartTextBind" type="textarea" class="w-110" placeholder="请输入文案" />
      </div>

      <div class="setting-item">
        <h3>轮次已开始文案</h3>
        <el-input v-model="roundStartedTextBind" type="textarea" class="w-110" placeholder="请输入文案" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
