<script setup lang="ts">
import { getDefaultMaterial, openSelectMaterial, useDataAttr } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'
import { defaultData, type IDesignIngRank1 } from './ingrank1'

const layer = defineModel<IDesignIngRank1>('layer', { required: true })

const textColorBind = useDataAttr(layer.value, 'textColor', defaultData.textColor || '')
const itemBgImgBind = useDataAttr(layer.value, 'itemBgImg', defaultData.itemBgImg)
const avatarXBind = useDataAttr(layer.value, 'avatarX', defaultData.avatarX)
const avatarYBind = useDataAttr(layer.value, 'avatarY', defaultData.avatarY)
const avatarSizeBind = useDataAttr(layer.value, 'avatarSize', defaultData.avatarSize)
const contentWidthBind = useDataAttr(layer.value, 'textContentWidth', defaultData.textContentWidth)
const contentXBind = useDataAttr(layer.value, 'textContentX', defaultData.textContentX)
const contentYBind = useDataAttr(layer.value, 'textContentY', defaultData.textContentY)
const fontSizeBind = useDataAttr(layer.value, 'textSize', defaultData.textSize)

type IType = 'itemBgImg'
async function updateMaterialFn(name: IType, isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, `${name}`)
    : await openSelectMaterial('PIC')
  if (result) {
    layer.value[name] = result
  } else if (isReset) {
    delete layer.value[name]
  }
}
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>字体颜色</h3>
        <hi-color v-model="textColorBind" />
      </div>
      <div class="setting-item mt-0!">
        <h3>背景图</h3>
        <div class="bgblank relative h-50 w-120">
          <MaterialThumbnail @select="updateMaterialFn('itemBgImg')" @reset="updateMaterialFn('itemBgImg', true)">
            <img v-if="itemBgImgBind" :src="itemBgImgBind">
          </MaterialThumbnail>
        </div>
      </div>
      <!-- 大小 -->
      <div class="setting-item">
        <h3>头像区域</h3>
        <div class="flex">
          <h3 class="ml-10">大小</h3>
          <el-input-number v-model="avatarSizeBind" v-input-number controls-position="right" :min="1" :step="1" />
        </div>
      </div>
      <div class="setting-item justify-end!">
        <h3 class="ml-10">X</h3>
        <el-input-number v-model="avatarXBind" v-input-number controls-position="right" :min="0" :max="100" :step="1" />
        <h3 class="ml-10">Y</h3>
        <el-input-number v-model="avatarYBind" v-input-number controls-position="right" :min="0" :max="100" :step="1" />
      </div>
      <div class="setting-item">
        <h3>内容区</h3>
        <div class="flex">
          <h3 class="ml-10">宽度</h3>
          <el-input-number v-model="contentWidthBind" v-input-number controls-position="right" :min="1" :step="1" />
        </div>
      </div>
      <div class="setting-item justify-end!">
        <div class="flex justify-end!">
          <h3 class="ml-10">X</h3>
          <el-input-number v-model="contentXBind" v-input-number controls-position="right" :min="0" :max="400" :step="1" />
          <h3 class="ml-10">Y</h3>
          <el-input-number v-model="contentYBind" v-input-number controls-position="right" :min="0" :max="100" :step="1" />
        </div>
      </div>
      <div class="setting-item">
        <h3>字体大小</h3>
        <div class="flex">
          <el-input-number v-model="fontSizeBind" v-input-number controls-position="right" :min="1" :step="1" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
