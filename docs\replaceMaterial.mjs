import path from 'node:path'
import { replaceInFileSync } from 'replace-in-file'

const arr = [
  'c5a55f57d4470217744892555a07eb92.png',
  'podium1.png',
  'c1c18fccddf81a77bdb517aa89771e81.png',
  'podium2.png',
  'e522fc6ec31c18e5ec7efff9d4915667.png',
  'bg.png',
  'd8249129d3eb0cb61fbce659676138b4.gif',
  'flower2.gif',
  'cc7f4267cd4e8248fe437a1e12d48e9b.gif',
  'flower1.gif',
  '78eef1ab9745002b0034fa80e9333aee.gif',
  'flower3.gif',
  '703ba6861ed6600886eae3eeee793517.png',
  'podium.png',
  '925a65725d035a60d0faf72b83e38a99.png',
  'light.png',
  '1f36484897e108ae1bc635fca9a8db93.png',
  'shake-title.png',
  '2079731b83134f6606d26788deec23c9.png',
  'front.png',
  '737950265cd7775c58b1754e914e633c.png',
  'shake1.png',
  '9b37754a2a456c5a197cdf77237ef10f.png',
  'shake3.png',
  '46d78c4f1c4e104a4d3da8253c8bf1ae.png',
  'rankings2-1.png',
  '145d1845eb2b57d75f63afa3878f0725.png',
  'rankings2-3.png',
  '4faa27e1172ae8fc0af26e902f074103.png',
  'shake-title3.png',
  'c394afdd2beb720685773ded8432d006.png',
  'flag.png',
  '4a1d9e7ccf401a9d6f6ab86e71604883.png',
  'start3.png',
  'ddb61aae5369373532289c02cfe9f00e.png',
  'again1.png',
  '044f748f1ac59244a2614df4b35012b1.png',
  'rankings2-2.png',
  'c90175b171ecdeccfdadc51e37bb5a45.png',
  'start2.png',
  '6db1de5e0658f711f8f86a84f47641f6.png',
  'start1.png',
  '004ee37e03ae97472c2102b3df618473.png',
  'audience.png',
  '0880e6ac0daf7044f92f48eb6abff120.png',
  'back.png',
  'd011c372997d0db4984e847fe27e6fd1.png',
  'again.png',
  '0ff0c53392dda4f53a077f949b8a09a5.png',
  'again2.png',
  '1c38bffd4ed0c4880e0cf0a5c912e9f8.png',
  'start.png',
  '2cf9f8c1eee36fe4810fe3d77a44c80a.png',
  'shake.png',
  '78719f7c3586f443b2926b17225e2adb.png',
  '03.png',
  '6a757455e87acd993cc9fe3ecacbe62a.png',
  '02.png',
  'e4d70f43226b99e36fbc3933e785890f.png',
  '01.png',
  '452262d1fcb313bd8ab07c64bde9fc0e.png',
  'headpic.png',
  'a85799dcdb552ec360a8c5613284e004.png',
  'decoration.png',
  '1e3c5c7a76d916f6ae8ebb4faf8fe045.png',
  'again3.png',
  'c7f39b0c2a6dff09e80bec80ccf1b0c7.png',
  'default.png',
  '90d8df737b4fbe6b1d1b7a5fa2b49749.png',
  'default1.png',
  '408e9e6679efeac41c96c5620c7df06b.png',
  'race.png',
  '9bb1052efa9a1f7a2808c00189d93d6e.png',
  'star.png',
]

const project = '/home/<USER>/k8s/hxc/web-next/src/'
// 遍历project下所有子孙目录中的vue、ts、json文件，并进行字符串替换，然后写回文件

for (let i = 0; i < arr.length; i += 2) {
  console.log(arr[i], arr[i + 1])

  const oldPath = `http://res.dev.hichoujiang.com/design/shake/${arr[i + 1]}`
  const newPath = `https://res.dev.hixianchang.com/qn/material/${arr[i]}`

  const options = {
    files: path.join(project, '**', '*.ts'),
    from: new RegExp(oldPath, 'g'),
    to: newPath,
  }

  const results = replaceInFileSync(options)
  for (const result of results) {
    if (result.hasChanged) {
      console.log('Replacement results:', result)
    }
  }
}
