<script setup lang="ts">
import defaultImg from '../assets/placeholder.png'

interface Props {
  qrcodeImg?: string
  logo?: string
  size?: number
}
const props = withDefaults(defineProps<Props>(), {
  qrcodeImg: defaultImg,
  logo: defaultImg,
  size: 500,
})
const emit = defineEmits(['loaded'])
const src = ref<string>(defaultImg)
function loadImg(url: string): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    if (!url) {
      reject(new Error('Empty URL'))
      return
    }

    const img = new Image()
    img.setAttribute('crossOrigin', 'Anonymous')
    img.onload = () => resolve(img)
    img.onerror = e => reject(e)
    img.src = url
  })
}

const content = computed(() => props.qrcodeImg + props.logo)

watch(content, async () => {
  try {
    const [img1, img2] = await Promise.all([
      loadImg(props.qrcodeImg),
      loadImg(props.logo),
    ])
    drawImg(img1, img2)
  } catch (error) {
    console.error('Error loading images:', error)
    src.value = defaultImg
  }
}, { immediate: true })

function drawImg(img1: HTMLImageElement, img2?: HTMLImageElement) {
  const canvasSize = props.size
  const circleSize = canvasSize * 0.46
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')

  if (!ctx) {
    console.error('Could not get canvas context')
    return
  }

  canvas.width = canvasSize
  canvas.height = canvasSize

  // Draw QR code background
  ctx.drawImage(img1, 0, 0, img1.width, img1.height, 0, 0, canvasSize, canvasSize)

  // Draw logo overlay
  const offset = (canvasSize - circleSize) / 2
  ctx.save()
  ctx.beginPath()
  ctx.arc(
    offset + circleSize / 2,
    offset + circleSize / 2,
    circleSize / 2,
    0,
    2 * Math.PI,
  )
  ctx.clip()

  if (img2) {
    const targetSize = Math.min(img2.width, img2.height)
    let offsetX = 0
    let offsetY = 0

    if (img2.width > img2.height) {
      offsetX = (img2.width - img2.height) / 2
    } else {
      offsetY = (img2.height - img2.width) / 2
    }

    ctx.drawImage(
      img2,
      offsetX,
      offsetY,
      targetSize,
      targetSize,
      offset,
      offset,
      circleSize,
      circleSize,
    )
  }

  ctx.restore()
  src.value = canvas.toDataURL('image/png')
  emit('loaded')
}

// 暴露公共方法
defineExpose({
  getImg: () => src.value,
})
</script>

<template>
  <img :src="src">
</template>

<style scoped>
img {
  display: block;
  max-width: 100%;
  height: auto;
}
</style>
