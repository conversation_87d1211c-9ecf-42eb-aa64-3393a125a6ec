<script setup lang="ts">
import type { IDesignMobileMoneyIng } from './mobile-money-ing'
import { random } from 'lodash-es'
import { computed, ref } from 'vue'
import { defineCustomEmits } from '../../index'

const layer = defineModel<IDesignMobileMoneyIng>('layer', { required: true })

const customEmits = defineCustomEmits(layer)

const participants = computed(() => layer.value?.data?.participants || [])
const showUp = computed(() => layer.value?.data?.showUp || false)
const upImg = computed(() => layer.value?.data?.upImg || '')
const bottomImg = computed(() => layer.value?.data?.bottomImg || '')
const participantWidth = computed(() => {
  return `${layer.value?.data?.participantWidth}%` || '60%'
})
const autoScale = computed(() => layer.value?.data?.autoScale ?? true)

interface ParticipantInstance {
  id: number
  imageSrc: string
  startY: number
  translateY: number
  timestamp: number // 如果时间过久直接删掉
}
const mobileMoneyIngRef = ref<HTMLElement>()

const clientHeight = computed(() => mobileMoneyIngRef.value?.clientHeight || window.innerHeight)

const participantInstances = ref<ParticipantInstance[]>([])

const isTouch = ref(false)

const identifierToId = new Map<number, number>()

function onTouchStart(event: TouchEvent) {
  if (participants.value.length === 0) return
  // 如果有多点触摸，忽略
  if (event.touches.length > 1) return
  const imageSrc = participants.value[random(0, participants.value.length - 1)]

  const id = Date.now() + Math.random()
  const touch = event.changedTouches[0]
  identifierToId.set(touch.identifier || 0, id)
  const startY = touch.clientY
  const instance: ParticipantInstance = {
    id,
    imageSrc,
    translateY: 0,
    startY,
    timestamp: Date.now(),
  }
  participantInstances.value.push(instance)
  // 如果超过5秒没有滑动，直接删除
  participantInstances.value = participantInstances.value.filter((instance) => {
    return Date.now() - instance.timestamp < 5000
  })
}

function onTouchMove(event: TouchEvent) {
  if (participants.value.length === 0) return
  const touch = event.changedTouches[0]
  const touchY = touch.clientY
  const id = identifierToId.get(touch.identifier || 0)
  const instance = participantInstances.value.find(instance => instance.id === id)
  if (instance) {
    instance.translateY = touchY - instance.startY
  }
}

function onTouchEndOrCancel(event: TouchEvent) {
  const touch = event.changedTouches[0]
  const id = identifierToId.get(touch.identifier || 0)
  const instance = participantInstances.value.find(instance => instance.id === id)
  if (!instance) {
    return
  }
  if (Math.abs(instance.translateY) > clientHeight.value / 10) {
    instance.translateY = -clientHeight.value
    isTouch.value = true
    updateScore()
  } else {
    instance.translateY = 0
  }
  identifierToId.delete(touch.identifier || 0)
  setTimeout(() => {
    removeInstance(instance.id)
  }, instance.translateY ? 1200 : 200)
}

function updateScore() {
  customEmits('updateScore', 1)
}

function removeInstance(id: number) {
  const index = participantInstances.value.findIndex((instance) => {
    return instance.id === id
  })
  if (index !== -1) {
    participantInstances.value.splice(index, 1)
  }
}

function participantStyle(id: number) {
  const instance = participantInstances.value.find((instance) => {
    return instance.id === id
  })
  if (!instance) return {}
  // 计算缩放比例，使图片逐渐变小
  const scale = autoScale.value ? Math.max(0.2, 1 + instance.translateY / 600) : 1
  // 计算模糊程度，根据滑动距离动态改变
  const blurAmount = Math.max(0, Math.abs(instance.translateY / clientHeight.value) / 24 * 100)
  return {
    transform: `translateY(${instance.translateY}px) scale(${scale})`,
    filter: `blur(${blurAmount}px)`,
    transition: 'transform 0.3s ease, filter 0.3s ease',
    width: participantWidth.value,
  }
}
</script>

<template>
  <div ref="mobileMoneyIngRef" class="size-full">
    <div
      class="mobile-money-ing-box"
      @touchstart.prevent.stop="onTouchStart"
      @touchmove.prevent.stop="onTouchMove"
      @touchend.prevent.stop="onTouchEndOrCancel"
      @touchcancel.prevent.stop="onTouchEndOrCancel"
    >
      <img
        v-if="showUp && !isTouch"
        :src="upImg"
        class="up-image"
        alt="Up Image"
      />
      <img
        v-for="instance in participantInstances"
        :key="instance.id"
        :src="instance.imageSrc"
        class="participant-image"
        :style="participantStyle(instance.id)"
        alt="Participant Image"
      />
      <img :src="bottomImg" class="bottom-image" alt="Bottom Image" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.mobile-money-ing-box {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.up-image {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 40%;
  object-fit: contain;
  animation: up 1.1s linear infinite;
  z-index: 2;
}

@keyframes up {
  0% {
    bottom: -10%;
    opacity: 0;
  }
  30% {
    bottom: 10%;
    opacity: 0.6;
  }
  60% {
    bottom: 32%;
    opacity: 1;
  }
  100% {
    bottom: 50%;
    opacity: 0;
  }
}

.participant-image {
  position: absolute;
  bottom: 6%;
  right: 0;
  left: 0;
  margin-left: auto;
  margin-right: auto;
  z-index: 2;
  pointer-events: none;
}

.bottom-image {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  object-fit: cover;
  object-position: top;
}
</style>
