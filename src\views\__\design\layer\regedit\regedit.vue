<script setup lang="ts">
import { defineCustomEmits, injectScale, useAdaptionQuantity, useDataAttr, useDesignState } from '../..'
import { processStyle } from '../../utils'
import { defaultAnimation, defaultAvatarSize, type IDesignRegedit } from './regedit'

const layer = defineModel<IDesignRegedit>('layer', { required: true })
const customEmits = defineCustomEmits(layer)
const scale = injectScale()
const designState = useDesignState()

const boxRef = ref<HTMLElement>()
const displaySize = useElementSize(boxRef)
const avatarSizeBind = useDataAttr(layer, 'avatarSize', defaultAvatarSize)
const animationBind = useDataAttr(layer, 'animation', defaultAnimation)

const adaptionQuantity = useAdaptionQuantity({
  displaySize: computed(() => {
    return {
      width: displaySize.width.value / scale.value,
      height: displaySize.height.value / scale.value,
    }
  }),
  itemWidth: computed(() => layer.value.itemWidth),
  aspectRatio: 0.8,
  gap: computed(() => layer.value.gap),
})

const ulStyle = computed(() => {
  return processStyle({
    gap: `${adaptionQuantity.gap}px`,
  }, scale)
})
const liStyle = computed(() => {
  return processStyle({
    width: `${adaptionQuantity.width}px`,
    height: `${adaptionQuantity.height}px`,
  }, scale)
})
const wrapStyle = computed(() => {
  return processStyle({
    width: `${adaptionQuantity.width}px`,
    height: `${adaptionQuantity.width}px`,
  }, scale)
})
const img1Style = computed(() => {
  const half = (100 - avatarSizeBind.value) / 2
  return {
    width: `${avatarSizeBind.value}%`,
    top: `${half}%`,
    left: `${half}%`,
  }
})
const img2Style = computed(() => {
  if (!animationBind.value) return {}
  return {
    animation: 'rotate 2s infinite linear',
  }
})
const nameStyle = computed(() => {
  return processStyle({
    color: layer.value.style.color || 'rgba(255,255,255,1)',
    fontSize: `${adaptionQuantity.height * 0.2}px`,
  }, scale)
})

watch(
  () => adaptionQuantity.count,
  (count) => {
    customEmits('regedit-count', count)
  },
)

interface IData {
  name: string
  avatar: string
}
const dataList = ref<IData[]>([])

watch(
  () => [designState.getLayerData('regeditList'), adaptionQuantity.count],
  () => {
    const data = designState.getLayerData('regeditList') || []
    const list: IData[] = []
    for (let i = 0; i < adaptionQuantity.count; i++) {
      const item = data[i] || {}
      if (!item.avatar) {
        item.avatar = layer.value.default
      }
      list.push(item)
    }
    dataList.value = list
  },
  { immediate: true },
)
</script>

<template>
  <ul ref="boxRef" :style="ulStyle">
    <li v-for="(item, index) in dataList" :key="index" :style="liStyle">
      <div class="wrap" :style="wrapStyle">
        <img :src="item.avatar" class="img1" alt="" :style="img1Style">
        <img :src="layer.decoration" class="img2" :style="img2Style">
      </div>
      <span :style="nameStyle">{{ item.name }}</span>
    </li>
  </ul>
</template>

<style scoped lang="scss">
ul {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  align-content: center;
  li {
    overflow: hidden;
    position: relative;
    .wrap {
      position: absolute;
      top: 0;
      left: 0;

      img {
        position: absolute;
        top: 0;
        left: 0;
      }
      .img1 {
        width: 80%;
        aspect-ratio: 1;
        border-radius: 50%;
        top: 10%;
        left: 10%;
      }
      .img2 {
        width: 100%;
        aspect-ratio: 1;
      }
    }
    span {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      //height: 20%;
      text-align: center;
      white-space: nowrap;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
@keyframes rotate {
  from {
    transform: rotateZ(0deg);
  }
  to {
    transform: rotateZ(360deg);
  }
}
</style>
