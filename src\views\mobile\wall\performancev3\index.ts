import { useDesignState, useDesignTemp } from 'design/index'
import { showToast } from 'vant'
import { useImData } from '~/src/hooks/useImData'
import { useInstanceRouter } from '~/src/hooks/useInstanceRouter'
import { useLimit } from '../../hooks/useLimit'
import { useMobileStore } from '../../stores/useMobileStore'
import { fetchTheme } from '../../utils'
import { mockConfig, mockRound } from './mock'
import 'vant/lib/toast/style'

// 类型
interface IConfig {
  performancev3Id: number
}
interface IRound {
  id: number
  themeId: number
}

interface IForm {
  key: string
  name: string
  type: string
  value?: string | null
  unit?: string | null
  conf?: string | null
  remark?: string | null
}

interface ITarget {
  id?: number
  performancev3Id: number
  p1?: string
  p2?: string
  p3?: string
  p4?: string
  p5?: string
  p6?: string
  p7?: string
  p8?: string
  p9?: string
  p10?: string
}

export function useMobilePerformancev3() {
  const router = useInstanceRouter()
  const route = router.currentRoute.value
  const isManage = route.path.startsWith('/manage/')

  const timestamp = useTimestamp()
  const designTemp = useDesignTemp()
  const designState = useDesignState()

  const isDesignEdit = designTemp.isEdit

  designTemp.showType = 'mobile'

  const mobileStore = useMobileStore()
  const { wall, featureConfig, isShowAd, isWedding } = storeToRefs(mobileStore)

  // 互动配置
  const round = ref<IRound>()
  const config = ref<IConfig>()

  const roundId = computed(() => round.value?.id || '')

  // 活动限制
  const { dealLimit } = useLimit()

  // design 表单
  const form = ref<IForm[]>()

  // target 数据
  const target = ref<ITarget>()

  const fetchFormList = async () => {
    const apiUrl = isManage ? api.man.performancev3.formlist : api.mobile.performancev3.formList
    const res = await apiUrl({})
    form.value = res || []
  }
  const fetchTarget = async () => {
    if (isDesignEdit) {
      return
    }
    const res = await api.mobile.performancev3.recordRead({
      where: {
        performancev3Id: config.value?.performancev3Id,
      },
    })
    target.value = res
  }
  const insertTarget = async (saveData: ITarget) => {
    const data = await api.mobile.performancev3.recordAdd({
      ...saveData,
      performancev3Id: config.value?.performancev3Id,
    })
    dealLimit(data)
  }
  const updateTarget = async (saveData: ITarget) => {
    const data = await api.mobile.performancev3.recordUpdate({
      where: {
        performancev3Id: config.value?.performancev3Id,
      },
      update: saveData,
    })
    dealLimit(data)
  }
  const saveTarget = async (saveData: ITarget) => {
    if (isDesignEdit) {
      target.value = { ...saveData, id: Date.now() }
      return
    }
    if (target.value?.id) {
      await updateTarget(saveData)
    } else {
      await insertTarget(saveData)
    }
    showToast({ message: '提交成功' })
    await fetchTarget()
  }

  // 主要作用是将数据暴露给图层组件
  designState
    .setStatusList([
      { label: '进行中', value: 'ing' },
    ])
    .setLayerData({
      '#主题名称#': computed(() => designTemp.theme?.name),
      'isShowAd': isShowAd,
      'isWedding': isWedding,
      'performance-form': computed(() => form.value),
      'performance-target': computed(() => target.value),
    })
    // 图层组件事件
    .setLayerEventList([
      {
        eventId: 'back-home',
        name: '返回首页',
        value() {
          window.parent.postMessage({ type: 'iframe:backHome' }, '*')
        },
      },
      // 广告交互
      {
        eventId: 'clickAd',
        value(data) {
          window.parent.postMessage({ type: 'iframe:clickAd', data }, '*')
        },
      },
      {
        eventId: 'SHOW_AD',
        value(data) {
          window.parent.postMessage({ type: 'iframe:SHOW_AD', data }, '*')
        },
      },
      {
        eventId: 'save',
        value: saveTarget,
      },
    ])

  const resetAllData = () => {
    designTemp.renderKey = timestamp.value
    fetchFormList()
    fetchTarget()
  }

  watch(() => round.value?.themeId, themeId => fetchTheme(themeId || ''))
  watch(() => roundId.value, () => {
    if (isDesignEdit) return
    resetAllData()
  })

  tryOnMounted(() => {
    if (isDesignEdit) {
      // 设计模式mock数据
      config.value = mockConfig()
      round.value = mockRound()
      fetchFormList()
    }
    designState.setStatus('ing')
  })

  // 数据同步
  useImData({
    'im:performancev3:config': config,
    'im:performancev3': round,
    'im:wall': wall,
    'im:feature:config': featureConfig,
  })
}
