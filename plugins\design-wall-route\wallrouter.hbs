<script setup lang="ts">
import HiDesign from 'design/index.vue'
import { useDesignTemp } from '~/src/views/__/design'
{{#if (eq model 'admin')}}
import { ModeEnum } from '~/src/views/__/design/types'
{{/if}}
{{#if (includes platforms "mobile")}}
import Mobile from './components/mobile.vue'
{{/if}}
{{#if (includes platforms "pcwall")}}
import Pcwall from './components/pcwall.vue'
{{/if}}

definePage({ meta: { label: '{{displayName}}' } })

const designTemp = useDesignTemp()

const interactive = '{{kebabCase name}}'

{{#if (eq model 'admin')}}
const mode = ModeEnum.edit
{{/if}}
</script>

<template>
  {{#if (eq model 'admin')}}
  <HiDesign :interactive="interactive" :mode="mode" />
  {{else}}
  <HiDesign :interactive="interactive" />
  {{/if}}
  {{#if (includes platforms "pcwall")}}
  <Pcwall v-if="designTemp.showType === 'pcwall'" />
  {{/if}}
  {{#if (includes platforms "mobile")}}
  <Mobile v-if="designTemp.showType === 'mobile'" />
  {{/if}}
</template>
