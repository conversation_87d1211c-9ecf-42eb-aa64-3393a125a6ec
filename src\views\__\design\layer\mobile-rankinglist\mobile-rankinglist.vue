<script setup lang="ts">
import { defineCustomEmits, EMITS_EVENT_SHOW_HIDE, useDesignState } from '../..'
import { defaultData, type IDesignMobileRankinglist } from './mobile-rankinglist'

export interface IRankItem {
  id: string | number
  avatarUrl: string
  nickName: string
  wxUserId?: string
  ranking: number
  teamId?: string
}

const layer = defineModel<IDesignMobileRankinglist>('layer', { required: true })
const customEmits = defineCustomEmits(layer)
const designState = useDesignState()

const titleImg = computed(() => layer.value.data.titleImg || defaultData.titleImg || '')
const unit = computed(() => layer.value.data.unit || defaultData.unit || '')
const bgImg = computed(() => layer.value.data.bgImg || defaultData.bgImg || '')
const closeImg = computed(() => layer.value.data.closeImg || defaultData.closeImg || '')

const rankingColor = computed(() => layer.value.data.rankingColor || defaultData.rankingColor || '')
const currentNameColor = computed(() => layer.value.data.currentNameColor || defaultData.currentNameColor || '')
const borderColor = computed(() => layer.value.data.borderColor || defaultData.borderColor || '')
const nameColor = computed(() => layer.value.data.nameColor || defaultData.nameColor || '')
const itemRankingColor = computed(() => layer.value.data.itemRankingColor || defaultData.itemRankingColor || '')

const avatarSize = computed(() => layer.value.data.avatarSize || defaultData.avatarSize || '')
const avatarRadius = computed(() => layer.value.data.avatarRadius || defaultData.avatarRadius || '')

const rankingTopIcon = computed(() => [
  layer.value.data.topOneIconImg || defaultData.topOneIconImg,
  layer.value.data.topTwoIconImg || defaultData.topTwoIconImg,
  layer.value.data.topThreeIconImg || defaultData.topThreeIconImg,
  layer.value.data.topOtherIconImg || defaultData.topOtherIconImg,
])

const rankList = computed<IRankItem[]>(() => {
  const data = designState.getLayerData('finishRankings')
  if (data === undefined) {
    customEmits('fetchRankings')
  }
  return data
})
const ranking = computed(() => designState.getLayerData('ranking') || 0)
const isTeam = computed(() => designState.getLayerData('isTeam') || false)

const DEFAULT_HEAD = new URL('./assets/default.png', import.meta.url).href

function emitOnClose() {
  customEmits(EMITS_EVENT_SHOW_HIDE, 'hide')
}
</script>

<template>
  <div class="design-mobile-rankinglist">
    <div class="modal-mask">
      <div
        class="rank-box"
        :style="{
          '--bg-img': bgImg ? `url(${bgImg})` : '',
          '--name-color': nameColor,
          '--current-name-color': currentNameColor,
          '--ranking-color': rankingColor,
          '--border-color': borderColor,
          '--item-ranking-color': itemRankingColor,
          '--avatar-size': `${avatarSize}px`,
          '--avatar-radius': `${avatarRadius}px`,
        }"
      >
        <div class="title">
          <img :src="titleImg" alt="排行榜" />
        </div>
        <div class="mine-ranking">
          <template v-if="ranking">
            {{ isTeam ? '您的队伍获得了第' : '您当前为第' }}
            <strong>{{ ranking }}</strong>
            名
          </template>
          <template v-else>未获得名次</template>
        </div>
        <div class="hr"></div>
        <ul v-if="rankList?.length" class="max-h-[calc(80%-40px)] overflow-y-scroll">
          <li
            v-for="(i, index) in rankList"
            :key="i.id"
            :style="{
              color: ranking === index + 1 ? currentNameColor : nameColor,
            }"
          >
            <div class="seq">
              <img
                v-if="rankingTopIcon[index] || rankingTopIcon[rankingTopIcon.length - 1]"
                :src="rankingTopIcon[index] || rankingTopIcon[rankingTopIcon.length - 1]"
                alt="icon"
              />
              <span v-if="index >= 3">{{ index + 1 }}</span>
            </div>
            <img :src="i.avatarUrl || DEFAULT_HEAD" class="avatar" alt="头像" />
            <p class="nickname line-clamp-1 flex-1">{{ i.nickName || '昵称' }}</p>
            <p class="num">{{ i.ranking }} {{ unit }}</p>
          </li>
        </ul>
        <button class="close" @click="emitOnClose">
          <img :src="closeImg" />
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.modal-mask {
  position: absolute;
  z-index: 9999;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  color: var(--ranking-color);
  transition: all 0.18s ease-in-out;
  padding-top: 200px;
}

.close {
  position: absolute;
  right: 0;
  top: -50px;
  width: 30px;
  height: 30px;
  img {
    width: 100%;
  }
}

.hr {
  height: 1px;
  background: var(--border-color);
  margin: 6px 10%;
}

.rank-box {
  position: relative;
  width: 70%;
  border-radius: 8px;
  height: 340px;
  margin: -20px auto 10px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-image: var(--bg-img, #444);
  .title {
    display: block;
    max-width: 58%;
    margin: 0 auto;
    img {
      width: 100%;
      margin: -26px auto 10px;
    }
  }
  .mine-ranking {
    margin: 0 auto;
    padding-bottom: 10px;
    text-align: center;
    font-weight: normal;
    color: var(--ranking-color);
  }
  ul {
    margin: 15px auto;
    padding: 0 10%;
    li {
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      text-align: center;
      font-size: 12px;

      .seq {
        width: 30px;
        margin-right: 14px;
        text-align: center;
        color: var(--name-color);
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          max-height: 30px;
        }
        span {
          position: absolute;
          color: var(--item-ranking-color, #8a6c23);
        }
      }
      .avatar {
        width: var(--avatar-size);
        height: var(--avatar-size);
        border-radius: var(--avatar-radius);
        object-fit: cover;
        img {
          width: 100%;
        }
      }
      .nickname {
        padding: 2px 5px;
        text-align: left;
      }
      .num {
        width: 50px;
        text-align: right;
        border-left: 1px solid var(--border-color);
        margin-left: 10px;
      }
    }
  }
}
</style>
