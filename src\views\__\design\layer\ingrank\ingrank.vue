<script setup lang="ts">
import { defineCustomEmits, injectScale, useDesignState } from '../..'
import { defaultColor, defaultData, defaultShowIndex, type IDesignIngRank } from './ingrank'

const layer = defineModel<IDesignIngRank>('layer', { required: true })
const customEmits = defineCustomEmits(layer)

const designState = useDesignState()
const scale = injectScale()

interface IDisplayData {
  id: number
  name: string
  avatar: string
  score: number
  progress?: string
  rank: number // 名次
}
const domRef = ref<HTMLElement>()
const domCellBoxRef = ref<HTMLElement>()
const ingrankSize = useElementSize(domRef, undefined, { box: 'border-box' })
const cellBoxSize = useElementSize(domCellBoxRef, undefined, { box: 'border-box' })

const count = ref<number>(10)

const outerData = computed<IDisplayData[]>(() => {
  return designState.getLayerData('ingRankings') || []
})

const showData = computed(() => {
  return outerData.value.slice(0, count.value)
})

watch(
  () => count.value,
  (c) => {
    customEmits('rankingCount', c)
  },
  { immediate: true },
)

const showIndex = computed(() => layer.value.showIndex || defaultShowIndex)

const wapStyle = computed(() => {
  const style = {
    '--base-width': `${ingrankSize.width.value / count.value}px`,
    '--color': layer.value.style.color || defaultColor,
  }
  return style
})

const bgTop = computed(() => layer.value.bgTop ?? defaultData.bgTop)
const bgBody = computed(() => layer.value.bgBody ?? defaultData.bgBody)
const bgBottom = computed(() => layer.value.bgBottom ?? defaultData.bgBottom)

const cellStyle = computed(() => {
  return {
    padding: `${layer.value.paddingTB || 0}% ${layer.value.paddingLR || 0}%`,
    backgroundImage: `url(${bgBody.value})`,
  }
})

watch(
  () => [cellBoxSize.width.value, layer.value.paddingTB, cellBoxSize.height.value],
  () => {
    const pt = (layer.value.paddingTB || 0) / 100
    const gap = 10 * scale.value
    count.value = Math.floor((cellBoxSize.height.value * (1 - pt * 2) + gap) / (36 * scale.value + gap))
  },
  { immediate: true },
)
</script>

<template>
  <div ref="domRef" class="rank-ing-box">
    <div class="wrap" :style="wapStyle">
      <img v-if="bgTop" class="bgimg bgtop" :src="bgTop" alt="">
      <transition-group ref="domCellBoxRef" name="show" tag="div" class="cell-box" :style="cellStyle">
        <div v-for="(item, index) in showData" :key="index" class="cell">
          <div v-if="showIndex" class="num" :style="{ background: layer.rankBgColor }">{{ index + 1 }}</div>
          <div class="header">
            <img :src="item?.avatar" class="avatar-item" :class="[`item-${index}`]">
          </div>
          <div class="nickname limit">{{ item?.name }}</div>
          <div class="score">{{ item?.score }}</div>
        </div>
      </transition-group>
      <img v-if="bgBottom" class="bgimg bgtottom" :src="bgBottom" alt="">
    </div>
  </div>
</template>

<style scoped lang="scss">
.rank-ing-box {
  width: 100%;
  height: 100%;
  background-position:
    top center,
    bottom center;
  background-repeat: no-repeat;
  background-size: contain, contain;
  .wrap {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
  }
  .cell-box {
    display: flex;
    flex: 1;
    flex-direction: column;
    background-repeat: repeat-y;
    padding: 10px 15px;
    background-size: 100%;
    gap: 10px;
    width: 100%;
    height: 100%;
  }
  .cell {
    width: calc(100% - 4px);
    height: 36px;
    opacity: 1;
    display: flex;
    justify-content: space-around;
    align-items: center;
    color: var(--color);
    gap: 2px;
  }
  .num {
    width: 22px;
    height: 22px;
    background: #ecbe7b;
    border-radius: 50%;
    text-align: center;
    line-height: 22px;
    font-size: 12px;
  }
  .header {
    position: relative;
    top: 0;
    left: 0;
    width: 30px;
    height: 30px;
    padding: 2px;
    background: #ecbe7b;
    border-radius: 50%;
    margin-right: 6px;
    flex-shrink: 0;
    .avatar-item {
      width: 100%;
      height: 100%;
      border-radius: 50%;
    }
    img {
      border-radius: 50%;
    }
  }

  .nickname {
    width: 40%;
    flex: 1;
    font-size: 12px;
    flex-shrink: 0;
  }

  .score {
    flex-shrink: 0;
    font-size: 14px;
    font-weight: bold;
    text-align: right;
  }

  .show-enter {
    opacity: 0;
    transform: scale(0);
  }

  .show-enter-active {
    transition: all 1s ease;
  }
  .bgimg {
    width: 100%;
    object-fit: contain;
  }
  .bgtottom {
    object-position: top center;
  }
}
</style>
