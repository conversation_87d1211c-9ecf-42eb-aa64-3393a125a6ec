import type { IMobileAwardsAwards } from '../../__/design/layer/mobile-awards/mobile-awards.vue'

// 设计模式mock数据，主要是一些公共表的数据，各项互动自己的数据在各自的文件中mock

export function mockFinishRankings() {
  return Array.from({ length: 10 }).map((_, index) => {
    return {
      count: index,
      id: index,
      wxUserId: index,
    }
  })
}

export function mockAwards(): IMobileAwardsAwards {
  return {
    name: '一等奖',
    img: 'https://res3.hixianchang.com/qn/material/0/979a797b5dc73ac08f5aff3b94b2ea76.png',
    type: 'REDPACK',
  }
}

export function mockTeamList() {
  return [
    {
      id: '1',
      teamName: '团队1',
      teamHeadImg: 'https://res3.hixianchang.com/qn/material/0/c7f39b0c2a6dff09e80bec80ccf1b0c7.png',
    },
    {
      id: '2',
      teamName: '团队2',
      teamHeadImg: 'https://res3.hixianchang.com/qn/material/0/c7f39b0c2a6dff09e80bec80ccf1b0c7.png',
    },
  ]
}

export function mockRecord() {
  return {
    awardsId: 1,
    redpackAmount: '1',
    status: 'SUCCESS',
    count: 1,
    score: 100,
    teamRanking: 1,
    ranking: 1,
  }
}

export function mockRegedit(teamId = 0) {
  return {
    name: '小明',
    avatar: 'https://res3.hixianchang.com/qn/material/0/c7f39b0c2a6dff09e80bec80ccf1b0c7.png',
    teamId,
  }
}
