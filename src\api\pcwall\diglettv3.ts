import { HiRequest } from '../request'

export default {
  configRead: (params: any) => HiRequest.post('/pro/hxc/web/prodiglettv3config/read.htm', params),

  read: (params: any) => HiRequest.post('/pro/hxc/web/prodiglettv3/read.htm', params),
  go: (params?: any) => HiRequest.post('/pro/hxc/web/prodiglettv3/go.htm', params),
  again: (params?: any) => HiRequest.post('/pro/hxc/web/prodiglettv3/again.htm', params),
  ranking: (params: any) => HiRequest.post('/pro/hxc/web/prodiglettv3/ranking.htm', params),

  regeditPage: (params: any) => HiRequest.post('/pro/hxc/web/prodiglettv3regedit/page.htm', params),
  regeditMembersCnt: (params: any) => HiRequest.post('/pro/hxc/web/prodiglettv3regedit/membersCnt.htm', params),

  switch: (params: any) => HiRequest.post('/pro/hxc/web/prodiglettv3/switch.htm', params),
}
