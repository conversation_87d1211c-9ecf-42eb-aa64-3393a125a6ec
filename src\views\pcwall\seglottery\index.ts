import { timer } from '@/utils'
import { useDesignData, useDesignState, useDesignTemp } from '@/views/__/design'
import { useImData } from '~/src/hooks/useImData'
import { Toast } from '~/src/utils/toast'

// 类型
interface IConfig {
  seglotteryId: number
  listlotteryThemeLimit: 'Y' | 'N'
  listlotteryAdvancedLimit: 'N' | 'Y'
}
type ExtractType = 'SINGLE' | 'MULTI'
type RollOrder = 'DESC' | 'ASC'
type Status = 'NOT_STARTED' | 'WIN_LIST' | 'STARTING' | 'IN' | 'FINISH'
interface IRound {
  id: number
  themeId: number
  title: string
  onceNum?: number
  rollContent: string
  extractType?: ExtractType // 抽奖类型
  rollOrder?: RollOrder // 滚动顺序
  state?: Status
  batchNo?: string // 批次号
}

interface IAward {
  id: number
  type: 'KIND' | 'CASH'
  name: string
  img: string
  mode: 'seglottery'
  count: number
}

type PlayStatus = 'noplay' | 'playing' | 'playover'
interface StageItem {
  key: string
  name: string
  id: number
  startIndex: number
  endIndex: number
  [key: string]: number | string | boolean
}
export function usePcwallSeglottery() {
  const designTemp = useDesignTemp()
  const designData = useDesignData()
  const designState = useDesignState()

  const isDesignEdit = designTemp.isEdit
  const isManage = designTemp.isManage
  const isOem = designTemp.isOem

  const config = ref<IConfig>()
  const round = ref<IRound>()
  const regeditCount = ref(0) // 参与人数量
  const segRollList = ref<any[]>([]) // 滚动数据列表
  // 奖品
  const awards = ref<IAward[]>()
  const awardLeftCount = ref<Record<number, number>>({})
  const totleWinCount = ref(0) // 总中奖人数
  const stageList = ref<StageItem[]>([]) // 导入表格格式
  // 一次抽奖人数
  const onceNum = ref(1)
  const resetLoging = ref(false)

  // 状态
  const temState = ref<string>()
  const status = computed(() => {
    const statusObj = {
      STARTING: 'staring',
      NOT_STARTED: 'ready',
      WIN_LIST: 'winlist',
      IN: 'ing_noroll',
      FINISH: 'finish',
    }
    if (!round.value) return ''

    if (isDesignEdit) {
      return designState.status
    }
    if (temState.value) {
      return temState.value
    }
    return round.value?.state ? statusObj[round.value.state] : ''
  })
  const onceRecordList = ref<any[]>([])
  const allRecordList = ref<any[]>([])

  const roolLabelList = computed(() => {
    return stageList.value.map(item => item.name || item.key)
  })
  const playStatus = ref<PlayStatus[]>([])
  const playEndDaley = computed(() => {
    if (!round.value?.extractType || round.value?.extractType !== 'MULTI') {
      return [0, 0, 0]
    }
    if (round.value?.rollOrder === 'ASC') {
      return [0, 500, 1000]
    } else {
      return [1000, 500, 0]
    }
  })
  // 获取配置
  async function fetchConfig() {
    if (isDesignEdit) {
      await timer(200)
      config.value = { seglotteryId: 1, listlotteryThemeLimit: 'Y', listlotteryAdvancedLimit: 'Y' }
    }
  }

  // 轮次
  async function fetchRound() {
    round.value = undefined
    if (isDesignEdit) {
      await timer(200)
      round.value = {
        id: 1,
        themeId: 1,
        title: '排座抽奖',
        rollContent: '[d1, d2, d3]',
      }
    }
  }

  // 查询主题
  async function fetchTheme() {
    if (isDesignEdit) {
      designTemp.fetchTheme()
    } else {
      const { themeId } = round.value || {}
      if (!themeId) return
      const theme = await api.pcwall.theme.readSimple({ where: { id: themeId } })
      designTemp.theme = theme
      if (theme) {
        designData.setState(JSON.parse(theme.webContent))
      }
    }
  }
  // 查询导入表格式
  async function fetchStageData() {
    try {
      // 开始
      if (isManage || isOem) {
        stageList.value = [
          { key: 'd1', name: '分段1', sort: 1, startIndex: 0, endIndex: 0, id: 1 },
          { key: 'd2', name: '分段2', sort: 1, startIndex: 0, endIndex: 0, id: 2 },
          { key: 'd3', name: '分段3', sort: 1, startIndex: 0, endIndex: 0, id: 3 },
        ]
      } else {
        const list = await api.pcwall.seglottery.stageList({ seglotteryId: round.value?.id })
        if (isDesignEdit) {
          stageList.value = list
        } else {
          const tem: StageItem[] = []
          const rollContent = JSON.parse(round.value?.rollContent || '[]') || []
          rollContent.forEach((item: string) => {
            list.forEach((i: StageItem) => {
              if (i.key === item) tem.push(i)
            })
          })
          stageList.value = tem
        }
      }
    } catch (error: any) {
      Toast.message(error?.message || error?.data?.msg || '操作失败')
      console.error(error)
    }
  }

  function dealShowData(item: any) {
    const list: any[] = []
    stageList.value.forEach((stage) => {
      const key = stage.key
      if (item[key] !== undefined) {
        list.push(item[key])
      }
    })
    return list
  }

  // 查询滚动数据列表
  async function fetchImportList() {
    if (isDesignEdit) {
      const arr = []
      for (let i = 0; i < stageList.value.length; i++) {
        const list: any[] = []
        for (let j = 0; j < 20; j++) {
          list.push(Math.floor(Math.random() * 100).toString())
        }
        arr.push(list)
      }
      segRollList.value = arr
    } else {
      // 查询参与人
      const seglotteryId = round.value?.id
      if (!seglotteryId) return
      const data = await api.pcwall.seglottery.importList({
        where: { seglotteryId },
      })
      const list: any[] = []
      stageList.value.forEach((item) => {
        const key = `${item.key}List`
        if (data[key]) {
          list.push(data[key])
        }
      })
      segRollList.value = list
    }
  }
  async function fetchImportCount() {
    if (isDesignEdit) {
      regeditCount.value = 200
    } else {
      // 查询参与人
      const seglotteryId = round.value?.id
      if (!seglotteryId) return
      const data = await api.pcwall.seglottery.importCnt({
        where: { seglotteryId },
      })
      regeditCount.value = data.count
    }
  }
  // 查询奖品
  async function fetchAward() {
    if (isDesignEdit) {
      awards.value = [{
        type: 'KIND',
        name: '实物奖品',
        img: '',
        mode: 'seglottery',
        count: 1,
        id: 0,
      }]
    } else {
      const moduleId = round.value?.id
      if (!moduleId) return
      const data = await api.pcwall.awards.list({
        where: {
          moduleId,
          module: 'seglottery',
        },
      })
      awards.value = data
    }
  }
  // 开始
  const staring = ref(false)
  async function startFn(showTips?: boolean) {
    try {
      if (isDesignEdit) return
      if (!round.value) return
      if (staring.value) return
      staring.value = true
      if (round.value.state !== 'NOT_STARTED') return
      await fetchImportCount()
      if ((regeditCount.value ?? 0) < onceNum.value) {
        throw new Error('名单数量不足，请调整后重新准备抽奖')
      } else {
        await api.pcwall.seglottery.start({ seglotteryId: round.value.id, onceNum: onceNum.value })
      }
    } catch (error: any) {
      ElNotification({
        title: '错误',
        message: error?.message || error?.data?.msg || '操作失败',
        type: 'error',
        duration: 1500,
      })
      console.log(showTips)
      // Toast.message(error?.message || error?.data?.msg || '操作失败')
      console.log(error)
    }
    await timer(500)
    staring.value = false
  }

  // 跳到结束态
  async function stopFn() {
    try {
      if (isDesignEdit) return
      if (!round.value) return
      if (round.value.state === 'FINISH') return
      // 开始
      await api.pcwall.seglottery.stop({ seglotteryId: round.value.id })
    } catch (error: any) {
      Toast.message(error?.message || error?.data?.msg || '操作失败')
      console.error(error)
    }
  }
  async function finishFn() {
    if (isDesignEdit) return
    if (round.value?.state !== 'FINISH') return
    await updateRemoteState('NOT_STARTED')
  }
  const roolLoading = ref(false)
  async function startRoll() {
    try {
      if (isDesignEdit) return
      if (resetLoging.value) return
      if (!['ing_noroll', 'ing_roll'].includes(status.value)) return
      if (roolLoading.value) return
      roolLoading.value = true
      await pullOnceRecordData()
      if (status.value !== 'ing_roll') {
        temState.value = 'ing_roll'
      }
      if (round.value?.extractType === 'MULTI') {
        playStatus.value = Array.from({ length: stageList.value.length }, () => 'playing')
      } else {
        // 单次抽取
        if (round.value?.rollOrder === 'ASC') {
          // 升序
          const index = playStatus.value.findIndex(item => item === 'noplay')
          if (index !== -1) {
            playStatus.value[index] = 'playing'
          }
        } else {
          // 降序
          let lastIndex = -1
          for (let i = playStatus.value.length - 1; i >= 0; i--) {
            if (playStatus.value[i] === 'noplay') {
              lastIndex = i
              break
            }
          }
          if (lastIndex !== -1) {
            playStatus.value[lastIndex] = 'playing'
          }
        }
        await timer(500)
      }
    } catch (e) {
      console.error(e)
    }
    roolLoading.value = false
  }
  const stopLoading = ref(false)
  async function stopRoll() {
    try {
      if (isDesignEdit) return
      if (status.value !== 'ing_roll') return
      if (resetLoging.value) return
      stopLoading.value = true
      playStatus.value.forEach((item, index) => {
        if (item === 'playing') {
          playStatus.value[index] = 'playover'
        }
      })
      // 如果全部都结束了，跳到结束态
      if (playStatus.value.every(item => item === 'playover')) {
        let rFn: ((value: void | PromiseLike<void>) => void) | null = null
        const promise = new Promise<void>((resolve) => {
          rFn = resolve
        })
        watchOnce(() => round.value?.state, (n) => {
          if (n === 'FINISH') {
            rFn?.()
            temState.value = ''
          }
        })
        await Promise.all([promise, stopFn()])
      } else {
        temState.value = 'ing_noroll'
      }
    } catch (error) {
      console.error(error)
    }
    await timer(500)
    stopLoading.value = false
  }
  async function pullAllRecordData() {
    allRecordList.value = []
    totleWinCount.value = 0
    // 中奖结果
    if (isDesignEdit) {
      // 生成列表
      const dataList = []
      for (let i = 0; i < 200; i++) {
        const obj = { id: i + 1, d1: Math.floor(Math.random() * 100).toString(), d2: Math.floor(Math.random() * 100).toString(), d3: Math.floor(Math.random() * 100).toString() }
        dataList.push(obj)
      }
      dataList.forEach((item: any) => {
        item.namesList = dealShowData(item)
      })
      allRecordList.value = dataList
      totleWinCount.value = 200
    } else {
      if (!round.value) return
      const where: any = {
        moduleId: round.value.id,
        module: 'seglottery',
      }
      const page = await api.pcwall.awards.recordpage({ where, pageIndex: 1, pageSize: 200, sort: { id: 'desc' } })
      totleWinCount.value = page.total
      const list: any = []

      page.dataList.forEach((item: any) => {
        const copyItem = JSON.parse(JSON.stringify(item))
        const remark = item.remark ? JSON.parse(item.remark) : {}
        copyItem.namesList = dealShowData(remark)
        list.push(copyItem)
      })
      allRecordList.value = list
    }
  }
  async function pullOnceRecordData() {
    if (isDesignEdit) {
      // 生成列表
      const dataList = []
      for (let i = 0; i < 200; i++) {
        const obj = { id: i + 1, d1: Math.floor(Math.random() * 100).toString(), d2: Math.floor(Math.random() * 100).toString(), d3: Math.floor(Math.random() * 100).toString() }
        dataList.push(obj)
      }
      dataList.forEach((item: any) => {
        item.namesList = dealShowData(item)
      })
      onceRecordList.value = dataList
    } else {
      if (!round.value) return
      const batchNo = round.value.batchNo || ''
      if (onceRecordList.value.length && onceRecordList.value[0].uuid === batchNo) {
        // 如果当前批次数据已经存在，则不再请求
        console.log('当前批次数据已存在，不再请求')
        return
      }
      if (!round.value?.state || ['NOT_STARTED', 'STARTING'].includes(round.value?.state)) return
      let resultList: any[] = []
      const list: any[] = []

      if (round.value?.state === 'FINISH') {
        const res = await api.pcwall.awards.recordpage({
          where: {
            moduleId: round.value.id,
            module: 'seglottery',
          },
          pageIndex: 1,
          pageSize: 200,
          sort: { id: 'desc' },
        })
        resultList = res.dataList || []
      } else {
        resultList = await api.pcwall.seglottery.result({ seglotteryId: round.value.id })
      }
      resultList.forEach((item: any) => {
        const copyItem = JSON.parse(JSON.stringify(item))
        const remark = item.remark ? JSON.parse(item.remark) : {}
        copyItem.namesList = dealShowData(remark)
        list.push(copyItem)
      })
      onceRecordList.value = list
      onceNum.value = list.length || 1
    }
  }
  function showWinlistFn() {
    if (isDesignEdit) {
      designState.setStatus('winlist')
    } else {
      temState.value = 'winlist'
    }
  }
  function hideWinFn() {
    if (isDesignEdit) {
      designState.setStatus('ready')
    } else {
      temState.value = ''
    }
  }
  async function deleteRemoteWinning(id: number) {
    if (isDesignEdit) return
    await api.pcwall.awards.recorddelete({
      where: { moduleId: round.value?.id, module: 'seglottery', id },
    })
    allRecordList.value = allRecordList.value.filter((i: { id: number }) => i.id !== id)
  }
  // 更新状态
  async function updateRemoteState(state: Status) {
    await api.pcwall.seglottery.updateState({ seglotteryId: round.value?.id, state })
  }
  // function onceNumChange(num: number) {
  //   return () => {
  //     if (onceNum.value + num < 1) return
  //     onceNum.value += num
  //   }
  // }

  async function reset() {
    if (isDesignEdit) return
    if (!round.value || !round.value.id) return
    if (resetLoging.value) return
    resetLoging.value = true
    onceNum.value = round.value?.onceNum || 1
    await api.pcwall.seglottery.reset({ seglotteryId: round.value?.id })
    await timer(1000)
    resetLoging.value = false
  }
  async function fetchAwardsleft() {
    if (isDesignEdit) return
    const data = await api.pcwall.awards.leftCnt({ where: { moduleId: round.value?.id, module: 'seglottery' } })
    return awardLeftCount.value = data
  }
  // async function switchRound(type: 'down' | 'up') {
  //   if (isDesignEdit) return
  //   if (['staring', 'in'].includes(status.value)) {
  //     Toast.message('当前状态不允许切换轮次')
  //     return
  //   }
  //   await api.pcwall.seglottery.switch({ type })
  // }
  // 切换播放状态
  function setPlayStatus() {
    const l = stageList.value.length
    if (playStatus.value.length !== l) {
      playStatus.value = Array.from({ length: l }, () => 'noplay')
    }
    if (['ready', 'staring'].includes(status.value)) {
      playStatus.value = Array.from({ length: l }, () => 'noplay')
    }
    if (status.value === 'finish') {
      playStatus.value = Array.from({ length: l }, () => 'playover')
    }
    if (isDesignEdit) {
      if (['ing_noroll'].includes(status.value)) {
        playStatus.value = Array.from({ length: l }, () => 'noplay')
      }
      if (status.value === 'ing_roll') {
        playStatus.value = Array.from({ length: l }, () => 'playing')
      }
    }
  }
  function rsetData() {
    // 重置数据
    regeditCount.value = 0
    temState.value = ''
    playStatus.value = []
    stageList.value = []
    segRollList.value = []
    onceRecordList.value = []
    allRecordList.value = []
    awards.value = []
    awardLeftCount.value = {}
    totleWinCount.value = 0
    onceNum.value = 1
    resetLoging.value = false
    roolLoading.value = false
    stopLoading.value = false
    staring.value = false
  }
  // 主要作用是将数据暴露给图层组件
  designState
    .setStatusList([
      { label: '准备抽奖', value: 'ready' },
      { label: '准备中', value: 'staring' },
      { label: '进行未滚动', value: 'ing_noroll' },
      { label: '进行中滚动', value: 'ing_roll' },
      { label: '已结束', value: 'finish' },
      { label: '全部中奖名单', value: 'winlist' },
    ])
    .setLayerData({
      '#轮次标题#': computed(() => {
        return round.value?.title || ''
      }),
      '#参与人数#': computed(() => {
        return regeditCount.value
      }),
      // 滚动数据
      'segRollList': computed(() => {
        return segRollList.value
      }),
      // 滚动单位
      'roolLabelList': computed(() => {
        return roolLabelList.value
      }),
      // 单次抽取数量
      '#每次抽取人数#': onceNum,
      // 单次中奖名单
      'onceRecordList': computed(() => {
        return onceRecordList.value
      }),
      // 全部中奖名单
      'allRecordList': computed(() => {
        return allRecordList.value
      }),
      // 奖品列表
      'awards': computed(() => {
        return awards.value?.map(item => (Object.assign({}, item, { count: awardLeftCount.value[item.id] ?? item.count }))) || []
      }),
      // 总中奖人数
      '#总中奖人数#': computed(() => {
        return totleWinCount.value
      }),
      '#奖品名称#': computed(() => {
        return awards.value?.[0]?.name || ''
      }),
      '#奖品数量#': computed(() => {
        const award = awards.value?.[0]
        if (!award) return 0
        return awardLeftCount.value[award.id] || award.count
      }),
      'playStatus': computed(() => {
        return playStatus.value
      }),
      'playEndDaley': computed(() => {
        return playEndDaley.value || [0, 0, 0]
      }),
    })
    // 图层组件事件
    .setLayerEventList([
      // 事件
      {
        eventId: 'start',
        name: '准备数据',
        value: () => startFn(true),
        status: ['ready'],
      },
      {
        eventId: 'reset',
        name: '刷新状态',
        value: reset,
        status: ['ing_noroll'],
      },
      {
        eventId: 'startRoll',
        name: '开始滚动',
        value: startRoll,
        status: ['ing_noroll'],
      },
      {
        eventId: 'stopRoll',
        name: '停止滚动',
        value: stopRoll,
        status: ['ing_roll'],
      },
      {
        eventId: 'over',
        name: '结束',
        value: finishFn,
        status: ['finish'],
      },
      {
        eventId: 'showWinlist',
        name: '打开中奖名单',
        value: showWinlistFn,
        status: ['ready', 'staring', 'ing_noroll', 'finish'],
      },
      {
        eventId: 'hideWinlist',
        name: '关闭中奖名单',
        value: hideWinFn,
        status: ['winlist'],
      },
      // {
      //   eventId: 'upRound',
      //   name: '上一轮',
      //   value: () => switchRound('up'),
      //   status: ['ready'],
      // },
      // {
      //   eventId: 'downRound',
      //   name: '下一轮',
      //   value: () => switchRound('down'),
      //   status: ['ready'],
      // },
      {
        // 中奖名单组件，取消中奖
        eventId: 'winning-delete',
        value: async (awardId) => {
          await deleteRemoteWinning(awardId)
          if (status.value === 'ready') {
            fetchAwardsleft()
          }
        },
      },

    ])

  // 变化监控
  // watch(
  //   () => round.value?.id,
  //   async (n, o) => {
  //     if (n === o) return
  //     onceNum.value = round.value?.onceNum || 1
  //     fetchAward()
  //   },
  //   { immediate: true },
  // )
  watch(() => config.value?.seglotteryId, () => {
    fetchRound()
    // fetchRoundList()
  })

  watch(() => round.value?.themeId, () => {
    fetchTheme()
  })
  // 变化监控
  watch(
    () => [status.value, round.value?.id] as const,
    async (current, previous) => {
      if (!round.value) return
      const [v, newId] = current || []
      const [o, oldId] = previous || []
      if (v === o && newId === oldId) return
      if (newId !== oldId) {
        // 重置数据
        rsetData()
        onceNum.value = round.value?.onceNum || 1
        fetchAward()
        fetchImportCount()
      }
      if (!stageList.value.length) {
        await fetchStageData()
        await fetchImportList()
      }
      setPlayStatus()
      if (v === 'ready') {
        await Promise.all([fetchAwardsleft()])
        await startFn()
      }
      if (v === 'winlist') {
        await pullAllRecordData()
      }
      if (v === 'staring') {

      }
      if (['ing_noroll'].includes(v)) {
        await pullOnceRecordData()
      }
      if (v === 'finish') {
        await pullOnceRecordData()
      }
      designState.setStatus(status.value)
    },
    { immediate: true },
  )
  // 数据同步
  useImData({
    'im:seglottery:config': config,
    'im:seglottery': computed({
      get() { return round.value },
      async set(v) {
        if (v?.id !== round.value?.id) {
          round.value = undefined
          await nextTick()
        }
        round.value = v
      },
    }),
  })

  tryOnMounted(async () => {
    if (isDesignEdit) {
      fetchConfig()
    }
  })
}
