import { HiRequest } from '../request'

export default {
  read: (params: any) => HiRequest.post('/pro/hxc/web/prolistlotteryv3/read.htm', params),
  start: (params?: any) => HiRequest.post('/pro/hxc/web/prolistlotteryv3/start.htm', params),
  list: (params?: any) => HiRequest.post('/pro/hxc/web/prolistlotteryv3/list.htm', params),
  stop: (params?: any) => HiRequest.post('/pro/hxc/web/prolistlotteryv3/stop.htm', params),
  next: (params?: any) => HiRequest.post('/pro/hxc/web/prolistlotteryv3/next.htm', params),
  poolList: (params: any) => HiRequest.post('/pro/hxc/web/prolistlotteryv3/poolList.htm', params),
  switch: (params: any) => HiRequest.post('/pro/hxc/web/prolistlotteryv3/switch.htm', params),
  updateState: (params: any) => HiRequest.post('/pro/hxc/web/prolistlotteryv3/updateState.htm', params),
  importformList: (params: any) => HiRequest.post('/pro/hxc/web/prolistlotteryv3importform/list.htm', params),
  updateLottery: (params: any) => HiRequest.post('/pro/hxc/web/prolistlotteryv3config/updateLottery.htm', params),
}
