<script setup lang="ts">
// import MaterialThumbnail from '../../components/material-thumbnail.vue'
// const layer = defineModel<IDesignMicrositeClassic>('layer', { required: true })
// const bgImgBind = useDataAttr(layer.value, 'bgImg', '')

// type IType = 'bgImg'
// async function updateMaterialFn(name: IType , isReset: boolean = false) {
//   const result = await openSelectMaterial('PIC')
//   if (result) {
//     layer.value[name] = result
//   }
// }
</script>

<template>
  <div class="setting-block">
  </div>
</template>

<style scoped lang="scss"></style>
