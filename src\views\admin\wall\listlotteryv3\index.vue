<script setup lang="ts">
import HiDesign from 'design/index.vue'
import { useDesignTemp } from '~/src/views/__/design'
import { ModeEnum } from '~/src/views/__/design/types'
import Mobile from './components/mobile.vue'
import Pcwall from './components/pcwall.vue'

definePage({ meta: { label: '名单抽奖' } })

const designTemp = useDesignTemp()

const interactive = 'listlotteryv3'

const mode = ModeEnum.edit
</script>

<template>
  <HiDesign :interactive="interactive" :mode="mode" />
  <Pcwall v-if="designTemp.showType === 'pcwall'" />
  <Mobile v-if="designTemp.showType === 'mobile'" />
</template>
