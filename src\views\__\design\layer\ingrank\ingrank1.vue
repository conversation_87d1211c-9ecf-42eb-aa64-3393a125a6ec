<script setup lang="ts">
import { useImageInfo } from '~/src/hooks/useImageInfo'
import { defineCustomEmits, injectScale, useDesignState } from '../..'
import { processStyle } from '../../utils'
import { defaultData, defaultWidth, type IDesignIngRank1 } from './ingrank1'

const layer = defineModel<IDesignIngRank1>('layer', { required: true })
const customEmits = defineCustomEmits(layer)
const scale = injectScale()
const designState = useDesignState()

interface IDisplayData {
  id: number
  name: string
  avatar: string
  score: number
  progress?: string
  rank: number // 名次
}
const domRef = ref<HTMLElement>()
const ingrankSize = useElementSize(domRef)

const count = ref<number>(5)

const outerData = computed<IDisplayData[]>(() => {
  return designState.getLayerData('ingRankings') || []
})

const showData = computed(() => {
  return outerData.value.slice(0, count.value)
})

watch(
  () => count.value,
  (c) => {
    customEmits('rankingCount', c)
  },
  { immediate: true },
)

const bgImg = computed(() => layer.value.itemBgImg ?? defaultData.itemBgImg)
const itemBgImgInfo = useImageInfo(bgImg)

const itemHeight = computed(() => {
  return ingrankSize.width.value / itemBgImgInfo.ratio.value
})

const itemStyle = computed(() => {
  return {
    height: `${itemHeight.value}px`,
  }
})

const baseScale = computed(() => {
  return ingrankSize.width.value / defaultWidth / scale.value
})

const headerStyle = computed(() => {
  return processStyle({
    width: `${(layer.value.avatarSize ?? defaultData.avatarSize)! * baseScale.value}px`,
    height: `${(layer.value.avatarSize ?? defaultData.avatarSize)! * baseScale.value}px`,
    top: `${(layer.value.avatarY ?? defaultData.avatarY)! * baseScale.value}px`,
    left: `${(layer.value.avatarX ?? defaultData.avatarX)! * baseScale.value}px`,
  }, scale)
})

const contentStyle = computed(() => {
  return processStyle({
    left: `${(layer.value.textContentX ?? defaultData.textContentX)! * baseScale.value}px`,
    top: `${(layer.value.textContentY ?? defaultData.textContentY)! * baseScale.value}px`,
    width: `${(layer.value.textContentWidth ?? defaultData.textContentWidth)! * baseScale.value}px`,
    fontSize: `${(layer.value.textSize ?? defaultData.textSize)! * baseScale.value}px`,
    color: layer.value.textColor ?? defaultData.textColor,
  }, scale)
})
const wapStyle = computed(() => {
  const style = {}
  return style
})

watch(
  () => [ingrankSize.width.value, ingrankSize.height.value, itemBgImgInfo.ratio.value],
  () => {
    count.value = Math.floor((ingrankSize.height.value) / (itemHeight.value))
  },
  { immediate: true },
)
</script>

<template>
  <div ref="domRef" class="rank-ing-box">
    <div class="wrap" :style="wapStyle">
      <transition-group name="show" tag="div" class="cell-box">
        <div v-for="(item, index) in showData" :key="index" class="cell" :style="itemStyle">
          <img :src="bgImg" class="bg-box">
          <div class="header" :style="headerStyle">
            <img v-if="item?.avatar" :src="item?.avatar" class="avatar-item" :class="[`item-${index}`]">
          </div>
          <div class="content felx-j-sb flex" :style="contentStyle">
            <div class="nickname limit">
              {{ item?.name }}
            </div>
            <div class="score">{{ item?.score }}</div>
          </div>
        </div>
      </transition-group>
    </div>
  </div>
</template>

<style scoped lang="scss">
.rank-ing-box {
  width: 100%;
  height: 100%;
  .wrap {
    height: 100%;
    width: 100%;
  }
  .cell-box {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
  }
  .cell {
    width: 100%;
    height: 36px;
    transform: scale(1);
    opacity: 1;
    display: flex;
    justify-content: space-around;
    align-items: center;
    color: var(--color);
    .bg-box {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      background-position: center;
      background-size: cover;
      background-repeat: no-repeat;
      position: relative;
    }
  }
  .header {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    .avatar-item {
      width: 100%;
      height: 100%;
      border-radius: 50%;
    }
  }

  .nickname {
    flex-shrink: 0;
    max-width: 70%;
  }

  .content {
    position: absolute;
    z-index: 2;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    .score {
      flex-shrink: 0;
      font-weight: bold;
      text-align: right;
      max-width: 30%;
      overflow: hidden;
    }
    .show-enter {
      opacity: 0;
      transform: scale(0);
    }

    .show-enter-active {
      transition: all 1s ease;
    }
  }
}
</style>
