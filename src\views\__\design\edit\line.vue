<script setup lang="ts">
import type { CSSProperties } from 'vue'

// 拖拽调整尺寸
const props = withDefaults(
  defineProps<{
    width?: number
    height?: number
    min?: number
  }>(),
  {
    width: 1,
    min: 250,
  },
)
const modelValue = defineModel<number>()
const lineStyle = computed(() => {
  const style: CSSProperties = {}
  if (props.width) {
    Object.assign(style, {
      width: `${props.width}px`,
      height: '100%',
    })
  }
  if (props.height) {
    Object.assign(style, {
      width: '100%',
      height: `${props.height}px`,
    })
  }
  return style
})

function mousedownFn(e: MouseEvent) {
  e.stopPropagation()
  e.preventDefault()

  const oldValue = modelValue.value
  if (!oldValue) return
  const startX = e.clientX
  const startY = e.clientY

  let dx = 0
  let dy = 0
  const move = (e: MouseEvent) => {
    dx = startX - e.clientX
    dy = startY - e.clientY

    let newValue = null
    if (props.width) {
      newValue = oldValue + dx
    } else if (props.height) {
      newValue = oldValue + dy
    }

    if (newValue === null) return
    if (newValue > props.min) {
      modelValue.value = newValue
    }
  }

  const up = () => {
    document.removeEventListener('mousemove', move)
    document.removeEventListener('mouseup', up)
    if (!modelValue.value) return

    if (modelValue.value < 250) {
      modelValue.value = 250
    }
  }

  document.addEventListener('mousemove', move)
  document.addEventListener('mouseup', up)
}
</script>

<template>
  <div class="line-box" :style="lineStyle" @mousedown="mousedownFn">
  </div>
</template>

<style scoped lang="scss">
.line-box {
  cursor: col-resize;
}
</style>
