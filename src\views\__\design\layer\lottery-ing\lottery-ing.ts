import type { IDesignLayer, IDesignSetup } from '../../types'
import { BisTypes, useDesignData } from '../..'
import { InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './lottery-ing-setting.vue'
import Comp from './lottery-ing.vue'
// 类型
export const type = 'lottery-ing'
export const defaultHeadCount = 250
export const defaultrotateSpeed = 5
export const defaultHeadSize = 12
export const defaultIsScaleAnimation = true
export const defaultPlaceHolderHeadImg = new URL('./assets/placehoder.png', import.meta.url).href
// 数据类型约束
export interface IDesignLotteryIng extends IDesignLayer {
  type: typeof type
  headCount?: number
  headSize?: number
  rotateSpeed?: number
  isScaleAnimation?: boolean
  placeHolderHeadImg?: string
  data: {
    name: string
    avatar: string
    score: number
  }[]
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.sportsIng,
    showType: ['pcwall'],
    status: ['ing', 'ready'],
    showInteractive: [InteractiveEnum.lotteryv3, InteractiveEnum.piclotteryv3],
    type,
    name: '抽奖3d效果',
    thumbnail: new URL('./lottery-ing.png', import.meta.url).href,
    Comp,
    CompSetting,
    defaultData() {
      const designData = useDesignData()
      const [width, height] = designData.option.drafts
      return {
        type,
        uuid: layerUuid(),
        name: '抽奖3d效果',
        data: [],
        style: {
          width: `${width}px`,
          height: `${height}px`,
          top: 0,
          left: 0,
        },
      }
    },
  })
}
