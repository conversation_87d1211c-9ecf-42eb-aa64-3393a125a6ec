// @ts-nocheck
function dealScroll() {
// key: identifier   value: screenY
  const startTouchObj = {}
  const dealTouchs = (touches) => {
    const nowTouchObj = {}
    for (let i = 0, len = touches.length; i < len; i++) {
      const touch = touches[i]
      const { identifier, screenY } = touch
      nowTouchObj[identifier] = screenY
      if (!startTouchObj[identifier]) {
        startTouchObj[identifier] = screenY
      }
    }
    Object.keys(startTouchObj).forEach((key) => {
      if (!nowTouchObj[key]) {
        delete startTouchObj[key]
      }
    })
    return nowTouchObj
  }
  const dealScroll = (e, eventName) => {
    if (e.target.closest('#__vconsole') || e.target.closest('.__ignore')) {
      return
    }
    let hasScroll = false
    let scrollDom = null
    let target = e.target.parentNode

    while (true) {
      if (target) {
        if (target === document.body || target === window) {
          break
        }
        const style = window.getComputedStyle(target)
        if (
          style.overflow === 'auto'
          || style.overflow === 'scroll'
          || style.overflowY === 'auto'
          || style.overflowY === 'scroll'
        ) {
          hasScroll = true
          scrollDom = target
          break
        }
        target = target.parentNode
      } else {
        break
      }
    }
    if (hasScroll) {
      const isFirst = !!Object.keys(startTouchObj)
      // 在顶部或者在底部
      const isTop = scrollDom.scrollTop === 0
      const isBottom = scrollDom.scrollTop + scrollDom.offsetHeight === scrollDom.scrollHeight
      if (isFirst && (isTop || isBottom)) {
        const nowTouchObj = dealTouchs(e.touches)
        let offset = 0
        Object.keys(nowTouchObj).forEach(key => (offset += nowTouchObj[key] - startTouchObj[key]))
        // 解决dom节点进行了z轴旋转180的问题，滚动条刚好反向
        if (scrollDom.dataset.image !== undefined) {
          if ((isTop && offset < 0) || (isBottom && offset > 0)) {
            e.preventDefault()
          }
        } else {
          if ((isTop && offset > 0) || (isBottom && offset < 0)) {
            e.preventDefault()
          }
        }
      }
    } else {
      if (eventName === 'touchstart') {
        if (!['input', 'textarea', 'select', 'video'].includes(e.target.nodeName.toLowerCase())) {
          if (!e.target.closest('a') && !e.target.closest('button') && !e.target.closest('label')) {
            e.preventDefault()
          }
        }
      } else {
        e.preventDefault()
      }
    }
  }
  document.removeEventListener('touchstart', window.globalDealScroll, { passive: false, capture: true })
  document.removeEventListener('touchmove', window.globalDealScroll, { passive: false, capture: true })
  document.removeEventListener('touchend', window.globalDealScroll, { passive: false, capture: true })
  document.addEventListener('touchstart', e => dealScroll(e, 'touchstart'), { passive: false, capture: true })
  document.addEventListener('touchmove', e => dealScroll(e, 'touchmove'), { passive: false, capture: true })
  document.addEventListener('touchend', e => dealTouchs(e, 'touchend'), { passive: false, capture: true })
}

export default dealScroll
