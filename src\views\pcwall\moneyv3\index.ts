import { randomAvatar, timer } from '@/utils'
import { fenToYuan } from '@/utils/math'
import { useDesignData, useDesignState, useDesignTemp } from '@/views/__/design'
import { faker } from '@faker-js/faker/locale/zh_CN'
import Big from 'big.js'
import { debounce } from 'lodash-es'
import { useImData } from '~/src/hooks/useImData'
import { envUtils } from '~/src/utils/env'
import { Toast } from '~/src/utils/toast'
import { useParticipantStore, usePcwallStore } from '..'

// 类型
interface IPage<T> {
  total: number
  totalPages: number
  pageIndex: number
  pageSize: number
  dataList: T[]
}
interface IConfig {
  moneyv3Id: number
  moneyThemeLimit: 'Y' | 'N'
  moneyAdvancedLimit: 'Y' | 'N'
}
interface IRound {
  id: number
  gameMode?: 'SPORTS' | 'SPLIT_MONEY'
  themeId: number
  title: string
  moneyTime: number
  startTime?: number
  endTime?: number
}
interface IRegedit {
  name: string
  avatar: string
}
interface IRanking {
  id: number
  name: string
  avatar: string
  score: number
  redpackAmount?: number
}

interface IApiRanking {
  id: number
  teamId?: string
  wxUserId?: number
  count: number
  redpackAmount?: number
}

export function usePcwallMoneyv3() {
  const timestamp = useTimestamp()

  const designTemp = useDesignTemp()
  const designData = useDesignData()
  const designState = useDesignState()

  const pcwallStore = usePcwallStore()
  const participantStore = useParticipantStore()

  const isDesignEdit = designTemp.isEdit

  const config = ref<IConfig>()
  const round = ref<IRound>()

  // 剩余金额
  const leftCount = ref(0)

  const isPersonalMode = computed(() => {
    return designState.gameMode === '$游戏模式-竞技$' || round.value?.gameMode === 'SPORTS'
  })
  const isSplitMoneyMode = computed(() => {
    if (isPersonalMode.value) return false
    return designState.gameMode === '$游戏模式-分钱$' || round.value?.gameMode === 'SPLIT_MONEY'
  })

  // 参与人
  let regeditTimer: NodeJS.Timeout | undefined
  const regeditPage = ref<IPage<IRegedit>>()
  const regeditCount = ref(10)

  // 排行榜
  let rankingsTimer: NodeJS.Timeout | undefined
  const rankingCount = ref(10)
  const rankings = ref<IRanking[]>()
  const finishRankingCount = ref(0)
  const finishRankings = ref<IRanking[]>()

  // 奖品
  const awards = ref<any[]>()
  const awardsType = computed(() => {
    return awards.value?.filter(item => item.type !== 'KIND').length ? 'CASH' : 'KIND'
  })

  // 名字
  const getName = (wxUserId: number) => {
    if (pcwallStore.isSignName) {
      return participantStore.getSignName(wxUserId)
    }
    return participantStore.getWxName(wxUserId)
  }

  // 奖品
  const getAwardObj = computed(() => {
    if (isDesignEdit) {
      return {
        type: 'REDPACK',
        redpackAmount: 1000, // 红包金额
        singleAmount: 1, // 每一积分奖励金额
        redpackSellername: '主办方',
        forbidDraw: 'N', // 禁止抽中
        mode: 'FIXED',
        count: 1,
      }
    }
    return awards.value?.[0] || {}
  })

  // 状态
  const status = computed(() => {
    if (!round.value) return ''

    if (isDesignEdit) {
      if (rankingsTimer && (round.value?.endTime || 0) < timestamp.value) {
        clearInterval(rankingsTimer)
        rankingsTimer = undefined
      }
      return designState.status
    }
    // ready
    if (!round.value.startTime) {
      return 'ready'
    }
    const offsetStart = timestamp.value - round.value.startTime
    if (offsetStart < -3000) {
      return 'ready'
    }
    // 321
    if (offsetStart > -3000 && offsetStart < 0) {
      return '321'
    }
    // ing
    if (!round.value.endTime) {
      console.warn('活动进行中结束时间未设置')
      return 'ing'
    }
    const offsetEnd = timestamp.value - round.value.endTime
    if (offsetEnd < 0) {
      return 'ing'
    }
    // finish
    return 'finish'
  })

  // 进行中时间进度
  const timeProgress = computed(() => {
    if (!round.value?.startTime) return 0
    const progress = (timestamp.value - round.value.startTime) / (round.value.moneyTime * 1000)
    if (progress < 0) return 0
    if (progress > 1) return 1
    return progress
  })

  // 获取配置
  async function fetchConfig() {
    if (isDesignEdit) {
      await timer(200)
      config.value = { moneyv3Id: 1, moneyThemeLimit: 'Y', moneyAdvancedLimit: 'Y' }
    }
  }

  // 轮次
  async function fetchRound() {
    round.value = undefined
    if (isDesignEdit) {
      await timer(200)
      round.value = {
        id: 1,
        themeId: 1,
        title: '大家来抢钱',
        moneyTime: 30,
      }
    }
  }

  // 查询主题
  async function fetchTheme() {
    if (isDesignEdit) {
      designTemp.fetchTheme()
    } else {
      const { themeId } = round.value || {}
      if (!themeId) return
      const theme = await api.pcwall.theme.readSimple({ where: { id: themeId } })
      designTemp.theme = theme
      if (theme) {
        designData.setState(JSON.parse(theme.webContent))
      }
    }
  }

  // 查询参与人列表
  async function fetchRegedit() {
    if (isDesignEdit) {
      await timer(200)

      if ((regeditPage.value?.total || 0) > 16) return

      const random = Math.random() * 10 | 0
      const total = (regeditPage.value?.total || 0) + random
      const pageSize = regeditCount.value

      // 生成列表
      if (envUtils.isPlayWright) {
        faker.seed(1)
      }
      const dataList = []
      for (let i = 0; i < Math.min(total, pageSize); i++) {
        dataList.push({ name: faker.person.fullName(), avatar: randomAvatar(i) })
      }

      regeditPage.value = {
        total,
        totalPages: Math.ceil(total / pageSize),
        pageIndex: 1,
        pageSize,
        dataList,
      }
    } else {
      // 查询参与人
      const moneyv3Id = round.value?.id
      if (!moneyv3Id) return
      const page = await api.pcwall.moneyv3.regeditPage({
        pageIndex: 1,
        pageSize: regeditCount.value,
        where: { moneyv3Id },
      })
      // 团队赛
      await participantStore.relationParticipant(page.dataList)

      const list: any = []
      page.dataList.forEach(({ wxUserId }: { wxUserId: number }) => {
        const name = getName(wxUserId)
        const avatar = participantStore.getAvatar(wxUserId)
        list.push({ name, avatar })
      })
      page.dataList = list
      // 出现定时调用的地方最好有判断，防止数据被重复覆盖触发页面重新渲染
      if (JSON.stringify(regeditPage.value) !== JSON.stringify(page)) {
        regeditPage.value = page
      }
    }
  }

  // 查询奖品
  async function fetchAward() {
    if (isDesignEdit) {
      return
    }
    const moduleId = round.value?.id
    if (!moduleId) return
    const data = await api.pcwall.awards.list({
      where: {
        moduleId,
        module: 'moneyv3',
      },
    })
    awards.value = data
  }

  // 开始
  async function startFn() {
    try {
      if (!round.value) return
      await checkUnLockTheme()
      await checkUnLockAwards()
      // 开始
      if (isDesignEdit) {
        await timer(200)
        const now = timestamp.value
        round.value.startTime = now + 4000
        round.value.endTime = round.value.startTime + (round.value.moneyTime * 1000)
      } else {
        // 判断参与人数
        if (!regeditPage.value?.total) {
          await Toast.message('参与人数不足')
          return
        }
        // 接口
        await api.pcwall.moneyv3.go()
      }
    } catch (error: any) {
      Toast.message(error?.message || error?.data?.msg || '操作失败')
      console.error(error)
    }
  }

  // 进行中排名
  async function fetchRankings() {
    // 进行中排名 目前默认10，后续会做成可变的
    if (isDesignEdit) {
      await timer(200)
      if (
        round.value?.endTime && timeProgress.value === 1
        && rankingCount.value === (rankings.value?.length || 0)
      ) {
        return
      }
      const oldList = rankings.value || []
      const list = []
      if (envUtils.isPlayWright) {
        faker.seed(1)
      }
      for (let i = 0; i < rankingCount.value; i++) {
        const old = oldList[i]
        list.push({
          id: i + 1,
          name: old?.name || faker.person.fullName(),
          avatar: old?.avatar || randomAvatar(),
          score: (old?.score || 0) + Math.random() * 10 | 0,
        })
      }

      // 计算总奖励金额 = 每个人的得分 * 单个金额的总和
      const totalAwardAmount = list.reduce((sum, item) => {
        const itemAmount = new Big(item.score).mul(getAwardObj.value.singleAmount)
        return sum.plus(itemAmount)
      }, new Big(0))

      // 剩余金额 = 总红包金额 - 已发放金额,不能小于0
      leftCount.value = Math.max(new Big(getAwardObj.value.redpackAmount).minus(totalAwardAmount).toNumber(), 0)

      rankings.value = list

      // 奖池金额为0时，停止排行榜
      if (leftCount.value === 0 && isSplitMoneyMode.value) {
        clearInterval(rankingsTimer)
        rankingsTimer = undefined
      }
    } else {
      let result: IApiRanking[] = []
      const where: Record<string, any> = { moneyv3Id: round.value?.id, showNum: rankingCount.value || 10 }
      // fix 保证结束状态时排行数据一定为最终数据
      if (status.value === 'finish') {
        where.state = 'FINISH'
      }
      // 结束后结果处理需要时间
      for (let i = 0; i < 10; i++) {
        try {
          const res = await api.pcwall.moneyv3.ranking({ where })
          result = res.ranking || []
          leftCount.value = res.leftCount
          break
        } catch (e) {
          if (where.state) {
            await timer(2000)
          } else {
            console.warn(e)
          }
        }
      }

      // name, avatar, score
      const list: IRanking[] = []

      await participantStore.relationParticipant(result as { wxUserId: number }[])
      for (const { wxUserId, count, redpackAmount } of result) {
        if (!wxUserId) continue
        const id = wxUserId
        const name = getName(wxUserId)
        const avatar = participantStore.getAvatar(wxUserId)
        list.push({ id, name, avatar, score: count, redpackAmount })
      }
      rankings.value = list
    }
  }
  const fetchFinishRankings = debounce(async () => {
    if (!finishRankingCount.value) return
    // 结束排名
    if (isDesignEdit) {
      const oldList = rankings.value || []

      if (finishRankings.value === undefined) {
        finishRankings.value = []
      }
      // last score
      const lastScore = oldList[oldList.length - 1]?.score || 100
      if (envUtils.isPlayWright) {
        faker.seed(1)
      }
      for (let i = 0; i < (finishRankingCount.value - finishRankings.value.length) / 2; i++) {
        let score = lastScore - Math.random() * 3 | 0
        if (score < 10) score = 10
        finishRankings.value.push({ id: i, name: faker.person.fullName(), avatar: randomAvatar(), score })
      }
    } else {
      for (let i = 0; i < 10; i++) {
        try {
          // 如果异常表明后端还未入库, 需要在此请求直到有结果
          const res = await api.pcwall.moneyv3.ranking({
            where: {
              moneyv3Id: round.value?.id,
              showNum: finishRankingCount.value,
              state: 'FINISH',
            },
          })
          const result: IApiRanking[] = res.ranking || []

          // name, avatar, score
          const list: IRanking[] = []

          await participantStore.relationParticipant(result as { wxUserId: number }[])
          for (const { wxUserId, count } of result) {
            if (!wxUserId) continue
            const id = wxUserId
            const name = getName(wxUserId)
            const avatar = participantStore.getAvatar(wxUserId)
            list.push({ id, name, avatar, score: count })
          }
          // 排序
          list.sort((a, b) => b.score - a.score)
          finishRankings.value = list
          break
        } catch {
          await timer(2000)
        }
      }
    }
  }, 300)

  // 再来一轮
  async function againFn() {
    try {
      if (awardsType.value !== 'KIND') return
      if (!config.value) return
      await checkUnLockTheme()
      await checkUnLockAwards()
      if (isDesignEdit) {
        config.value.moneyv3Id++
      } else {
        await api.pcwall.moneyv3.again()
      }
    } catch (error: any) {
      Toast.message(error?.message || error?.data?.msg || '操作失败')
      console.error(error)
    }
  }

  // 检查主题是否解锁
  async function checkUnLockTheme() {
    return new Promise<void>((resolve, reject) => {
      if (isDesignEdit) resolve()
      const freeTag = designTemp.theme?.freeTag
      if (freeTag === 'Y') resolve()
      if (config.value?.moneyThemeLimit === 'Y') {
        resolve()
      } else {
        reject(new Error('主题未解锁'))
      }
    })
  }
  // 检查主题是否解锁
  async function checkUnLockAwards() {
    return new Promise<void>((resolve, reject) => {
      if (isDesignEdit) resolve()
      if (config.value?.moneyAdvancedLimit === 'N' && awards.value?.length) {
        reject(new Error('高级功能未解锁，奖品无法使用'))
      } else {
        resolve()
      }
    })
  }
  // 主要作用是将数据暴露给图层组件
  designState
    .setStatusList([
      { label: '准备中', value: 'ready' },
      { label: '倒计时', value: '321' },
      { label: '进行中', value: 'ing' },
      { label: '排行榜', value: 'finish' },
    ])
    .setLayerData({
      '#轮次标题#': computed(() => {
        return round.value?.title || ''
      }),
      '#参与人数#': computed(() => {
        return regeditPage.value?.total || 0
      }),
      '#奖池金额#': computed(() => {
        if (status.value === 'ready') {
          return fenToYuan(getAwardObj.value?.redpackAmount ?? 0)
        }
        return fenToYuan(leftCount.value || 0)
      }),
      '$中途$': computed(() => {
        return timeProgress.value > 0.4 && timeProgress.value < 0.6
      }),
      '$游戏模式-竞技$': computed(() => isPersonalMode.value),
      '$游戏模式-分钱$': computed(() => isSplitMoneyMode.value),
      'regeditList': computed(() => {
        return regeditPage.value?.dataList || []
      }),
      'downFlagTime': computed(() => {
        if (designState.status === '321') {
          return round.value?.startTime
        } else if (designState.status === 'ing') {
          return round.value?.endTime
        } else {
          return 0
        }
      }),
      // 进行中时间进度
      'ingTimeProgress': timeProgress,
      'ingRankings': computed(() => {
        // 处理分钱模式返回的排行榜
        if (isSplitMoneyMode.value) {
          return rankings.value?.map((item) => {
            const money = (item.score ?? 0) * (getAwardObj.value?.singleAmount ?? 0)
            return {
              ...item,
              score: `${fenToYuan(money)}元`,
            }
          })
        }
        return rankings.value
      }),
      'endRankings': computed(() => {
        return finishRankings.value
      }),
      'gameTime': computed(() => {
        return round.value?.moneyTime
      }),
    })
    // 图层组件事件
    .setLayerEventList([
      // 数量
      {
        eventId: 'regedit-count',
        value(data: number) {
          regeditCount.value = Math.max(regeditCount.value, data)
        },
      },
      {
        eventId: 'rankingCount',
        value(data: number) {
          if (data > rankingCount.value) {
            rankingCount.value = data
          }
        },
      },
      {
        eventId: 'finishRankingCount',
        value(data: number) {
          if (data > finishRankingCount.value) {
            finishRankingCount.value = data
            fetchFinishRankings()
          }
        },
      },
      // 事件
      {
        eventId: 'start',
        name: '开始',
        value: startFn,
        status: ['ready'],
      },
      {
        eventId: 'again',
        name: '再来一轮',
        value: againFn,
        status: computed(() => awardsType.value === 'KIND' ? ['finish'] : []),
      },
    ])

  // 变化监控
  watch(
    () => [status.value, round.value?.id] as const,
    async ([v]) => {
      if (v === 'ready') {
        clearInterval(regeditTimer)
        regeditTimer = setInterval(fetchRegedit, 2000)
        fetchRegedit()
      } else {
        clearInterval(regeditTimer)
        regeditTimer = undefined
      }

      if (v === '321') {
        if (isDesignEdit && round.value) {
          round.value.startTime = timestamp.value + 3000
          round.value.endTime = timestamp.value + 3000 + 30 * 1000
        }
      }

      if (v === 'ing') {
        if (isDesignEdit && round.value) {
          leftCount.value = getAwardObj.value.redpackAmount
          rankings.value = []
          round.value.moneyTime = 30
          round.value.startTime = timestamp.value - 3000
          round.value.endTime = timestamp.value - 3000 + round.value.moneyTime * 1000
        }
        clearInterval(rankingsTimer)
        rankingsTimer = setInterval(fetchRankings, 1000)
        fetchRankings()
      } else {
        clearInterval(rankingsTimer)
        rankingsTimer = undefined
      }

      if (v === 'finish') {
        if (isDesignEdit && round.value) {
          round.value.startTime = timestamp.value - round.value.moneyTime * 1000
          round.value.endTime = timestamp.value
        }
        await fetchRankings()
      }
      designState.setStatus(status.value)
    },
    { immediate: true },
  )
  watch(() => config.value?.moneyv3Id, fetchRound)
  watch(() => round.value?.id, async () => {
    if (isDesignEdit) return
    // 重置数据
    regeditPage.value = undefined
    regeditCount.value = 3
    rankings.value = undefined
    finishRankingCount.value = 0
    finishRankings.value = undefined
    await fetchAward()
  })
  watch(() => round.value?.themeId, fetchTheme)

  // 数据同步
  useImData({
    'im:moneyv3:config': config,
    'im:moneyv3': computed({
      get() { return round.value },
      async set(v) {
        if (v?.id !== round.value?.id) {
          round.value = undefined
          await nextTick()
        }
        round.value = v
      },
    }),
  })
  tryOnMounted(async () => {
    await pcwallStore.fetchApplySignConfig()
    if (isDesignEdit) {
      fetchConfig()
    }
  })
  tryOnBeforeUnmount(() => {
    clearInterval(regeditTimer)
    clearInterval(rankingsTimer)
  })
}
