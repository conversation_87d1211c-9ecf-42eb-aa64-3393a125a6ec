<script setup lang="ts">
import type { IDesignImage2, IMode, IRepeat } from './image2'
import { openSelectMaterial, useDataAttr, useDesignState } from '../../index'
import { defaultDirection, defaultDuration, defaultMode, defaultPosition, defaultRepeat, defaultSize } from './image2'

const layer = defineModel<IDesignImage2>('layer', { required: true })
const designState = useDesignState()

const modes: { label: string, value: IMode }[] = [
  { label: '手动', value: 'none' },
  { label: '适应', value: 'contain' },
  { label: '拉伸', value: 'fill' },
  { label: '裁剪', value: 'cover' },
]
const repeats: { label: string, value: IRepeat }[] = [
  { label: '无', value: 'no-repeat' },
  { label: '横向', value: 'repeat-x' },
  { label: '纵向', value: 'repeat-y' },
  { label: '平铺', value: 'repeat' },
]

async function updateMaterialFn() {
  const result = await openSelectMaterial('PIC')
  if (result) {
    layer.value.data = result
  }
}

const modeBind = useDataAttr(layer.value, 'mode', defaultMode)
const repeatBind = useDataAttr(layer.value, 'repeat', defaultRepeat)
const sizeXBind = useDataAttr(layer.value, 'sizeX', defaultSize)
const sizeYBind = useDataAttr(layer.value, 'sizeY', defaultSize)
const positionXBind = useDataAttr(layer.value, 'posX', defaultPosition)
const positionYBind = useDataAttr(layer.value, 'posY', defaultPosition)

const directionBind = useDataAttr(layer.value, 'direction', defaultDirection)
const reverseBind = useDataAttr(layer.value, 'reverse', false)
const durationBind = useDataAttr(layer.value, 'duration', defaultDuration)
const playStatesBind = useDataAttr(layer.value, 'playStates', [])
const forceStopBind = useDataAttr(layer.value, 'forceStop', false)
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <div class="thumbnail-box bgblank" @click="updateMaterialFn">
          <img :src="layer.data">
        </div>
      </div>
      <div class="setting-item">
        <h3>显示模式</h3>
        <el-select v-model="modeBind" placeholder="选择填充方式" class="w-100">
          <el-option v-for="item in modes" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div v-if="modeBind === 'none'" class="setting-item gap-10">
        <hi-input-number v-model="sizeXBind" :min="0" :max="100" prepend="宽度" append="%" />
        <hi-input-number v-model="sizeYBind" :min="0" :max="100" prepend="高度" append="%" />
      </div>
      <div class="setting-item gap-10">
        <hi-input-number v-model="positionXBind" :min="0" :max="100" prepend="横向" append="%" />
        <hi-input-number v-model="positionYBind" :min="0" :max="100" prepend="纵向" append="%" />
      </div>
      <template v-if="modeBind === 'none' || modeBind === 'contain'">
        <div class="setting-item">
          <h3>平铺方式</h3>
          <el-select v-model="repeatBind" placeholder="选择平铺方式" class="w-100">
            <el-option v-for="item in repeats" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div v-if="repeatBind !== 'no-repeat'" class="setting-item">
          <h3>动画</h3>
          <el-radio-group v-model="directionBind">
            <el-radio value="none">无</el-radio>
            <el-radio value="x">横向</el-radio>
            <el-radio value="y">纵向</el-radio>
          </el-radio-group>
        </div>
        <template v-if="directionBind !== 'none'">
          <div class="setting-item">
            <h3>反向</h3>
            <el-switch v-model="reverseBind"></el-switch>
          </div>
          <div class="setting-item">
            <h3>时长</h3>
            <hi-input-number v-model="durationBind" class="w-100" :min="0.1" :precision="1" append="秒" />
          </div>
          <div class="setting-item">
            <h3>播放条件</h3>
            <el-select v-model="playStatesBind" class="w-120" multiple placeholder="播放的活动状态">
              <el-option v-for="{ label, value } in designState.statusList" :key="value" :label="label" :value="value"></el-option>
            </el-select>
          </div>
          <div class="setting-item">
            <h3>立即停止</h3>
            <el-switch v-model="forceStopBind"></el-switch>
          </div>
        </template>
      </template>
    </div>
  </div>
</template>

<style scoped lang="scss">
.thumbnail-box {
  width: 120px;
  height: 120px;
  outline: 1px solid #e6ebed;
  border-radius: 4px;
  position: relative;

  img {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }

  > img {
    object-fit: contain;
  }
}
</style>
