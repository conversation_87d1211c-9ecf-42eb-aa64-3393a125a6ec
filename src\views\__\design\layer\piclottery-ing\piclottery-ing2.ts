import type { IDesignLayer, IDesignSetup } from '../../types'
import { BisTypes, useDesignData } from '../..'
import { InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './piclottery-ing2-setting.vue'
import Comp from './piclottery-ing2.vue'
// 类型
export const type = 'piclottery-ing2'
export const defaultAnimateSpeed = 5
export const defaultHeadSizeW = 200
export const defaultHeadSizeH = 250
export const defultRowCount = 4
export const defaultPlaceHolderImg = new URL('./assets/placehoder.png', import.meta.url).href
export const defaultImgMode = 'cover'
export const defaultItemBorderColor = '#fff'
// 数据类型约束
export interface IDesignPiclotteryIng2 extends IDesignLayer {
  type: typeof type
  headSizeW?: number
  headSizeH?: number
  animiteSpeed?: number
  rowCount?: number
  itemBorderColor?: string
  itemBorderWidth?: number
  placeHolderImg?: string
  imgMode?: 'cover' | 'contain' | 'fill'
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.sportsIng,
    showType: ['pcwall'],
    status: ['ing', 'ready'],
    showInteractive: [InteractiveEnum.piclotteryv3],
    type,
    name: '图片抽奖动效',
    thumbnail: new URL('./piclottery-ing2.png', import.meta.url).href,
    Comp,
    CompSetting,
    defaultData() {
      const designData = useDesignData()
      const [width, height] = designData.option.drafts
      return {
        type,
        uuid: layerUuid(),
        name: '图片抽奖动效',
        style: {
          width: `${width}px`,
          height: `${height}px`,
          top: 0,
          left: 0,
        },
      }
    },
  })
}
