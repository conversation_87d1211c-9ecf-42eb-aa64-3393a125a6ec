<script setup lang="ts">
import { dateNormalize, offsetInfo } from '~/src/utils/date'

const timeFormat = 'YYYY-MM-DD HH:mm:ss SSS'

const currentTime = ref(new Date().getTime())
const timestamp = useTimestamp()

const show1 = computed(() => {
  return dateNormalize(currentTime.value, timeFormat)
})
const show2 = computed(() => {
  return dateNormalize(timestamp.value, timeFormat)
})

let handler: number
function controls() {
  handler = requestAnimationFrame(controls)
  currentTime.value = new Date().getTime()
}

onMounted(() => {
  controls()
})
onUnmounted(() => {
  cancelAnimationFrame(handler)
})
</script>

<template>
  <div class="time-box">
    <div>当前时间: {{ show1 }}</div>
    <div>推断时间: {{ show2 }}</div>
    <div>
      <span class="name">startTime:</span><span>{{ offsetInfo.startTime }}</span>
    </div>
    <div>
      <span class="name">serverTime:</span><span>{{ offsetInfo.serverTime }}</span>
    </div>
    <div>
      <span class="name">endTime:</span><span>{{ offsetInfo.endTime }}</span>
    </div>
    <div>
      <span class="name">offset:</span><span>{{ offsetInfo.offset }}</span>
    </div>
    <iframe src="https://time.is" frameBorder="0"></iframe>
  </div>
</template>

<style scoped lang="scss">
.time-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  font-size: 20px;
  color: var(--color);
  div {
    margin: 10px;
  }
  span.name {
    display: inline-block;
    width: 120px;
    text-align: right;
  }
  iframe {
    width: 660px;
    height: 500px;
  }
}
</style>
