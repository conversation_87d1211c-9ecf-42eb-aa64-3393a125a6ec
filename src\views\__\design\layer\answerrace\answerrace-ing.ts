import type { IDesignLayer, IDesignSetup } from '../../types'
import { merge } from 'lodash-es'
import { BisTypes } from '../..'
import { useDesignData } from '../../index'
import { InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './answerrace-ing-setting.vue'
import Comp from './answerrace-ing.vue'

// 类型
export const type = 'answerrace-ing'
export const defaultData = {
  readyDownImg: new URL('./assets/downbg.png', import.meta.url).href,
  readyDownColor: '#fec500',
  // 题目
  ingDownColor: '#fec500',
  subjectFontSize: 20,
  subjectColor: '#fff',
  optionFontSize: 16,
  optionColor: '#fff',
  optionActiveColor: '#000',
  optionBgColor: 'rgba(0, 0, 0, 0.5)',
  optionActiveBgColor: '#00D29F‌',
  optionImgAlign: 'left',
  // rank
  rankTitleFontSize: 18,
  rankTitleColor: '#fec500',
  rankSortFontSize: 16,
  rankSortColor: '#fec500',
  rankHeaderSize: 50,
  rankContentTextAlign: 'center',
  rankContentColor: '#fff',
  rankContentFontSize: 16,
  rankTitleImg: new URL('./assets/rank-title.png', import.meta.url).href,
  rankBgColor: 'rgba(0, 0, 0, 0.5)',
  rankBorderColor: '#fec500',
  // btn
  pauseBtnColor: 'rgb(255,87,70)',
  nextBtnColor: 'rgb(0,220,255)',
  rankBtnColor: 'rgba(0, 0, 0, 0)',
  closerankBtnColor: 'rgba(0, 0, 0, 0)',
}
export const editTempData = ref({
  lookState: 'answering', // 组件状态
  lookType: 'IMAGE',
})
// 数据类型约束
export interface IDesignAnswerIng extends IDesignLayer {
  type: typeof type
  readyDownImg?: string
  readyDownColor?: string
  // mediaFit?: 'fill' | 'contain' | 'cover'
  // 题目
  ingDownColor?: string
  subjectColor?: string
  subjectFontSize?: number
  optionFontSize?: number
  optionColor?: string
  optionActiveColor?: string
  optionBgColor?: string
  optionActiveBgColor?: string
  optionImgAlign?: 'left' | 'right' | 'center'
  // rank
  rankTitleImg?: string
  rankTitleFontSize?: number
  rankTitleColor?: string
  rankContentTextAlign?: string
  rankContentFontSize?: number
  rankContentColor?: string
  rankSortFontSize?: number
  rankSortColor?: string
  rankHeaderSize?: number
  rankBgColor?: string
  rankBorderColor?: string
  // btn
  pauseBtnColor?: string
  nextBtnColor?: string
  rankBtnColor?: string
  closerankBtnColor?: string
}

// 注册组件
export function setup(app: IDesignSetup) {
  const designData = useDesignData()
  app.registry({
    bisType: BisTypes.sportsIng,
    type,
    showType: ['pcwall'],
    showInteractive: [InteractiveEnum.answerracev3],
    status: ['ing'],
    name: '答题进行中',
    thumbnail: new URL('./answerrace-ing.png', import.meta.url).href,
    Comp,
    CompSetting,
    defaultData() {
      return merge({
        uuid: layerUuid(),
        name: '答题进行中',
        type,
        style: {
          width: `${designData.option.drafts[0] * 0.6}px`,
          height: `${designData.option.drafts[1] * 0.9}px`,
        },
      })
    },
  })
}
