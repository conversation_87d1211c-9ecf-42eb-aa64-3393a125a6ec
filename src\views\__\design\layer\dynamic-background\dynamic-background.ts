import type { IDesignLayer, IDesignSetup } from '../../types'
import { BisTypes } from '../..'
import { layerUuid } from '../../utils'
import CompSetting from './dynamic-background-setting.vue'
import Comp from './dynamic-background.vue'

// 类型
export const type = 'dynamic-background'

export interface IDesignDynamicBackground extends IDesignLayer {
  type: typeof type
  data: {
    front: string
    back: string
    race: string
  }
}

export function setup(app: IDesignSetup) {
  app.registry({
    dev: true,
    bisType: BisTypes.bg,
    showType: ['pcwall'],
    type,
    name: '动态背景',
    Comp,
    CompSetting,
    defaultData() {
      return {
        uuid: layerUuid(),
        name: '动态背景',
        type,
        data: {
          front: new URL(`./assets/front.png`, import.meta.url).href,
          back: new URL(`./assets/back.png`, import.meta.url).href,
          race: new URL(`./assets/race.png`, import.meta.url).href,
        },
        style: {
          width: '1280px',
          height: '800px',
          top: '0',
          left: '0',
        },
      }
    },
  })
}
