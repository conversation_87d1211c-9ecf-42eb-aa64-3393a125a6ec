import type { MaybePromise } from 'rollup'
import type { Ref } from 'vue'

export interface IPage {
  pageIndex: number // 页码
  pageSize: number // 页容量
  total: number // 总条数
}

export type Request = (page: Omit<IPage, 'total'>) => Promise<any>

interface useTableOptions<T extends Request> {
  queryFn: T // api方法
  defaultPage?: Partial<IPage> // 默认页码信息
  immediate?: boolean // 是否立即触发调用
  append?: boolean // 是否追加，默认直接替换当前页数据
}

interface useTableResult<T extends Request> {
  isLoading: Ref<boolean> // 加载状态
  page: Ref<IPage> // 当前页码信息
  pageData: Ref<Awaited<ReturnType<T>>> // 数据列表
  refetch: (options?: any) => MaybePromise<void> // 刷新方法
}

export function usePage<T extends Request>({ queryFn, defaultPage, immediate = true, append = false }: useTableOptions<T>): useTableResult<T> {
  // 定义响应式变量
  const isLoading = ref(false) // 加载状态
  const page = ref<Required<IPage>>({ pageIndex: 1, pageSize: 10, total: 0 }) // 当前页码信息

  if (defaultPage) {
    page.value = Object.assign(page.value, defaultPage)
  }

  const pageData = ref({
    pageIndex: 1,
    pageSize: 10,
    total: 0,
    records: [],
  }) as Awaited<ReturnType<T>> // 数据列表

  const refetch = async <T>(options?: T) => {
    if (options instanceof MouseEvent) {
      options = undefined
    }
    if (isLoading.value) return
    if (page.value.pageIndex === 0) return
    const maxPage = page.value.total ? Math.ceil(page.value.total / page.value.pageSize) : defaultPage?.pageIndex || 1
    if (page.value.pageIndex > maxPage) {
      page.value.pageIndex = maxPage
    }
    isLoading.value = true // 设置加载状态为true
    try {
      // 调用api方法，传入页码，获取数据
      const _pageQuery = { pageIndex: page.value.pageIndex, pageSize: page.value.pageSize, ...options }
      const res = await queryFn(_pageQuery)
      // 更新数据列表
      if (page.value.pageIndex === 1 || !append) {
        pageData.value = res
      } else if (append) {
        const records = [...pageData.value.records, ...res.records]
        pageData.value = {
          ...res,
          records,
        }
      }
      // 更新页码信息
      page.value = {
        pageIndex: res.pageIndex,
        pageSize: res.pageSize,
        total: res.total,
      }
    } finally {
      isLoading.value = false // 设置加载状态为false
    }
  }

  // 页码变化，自动刷新数据
  const _refetchPage = computed(() => {
    return {
      pageIndex: page.value.pageIndex,
      pageSize: page.value.pageSize,
    }
  })
  watch(
    _refetchPage,
    (newVal, oldVal) => {
      if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
        refetch()
      }
    },
    { deep: true, immediate },
  )

  // 返回响应式变量和刷新方法
  return { isLoading, page, pageData, refetch }
}
