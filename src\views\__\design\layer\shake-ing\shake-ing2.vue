<script setup lang="ts">
import type { CSSProperties } from 'vue'
import { cloneDeep } from 'lodash-es'
import { useImageInfo } from '~/src/hooks/useImageInfo'
import { envUtils } from '~/src/utils/env'
import { defineCustomEmits, useDataAttr, useDesignState } from '../..'
import { defaultBorderColor, defaultColor, defaultCount, defaultDirection, type IDesignShakeIng2 } from './shake-ing2'

const layer = defineModel<IDesignShakeIng2>('layer', { required: true })
const customEmits = defineCustomEmits(layer)

const designState = useDesignState()
const shakeIngRef = ref<HTMLElement>()
const shakeIngSize = useElementSize(shakeIngRef)

interface IDisplayData {
  id: number
  name: string
  avatar: string
  score: number
  progress?: string
  rank: number // 名次
}
const count = computed(() => layer.value.count || defaultCount)
const directionBind = useDataAttr(layer.value, 'direction', defaultDirection)
/// 数据开始 ///////////////////
const timeProgress = computed(() => {
  if (envUtils.isPlayWright) {
    return 0.5
  }
  return designState.getLayerData('ingTimeProgress')
})
const outerData = computed<IDisplayData[]>(() => {
  return designState.getLayerData('ingRankings') || []
})

const displayData = ref<(IDisplayData | null)[]>([])

watch(
  () => count.value,
  (c) => {
    customEmits('rankingCount', c)

    const resultCount = displayData.value.length
    if (resultCount < c) {
      displayData.value.push(...Array.from({ length: c - resultCount }, () => null))
    } else {
      displayData.value.splice(c)
    }
  },
  { immediate: true },
)

function updateProgress() {
  // 时时计算显示信息
  let minScore = 0
  let maxScore = 0

  displayData.value.forEach((item) => {
    if (item) {
      minScore = Math.min(minScore, item.score)
      maxScore = Math.max(maxScore, item.score)
    }
  })

  const minP = [0, 0.1]
  const maxP = [0.1, 1]

  displayData.value.forEach((item) => {
    if (item) {
      const _minP = minP[0] + (minP[1] - minP[0]) * timeProgress.value
      const _maxP = maxP[0] + (maxP[1] - maxP[0]) * timeProgress.value

      const x = (_maxP - _minP) / (maxScore - minScore)
      const p = _minP + (item.score - minScore) * x

      if (directionBind.value === 'up') {
        item.progress = `${(1 - p) * 100}%`
      } else if (directionBind.value === 'down') {
        item.progress = `${p * 100}%`
      }
    }
  })
}
function updateList(newList: IDisplayData[]) {
  const copyList = cloneDeep(newList).sort((a, b) => b.score - a.score)
  // 排序设置名次
  copyList.forEach((item, index) => {
    item.rank = index + 1
  })

  const resultObj: Record<number, (IDisplayData | null)> = {}
  displayData.value.forEach((item) => {
    if (item) {
      resultObj[item.id] = item
    }
  })

  const newObj: Record<number, IDisplayData> = {}
  copyList.forEach((item) => {
    if (newObj[item.id]) {
      console.error('id重复', item.id)
    }
    newObj[item.id] = item
  })

  const waitRemoveList: { score?: number, index: number }[] = []
  // 1.将已经存在的坑位数据进行更新
  // 2.挑出旧列表中需要移除的数据, 并排序
  displayData.value.forEach((item, index) => {
    if (item) {
      if (newObj[item.id]) {
        Object.assign(item, newObj[item.id])
      } else {
        waitRemoveList.push({ score: item.score, index })
      }
    } else {
      waitRemoveList.push({ index })
    }
  })
  // 排序
  waitRemoveList.sort((a, b) => {
    if (!a.score || !b.score) {
      if (envUtils.isPlayWright) {
        return -1
      }
      return Math.random() - 0.5
    }
    return b.score - a.score
  })

  // 3.挑出新列表不在就列表中的数据，并排序
  const waitAddList: IDisplayData[] = []
  copyList.forEach((item) => {
    if (!resultObj[item.id]) {
      waitAddList.push(item)
    }
  })
  waitAddList.sort((a, b) => b.score - a.score)
  // 4.将新列表中的数据按照排序替换到旧列表中
  waitAddList.forEach((item) => {
    if (waitRemoveList.length) {
      const { index } = waitRemoveList.pop()!
      displayData.value[index] = item
    } else {
      console.warn('新增的多')
    }
  })
  if (waitRemoveList.length) {
    waitRemoveList.forEach(({ index }) => {
      displayData.value[index] = null
    })
  }
}

watch(
  () => outerData.value,
  (list) => {
    updateList(list)
    updateProgress()
  },
  { immediate: true },
)
/// 数据结束 ///////////////////
const stop = ref(false)

const { width: trackWidth, height: trackHeight } = useImageInfo(layer.value.track)

const resultTrackHeight = computed(() => {
  // 显示宽度
  const showWidth = shakeIngSize.width.value / count.value
  return showWidth / trackWidth.value * trackHeight.value
})

const shakeIngWrapStyle = computed(() => {
  const style: CSSProperties = {
    '--base-width': `${shakeIngSize.width.value / count.value}px`,
    '--track-height': `${resultTrackHeight.value}px`,
    '--color': layer.value.style.color || defaultColor,
    '--border-color': layer.value.style.borderColor || defaultBorderColor,
  }
  if (stop.value && layer.value.trackTop) {
    if (directionBind.value === 'up') {
      style.transform = 'translateY(20%)'
    } else if (directionBind.value === 'down') {
      style.transform = 'translateY(-20%)'
    }
  }
  return style
})

const trackItemStyle = computed(() => {
  const style: CSSProperties = {}
  if (layer.value.trackTop) {
    Object.assign(style, {
      backgroundImage: `url(${layer.value.trackTop})`,
      backgroundRepeat: 'no-repeat',
      backgroundSize: '100% auto',
      backgroundPosition: 'center bottom',
    })
  }
  return style
})

const itemBoxStyle = computed(() => {
  return {}
})
const itemStyle = computed(() => {
  const style: CSSProperties = {}
  if (layer.value.trackTop) {
    Object.assign(style, {
      backgroundImage: `url(${layer.value.track})`,
      backgroundRepeat: 'repeat-y',
      backgroundSize: '100% auto',
      backgroundPosition: 'center top',
      animationPlayState: stop.value ? 'paused' : 'running',
    })
    if (directionBind.value === 'down') {
      style.animationDirection = 'reverse'
    }
  }
  return style
})

function wrapStyle(item: IDisplayData | null) {
  if (!item) {
    return {
      opacity: 0,
    }
  }
  return {
    opacity: item?.id ? 1 : 0,
    top: item?.progress ?? '100%',
  }
}

function iterationFn(index: number) {
  // 数据实际下标是从0开始的
  if (index) return
  if (timeProgress.value >= 1) {
    stop.value = true
  } else if (stop.value) {
    stop.value = false
  }
}

function getContestant(index: number) {
  return layer.value.contestant[index % layer.value.contestant.length]
}
</script>

<template>
  <div ref="shakeIngRef" class="shake-ing">
    <div class="shake-ing-wrap" :style="shakeIngWrapStyle">
      <ul v-if="directionBind === 'up'" class="track-box up">
        <li
          v-for="(item, index) in displayData"
          :key="index"
          class="track-item"
          :style="trackItemStyle"
        ></li>
      </ul>
      <ul class="item-box" :style="itemBoxStyle">
        <li
          v-for="(item, index) in displayData"
          :key="index"
          class="item"
          :style="itemStyle"
          @animationiteration="iterationFn(index)"
        >
          <div class="wrap" :style="wrapStyle(item)">
            <img class="contestant" :src="getContestant(index)" />
            <div v-if="item" class="item-info">
              <span class="count">{{ item.score }}</span>
              <div
                class="avatar"
                :class="{ flag: item?.rank <= (layer.rankColor?.length || 0) }"
                :data-rank="`NO.${item.rank}`"
                :style="{ '--rank-color': layer.rankColor?.[item.rank - 1] }"
              >
                <img :src="item.avatar" />
              </div>
              <span class="name">{{ item.name }}</span>
            </div>
          </div>
        </li>
      </ul>
      <ul v-if="directionBind === 'down'" class="track-box down">
        <li
          v-for="(item, index) in displayData"
          :key="index"
          class="track-item"
          :style="trackItemStyle"
        ></li>
      </ul>
    </div>
  </div>
</template>

<style scoped lang="scss">
.shake-ing {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.shake-ing-wrap {
  --color: #fff000;
  --border-color: #ffc000;

  position: relative;
  width: 100%;
  height: 100%;
  transition: 1s linear;
}
.track-box {
  position: absolute;
  left: 0;
  display: flex;
  width: 100%;
  height: 20%;

  &.up {
    // 中间会有一条线
    top: calc(-20% + 1px);
  }
  &.down {
    top: calc(100% - 1px);
  }

  .track-item {
    flex: 1;
    width: 100%;
  }
}
.item-box {
  display: flex;
  height: 100%;
  .item {
    flex: 1;
    height: 100%;
    position: relative;
    animation: roll 1s infinite linear;

    .wrap {
      position: absolute;
      width: 100%;
      text-align: center;
      left: 0;
      transition: top 1s linear;

      .contestant {
        width: 100%;
      }
    }
  }
}
.item-info {
  position: absolute;
  top: calc(var(--base-width) * -1);
  left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  .count {
    font-size: calc(var(--base-width) * 0.18);
    font-weight: bold;
    color: var(--color);
    margin-bottom: calc(var(--base-width) * 0.03);
  }
  .avatar {
    width: 45%;
    aspect-ratio: 1;
    position: relative;

    &.flag::after {
      content: attr(data-rank);
      display: block;
      position: absolute;
      bottom: -10%;
      left: 0;
      width: 100%;
      height: calc(var(--base-width) * 0.13);
      line-height: calc(var(--base-width) * 0.14);
      border: calc(var(--base-width) * 0.01) solid var(--border-color);
      border-radius: calc(var(--base-width) * 0.13);
      font-size: calc(var(--base-width) * 0.09);
      background-color: var(--rank-color);
      color: #fff;
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    > img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 50%;
      border: calc(var(--base-width) * 0.02) solid var(--border-color);
    }
  }
  .name {
    width: 100%;
    font-size: calc(var(--base-width) * 0.16);
    margin-top: calc(var(--base-width) * 0.1);
    color: var(--color);
    text-shadow: 1px 1px 2px #000;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
@keyframes roll {
  0% {
    background-position: center 0px;
  }
  100% {
    background-position: center var(--track-height);
  }
}
</style>
