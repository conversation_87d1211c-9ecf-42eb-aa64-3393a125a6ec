<script setup lang="ts">
import { defineCustomEmits, injectScale, useDesignState, useDesignTemp } from '../..'
import { processStyle } from '../../utils'
import { DEFAULT_DATA, type IDesignWinningList2 } from './winning-list2'

const layer = defineModel<IDesignWinningList2>('layer', { required: true })

const scale = injectScale()
const designState = useDesignState ()
const designTemp = useDesignTemp()
const customEmits = defineCustomEmits(layer)

const data = computed(() => {
  return {
    ...DEFAULT_DATA,
    ...layer.value.data,
  }
})

interface Award {
  id: string | number
  avatar: string
  name: string
}

const liStyle = computed(() => {
  return processStyle({
    fontSize: `${data.value.nameFontSize}px`,
    marginTop: `${data.value.yGap}px`,
    marginBottom: `${data.value.yGap}px`,
    color: data.value.nameColor,
  }, scale)
})

const avatarStyle = computed(() => {
  return processStyle({
    width: `${data.value.avatarSize}px`,
    height: `${data.value.avatarSize}px`,
    fontSize: `${data.value.avatarSize * 0.15}px`,
  }, scale)
})

const awardList = computed<Award[]>(() => {
  return designState.getLayerData('winningList2') || []
})

function onDeleteItem(item: Award) {
  ElMessageBox.confirm('确定要取消当前用户的中奖资格吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '再想想',
    type: 'warning',
  }).then(() => {
    if (designTemp.isEdit) {
      ElMessageBox.alert('当前中奖名单是模拟数据，无法取消中奖，请创建活动后在大屏幕中操作', '需要在大屏幕中操作')
    } else {
      customEmits('winning-delete', item.id)
    }
  }).catch(() => {
    // do nothing
  })
}

onMounted(() => {
})
</script>

<template>
  <ul>
    <li
      v-for="(item, index) in awardList"
      :key="item.id || index"
      :style="liStyle"
    >
      <div class="avatar" :style="avatarStyle">
        <img :src="data.avatarDecoration" class="img2" alt="">
        <img :src="item.avatar || data.defaultAvatar" class="img1" alt="">
        <div class="delete absolute z-10 hidden cursor-pointer items-center justify-center bg-black bg-opacity-50 text-white" @click="onDeleteItem(item)">
          <icon-ph-x-bold />
        </div>
      </div>
      <div class="name">
        {{ item.name }}
      </div>
    </li>
  </ul>
</template>

<style scoped lang="scss">
ul {
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  li {
    position: relative;
    display: flex;
    align-items: center;

    .avatar {
      aspect-ratio: 1;
      border-radius: 50%;
      position: relative;
      img {
        position: absolute;
      }
      .img1,
      .delete {
        width: 76%;
        aspect-ratio: 1;
        border-radius: 50%;
        top: 12%;
        left: 12%;
      }
      .img2 {
        width: 100%;
        aspect-ratio: 1;
        top: 0%;
        left: 0%;
        &:hover {
          & ~ .delete {
            display: flex;
          }
        }
      }
    }
    .name {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      padding-left: 10px;
      padding-right: 10px;
    }
  }

  .delete {
    &:hover {
      display: flex;
    }
  }
}
</style>
