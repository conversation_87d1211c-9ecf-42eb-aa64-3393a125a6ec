<script setup lang="ts">
import { injectScale } from '../../../'
import { processStyle } from '../../../utils'

const props = defineProps<{
  config?: {
    titleFontSize?: number
    titleColor?: string
    titleTextAlign?: string
    contentTextAlign?: string
    sortFontSize?: number
    sortColor?: string
    headerSize?: number
    contentFontSize?: number
    contentColor?: string
  }
  loading?: boolean
  scoreName?: string
  rankData: any[]
}>()

const scale = injectScale()

const tableStyle = computed(() => {
  return processStyle({
    '--title-color': props.config?.titleColor || 'gold',
    '--title-font-size': `${props.config?.titleFontSize || 20}px`,
    '--content-text-align': props.config?.contentTextAlign || 'left',
    '--sort-color': props.config?.sortColor || 'gold',
    '--sort-font-size': `${props.config?.sortFontSize || 20}px`,
    '--header-size': `${props.config?.headerSize || 50}px`,
    '--content-font-size': `${props.config?.contentFontSize || 16}px`,
    '--content-color': props.config?.contentColor || '#fff',
  }, scale.value)
})
</script>

<template>
  <div class="rank-table" :style="tableStyle">
    <div v-if="loading ?? false" class="load-box"><img src="../assets/loading.gif" alt="数据加载中"></div>
    <div class="wrap" :class="[{ 'filter-load': loading }]">
      <table>
        <thead class="table-title">
          <tr class="w-full">
            <th class="num">名次</th>
            <th class="header">头像</th>
            <th class="nickname">名称</th>
            <th class="score">{{ scoreName || '分数/用时' }}</th>
          </tr>
        </thead>
        <tbody class="table-content">
          <tr v-for="(item, index) in rankData" :key="index" class="w-full">
            <td class="num">{{ index + 1 }}</td>
            <td class="header">
              <div class="avatar">
                <img :src="item?.avatar" :class="[`item-${index}`]">
              </div>
            </td>
            <td class="nickname limit">{{ item?.name }}</td>
            <td class="score">{{ item?.score }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<style scoped lang="scss">
 .rank-table {
  width: 100%;
  height: 100%;
  position: relative;
  .wrap {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    &.filter-load {
      filter: blur(5px);
    }
  }
  .load-box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 40px;
      height: 40px;
    }
  }
  table {
    width: 100%;
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .table-title,
  .table-content {
    color: #fff;
    width: 100%;
  }
  .table-title {
    display: block; // 将 thead 变为块级元素以与 tbody 匹配
    tr {
      display: flex;
      width: 100%;
    }
    .num,
    .header,
    .nickname,
    .score {
      font-weight: bold;
      color: var(--title-color);
      font-size: var(--title-font-size);
      text-align: center;
      padding-bottom: 10px;
    }
  }
  .table-content {
    display: block; // 将 tbody 变为块级元素
    overflow-y: auto; // 现在滚动条会生效
    flex: 1; // 占据剩余的所有可用高度
    font-size: 16px;
    color: var(--content-color);
    text-align: var(--content-text-align);
    font-size: var(--content-font-size);
    tr {
      display: flex;
      width: 100%;
      align-items: center; // 垂直居中对齐行内内容
    }
    td {
      padding: 5px 0;
    }
  }
  .num {
    width: 60px;
    text-align: center;
    color: var(--sort-color);
    font-size: var(--sort-font-size);
  }
  .score {
    width: 120px;
    text-align: center; // 保持与表头对齐
    word-wrap: break-word;
    word-break: break-all;
  }
  .header {
    min-width: 80px;
    width: var(--header-size);
    display: flex; // 用于居中头像
    justify-content: center; // 用于居中头像
  }
  .avatar {
    img {
      border-radius: 50%;
      width: var(--header-size);
      height: var(--header-size);
      object-fit: cover;
    }
  }
  .nickname {
    flex: 1; // 占据剩余空间
    min-width: 100px; // 最小宽度
    padding: 0 15px; // 增加一些内边距
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
