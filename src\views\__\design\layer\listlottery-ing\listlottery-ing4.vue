<script setup lang="ts">
import type { IDesignListlotteryIng4 } from './listlottery-ing4'
import gsap from 'gsap'
import { sample, throttle } from 'lodash-es'
import * as THREE from 'three'
// import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { CSS3DObject, CSS3DRenderer, CSS3DSprite } from 'three/examples/jsm/renderers/CSS3DRenderer.js'
import { useDesignState } from '../..'
import { defalutTextStyle, defaultAnimateSpeed, defaultItemSize } from './listlottery-ing4'

const layer = defineModel<IDesignListlotteryIng4>('layer', { required: true })
const designState = useDesignState()
const status = computed(() => designState.status)

const timeline = gsap.timeline({ repeat: -1, paused: false })
const itemSize = computed(() => layer.value.itemSize ?? defaultItemSize)
const animiteSpeed = computed(() => layer.value.animiteSpeed ?? defaultAnimateSpeed)

// 参与人奖池
const regeditList = computed(() => {
  return designState.getLayerData('regeditList') || []
})
const importformShowOption = computed<Record<number, boolean>>(() => {
  return designState.getLayerData('importformShowOption') || []
})
const onceNum = computed(() => {
  return designState.getLayerData('#每次抽取人数#')
})

const threeRoot = ref<HTMLElement>()
const threeRootSize = useElementSize(threeRoot)

let requestAnimationFrameId: number | null = null

let camera: THREE.PerspectiveCamera
let scene: THREE.Scene
let renderer: CSS3DRenderer
let wallGroup: THREE.Group | null = null
let cubeGroupS: THREE.Group | null = null // 用于存储每个立方体的组
const cameraZ = 1500 // 相机 Z 位置

function getItem() {
  const tem = sample(regeditList.value)
  const itemData = Object.assign({}, tem)
  return itemData
}
function getItemBgColor() {
  const arr = layer.value.itemBgColors as string[]
  const index = Math.floor(Math.random() * arr.length)
  return arr[index]
}
function createElement(itemData: any, options: { size?: number, bgColor?: string, fontSize?: number[] } = {}) {
  const { size, bgColor, fontSize } = options
  const element = document.createElement('div')
  element.setAttribute('class', 'css3d')
  element.style = `
    width: ${size || (itemSize.value - 10)}px;
    height:${size || (itemSize.value - 10)}px;
    background: ${bgColor || getItemBgColor()};
  `
  const arr: string[] = []
  layer.value.contentStyle.forEach((item, index) => {
    if (importformShowOption.value[index]) {
      if (itemData[`nameD${index}`]) {
        arr.push(`
      <div style="font-size:${fontSize ? fontSize[index] : item.fontSize}px;color:${item.fontColor};font-weight:${item.fonBold ? 'bold' : 'normal'}">${itemData[`nameD${index}`]}</div>
      `)
      }
    }
  })
  const str = arr.join('')
  element.innerHTML = str
  return element
}

function initShape() {
  if (!scene) {
    return
  }
  if (wallGroup) {
    const children = wallGroup.children
    scene.remove(wallGroup)
    wallGroup = null
    for (const item of children) {
      if (item instanceof CSS3DObject || item instanceof CSS3DSprite) {
        item.element.remove()
      }
    }
  }

  const startAngle = 0// 起始角度
  const endAngle = Math.PI * 2 // 结束角度
  const rangeAngle = endAngle - startAngle
  const n = 0.6
  const viewSizeAtOrigin = getDisplayArea(0)
  const radius = viewSizeAtOrigin.width * n // 调整系数以控制半径

  const actualRowCount = viewSizeAtOrigin.height / itemSize.value - 1

  const colCount = Math.floor(rangeAngle * radius / itemSize.value)
  const itemR = rangeAngle / colCount

  wallGroup = new THREE.Group()
  scene.add(wallGroup)
  wallGroup.rotateY(Math.PI / 2) // 旋转墙体，使其面向相机
  // 创建墙体
  for (let i = 0; i < actualRowCount; i++) {
    const y = (i - (actualRowCount - 1) / 2) * itemSize.value

    for (let j = 0; j < colCount; j++) {
      const currentItemAngle = startAngle + (j + 0.5) * itemR
      const x = radius * Math.cos(currentItemAngle)
      const z = -radius * Math.sin(currentItemAngle)

      const itemData = getItem()
      if (!itemData) continue

      const element = createElement(itemData)
      const object = new CSS3DObject(element)
      object.position.set(x, y, z)

      const lookAtTarget = new THREE.Vector3(0, y, 0)
      object.lookAt(lookAtTarget)
      wallGroup.add(object)
    }
  }

  initCubeGroup()
  timeline.clear()
  play()
  timeline.timeScale(animiteSpeed.value)
}

function initCubeGroup() {
  if (!scene) {
    return
  }
  if (cubeGroupS) {
    cubeGroupS.children.forEach((cubeRroup) => {
      const children = cubeRroup.children
      for (const item of children) {
        if (item instanceof CSS3DObject || item instanceof CSS3DSprite) {
          item.element.remove()
        }
      }
    })
    scene.remove(cubeGroupS)
    cubeGroupS = null
  }
  const cubeNum = Math.min(10, onceNum.value) // 立方体数量
  const itemAngle = (2 * Math.PI) / cubeNum
  cubeGroupS = new THREE.Group()
  scene.add(cubeGroupS)
  // 创建立方体
  for (let i = 0; i < cubeNum; i++) {
    // 实现一个立方体,每一个面都是一个CSS3DObject
    const cubeGroup = new THREE.Group()
    cubeGroup.position.x = 500 * Math.sin(i * itemAngle)
    cubeGroup.position.z = 500 * Math.cos(i * itemAngle)
    cubeGroup.position.y = -100 // 立方体高度
    // cubeGroup.scale.set(0.8, 0.8, 0.8) // 缩放立方体
    cubeGroupS.add(cubeGroup)
    // 创建立方体面
    const faces = []
    const bgColor = getItemBgColor() // 获取背景颜色
    for (let i = 0; i < 6; i++) {
      const itemData = getItem()
      if (!itemData) return
      const scale = itemSize.value / 150
      const f1 = (layer.value.contentStyle[0]?.fontSize ?? 20) / scale
      const f2 = (layer.value.contentStyle[1]?.fontSize ?? 20) / scale
      const f3 = (layer.value.contentStyle[2]?.fontSize ?? 20) / scale
      const element = createElement(itemData, { size: 150, bgColor, fontSize: [f1, f2, f3] })
      const object = new CSS3DObject(element)
      cubeGroup.add(object)
      faces.push(object)
    }
    // 设置立方体面位置和旋转
    const size = 74
    faces[0].position.set(0, 0, size) // Front
    faces[1].position.set(0, 0, -size) // Back
    faces[1].rotation.y = Math.PI // Back需要旋转180度

    faces[2].position.set(-size, 0, 0) // Left
    faces[2].rotation.y = -Math.PI / 2

    faces[3].position.set(size, 0, 0) // Right
    faces[3].rotation.y = Math.PI / 2

    faces[4].position.set(0, size, 0) // Top
    faces[4].rotation.x = -Math.PI / 2

    faces[5].position.set(0, -size, 0) // Bottom
    faces[5].rotation.x = Math.PI / 2

    cubeGroup.rotateOnAxis(new THREE.Vector3(0, 1, 0), Math.PI / 4) // 旋转立方体
  }
}
function play() {
  if (wallGroup) {
    timeline.to(wallGroup.rotation, { y: -2 * Math.PI, repeat: Infinity, duration: 100, ease: 'none' })
  }
  if (cubeGroupS) {
    timeline.to(cubeGroupS.rotation, { y: 2 * Math.PI, repeat: Infinity, duration: 50, ease: 'none' }, 0)
  }
}
function getDisplayArea(z = 0) {
  const radian = Math.PI / 180
  const height = Math.round(2 * Math.tan((camera.fov / 2) * radian) * (camera.position.z - z))
  const width = Math.round(camera.aspect * height)
  return {
    width,
    height,
  }
}
function init() {
  if (!threeRoot.value) {
    return
  }
  scene = new THREE.Scene()
  camera = new THREE.PerspectiveCamera(50, window.innerWidth / window.innerHeight, 1, 1000)
  camera.position.z = cameraZ
  camera.position.y = 100
  renderer = new CSS3DRenderer()
  renderer.setSize(0, 0)
  threeRoot.value.appendChild(renderer.domElement)
}

function resize(width: number, height: number) {
  if (renderer) {
    renderer.setSize(width, height)
  }
  if (camera) {
    camera.aspect = width / height
    camera.updateProjectionMatrix()
  }
}

function render() {
  requestAnimationFrameId = requestAnimationFrame(render)
  renderer.render(scene, camera)
}

watch(
  () => [threeRootSize.width.value, threeRootSize.height.value],
  ([w, h]) => {
    resize(w, h)
  },
)
watch(
  () => [animiteSpeed.value],
  () => {
    timeline.timeScale(animiteSpeed.value)
  },
)
const initReady = ref(false)
watch(
  () => [regeditList.value, onceNum.value, status.value, itemSize.value, layer.value.itemBgColors, layer.value.contentStyle],
  () => {
    if (!initReady.value || !regeditList.value.length) {
      return
    }
    throttle(() => {
      initShape()
    }, 200, { leading: false })()
  },
  { deep: true },
)

function initContentStyle() {
  for (let i = 0; i < 3; i++) {
    if (!layer.value.contentStyle[i]) {
      layer.value.contentStyle[i] = Object.assign({}, defalutTextStyle)
    } else {
      layer.value.contentStyle[i] = Object.assign({}, defalutTextStyle, layer.value.contentStyle[i])
    }
  }
}

onMounted(async () => {
  initContentStyle()
  init()
  render()
  initReady.value = true
})
onUnmounted(() => {
  // 清理资源
  if (requestAnimationFrameId) {
    cancelAnimationFrame(requestAnimationFrameId)
    requestAnimationFrameId = null
  }
})
</script>

<template>
  <div ref="threeRoot" class="three-root"></div>
</template>

<style scoped lang="scss">
.three-root {
  width: 100%;
  height: 100%;
  overflow: hidden;
  :deep() {
    .css3d {
      overflow: hidden;
      color: #fff;
      text-align: center;
      transform: translateZ(0);
      will-change: transform;
      backface-visibility: hidden;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      border-radius: 3%;
      > div {
        width: 90%;
        max-height: 100%;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        line-height: 1.3;
      }
    }
  }
}
</style>
