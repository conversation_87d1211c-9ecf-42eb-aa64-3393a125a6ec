export function useImData(
  option: {
    [type: string]: Ref<any>
  },
  needPostReady?: boolean,
) {
  needPostReady = needPostReady ?? true

  function onMessage(
    { data }: { data: { type: string, message?: any } },
  ) {
    const refValue = option[data.type]
    if (refValue) {
      refValue.value = data.message
    }
  }

  tryOnMounted(() => {
    if (window.parent) {
      window.addEventListener('message', onMessage)
      // 向父节点发送准备好消息
      if (needPostReady) {
        window.parent.postMessage({ type: 'iframe:ready' }, '*')
      }
    }
  })

  tryOnBeforeUnmount(() => {
    window.removeEventListener('message', onMessage)
  })
}
