import type { CSSProperties, ModelRef } from 'vue'
import type { IDesignGroup } from './layer/group/group'
import type { IDesignLayer, InteractiveEnum } from './types'
import { showConfirmDialog } from 'vant'
import { useWallStore } from '~/src/store/wall'
import { isMobile } from '~/src/utils'
import { envUtils } from '~/src/utils/env'

/**
 * @returns layer唯一id
 */
export const layerUuid = () => `layer-${Math.random().toString(36).slice(2, 12)}`

/**
 * 模板id
 */
export const templateId = () => `template-${Math.random().toString(36).slice(2, 12)}`

/**
 * 生成随机字符串
 */
export function randomStr(len = 10) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  const maxPos = chars.length
  let str = ''
  for (let i = 0; i < len; i++) {
    str += chars.charAt(Math.floor(Math.random() * maxPos))
  }
  return str
}

// 对style中的像素进行处理
export function processStyle(style: CSSProperties | string | undefined, scale: number | ComputedRef<number> | Ref<number> = 1, fractionDigits = 2) {
  if (!style)
    return {}
  const scaleStr = JSON.stringify(style)
    .replace(/(\d+(\.\d+)?)(px|pt)/g, (match, p1, p2, p3) => {
      return `${Number.parseFloat((Number.parseFloat(p1) * toValue(scale)).toFixed(fractionDigits))}${p3}`
    })
  return JSON.parse(scaleStr)
}

/**
 * 样式绑定工具
 */
export const layerStyleModel = {
  get(layer: ModelRef<IDesignLayer>, styleName: 'style', key: keyof CSSProperties, defaultValue: any = undefined) {
    return () => {
      const sObj = layer.value[styleName] as CSSProperties
      if (sObj === undefined || sObj === null) return defaultValue
      const value = sObj[key]
      if (value === undefined || value === null) return defaultValue
      return value
    }
  },
  set(layer: ModelRef<IDesignLayer>, styleName: 'style', key: keyof CSSProperties) {
    let sObj = layer.value[styleName] as CSSProperties
    if (!sObj) {
      sObj = {} as CSSProperties
      layer.value[styleName] = sObj
    }
    return (v: CSSProperties[keyof CSSProperties]) => {
      // @ts-expect-error
      sObj[key] = v
    }
  },
}
export function layerStyleComputed(layer: ModelRef<IDesignLayer>, styleName: 'style', key: keyof CSSProperties, defaultValue: any = undefined) {
  return computed({
    get: layerStyleModel.get(layer, styleName, key, defaultValue),
    set: layerStyleModel.set(layer, styleName, key),
  })
}

export function getImageInfo(url: string): Promise<{ width: number, height: number }> {
  return new Promise((resolve) => {
    const img = new Image()
    img.src = url
    img.onload = () => {
      resolve({
        width: img.width,
        height: img.height,
      })
    }
  })
}

// todo 移除，获取互动需要从reject中获取
// inject只能在setup阶段调用，如果在不依赖组件的函数等情况调用可能存在空白的情况
export function getInteractive(): InteractiveEnum {
  return window.location.pathname.split('/').at(-1) as InteractiveEnum
}

interface IFileUploadOptions {
  type: string // 限制类型
  size: number // 限制大小，单位kb，默认1M
}
export async function fileUpload(file: File, options: IFileUploadOptions) {
  const { type, size = 1024 } = options
  if (type && !file.type.includes(type)) {
    throw new Error('文件类型错误')
  }
  if (size && file.size / 1000 > size) {
    throw new Error('文件大小超出限制')
  }
  const formData = new FormData()
  formData.append('file', file)
  const res = await axios.post(`/pro/hxc/procommonfile/upload.htm`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
      // TODO wallFlag， 超管没有相关参数
      'wf': '6etuMjba',
    },
  })
  // TODO 添加到素材库
  const img = `${envUtils.resdomain}/${res.data.data.url}`
  return img
}

// 跳转到互动，优先通过事件传递到iframe处理，如果没有则通过url跳转
export function toAct(name: string, query: { [key: string]: string } = {}) {
  const { wallConfig } = useWallStore()
  const matchs = location.href.match(/mobileFlag=([^&]+)/) || []
  const mobileFlag = wallConfig?.mobileFlag || matchs[1]

  let queryStr = ''
  for (const key in query) {
    queryStr += `&${key}=${query[key]}`
  }

  if (!name.startsWith('wall-') && !name.startsWith('wxuser-')) {
    name = `wall-${name}`
  }

  const mobileUrl = `/p/m/${mobileFlag}.html?/#/common/transfer-route.html?mobileFlag=${mobileFlag}&route=${name}${queryStr}`

  if (window.parent !== window.self) {
    window.parent.postMessage({
      type: 'iframe:to-act',
      data: {
        name,
        query,
      },
    }, '*')
  } else {
    location.href = mobileUrl
  }
}

// 外链跳转，统一通过这个方法，能够拦截非白名单域名增加提示
const LINK_WHITELIST = [
  'test.hixianchang.com',
  'www.hixianchang.com',
  'www.dev.hixianchang.com',
  'm.hixianchang.com',
  'hixianchang.cn',
  'fthuyu.com',
  'hd.fthuyu.com',
  'horse.hixianchang.com',
]
// 敏感字段，限制执行
const BLACK_LIST = ['file:', 'data:', 'blob:', 'javascript:', 'tel:', 'mailto:', 'sms:']
const protocol = ['https:', 'http:', 'weixin:']

// NOTE: 目前next项目被嵌套在iframe中，直接跳转存在安全问题
export async function toLink(url: string, isNewWindowOpen = true) {
  url = url.trim()

  if (BLACK_LIST.some(b => url.startsWith(b))) {
    return
  }

  if (url.startsWith('//')) {
    url = `${location.protocol}}:${url}`
  }

  if (!protocol.some(p => url.startsWith(p))) {
    url = `${location.origin}${url}`
  }

  const _url = new URL(url)
  if (LINK_WHITELIST.includes(_url.hostname) || url.startsWith('weixin:')) {
    isNewWindowOpen ? window.open(url) : location.href = url
  } else {
    const msg = `您即将访问外部链接 ${url} ，外部链接可能存在安全风险，是否继续？`
    if (isMobile) {
      await import('vant/lib/dialog/style')
      showConfirmDialog({
        title: '提示',
        message: msg,
        confirmButtonText: '继续访问',
        cancelButtonText: '取消',
      }).then(() => {
        isNewWindowOpen ? window.open(url) : location.href = url
      }).catch(() => {
      })
    } else {
      ElMessageBox.confirm(msg, '提示', {
        confirmButtonText: '继续访问',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        isNewWindowOpen ? window.open(url) : location.href = url
      }).catch(() => { })
    }
  }
}

// 递归判断是否有任何子图层可见
export function checkGroupVisible(group: IDesignGroup): boolean {
  if (!group) return false
  return group.layers?.some((layer) => {
    if (layer.spoVisible) return true
    if (layer.type === 'group') {
      return checkGroupVisible(layer as IDesignGroup)
    }
    return false
  })
}
