import type { IDesignLayer, IDesignSetup } from '../../types'
import { BisTypes, useDesignData, useDesignState } from '../..'
import { layerUuid } from '../../utils'

import CompSetting from './mobile-awards-setting.vue'
import Comp from './mobile-awards.vue'

export const type = 'mobile-awards'

export interface IDesignMobileAwards extends IDesignLayer {
  type: typeof type
}

export function setup(app: IDesignSetup) {
  const designData = useDesignData()

  app.registry({
    type,
    bisType: BisTypes.result,
    showType: ['mobile'],
    name: '移动端中奖结果',
    thumbnail: new URL('./mobile-awards.png', import.meta.url).href,
    status: ['finish'],
    // 渲染前置条件
    mountedCondition: computed(() => {
      const designState = useDesignState()
      const record = designState.getLayerData('record')
      const awardCount = designState.getLayerData('awardCount')
      const awards = designState.getLayerData('awards')
      return !!record && (awardCount !== undefined || !!awards)
    }),
    Comp,
    CompSetting,
    defaultData(): IDesignMobileAwards {
      return {
        uuid: layerUuid(),
        lock: false,
        name: '移动端中奖结果',
        type,
        style: {
          width: `${designData.option.drafts[0] || 375}px`,
          height: `${designData.option.drafts[1] || 630}px`,
          backgroundColor: 'rgba(0,0,0,0.4)',
        },
      }
    },
  })
}
