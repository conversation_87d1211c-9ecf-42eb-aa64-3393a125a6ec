import { merge } from 'lodash-es'
import { BisTypes } from '../../'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './mobile-lotteryv3-setting.vue'
import Comp from './mobile-lotteryv3.vue'

// 类型
export const type = 'mobile-lotteryv3'

export const DEFAULT_DATA = {
  confirmBtnImg: new URL(`./assets/confirmBtnImg.png`, import.meta.url).href,
}

// 数据类型约束
export interface IDesignMobileLotteryv3 extends IDesignLayer {
  type: typeof type
  data: {
    confirmBtnImg?: string
  }
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.ready,
    showType: ['mobile'],
    showInteractive: [InteractiveEnum.lotteryv3],
    status: ['sign'],
    type,
    name: '滚动抽奖-报名',
    Comp,
    CompSetting,
    thumbnail: new URL('./mobile-lotteryv3.png', import.meta.url).href,
    defaultData(options): IDesignMobileLotteryv3 {
      return merge({
        uuid: layerUuid(),
        name: '滚动抽奖-报名',
        type,
        style: {
          width: '300px',
          height: '300px',
          top: '200px',
          left: '40px',
        },
        data: {},
      }, options as IDesignMobileLotteryv3)
    },
  })
}
