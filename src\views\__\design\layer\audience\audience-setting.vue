<script setup lang="ts">
import { getDefaultMaterial, openSelectMaterial, useDataAttr } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'
import { defaultColor, type IDesignAudience } from './audience'

const layer = defineModel<IDesignAudience>('layer', { required: true })

async function updateMaterialFn(isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, 'data')
    : await openSelectMaterial('PIC')
  if (result || isReset) {
    layer.value.data = result
  }
}

const colorBind = useDataAttr(layer, 'color', defaultColor)
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <MaterialThumbnail @select="updateMaterialFn()" @reset="updateMaterialFn(true)">
          <img :src="layer.data">
        </MaterialThumbnail>
      </div>
      <div class="setting-item">
        <span>颜色</span>
        <hi-color v-model="colorBind" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.thumbnail-box {
  width: 260px;
  height: 60px;
  outline: 1px solid #e6ebed;
  border-radius: 4px;
  position: relative;

  img {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }

  > img {
    object-fit: contain;
  }
}
</style>
