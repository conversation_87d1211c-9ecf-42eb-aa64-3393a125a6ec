import type { IDesignLayer, IDesignSetup } from '../../types'
import { merge } from 'lodash-es'
import { BisTypes } from '../../'
import { layerUuid } from '../../utils'
import CompSetting from './particle-setting.vue'
import Comp from './particle.vue'

// 类型
export const type = 'particle'

// 默认值
export const defaultValues = {
  maxParticles: 70,
  sprayCount: 10,
  full: false,
  gravity: 0.1,
  collision: true,
  fanAngle: 180,
  emitAngle: 90,
  maxSize: 50,
  minSize: 30,
  emissionSpeed: 5,
}

// 数据类型约束
export interface IDesignParticle extends IDesignLayer {
  type: typeof type
  maxParticles?: number // 数量
  full?: boolean // 是否是扇形
  emitterX?: number // 位置
  emitterY?: number // 位置
  contestant: string[]
  gravity?: number // 重力
  collision?: boolean // 碰撞
  sprayCount?: number // 喷射数量
  fanAngle?: number // 扇形夹角度数
  emitAngle?: number // 扇形整体角度（喷射方向）
  maxSize?: number // 最大大小
  minSize?: number // 最小大小
  emissionSpeed?: number // 发射速度
  data: any
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.common,
    type,
    name: '掉落物',
    thumbnail: new URL('particle.png', import.meta.url).href,
    Comp,
    CompSetting,
    defaultData(options): IDesignParticle {
      return merge({
        uuid: layerUuid(),
        name: '掉落物',
        type,
        emitterX: '50%',
        style: {
          width: '500px',
          height: '500px',
        },
        contestant: [new URL('./assets/gold.png', import.meta.url).href],
        data: {},
      }, options as IDesignParticle)
    },
  })
}
