import { isWeiXin } from './env'

const jsApiList = [
  'checkJsApi',
  'onMenuShareTimeline',
  'onMenuShareAppMessage',
  'updateAppMessageShareData',
  'updateTimelineShareData',
  'chooseImage',
  'previewImage',
  'uploadImage',
  'downloadImage',
  'hideOptionMenu',
  'showOptionMenu',
  'hideMenuItems',
  'showMenuItems',
  'hideAllNonBaseMenuItem',
  'showAllNonBaseMenuItem',
  'closeWindow',
  'scanQRCode',
  'startRecord',
  'stopRecord',
  'translateVoice',
  'getLocation',
  'miniProgram',
]

function signConfig(config: any): Promise<void> {
  return new Promise((resolve, reject) => {
    config.jsApiList = jsApiList
    config.openTagList = ['wx-open-launch-weapp']
    // config.debug = true
    window.wx.error((res: any) => reject(res))
    window.wx.ready(() => resolve())
    window.wx.config(config)
  })
}

export async function sign(): Promise<void> {
  if (!isWeiXin) throw new Error('非微信浏览器')
  const url = location.href.split('#')[0]
  const data = await api.mobile.wx.sign({ url })
  if (data?.config) await signConfig(data.config)
  else throw new Error('签名失败')
}

export function getLocation(): Promise<any> {
  return new Promise((resolve, reject) => {
    window.wx.getLocation({
      type: 'gcj02',
      success: resolve,
      fail: reject,
      cancel: reject,
    })
  })
}

export function startRecord(): void {
  return window.wx.startRecord()
}

export function stopRecord(): Promise<any> {
  return new Promise((resolve) => {
    window.wx.stopRecord({ complete: resolve })
  })
}

export function translateVoice(localId: string): Promise<string> {
  return new Promise((resolve, reject) => {
    if (!localId) {
      return reject(new Error('无有效音频'))
    }
    window.wx.translateVoice({
      localId,
      isShowProgressTips: 1,
      complete: (res: any) => {
        if ('translateResult' in res) resolve(res.translateResult)
        else reject(res.errMsg)
      },
    })
  })
}

export function scanQRCode(): Promise<any> {
  return new Promise((resolve, reject) => {
    window.wx.scanQRCode({
      needResult: 1,
      success: resolve,
      fail: reject,
    })
  })
}
