import type { HowlOptions } from 'howler'
import type { CSSProperties, MaybeRef, ModelRef } from 'vue'
import type { IDesignGroup } from './layer/group/group'
import type { IDesignComponentSetupOption, IDesignData, IDesignDisplayInfo, IDesignLayer, IDesignMaterialType, IDesignMusic, IDesignSetup, IDesignStateEvent, IDesignStateStatus, IDesignTemplateSetup, IDesignTemplateSetupOption, IDesignType, ILayerTypes, IObjectFit, ValueRef } from './types'
import type { ITheme } from '~/src/api/types'
import { componentSizes, SIZE_INJECTION_KEY } from 'element-plus'
import { Howl } from 'howler/dist/howler.core.min'
import { cloneDeep, get, isEqual, throttle } from 'lodash-es'
import mitt from 'mitt'
import { render } from 'vue'
import { useInstanceRouter } from '~/src/hooks/useInstanceRouter'
import { difference } from '~/src/utils'
import { envUtils } from '~/src/utils/env'
import HiEditMaterial from './edit/material/index.vue'
import { useFontLoader } from './hooks/useFontLoader'
import { useMobileDevice } from './hooks/useMobileDevice'
import { ModeEnum } from './types'
import { processStyle } from './utils'

const windowSize = useWindowSize()

export const designVersion = 3

export function provideElementSize() {
  // 统一设置element的组件大小
  provide(SIZE_INJECTION_KEY, { size: ref(componentSizes[2]) })
}

/**
 * injectComputed
 */
export function injectComputed<T>(name: string | symbol, d: T) {
  return inject<ComputedRef<T>>(
    name,
    computed<T>(() => d),
  )
}

// 缩放
export function injectScale() {
  return injectComputed('scale', 1)
}

/**
 * 状态
 */
export const useDesignState = defineStore('designState', () => {
  // 活动状态管理
  const statusList = ref<IDesignStateStatus[]>([])
  const status = ref<string>('')

  const statusMap = computed(() => {
    const obj: Record<string, { label: string, value: string }> = {}
    statusList.value.forEach((item) => {
      obj[item.value] = { label: item.label, value: item.value }
    })
    return obj
  })
  // 数据管理
  const layerData = ref<Record<string, any>>({})
  const gameMode = ref<string>('') // 用于左侧切换互动模式
  // 事件管理
  const layerEventList = ref<IDesignStateEvent[]>()

  return {
    reset() {
      statusList.value = []
      status.value = ''
      layerData.value = {}
      layerEventList.value = []
      gameMode.value = ''
    },
    // 活动状态
    statusList,
    statusMap,
    setStatusList(list: IDesignStateStatus[]) {
      statusList.value = list
      return this
    },
    addStatus(val: IDesignStateStatus) {
      statusList.value.push(val)
      return this
    },
    removeStatus(val: string) {
      const index = statusList.value.findIndex(item => item.value === val)
      if (index !== -1) {
        statusList.value.splice(index, 1)
      }
      if (status.value === val) {
        status.value = statusList.value[0].value
      }
    },
    status,
    setStatus(s: string) {
      status.value = s
      return this
    },
    getPrevStatus(s?: string) {
      const index = statusList.value.findIndex(item => item.value === (s || status.value))
      if (index === -1) return []
      return statusList.value.slice(0, index)
    },
    // 状态是否是之前的状态
    isBeforeStatus(s: string) {
      return statusList.value.findIndex(item => item.value === s) > statusList.value.findIndex(item => item.value === status.value)
    },
    // 数据
    layerData,
    getLayerData(type: string): any {
      const data = layerData.value[type]
      if (typeof data === 'function') {
        return data()
      }
      return data
    },
    // #xx# 表示动态数据，富文本变量。
    // $xx$ 表示根据业务逻辑决定图层显示隐藏，符合 $游戏模式-xx$ 会被当作游戏模式依据在编辑阶段显示出来可以切换。
    // %xx% 表示可以被图层内直接修改，返回值是ref或带set的computed，designState.layerData['%xxx%'] 使用。
    // 其他都是常规数据，取决于各个组件怎么消费
    setLayerData(data: Record<string, any>) {
      layerData.value = data
      return this
    },
    addLayerData(data: Record<string, any>) {
      layerData.value = Object.assign(layerData.value, data)
      return this
    },
    gameMode,
    setGameMode(mode: string) {
      gameMode.value = mode
      return this
    },
    // 事件
    layerEventList,
    setLayerEventList(data: IDesignStateEvent[]) {
      layerEventList.value = data
      return this
    },
  }
})
const designState = useDesignState()
/**
 * 设计器渲染数据
 */
const defaultDesignData: IDesignData = {
  version: designVersion,
  option: {
    drafts: [0, 0],
  },
  style: undefined,
  status: undefined,
  layers: [],
  background: undefined,
  music: [],
  $ready: false,
}
export const useDesignData = defineStore('designData', {
  state: (): IDesignData => {
    return cloneDeep(defaultDesignData)
  },
  actions: {
    async setState(data: IDesignData) {
      // 如果`$state`= 只有部分值并不会直接替换到state
      this.$state = cloneDeep({
        ...defaultDesignData,
        ...data,
        $ready: true,
      })
    },
    setStatus(val: IDesignStateStatus[]) {
      this.$state.status = val
    },
    setMusic(music: any[]) {
      this.music = music
    },
    getLayerByUuid(uuid: string): ILayerTypes | undefined {
      let layer
      const walk = (list: ILayerTypes[]) => {
        for (const item of list) {
          if (item.uuid === uuid) {
            layer = item
            break
          }
          if (item.type === 'group') {
            walk((item as IDesignGroup).layers)
          }
        }
      }
      walk(this.layers)
      return layer
    },
    getLayerByUuids(uuids: string[]) {
      const cur: ILayerTypes[] = []
      if (!uuids.length) return cur

      const walk = (list: ILayerTypes[]) => {
        for (const item of list) {
          if (uuids.includes(item.uuid)) {
            cur.push(item)
          }
          if (item.type === 'group') {
            walk((item as IDesignGroup).layers)
          }
        }
      }
      walk(this.layers)
      return cur
    },
    findOneLayerByType(type: string): ILayerTypes | undefined {
      let layer
      const walk = (list: ILayerTypes[]) => {
        for (const item of list) {
          if (item.type === type) {
            layer = item
            break
          }
          if (item.type === 'group') {
            walk((item as IDesignGroup).layers)
          }
        }
      }
      walk(this.layers)
      return layer
    },
    walk(fn: (v: ILayerTypes) => void) {
      const _walk = (list: ILayerTypes[]) => {
        for (const item of list) {
          fn(item)
          if (item.type === 'group') {
            _walk((item as IDesignGroup).layers)
          }
        }
      }
      _walk(this.layers)
    },
    toString() {
      return JSON.stringify(this.$state, (key, value) => {
        if (key.startsWith('$') || key.startsWith('_')) {
          return undefined
        }
        return value
      })
    },
  },
})
const designData = useDesignData()
// 维护$parent的引用
watch(
  () => {
    const uuidArr: string[] = []
    const walk = (layers: IDesignLayer[], pId = '') => {
      for (const layer of layers) {
        const idTmp = pId ? `${pId}_${layer.uuid}` : layer.uuid
        uuidArr.push(idTmp)
        if (layer.type === 'group') {
          walk((layer as IDesignGroup).layers, idTmp)
        }
      }
    }
    walk(designData.$state.layers)
    return uuidArr.join(',')
  },
  () => {
    const walk = (layers: IDesignLayer[], parent: IDesignLayer | undefined) => {
      for (const layer of layers) {
        layer.$parent = parent
        if (layer.type === 'group') {
          walk((layer as IDesignGroup).layers, layer)
        }
      }
    }
    walk(designData.$state.layers, undefined)
  },
  { deep: true, immediate: true },
)

const _undoArr = ref<string[]>([])
const _redoArr = ref<string[]>([])
const _pause = ref(false)
watch(
  () => designData,
  throttle(() => {
    if (_pause.value) return
    // @ts-ignore
    if (designData.$state._ignore) {
      // @ts-ignore
      delete designData.$state._ignore
      return
    }
    const designDataStr = designData.toString()
    const lastDesignDataStr = _undoArr.value[_undoArr.value.length - 1] || ''
    if (designDataStr && lastDesignDataStr !== designDataStr) {
      _undoArr.value.push(designDataStr)
    }

    if (_redoArr.value.length) {
      _redoArr.value = []
    }
  }, 1000, { trailing: true }),
  { immediate: true, deep: true },
)
export const designDataHistory = computed(() => {
  return {
    pause() { _pause.value = true },
    resume() { _pause.value = false },
    undo() {
      if (_undoArr.value.length > 1) {
        const tmp = _undoArr.value.pop()
        if (tmp) {
          _redoArr.value.push(tmp)
        }
      }
      if (_undoArr.value.length) {
        const nowData = JSON.parse(_undoArr.value[_undoArr.value.length - 1])
        nowData._ignore = true
        designData.setState(nowData)
      }
    },
    redo() {
      const tmp = _redoArr.value.pop()
      if (tmp && _undoArr.value[_undoArr.value.length - 1] !== tmp) {
        _undoArr.value.push(tmp)
      }
      if (_undoArr.value.length) {
        const nowData = JSON.parse(_undoArr.value[_undoArr.value.length - 1])
        nowData._ignore = true
        designData.setState(nowData)
      }
    },
    canUndo: _undoArr.value.length > 1,
    canRedo: _redoArr.value.length > 0,
    clear() {
      _undoArr.value = [designData.toString()]
      _redoArr.value = []
    },
  }
})

/**
 * designTemp
 */
export const useDesignTemp = defineStore('designTemp', () => {
  const router = useInstanceRouter()
  const mode = ref<ModeEnum>(ModeEnum.preview)
  const fontLoader = useFontLoader()
  const isManage = ref(router.currentRoute.value.path.startsWith('/manage'))
  const isAdmin = ref(router.currentRoute.value.path.startsWith('/admin'))
  const isTest = ref(router.currentRoute.value.path.startsWith('/test'))
  const isOem = ref(router.currentRoute.value.path.startsWith('/oem'))
  const isMobile = ref(router.currentRoute.value.path.startsWith('/mobile'))
  const designSize = ref<{ width: number, height: number }>({ width: 0, height: 0 })
  // 渲染key
  const renderKey = ref<number | string>(0)

  watch(
    () => router.currentRoute.value.path,
    (v) => {
      isManage.value = v.startsWith('/manage')
      isAdmin.value = v.startsWith('/admin')
      isOem.value = v.startsWith('/oem')

      if (isManage.value || isAdmin.value || isOem.value) {
        mode.value = ModeEnum.edit
      } else {
        mode.value = ModeEnum.preview
      }
    },
    { immediate: true },
  )

  // 屏幕模式
  const typeList = ref<{ label: string, value: IDesignType }[]>([
    { label: '大屏幕', value: 'pcwall' },
    { label: '移动端', value: 'mobile' },
  ])
  const showType = ref<IDesignType>()
  const activeList = ref<string[]>([])
  // 输入中的uuid
  const inputing = ref<string>('')
  // 缩放比例
  const scale = ref(100)

  watch(scale, (val) => {
    if (val < 50) {
      scale.value = 50
    } else if (val > 500) {
      scale.value = 500
    }
  })

  // 父主题
  const parentTheme = ref<ITheme>()
  // 主题对象
  const tempTheme = ref<ITheme>()
  const theme = ref<ITheme>()
  const themeChanged = computed(() => !isEqual(theme.value, tempTheme.value))
  watch(
    () => tempTheme.value,
    (v) => {
      if (v) {
        theme.value = cloneDeep(v)
      }
    },
    { deep: true, immediate: true },
  )

  const defaultTemplate = {
    id: -1,
    name: '测试主题',
    themeImg: '',
    webContent: JSON.stringify({
      version: designVersion,
      option: {
        drafts: [1280, 800],
      },
      style: {},
      layers: [
      ],
    }),
    mobContent: JSON.stringify({
      version: designVersion,
      option: {
        drafts: [375, 820],
      },
      style: {},
      layers: [
      ],
    }),
  }

  const fetchParentTheme = async () => {
    // 编辑模式生效
    if (ModeEnum.edit !== mode.value) return
    if (isManage.value && !theme.value?.pId) return
    if (isOem.value && !theme.value?.pId) return

    const id = theme.value?.pId || theme.value?.id
    if (!id) return
    if (parentTheme.value?.id === id) return

    if (!isManage.value && !isAdmin.value && !isOem.value) {
      console.warn('无主题信息')
      return
    }
    let _theme

    if (isManage.value) {
      _theme = await api.man.theme.read({ where: { id } })
    } else if (isOem.value) {
      _theme = await api.oem.theme.read({ where: { id } })
    } else if (isAdmin.value) {
      _theme = await api.admin.theme.read({ where: { id } })
    }
    parentTheme.value = _theme
  }
  const fetchTheme = async () => {
    // 查询主题信息(是否是超管)
    if (!isManage.value && !isAdmin.value && !isOem.value) {
      if (!theme.value) {
        console.warn('无主题信息')
      }
      return
    }
    // 如果是dev且是登录页面，继续请求没有意义
    if (envUtils.isDev && router.currentRoute.value.path.endsWith('login')) {
      return
    }
    let id = Number.parseInt(location.href.match(/themeId=(\d+)/)?.[1] || '')
    if (Number.isNaN(id) && envUtils.isDev) {
      const model = router.currentRoute.value?.path?.split('/').pop() || 'default'
      const localThemeId = JSON.parse(localStorage.getItem('themeId') || '{}')
      const inputValue = await ElMessageBox.prompt('', '主题ID', { inputPlaceholder: '请输入主题id', inputValue: localThemeId[model] || '' })
      id = Number.parseInt(inputValue.value)
      if (localThemeId[model] !== inputValue) {
        localStorage.setItem('themeId', JSON.stringify({ ...localThemeId, [model]: id }))
      }
      const query = { ...router.currentRoute.value.query, themeId: id }
      router.replace({ query })
    }
    if (Number.isNaN(id)) {
      ElMessage.error('主题id错误')
      return
    }
    if (id === theme.value?.pId || theme.value?.id) {
      return
    }

    let _theme
    if (isManage.value) {
      _theme = await api.man.theme.read({ where: { id } })
    } else if (isOem.value) {
      _theme = await api.oem.theme.read({ where: { id } })
    } else if (isAdmin.value) {
      // 需要wallId
      let wallId = Number.parseInt(location.href.match(/wallId=(\d+)/)?.[1] || '')
      if (Number.isNaN(wallId) && envUtils.isDev) {
        const localWallId = localStorage.getItem('wallId')
        const inputValue = await ElMessageBox.prompt('', '活动ID', { inputPlaceholder: '请输入活动id', inputValue: localWallId || '' })
        wallId = Number.parseInt(inputValue.value)
        localStorage.setItem('wallId', wallId.toString())
        const query = { ...router.currentRoute.value.query, wallId }
        router.replace({ query })
      }
      if (Number.isNaN(wallId)) {
        ElMessage.error('活动id错误')
        return
      }
      _theme = await api.admin.theme.readByWallId({ where: { id, wallId } })
    }

    if (!_theme?.id) {
      ElMessage.error('主题不存在')
      return
    }

    // 预加载字体
    if (_theme.webContent) {
      try {
        const webContent = JSON.parse(_theme.webContent || '{}')
        if (webContent.layers && Array.isArray(webContent.layers)) {
          const fonts = new Set<any>()
          const findTextLayers = (layers: any[]) => {
            for (const layer of layers) {
              if ((layer.type === 'text' || layer.type === 'text-rich') && layer.style) {
                const fontItem = fontLoader.fontList.find((item: any) =>
                  item?.value && item.value === layer.style.fontFamily,
                )
                if (layer.style.fontFamily
                  && layer.style.fontFamily !== 'inherit'
                  && layer.style.fontFamily !== 'default'
                  && fontItem?.url) {
                  fonts.add({
                    ...fontItem,
                    text: layer.data,
                  })
                }
              } else if (layer.type === 'group' && Array.isArray(layer.layers)) {
                findTextLayers(layer.layers)
              }
            }
          }

          findTextLayers(webContent.layers)
          console.log('🚀 ~ fetchTheme ~ fonts:', fonts)

          if (fonts.size) {
            try {
              await fontLoader.preloadFonts(Array.from(fonts))
            } catch (fontError) {
              console.warn('字体预加载失败:', fontError)
            }
          }
        }
      } catch (error) {
        console.error('解析主题内容失败:', error)
      }
    }
    tempTheme.value = _theme
  }
  const saveTheme = async () => {
    if (!themeChanged.value) return
    if (!theme.value) return

    // 如果是test路由，不保存只打日志
    if (isTest.value) {
      console.log(theme.value.webContent)
      return
    }
    const { id, userId } = theme.value
    const update = difference(theme.value, tempTheme.value)
    if (!update) return
    if (isManage.value) {
      // 直接更新
      await api.man.theme.update({ where: { id }, update })
      // 更新最新的主题信息
      tempTheme.value = await api.man.theme.read({ where: { id } })
    } else if (isOem.value) {
      // 直接更新
      await api.oem.theme.update({ where: { id }, update })
      // 更新最新的主题信息
      tempTheme.value = await api.oem.theme.read({ where: { id } })
    } else if (isAdmin.value) {
      // 检查是新增还是更新
      if (userId) {
        // 修改
        await api.admin.theme.update({ where: { id }, update })
        const wallId = new URLSearchParams(location.search).get('wallId') || ''
        tempTheme.value = await api.admin.theme.readByWallId({ where: { id, wallId } })
      } else {
        // 新增
        const wallId = new URLSearchParams(location.search).get('wallId') || null
        if (!wallId) {
          ElMessage.error('活动id不存在')
          return
        }
        const { id: pId, name, themeImg, webContent = '{}', mobContent } = theme.value
        const data = { wallId, pId, name, themeImg, webContent, mobContent }
        const newId = await api.admin.theme.add(data)
        tempTheme.value = await api.admin.theme.readByWallId({ where: { id: newId, wallId } })
      }
    }
    ElNotification.success({
      message: '保存成功',
      position: 'bottom-right',
    })
  }

  watch(
    () => [theme.value?.id, theme.value?.pId],
    () => {
      fetchParentTheme()
    },
    { immediate: true },
  )
  watch(
    () => designData.toString(),
    (value) => {
      if (!theme.value) return
      if (showType.value === 'pcwall') {
        if (value !== theme.value.webContent) {
          theme.value.webContent = value
        }
      } else if (showType.value === 'mobile') {
        if (value !== theme.value.mobContent) {
          theme.value.mobContent = value
        }
      }
    },
    { deep: true },
  )

  watch(
    () => designData.layers,
    () => {
      // 撤销等操作时可能出现图层不在了，但是activeList还有，这里做一次清理
      const layers = designData.getLayerByUuids(activeList.value)
      activeList.value = activeList.value.filter(uuid => layers.find(layer => layer.uuid === uuid))
    },
    { deep: true },
  )

  watch(
    () => [showType.value, theme.value?.webContent, theme.value?.mobContent] as const,
    ([v, webContent, mobContent]) => {
      if (v === 'pcwall') {
        if (webContent && designData.toString() !== webContent) {
          const _webContent = JSON.parse(webContent).layers ? webContent : defaultTemplate.webContent
          designData.setState(JSON.parse(_webContent))
          designDataHistory.value.clear()
        }
      } else if (v === 'mobile') {
        if (mobContent && designData.toString() !== mobContent) {
          const _mobContent = JSON.parse(mobContent).layers ? mobContent : defaultTemplate.mobContent
          designData.setState(JSON.parse(_mobContent))
          designDataHistory.value.clear()
        }
      }
    },
    { immediate: true },
  )

  return {
    mode,
    isEdit: computed(() => mode.value === ModeEnum.edit),
    isPreview: computed(() => mode.value === ModeEnum.preview),
    designSize,
    renderKey,
    reset() {
      if (activeList.value.length) {
        activeList.value = []
      }
    },
    typeList,
    showType,
    activeList,
    inputing,
    scale,
    bus: mitt(),
    // 父主题
    parentTheme,
    // 根据类型获取父主题中的相同图层配置
    findParentLayerByType(type: string) {
      if (!parentTheme.value) return
      const content = parentTheme.value[showType.value === 'pcwall' ? 'webContent' : 'mobContent']
      if (!content) return
      const layers = JSON.parse(content).layers || []
      // 递归查找
      const find = (list: ILayerTypes[]): ILayerTypes | undefined => {
        for (const item of list) {
          if (item.type === type) {
            return item
          }
          if (item.type === 'group') {
            const result = find((item as IDesignGroup).layers)
            if (result) return result
          }
        }
      }
      return find(layers)
    },
    findParentLayerByUUID(uuid: string) {
      if (!parentTheme.value) return
      const content = parentTheme.value[showType.value === 'pcwall' ? 'webContent' : 'mobContent']
      if (!content) return
      const layers = JSON.parse(content).layers || []
      // 递归查找
      const find = (list: ILayerTypes[]): ILayerTypes | undefined => {
        for (const item of list) {
          if (item.type === uuid) {
            return item
          }
          if (item.type === 'group') {
            const result = find((item as IDesignGroup).layers)
            if (result) return result
          }
        }
      }
      return find(layers)
    },
    // 主题
    theme,
    themeChanged,
    fetchTheme,
    saveTheme,
    isAdmin,
    isManage,
    isOem,
    isMobile,
  }
})

const designComponentSetupOptions: Record<string, IDesignComponentSetupOption> = {}
const designTemplateSetupOptions: IDesignTemplateSetupOption[] = []
export function useDesignSetup() {
  const isReady = ref(false)
  return {
    isReady,
    init(interactive: string) {
      if (isReady.value) {
        console.warn('useDesignSetup init 已经初始化')
        return
      }
      isReady.value = true
      // 初始化自定义组件
      const forms = import.meta.glob('./layer/**/*.ts', { eager: true })
      Object.keys(forms).reduce((obj: Record<string, any>, key) => {
        const module = forms[key] as any
        if (module.setup) {
          const setupOption: IDesignSetup = {
            interactive,
            registry(option: IDesignComponentSetupOption) {
              designComponentSetupOptions[option.type] = option
            },
          }
          module.setup(setupOption)
        }
        return obj
      }, {})
      // 初始化模板
      const templateForms = import.meta.glob('./template/**/*.ts', { eager: true })
      Object.keys(templateForms).reduce((obj: Record<string, any>, key) => {
        const module = templateForms[key] as any
        if (module.setup) {
          const setupOption: IDesignTemplateSetup = {
            interactive,
            registry(option: IDesignTemplateSetupOption) {
              designTemplateSetupOptions.push(option)
            },
          }
          module.setup(setupOption)
        }
        return obj
      }, {})
    },
    getDesignComponentSetupOptions() {
      return designComponentSetupOptions
    },
    getComponentSetupOption(type: string): IDesignComponentSetupOption | undefined {
      return designComponentSetupOptions[type]
    },
    getDesignTemplateSetupOptions() {
      return designTemplateSetupOptions
    },
  }
}

/**
 * 显示区域
 */
interface IDisplayOption {
  rootRef: Ref<HTMLElement | undefined>
  witchSpace?: number
}

export function useDisplayInfo({ rootRef, witchSpace = 0 }: IDisplayOption) {
  const rootSize = useElementSize(rootRef)
  const { currentDevice } = useMobileDevice()

  const mobileWidth = 750
  return computed<IDesignDisplayInfo>(() => {
    const result = {
      ready: false,
      width: 0,
      height: 0,
      top: 0,
      left: 0,
      scale: 1,
      scaleX: 1,
      scaleY: 1,

      rootStyle: {},
      pageStyle: {},
    }
    if (!rootSize.width.value || !rootSize.height.value) {
      return result
    }

    const _designData = cloneDeep(designData.$state)
    let { drafts, display, objectFit = 'contain' } = _designData.option

    if (!display) {
      if (witchSpace === 0 && drafts[0] < mobileWidth) {
        display = [windowSize.width.value, 0]
      } else {
        display = [rootSize.width.value - witchSpace, rootSize.height.value - witchSpace]
      }
    }
    result.ready = true

    if (!drafts[0] || !display[0]) {
      // 宽度方向不限制
      result.scale = display[1] / drafts[1]
    } else if (!drafts[1] || !display[1]) {
      // 高度方向不限制
      result.scale = display[0] / drafts[0]
    } else if (objectFit === 'contain' || objectFit === 'fill') {
      if (drafts[0] / drafts[1] < display[0] / display[1]) {
        result.scale = display[1] / drafts[1]
      } else {
        result.scale = display[0] / drafts[0]
      }
    } else if (objectFit === 'cover') {
      if (drafts[0] / drafts[1] > display[0] / display[1]) {
        result.scale = display[1] / drafts[1]
      } else {
        result.scale = display[0] / drafts[0]
      }
    }

    result.scale = Math.round(result.scale * 100) / 100

    // 根据计算好的缩放重新计算宽高
    let _width = drafts[0] * result.scale
    if (!_width) {
      _width = rootSize.width.value * 0.8
    }
    let _height = drafts[1] * result.scale
    if (!_height) {
      _height = rootSize.height.value * 0.8
    }
    if (_width) {
      result.width = _width
    }
    if (_height) {
      result.height = _height
    }

    const designTemp = useDesignTemp()
    const isMobileMode = designTemp.showType === 'mobile'

    if (isMobileMode && currentDevice.value?.height) {
      // 根据计算好的缩放重新计算宽高
      const realHeight = (currentDevice.value.height - currentDevice.value.statusBar) * (result.width / currentDevice.value.width)
      result.height = realHeight
    }

    if (!isMobileMode || designTemp.isEdit) {
      result.top = (rootSize.height.value - result.height) / 2
      result.left = (rootSize.width.value - result.width) / 2
    }

    result.rootStyle = {
      '--design-scale': result.scale,
    }

    const pageStyleObj: CSSProperties = {
      position: 'absolute',
      zIndex: 10,
      width: `${result.width}px`,
      left: `${result.left}px`,
      height: `${result.height}px`,
      top: `${result.top}px`,
      // backgroundColor: '#fff',
    }

    if (isMobileMode && currentDevice.value?.name) {
      pageStyleObj.overflow = 'hidden'
    }

    const transformArr = []
    if (objectFit === 'fill') {
      result.scaleX = display[0] / result.width
      result.scaleY = display[1] / result.height
      transformArr.push(`scale3d(${result.scaleX}, ${result.scaleY}, 1)`)
    }
    if (designTemp.isEdit) {
      transformArr.push(`translate3d(calc(var(--page-offset-x) * 1px), calc(var(--page-offset-y) * 1px), 0)`)
    }
    if (transformArr.length) {
      pageStyleObj.transform = transformArr.join(' ')
    }

    result.pageStyle = Object.assign(pageStyleObj, processStyle(_designData.style, result.scale))
    // 如果是预览模式，同时display的方向是0时不需要设置该方向的距离
    if (display[0] === 0) {
      Object.assign(result.pageStyle, { width: '100%', left: 0, overflowX: 'auto' })
    }
    if (display[1] === 0) {
      Object.assign(result.pageStyle, { height: '100%', top: 0, overflowY: 'auto' })
    }

    if (isMobileMode && !designTemp.isEdit) {
      // 手机端默认禁止滚动，如果需要滚动可使用分组图层
      Object.assign(result.pageStyle, { width: '100%', height: '100%', overflow: 'hidden' })
    }

    return result
  })
}
interface IAdaptionQuantity {
  displaySize: ValueRef<{ width: number, height: number }>
  itemWidth: ValueRef<number | undefined>
  aspectRatio?: ValueRef<number | undefined> | number
  gap?: ValueRef<number | undefined> | number
}
export function useAdaptionQuantity({ displaySize, itemWidth, aspectRatio, gap }: IAdaptionQuantity) {
  const adaptionQuantity = reactive({ count: 0, row: 0, col: 0, width: 0, height: 0, gap: 0 })
  watch(
    () => [displaySize.value.width, displaySize.value.height, itemWidth.value, toValue(aspectRatio), toValue(gap)],
    ([_width, _height, _itemWidth = 60, _aspectRatio = 1, _gap = 0]) => {
      if (!_width || !_height) return

      if (_width === Infinity || _height === Infinity) {
        adaptionQuantity.count = 0
        adaptionQuantity.row = 0
        adaptionQuantity.col = 0
        adaptionQuantity.width = 0
        adaptionQuantity.height = 0
        adaptionQuantity.gap = 0
        return
      }

      const _itemHeight = _itemWidth / _aspectRatio
      const row = Math.floor((_height + _gap) / (_itemHeight + _gap)) || 1
      const col = Math.floor((_width + _gap) / (_itemWidth + _gap)) || 1

      adaptionQuantity.count = row * col
      adaptionQuantity.row = row
      adaptionQuantity.col = col
      adaptionQuantity.width = _itemWidth
      adaptionQuantity.height = _itemHeight
      adaptionQuantity.gap = _gap
    },
    { immediate: true },
  )
  return adaptionQuantity
}

/**
 * 组件内部自定义事件向外通知
 */
export const EMITS_EVENT_SHOW_HIDE = Symbol('show-hide')
export function defineCustomEmits(layer: ModelRef<IDesignLayer>) {
  const uuid = layer.value.uuid
  const designTemp = useDesignTemp()
  return (eventId: string | symbol, data?: any) => {
    if (eventId === EMITS_EVENT_SHOW_HIDE) {
      return designTemp.bus.emit(`${data.uuid || uuid}:event:show-hide`, data.show || data)
    }
    designTemp.bus.emit(`${uuid}:event:custom`, { eventId, data })
  }
}

// 判断图层是否禁用
export function isLayerDisable(layer: IDesignLayer) {
  const setupOption = designComponentSetupOptions[layer.type]
  if (!setupOption?.status)
    return false
  // 如果当前状态不在status中则禁用， status为空则不禁用
  return !setupOption.status.includes(designState.status)
}
// 判断图层是否隐藏
export function isLayerHide(layer: IDesignLayer) {
  // 禁用就是不显示
  if (isLayerDisable(layer))
    return true
  // show为空默认显示
  if (!layer.show)
    return false
  // 看当前状态是否不在show中
  return !layer.show.includes(designState.status)
}

export function openSelectMaterial(materialType: IDesignMaterialType = 'PIC') {
  return new Promise<string>((resolve) => {
    const vnode = h(HiEditMaterial, {
      materialType,
      onCancle: () => {
        // reject(new Error('cancel'))
        /* 销毁组件重新走组件生命周期  */
        render(null, document.body)
      },
      onChange: (select) => {
        resolve(select)
        /* 销毁组件重新走组件生命周期  */
        render(null, document.body)
      },
    })

    // @ts-expect-error
    vnode.appContext = window.__bootstrap__?._context
    render(vnode, document.body)
  })
}

export function sleep(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

export function useDesignMusic() {
  const isPlaying: Ref<boolean> = ref(false)
  const currentHowls: Record<string, Howl[]> = {}
  const pathname = window.location.pathname

  // 添加停止所有音频的函数
  const stopAll = () => {
    currentHowls[pathname]?.forEach((howl) => {
      howl.stop()
      howl.unload()
    })
    currentHowls[pathname] = []
  }

  const play = async (status: string) => {
    const data: IDesignMusic | undefined = designData.music?.find(item => item.state === status)
    if (data) {
      // 停止并清理之前的音频
      stopAll()

      for (const item of data.list) {
        const playCount = item.playCount || 1
        const objectFits: HowlOptions = {
          src: [item.url],
          loop: item.loop,
          volume: item.volume,
          rate: item.rate,
          html5: true,
          autoplay: false, // 改为 false，手动控制播放
        }
        if (item.duration) {
          objectFits.sprite = {
            actualSound: [item.offset ?? 0, item.duration],
          }
        }

        if (item.delay) {
          await sleep(item.delay)
        }

        try {
          const music = new Howl(objectFits)
          currentHowls[pathname].push(music)

          for (let i = 0; i < playCount; i++) {
            if (!isPlaying.value) break // 检查是否应该继续播放

            await new Promise((resolve, reject) => {
              music.once('end', resolve)
              music.once('loaderror', reject)
              music.play(item.duration ? 'actualSound' : undefined)
            })
          }
        } catch (error) {
          console.error('Music playback error:', error)
        }
      }
    }
  }

  // 组件卸载时清理
  onUnmounted(() => {
    stopAll()
  })

  watch([() => designState.status, isPlaying, () => designData.music?.length], async ([status, playing, musicLength]) => {
    if (!status || !musicLength) return

    if (!playing) {
      stopAll()
      return
    }

    await useDebounceFn(async () => {
      await play(status)
    }, 500)()
  }, {
    immediate: true,
  })

  return {
    isPlaying,
  }
}

// 组件的业务类型，用于归类
export const BisTypes = {
  common: '通用',
  bg: '背景',
  countDown: '倒计时',
  regedit: '参与人',
  sportsIng: '奖品展示',
  rankingList: '排行榜',
  result: '中奖结果',
  rule: '活动规则',
  ready: '准备页',
}

// objectFit
export const objectFits: { label: string, value: IObjectFit }[] = [
  { label: '无', value: 'none' },
  { label: '拉伸', value: 'fill' },
  { label: '适应', value: 'contain' },
  { label: '裁剪', value: 'cover' },
]

// 图片常用工具
export const ResourceUtil = {
  async loadImage(
    url: string,
    option?: {
      // default
      width?: number
      height?: number
      // 相对设计稿的缩放比例
      draftScale?: number
    },
  ) {
    try {
      const img = new Image()
      img.src = url
      await new Promise((resolve, reject) => {
        img.onload = resolve
        img.onerror = reject
      })
      const w1 = img.width
      const h1 = img.height

      const designData = useDesignData()
      const [w, h] = designData.option?.drafts || [100, 100]

      let width = w
      let height = h
      // 如果图片是横向的，那么宽度就是设计稿的宽度，高度按比例缩放
      if (w1 > h1) {
        height = w * h1 / w1
      } else {
        width = h * w1 / h1
      }

      const draftScale = option?.draftScale || 1
      return {
        width: width * draftScale,
        height: height * draftScale,
        top: (h - height) / 2 * draftScale,
        left: (w - width) / 2 * draftScale,
      }
    } catch (e) {
      console.error(e)
      return {
        width: option?.width || 100,
        height: option?.height || 100,
        top: 0,
        left: 0,
      }
    }
  },
  async loadVideo(
    url: string,
    option?: {
      // default
      width?: number
      height?: number
      // 相对设计稿的缩放比例
      draftScale?: number
    },
  ) {
    try {
      const video = document.createElement('video')
      video.src = url
      await new Promise((resolve, reject) => {
        video.onloadedmetadata = resolve
        video.onerror = reject
      })
      const w1 = video.videoWidth
      const h1 = video.videoHeight
      video.remove()

      const designData = useDesignData()
      const [w, h] = designData.option?.drafts || [100, 100]

      let width = w
      let height = h
      // 如果图片是横向的，那么宽度就是设计稿的宽度，高度按比例缩放
      if (w1 > h1) {
        height = w * h1 / w1
      } else {
        width = h * w1 / h1
      }

      const draftScale = option?.draftScale || 1
      return {
        width: width * draftScale,
        height: height * draftScale,
      }
    } catch (e) {
      console.error(e)
      return {
        width: option?.width || 100,
        height: option?.height || 100,
        top: 0,
        left: 0,
      }
    }
  },
}

// 用于绑定样式属性 和 默认数据
export function useDataAttr<T extends Record<string, any>, D>(
  source: MaybeRef<T>,
  key: keyof T & string,
  defaultVal: D,
  unit?: MaybeRef<string>,
): Ref<D> {
  if (typeof defaultVal === 'object' && unit) {
    throw new Error('defaultVal不能是复杂对象且unit存在')
  }

  // 源值 → Ref 值（去单位 / 数字化 / 默认回退）
  const fromSource = (raw: any): D => {
    let v = raw === undefined ? cloneDeep(defaultVal) : raw

    // 去单位
    if (unit && typeof v === 'string') {
      const u = toValue(unit)
      if (v.endsWith(u)) v = Number.parseFloat(v)
    }

    // 数值化
    if (typeof defaultVal === 'number') {
      v = Number.parseFloat(`${v}`)
      if (Number.isNaN(v)) v = defaultVal
    }

    return cloneDeep(v) as D
  }

  // Ref 值 → 源值（补单位）
  const toSource = (val: D): any =>
    unit && typeof val !== 'object' ? `${val}${toValue(unit)}` : val

  // ref 初始化
  const initial = fromSource((toValue(source) as any)?.[key])
  const propRef = ref(initial) as Ref<D>

  // 源 → Ref
  watch(
    () => (toValue(source) as any)[key],
    (newVal) => {
      const converted = fromSource(newVal)
      if (!isEqual(converted, propRef.value)) propRef.value = converted
    },
    { deep: true },
  )

  // Ref → 源
  watch(
    propRef,
    (newVal) => {
      const src = toValue(source) as any
      const withUnit = toSource(newVal)

      let finalVal = withUnit
      if (['width', 'height', 'top', 'left', 'borderWidth'].includes(key) && withUnit === '0px') finalVal = 0

      // (1) 与默认值深相等 → 删除
      if (isEqual(newVal, defaultVal)) {
        if (src[key] !== undefined) {
          src[key] = undefined
          nextTick(() => delete src[key])
        }
        return
      }

      // (2) 与源值完全一致 → 跳过
      if (isEqual(src[key], finalVal)) return

      src[key] = finalVal
    },
    { deep: true, immediate: true },
  )

  // unit 变化处理
  if (unit) {
    watch(
      () => toValue(unit),
      (newUnit) => {
        const src = toValue(source) as any
        const raw = src[key]
        if (raw === undefined) return
        const num = Number.parseFloat(`${raw}`)
        if (!Number.isNaN(num)) src[key] = `${num}${newUnit}`
        // 同步回 Ref，确保两端一致
        propRef.value = fromSource(src[key])
      },
    )
  }

  return propRef as Ref<D>
}

// "transform":"perspective(1000px) rotateX(2deg) rotateY(5deg) rotateZ(14deg)"
export function useTransformAttr<T extends CSSProperties>(
  data: MaybeRef<T>,
  name: 'rotateX' | 'rotateY' | 'rotateZ' | 'translateX' | 'translateY' | 'translateZ' | 'perspective',
  defaultValue = 0,
) {
  const _data = toValue(data)
  const unit = name.startsWith('rotate') ? 'deg' : 'px'
  return computed({
    get() {
      const transform = _data.transform || ''
      const reg = name === 'perspective'
        ? new RegExp(`${name}\\(([^)]+)px\\)`)
        : new RegExp(`${name}\\(([^)]+)${unit}\\)`)
      const match = transform.match(reg)
      return match && match[1] ? Number.parseInt(match[1]) : defaultValue
    },
    set(v) {
      const transform = _data.transform || ''
      const reg = name === 'perspective'
        ? new RegExp(`${name}\\(([^)]+)px\\)`)
        : new RegExp(`${name}\\(([^)]+)${unit}\\)`)
      const match = transform.match(reg)
      if (match) {
        _data.transform = transform.replace(reg, `${name}(${v}${unit})`)
      } else {
        _data.transform = `${transform} ${name}(${v}${unit})`
      }
      // 如果是defaultValue的话就删除
      if (v === defaultValue) {
        _data.transform = _data.transform.replace(reg, '')
      }
      // 确保transform属性按照固定顺序排列: perspective -> rotateX -> rotateY -> rotateZ
      const transformParts = _data.transform.trim().split(/\s+/).filter(Boolean)
      const perspectivePart = transformParts.find(part => part.startsWith('perspective'))
      const rotateXPart = transformParts.find(part => part.startsWith('rotateX'))
      const rotateYPart = transformParts.find(part => part.startsWith('rotateY'))
      const rotateZPart = transformParts.find(part => part.startsWith('rotateZ'))
      const otherParts = transformParts.filter(part =>
        !part.startsWith('perspective')
        && !part.startsWith('rotateX')
        && !part.startsWith('rotateY')
        && !part.startsWith('rotateZ'),
      )

      const orderedParts = [
        perspectivePart,
        rotateXPart,
        rotateYPart,
        rotateZPart,
        ...otherParts,
      ].filter(Boolean)

      _data.transform = orderedParts.join(' ')

      _data.transform = _data.transform.trim()
      // 如果 transform 为空则删除
      if (!_data.transform) {
        delete _data.transform
      }
    },
  })
}

// 四值扩展
export function fourValueExpand(layer: Ref<IDesignLayer>, key: 'padding' | 'borderRadius' | 'borderWidth') {
  return computed({
    get() {
      const values = `${layer.value.style[key] || '0'}`.split(' ')
      return values.length > 1
    },
    set(v: boolean) {
      const values = `${layer.value.style[key] || '0'}`.split(' ')
      if (v) {
        if (values.length > 1) return
        layer.value.style[key] = `${values[0]} ${values[0]} ${values[0]} ${values[0]}`
      } else {
        if (values.length === 1) return
        layer.value.style[key] = values[0]
      }
    },
  })
}
export function fourValueBind(layer: Ref<IDesignLayer>, key: 'padding' | 'borderRadius' | 'borderWidth', idx: 0 | 1 | 2 | 3) {
  return computed({
    get() {
      const values = `${layer.value.style[key] || '0'}`.split(' ').map(i => Number.parseFloat(i))
      if (idx && values.length !== 4) {
        // 整理成四个值
        if (values.length === 1) {
          values[1] = values[0]
          values[2] = values[0]
          values[3] = values[0]
        } else if (values.length === 2) {
          values[2] = values[0]
          values[3] = values[1]
        } else if (values.length === 3) {
          values[3] = values[1]
        }
      }
      return values[idx]
    },
    set(v) {
      const values = `${layer.value.style[key] || '0'}`.split(' ').map(i => Number.parseFloat(i))
      if (idx && values.length !== 4) {
        // 整理成四个值
        if (values.length === 1) {
          values[1] = values[0]
          values[2] = values[0]
          values[3] = values[0]
        } else if (values.length === 2) {
          values[2] = values[0]
          values[3] = values[1]
        } else if (values.length === 3) {
          values[3] = values[1]
        }
      }
      values[idx] = v
      layer.value.style[key] = values.map(i => i ? `${i}px` : i).join(' ')
    },
  })
}

export function isLockedInHierarchy(layer: IDesignLayer | null, self = true) {
  if (!layer) return false

  if (self) {
    if (layer.lock) {
      return true
    }
  }

  while (layer?.$parent) {
    if (layer.$parent.lock) {
      return true
    }
    layer = layer.$parent
  }
  return false
}

export function isHideInHierarchy(layer: IDesignLayer | null, self = true) {
  if (!layer) return false

  if (self) {
    const hide = isLayerHide(layer)
    if (hide) {
      return true
    }
  }

  while (layer?.$parent) {
    if (isLayerHide(layer.$parent)) {
      return true
    }
    layer = layer.$parent
  }

  return false
}

// 获取图层配置某个KEY的默认值
export async function getDefaultMaterial(layer: ILayerTypes, keyPath: string) {
  if (!layer || !keyPath) {
    return
  }
  const designTmep = useDesignTemp()
  // 优先获取父主题相同uuid的数据
  let data = designTmep.findParentLayerByUUID(layer.uuid)
  if (!data) {
    const isBase = ['text', 'text-rich', 'image', 'video'].includes(layer.type)
    if (isBase) {
      return
    }
    // 获取父主题相同type类型的数据
    data = designTmep.findParentLayerByType(layer.type)
  }
  if (!data) {
    // 重新初始化组件读取默认数据
    const designSetup = useDesignSetup()
    const setupOption = designSetup.getComponentSetupOption(layer.type)
    if (!setupOption) return
    data = await setupOption.defaultData()
  }
  if (!data) {
    return ''
  }
  return get(data, keyPath)
}
