import { BisTypes, useDesignData } from '../..'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './shake-ing1-setting.vue'
import Comp from './shake-ing1.vue'

// 类型
export const type = 'shake-ing1'

export interface IDesignShakeIng1 extends IDesignLayer {
  type: typeof type
  columnColors: string[]
  flag: string[]
  decoration: string
  columnBgcolor: string
  data: {
    name: string
    avatar: string
    score: number
    progress: number
  }[]
}

export function setup(app: IDesignSetup) {
  let status: string[]
  switch (app.interactive) {
    case InteractiveEnum.shakev3:
      status = ['ing']
      break
    default:
      status = ['ing']
      break
  }

  app.registry({
    showType: ['pcwall'],
    bisType: BisTypes.sportsIng,
    showInteractive: [InteractiveEnum.shakev3, InteractiveEnum.moneyv3, InteractiveEnum.goldcoinv3, InteractiveEnum.diglettv3, InteractiveEnum.firev3],
    type,
    name: '进行中排行',
    thumbnail: new URL('./shake-ing1.png', import.meta.url).href,
    status,
    Comp,
    CompSetting,
    defaultData() {
      const designData = useDesignData()
      const [width, height] = designData.option.drafts

      return {
        uuid: layerUuid(),
        name: '进行中排行',
        type,
        columnColors: ['#FFD700', '#FF66FF', '#0000FF'],
        columnBgcolor: '#000000',
        flag: [
          new URL(`./assets/flag1.png`, import.meta.url).href,
          new URL(`./assets/flag2.png`, import.meta.url).href,
          new URL(`./assets/flag3.png`, import.meta.url).href,
        ],
        decoration: new URL(`./assets/decoration.png`, import.meta.url).href,
        data: [],
        style: {
          width: `${width}px`,
          height: `${height}px`,
          top: 0,
          left: 0,
        },
      }
    },
  })
}
