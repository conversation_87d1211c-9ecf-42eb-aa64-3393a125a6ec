<script setup lang="ts">
import type { IDesignLotteryPrize } from './lottery-prize'
import { injectScale, useDesignState } from '../..'
import { processStyle } from '../../utils'
import { defaultCountColor, defaultCoutFontSize, defaultNameColor, defaultNameFontSize, defaultPrizeImgHeight, defaultPrizeImgWidth, defaultShowPrizeCount, defaultShowPrizeName } from './lottery-prize'

const layer = defineModel<IDesignLotteryPrize>('layer', { required: true })
const scale = injectScale()
const designState = useDesignState()

interface IDisplayData {
  id: number
  img: string
  name: string
  count: number
}
const domRef = ref<HTMLElement>()

const awards = computed<IDisplayData[]>(() => {
  return designState.getLayerData('awards') || []
})

const showAward = computed(() => {
  return awards.value[0] || {}
})
const prizeImgWidth = computed(() => layer.value.prizeImgWidth ?? defaultPrizeImgWidth)
const prizeImgHeight = computed(() => layer.value.prizeImgHeight ?? defaultPrizeImgHeight)
const nameColor = computed(() => layer.value.nameColor ?? defaultNameColor)
const countColor = computed(() => layer.value.countColor ?? defaultCountColor)
const coutFontSize = computed(() => layer.value.coutFontSize ?? defaultCoutFontSize)
const nameFontSize = computed(() => layer.value.nameFontSize ?? defaultNameFontSize)
const showPrizeName = computed(() => layer.value.showPrizeName ?? defaultShowPrizeName)
const showPrizeCount = computed(() => layer.value.showPrizeCount ?? defaultShowPrizeCount)

const defaultPrizeImg = new URL('./assets/prize.png', import.meta.url).href
const lotteryStyle = computed(() => {
  return processStyle({
    '--prizeimg_width': `${prizeImgWidth.value}%`,
    '--prizeimg_height': `${prizeImgHeight.value}%`,
    '--count_color': countColor.value,
    '--namecolor': nameColor.value,
    '--nameFontSize': `${scale.value * nameFontSize.value}px`,
    '--coutFontSize': `${scale.value * coutFontSize.value}px`,
  })
})
</script>

<template>
  <div ref="domRef" class="lottery-prize-box" :style="lotteryStyle">
    <div class="prize-img"><img class="img" :src="showAward.img || defaultPrizeImg" alt=""></div>
    <div class="prize-info">
      <div v-if="showPrizeName" class="prize-name">{{ showAward.name }}</div>
      <div v-if="showPrizeCount" class="prize-cout">剩余数量：{{ showAward.count }}</div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.lottery-prize-box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  .prize-img {
    height: var(--prizeimg_height);
    width: var(--prizeimg_width);
    display: flex;
    justify-content: center;
    align-items: center;
    .img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
  .prize-info {
    font-size: 18px;
    text-align: center;
    .prize-name {
      color: var(--namecolor);
      font-size: var(--nameFontSize);
    }
    .prize-cout {
      margin-top: 10px;
      color: var(--count_color);
      font-size: var(--coutFontSize);
    }
  }
}
</style>
