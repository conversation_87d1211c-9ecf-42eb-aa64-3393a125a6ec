<script setup lang="ts">
import type { UploadFile, UploadFiles, UploadProps, UploadUserFile } from 'element-plus'
import { ElMessage, ElUpload } from 'element-plus'

const props = withDefaults(
  defineProps<{
    use: string
    maxSize: string | number
  }>(),
  {
    use: 'audio',
    maxSize: 20,
  },
)

const emits = defineEmits<{
  (e: 'beforeUpload'): void
  (e: 'error', err: Error): void
  (event: 'success', url: string, uploadFile: UploadFile, uploadFiles: UploadFiles): void
}>()

const acceptType = 'audio/mp3'

const upload = ref()
const fileList = ref<UploadUserFile[]>([])

const verifyAudio: UploadProps['beforeUpload'] = (file) => {
  const pattern = file.type
  const isLt15M = Number(file.size / 1024 / 1024) > Number(props.maxSize)

  if (!/audio/.test(pattern)) {
    ElMessage.error({ message: `只支持音频格式的文件` })
    return false
  }

  if (isLt15M) {
    let message = `上传图片大小不能超过${props.maxSize}M`
    if (Number(props.maxSize) < 1) {
      // 之前maxSize是以M为单位的，但提示的时候如果提示0.2M这种效果可能不好，判断一下小于1M的时候换成k提示
      message = `上传图片大小不能超过${Math.trunc((Number(props.maxSize) * 1024) / 100) * 100}k`
    }
    ElMessage.error({ message })
    return false
  }
  emits('beforeUpload')
}

const uploadError: UploadProps['onError'] = (err) => {
  const result = JSON.parse(err.message)
  ElMessage.error({ message: result.msg || `上传出错` })
  emits('error', err)
}

const uploadSuccess: UploadProps['onSuccess'] = (args, file, fileList) => {
  emits('success', args.data, file, fileList)
}
</script>

<template>
  <ElUpload
    ref="upload"
    v-model:file-list="fileList"
    :action="`/api/spo/commonFile/uploadAudio?use=${use}`"
    v-bind="$attrs"
    :before-upload="verifyAudio"
    :show-file-list="false"
    :accept="acceptType"
    :on-success="uploadSuccess"
    :on-error="uploadError"
  >
    <div>
      <slot></slot>
    </div>
  </ElUpload>
</template>
