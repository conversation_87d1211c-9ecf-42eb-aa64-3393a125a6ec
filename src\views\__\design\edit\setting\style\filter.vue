<script setup lang="ts">
import type { IDesignLayer } from '../../../types'

defineOptions({ label: '滤镜' })

const layer = defineModel<IDesignLayer>('layer', { required: true })

const filterObj = ref({
  blur: 0, // 模糊
  brightness: 100, // 亮度
  contrast: 100, // 对比度
  grayscale: 0, // 灰度
  saturate: 100, // 饱和度
  hueRotate: 0, // 色相旋转
})
watch(
  () => layer.value.style.filter,
  () => {
    const { filter } = layer.value.style
    if (!filter) return

    const arr = filter.split(' ').map(i => i.trim())
    //
    filterObj.value.blur = Number.parseFloat(findItem(arr, 'blur').replace('blur(', '').replace('px)', ''))
    filterObj.value.brightness = Number.parseFloat(findItem(arr, 'brightness').replace('brightness(', '').replace('%)', ''))
    filterObj.value.contrast = Number.parseFloat(findItem(arr, 'contrast').replace('contrast(', '').replace('%)', ''))
    filterObj.value.grayscale = Number.parseFloat(findItem(arr, 'grayscale').replace('grayscale(', '').replace('%)', ''))
    filterObj.value.saturate = Number.parseFloat(findItem(arr, 'saturate').replace('saturate(', '').replace('%)', ''))
    filterObj.value.hueRotate = Number.parseFloat(findItem(arr, 'hue-rotate').replace('hue-rotate(', '').replace('deg)', ''))
  },
  { immediate: true },
)
watch(
  filterObj,
  () => {
    const { blur, brightness, contrast, grayscale, saturate, hueRotate } = filterObj.value
    const arr = [`blur(${blur}px)`, `brightness(${brightness}%)`, `contrast(${contrast}%)`, `grayscale(${grayscale}%)`, `saturate(${saturate}%)`, `hue-rotate(${hueRotate}deg)`]
    const str = arr.join(' ')
    if (layer.value.style.filter !== str) {
      layer.value.style.filter = str
    }
  },
  { deep: true },
)

function findItem(arr: string[], key: string) {
  return arr.find(i => i.includes(key)) || ''
}
</script>

<template>
  <div class="setting-wrap">
    <div class="setting-item">
      <h3>模糊</h3>
      <el-slider v-model="filterObj.blur" :min="0" :max="30" />
    </div>
    <div class="setting-item">
      <h3>亮度</h3>
      <el-slider v-model="filterObj.brightness" :min="0" :max="200" />
    </div>
    <div class="setting-item">
      <h3>对比度</h3>
      <el-slider v-model="filterObj.contrast" :min="0" :max="200" />
    </div>
    <div class="setting-item">
      <h3>灰度</h3>
      <el-slider v-model="filterObj.grayscale" :min="0" :max="200" />
    </div>
    <div class="setting-item">
      <h3>饱和度</h3>
      <el-slider v-model="filterObj.saturate" :min="0" :max="200" />
    </div>
    <div class="setting-item">
      <h3>色相旋转</h3>
      <el-slider v-model="filterObj.hueRotate" :min="0" :max="360" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.setting-wrap {
  h3 {
    display: inline-block;
    width: 80px;
    margin: 0;
    text-align: right;
  }
  :deep() {
    .el-slider {
      padding: 0 10px;
    }
  }
}
</style>
