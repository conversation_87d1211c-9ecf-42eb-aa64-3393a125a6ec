import type { IMobileAwardsAwards, IMobileAwardsRecord } from 'design/layer/mobile-awards/mobile-awards.vue'
import type { ITeamInfo } from 'design/layer/mobile-choose-team/mobile-choose-team.vue'
import { timer } from '@/utils'
import { countdownFormat, dealMoney } from '@/utils/format'
import { useDesignState, useDesignTemp } from 'design/index'
import { closeDialog } from 'vant'
import { useImData } from '~/src/hooks/useImData'
import { useLimit } from '../../hooks/useLimit'
import { useScore } from '../../hooks/useScore'
import { useStatus } from '../../hooks/useStatus'
import { useMobileStore } from '../../stores/useMobileStore'
import { fetchTheme } from '../../utils'
import { mockAwards, mockFinishRankings, mockRecord, mockRegedit, mockTeamList } from '../../utils/mock'
import { relationParticipant } from '../../utils/relation'
import { transformRankings } from '../../utils/transform'
import { mockConfig, mockRound } from './mock'

// 类型
interface IConfig {
  goldcoinv3Id: number
}
interface IRound {
  id: number
  themeId: number
  title: string
  time: number
  startTime?: number
  endTime?: number
  gameType?: 'PERSONAL' | 'TEAM'
  gameMode?: 'SPORTS' | 'SPLIT_MONEY'
  teamJoinType?: 'AUTO' | 'MANUAL'
  maxScore: number
}
interface IRegedit {
  name: string
  avatar: string
  teamId?: number
}

interface IRankings {
  teamId?: number
  count: number
  wxUserId?: number
  id: number
}

export function useMobileGoldcoinv3() {
  const timestamp = useTimestamp()
  const designTemp = useDesignTemp()
  const designState = useDesignState()

  const isDesignEdit = designTemp.isEdit

  designTemp.showType = 'mobile'

  const mobileStore = useMobileStore()
  const { wall, featureConfig, isShowAd, isWedding } = storeToRefs(mobileStore)

  // 接金币轮次配置、报名信息
  const round = ref<IRound>()
  const config = ref<IConfig>()
  // 报名信息
  const regedit = ref<IRegedit | null>()
  // 奖品信息
  const awards = ref<IMobileAwardsAwards>()
  const awardCount = ref<number>()
  // 名次信息
  const record = ref<IMobileAwardsRecord>()
  // 结果排行榜
  const finishRankings = ref<IRankings[]>()

  const roundId = computed(() => round.value?.id || '')

  const isTeamMode = computed(() => {
    return designState.gameMode === '$游戏模式-团队$' || round.value?.gameType === 'TEAM'
  })
  const isPersonalMode = computed(() => {
    if (isTeamMode.value) return false
    return designState.gameMode === '$游戏模式-竞技$' || round.value?.gameMode === 'SPORTS'
  })
  const isSplitMoneyMode = computed(() => {
    if (isTeamMode.value || isPersonalMode.value) return false
    return designState.gameMode === '$游戏模式-分钱$' || round.value?.gameMode === 'SPLIT_MONEY'
  })

  // 活动状态
  const status = useStatus({
    round,
    regedit,
  })

  // 活动限制
  const { limit, dealLimit } = useLimit()

  let regeditCount = 0
  const readRegedit = async () => {
    // 报名 & 更换团队
    if (isDesignEdit) {
      await timer(200)
      regedit.value = mockRegedit(isTeamMode.value ? 1 : 0)
      return
    }
    const goldcoinv3Id = roundId.value
    if (!goldcoinv3Id) return
    if (status.value === 'finish') return
    const res = await api.mobile.goldcoinv3.readRegedit({ where: { goldcoinv3Id } })
    if (res) {
      regedit.value = res
    } else {
      // 个人赛或团队随机加入直接报名
      if (!isTeamMode.value || round.value?.teamJoinType === 'AUTO') {
        if (regeditCount > 5) return
        if (regeditCount) {
          await timer(1500)
        }
        regeditCount++
        await api.mobile.goldcoinv3.insertRegedit({ goldcoinv3Id, teamId: 0 })
        readRegedit()
      } else {
        regedit.value = null
      }
    }
  }

  // 团队相关
  const teamList = ref<ITeamInfo[]>()
  const joinTeam = async (teamId: number) => {
    if (!teamId) return
    if (isDesignEdit) return
    await api.mobile.goldcoinv3.insertRegedit({ goldcoinv3Id: roundId.value, teamId })
    readRegedit()
  }

  const leaveTeam = async () => {
    if (isDesignEdit) return
    await api.mobile.goldcoinv3.quitRegedit({ goldcoinv3Id: roundId.value })
    readRegedit()
  }

  const fetchTeamList = async () => {
    if (isDesignEdit) {
      await timer(200)
      // 如果不需要团队信息直接返回即可
      teamList.value = mockTeamList()
      return
    }
    if (!isTeamMode.value) {
      teamList.value = undefined
      return
    }
    if (!roundId.value) {
      return
    }
    teamList.value = await api.mobile.goldcoinv3.listTeam({ where: { goldcoinv3Id: roundId.value } })
  }

  const prizePool = ref<IMobileAwardsAwards>()
  const fetchPrizePool = async () => {
    if (isDesignEdit) {
      await timer(200)
      prizePool.value = {
        type: 'REDPACK',
        name: '一等奖',
        img: '',
        redpackAmount: 0,
        singleAmount: 0,
      }
      return
    }
    const res = await api.mobile.awards.list({ where: { module: 'goldcoinv3', moduleId: roundId.value, valid: true } })
    prizePool.value = res[0] || {}
  }

  // 积分模式下，最大不能超过maxScore，分钱模式下最大不能超过 redpackAmount/singleAmount
  const maxScore = computed(() => {
    if (isSplitMoneyMode.value) {
      const { redpackAmount = 0, singleAmount = 0 } = prizePool.value || {}
      return redpackAmount / singleAmount
    }
    return round.value?.maxScore || 0
  })
  // 结果上报
  const { score, scoreCount, startReport, getScore, stopReport } = useScore({
    reportFn: async (oldScore) => {
      if (isDesignEdit || !round.value) return Math.max(0, oldScore + scoreCount.value)
      const data = await api.mobile.goldcoinv3.reportRecord({ goldcoinv3Id: round.value.id, score: oldScore })
      dealLimit(data)
      // 返回值需要是最终的分数
      return data.score
    },
    getCurScoreFn: async () => {
      if (isDesignEdit || !round.value) return 0
      return await api.mobile.goldcoinv3.curScore({ goldcoinv3Id: round.value.id })
    },
  })

  let errorCount = 0
  async function fetchRecord() {
    if (isDesignEdit || !round.value) {
      await timer(200)
      record.value = mockRecord()
      // 查询奖品信息
      await fetchAwards()
    } else {
      try {
        const [tempRecord, awardsrecord] = await Promise.all([
          api.mobile.goldcoinv3.readRecord({ where: { goldcoinv3Id: round.value.id } }),
          api.mobile.awards.recordRead({ where: { module: 'goldcoinv3', moduleId: round.value.id } }),
        ])
        if (awardsrecord) {
          tempRecord.awardsId = awardsrecord.awardsId
          tempRecord.redpackAmount = awardsrecord.redpackAmount
          tempRecord.status = awardsrecord.status
        }
        record.value = tempRecord || { count: 0, ranking: 0, redpackAmount: 0 }
        if (record.value?.awardsId) {
          await fetchAwards(record.value?.awardsId)
        } else {
          awards.value = undefined
          awardCount.value = await api.mobile.awards.count({ where: { module: 'goldcoinv3', moduleId: round.value.id } }) || 0
        }
      } catch {
        // 为了避免活动状态是“活动处理中”不显示结果弹窗，多查询几次
        errorCount++
        if (errorCount < 5) {
          setTimeout(() => {
            fetchRecord()
          }, 1000)
        }
      }
    }
  }
  async function fetchAwards(awardsId = 0) {
    if (isDesignEdit) {
      await timer(200)
      awards.value = mockAwards()
      return
    }
    if (!awardsId) {
      awards.value = undefined
      return
    }
    awards.value = await api.mobile.awards.read({ where: { id: awardsId } })
  }

  const fetchFinishRankings = async () => {
    // 结果排行榜
    if (isDesignEdit) {
      await timer(200)
      finishRankings.value = mockFinishRankings()
      return
    }
    if (isTeamMode.value && !teamList.value?.length) {
      await fetchTeamList()
    }
    finishRankings.value = await api.mobile.goldcoinv3.ranking({
      where: {
        goldcoinv3Id: roundId.value,
        showNum: 10,
        state: 'FINISH',
      },
    }).then(res => res.ranking)
    if (!isTeamMode.value) {
      await relationParticipant(finishRankings.value as { wxUserId: number }[])
    }
  }

  const finishTeamListAllRankings = computed(() => {
    if (!finishRankings.value) return finishRankings.value
    return transformRankings({
      rankings: finishRankings.value || [],
      teamList: teamList.value || [],
      isTeam: isTeamMode.value,
    })
  })

  // 主要作用是将数据暴露给图层组件
  designState
    .setStatusList([
      { label: '准备中', value: 'ready' },
      { label: '倒计时', value: '321' },
      { label: '进行中', value: 'ing' },
      { label: '排行榜', value: 'finish' },
    ])
    .setLayerData({
      '#主题名称#': computed(() => designTemp.theme?.name),
      '#轮次标题#': computed(() => round.value?.title),
      '$游戏模式-竞技$': computed(() => isPersonalMode.value),
      '$游戏模式-分钱$': computed(() => isSplitMoneyMode.value),
      '$游戏模式-团队$': computed(() => isTeamMode.value),
      'isShowAd': isShowAd,
      'isWedding': isWedding,
      // 团队信息
      'isTeam': computed(() => isTeamMode.value),
      'teamJoinType': computed(() => round.value?.teamJoinType),
      teamList,
      // 报名信息
      regedit,
      // 排行榜
      'finishRankings': computed(() => finishTeamListAllRankings.value),
      // 成绩信息
      record,
      'ranking': computed(() => record.value?.teamRanking || record.value?.ranking),
      // 奖品信息
      awards,
      'awardCount': computed(() => awardCount.value),
      // 互动倒计时（倒计时组件）
      'downFlagTime': computed(() => {
        if (designState.status === '321') {
          return round.value?.startTime
        } else if (designState.status === 'ing') {
          return round.value?.endTime
        } else {
          return 0
        }
      }),
      '#游戏剩余时间#': computed(() => {
        if (designState.status === 'ing') {
          return `${countdownFormat((round.value?.endTime || 0), {
            format: 's',
            nowTime: timestamp.value,
            padZero: true,
          })}秒`
        } else {
          return '0秒'
        }
      }),
      '#奖池金额#': computed(() => {
        if (isSplitMoneyMode.value) {
          return `￥${dealMoney(prizePool.value?.redpackAmount || 0)}`
        }
        return 0
      }),
      '#当前成绩#': computed(() => {
        if (isSplitMoneyMode.value) {
          return `￥${dealMoney((scoreCount.value || 0) * (prizePool.value?.singleAmount || 0))}`
        } else {
          return `${scoreCount.value || 0}分`
        }
      }),
    })
    // 图层组件事件
    .setLayerEventList([
      {
        eventId: 'back-home',
        name: '返回首页',
        value() {
          window.parent.postMessage({ type: 'iframe:backHome' }, '*')
        },
      },
      // 报名或者更换团队
      {
        eventId: 'join-team',
        value(teamId) {
          joinTeam(teamId)
        },
      },
      {
        eventId: 'leave-team',
        value() {
          leaveTeam()
        },
      },
      // 查询排行榜
      { eventId: 'fetchRankings', value: fetchFinishRankings },
      // 广告交互
      {
        eventId: 'clickAd',
        value(data) {
          window.parent.postMessage({ type: 'iframe:clickAd', data }, '*')
        },
      },
      {
        eventId: 'SHOW_AD',
        value(data) {
          window.parent.postMessage({ type: 'iframe:SHOW_AD', data }, '*')
        },
      },
      {
        eventId: 'clickAwards',
        name: '中奖记录',
        value() {
          window.parent.postMessage({ type: 'iframe:clickAwards' }, '*')
        },
      },
      {
        eventId: 'updateScore',
        value(number) {
          if (limit.value) return
          // 分数增加，或 当前总分数大于要扣的分数。总之要确保最低分数为 0
          if (number >= 0 || scoreCount.value + number >= 0) {
            // 不能超过最大分数的配置
            const newCount = Math.min(scoreCount.value + number, maxScore.value)
            const isMaxCount = newCount === maxScore.value
            score.value = score.value + (isMaxCount ? maxScore.value - scoreCount.value : number)
            if (!isSplitMoneyMode.value) {
              // 分钱模式下，总分根据上报接口获取，避免显示的比实际获取的多
              scoreCount.value = newCount
            }
          } else if (scoreCount.value > 0) {
            // 如果当前总分不够扣，则扣当前总分，扣到总分是 0
            score.value = score.value - scoreCount.value
            scoreCount.value = 0
          }
        },
      },
    ])

  // 变化监控
  watch(
    () => [status.value, roundId.value || 0] as const,
    async ([v], old) => {
      if (!round.value) return
      const [oStatus] = old || []
      if (v === 'ready') {
        fetchTeamList()
        readRegedit()
        fetchPrizePool()
        // 重置
        limit.value = false
        errorCount = 0
      }
      if (v === '321') {
        if (isDesignEdit) {
          round.value.startTime = timestamp.value + 3000
          round.value.endTime = timestamp.value + 3000 + 30 * 1000
        }
      }
      if (!isDesignEdit && v === 'ing' && !['321'].includes(`${oStatus}`)) {
        const data = await api.mobile.wxtag.getLimitInfo({
          where: { module: 'goldcoinv3', actId: roundId.value },
        })
        if (data && data.limitLevel && !data.state) {
          data.state = 'limit'
        }
        dealLimit(data)
      }
      if (v === 'ing') {
        if (isDesignEdit) {
          round.value.startTime = timestamp.value - 3000
          round.value.endTime = timestamp.value - 3000 + 30 * 1000
        }
        score.value = 0
        scoreCount.value = 0
        await getScore()
        startReport()
      } else {
        stopReport(v === 'finish')
      }
      if (v === 'finish') {
        // 查询最终结果
        if (limit.value) {
          closeDialog()
        }
        await fetchRecord()
      }
      designState.setStatus(status.value)
    },
    { immediate: true, deep: true },
  )

  const resetAllData = () => {
    designTemp.renderKey = Date.now()
    record.value = undefined
    finishRankings.value = undefined
    teamList.value = undefined
    awards.value = undefined
    awardCount.value = undefined
    prizePool.value = undefined
    regedit.value = undefined
    regeditCount = 0
  }

  watch(() => round.value?.themeId, themeId => fetchTheme(themeId || ''))
  watch(() => roundId.value, () => {
    if (isDesignEdit) return
    resetAllData()
  })

  tryOnMounted(() => {
    if (isDesignEdit) {
      // 设计模式mock数据
      config.value = mockConfig()
      round.value = mockRound()
    }
  })

  // 数据同步
  useImData({
    'im:goldcoinv3:config': config,
    'im:goldcoinv3': round,
    'im:wall': wall,
    'im:feature:config': featureConfig,
  })
}
