import { HiRequest } from '../request'

export default {
  read: (params: any) => HiRequest.post('/pro/hxc/web/prolotteryv3/read.htm', params),
  start: (params?: any) => HiRequest.post('/pro/hxc/web/prolotteryv3/start.htm', params),
  list: (params?: any) => HiRequest.post('/pro/hxc/web/prolotteryv3/list.htm', params),
  stop: (params?: any) => HiRequest.post('/pro/hxc/web/prolotteryv3/stop.htm', params),
  next: (params?: any) => HiRequest.post('/pro/hxc/web/prolotteryv3/next.htm', params),
  lotteryList: (params: any) => HiRequest.post('/pro/hxc/web/prolotteryv3/lotteryList.htm', params),
  switch: (params: any) => HiRequest.post('/pro/hxc/web/prolotteryv3/switch.htm', params),
  updateState: (params: any) => HiRequest.post('/pro/hxc/web/prolotteryv3/updateState.htm', params),
  updateLottery: (params: any) => HiRequest.post('/pro/hxc/web/prolotteryv3config/updateLottery.htm', params),
}
