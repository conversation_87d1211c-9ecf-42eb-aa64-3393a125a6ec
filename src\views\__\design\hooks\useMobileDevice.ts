// 目前的width、height基于Ai获取手机分辨率，实际渲染高度可能还需要去除状态栏、导航栏的高度

const allMobileDevices = [
  { name: '取消预览' },
  {
    name: '小米 15 Ultra',
    width: 1440,
    height: 3200,
    statusBar: 112,
  },
  {
    name: 'iPhone 16 Pro',
    width: 1284,
    height: 2778,
    statusBar: 100,
  },
  {
    name: 'OPPO Find X8',
    width: 1240,
    height: 2772,
    statusBar: 100,
  },
  {
    name: '华为Pura80 Ultra',
    width: 1344,
    height: 2880,
    statusBar: 100,
  },
  {
    name: '小米平板6 Pro',
    width: 1800,
    height: 2880,
    statusBar: 100,
  },
  {
    name: 'iPad',
    width: 768,
    height: 1024,
    statusBar: 100,
  },
]
const previewMobileDevice = ref('')

export function useMobileDevice() {
  const currentDevice = computed(() => {
    if (previewMobileDevice.value === '取消预览') return null
    const device = allMobileDevices.find(item => item.name === previewMobileDevice.value)
    return device
  })

  return {
    allMobileDevices,
    previewMobileDevice,
    currentDevice,
  }
}
