import type { IDesignLayer, IDesignSetup } from '../../types'
import { merge } from 'lodash-es'
import { openSelectMaterial, ResourceUtil } from '../..'
import { layerUuid } from '../../utils'

import CompSetting from './video-setting.vue'
import Comp from './video.vue'

export const type = 'video'
export const defaultControls = false
export interface IDesignVideo extends IDesignLayer {
  type: typeof type
  data: string
  repeat?: '' | 'x' | 'y'
  muted?: boolean
  loop?: boolean
  autoplay?: boolean
  controls?: boolean
}

export function setup(app: IDesignSetup) {
  app.registry({
    type,
    name: '视频',
    Comp,
    CompSetting,
    async defaultData(options) {
      const data = (options as IDesignVideo)?.data || await openSelectMaterial('VIDEO')
      if (!data) return

      const { width, height } = await ResourceUtil.loadVideo(data, { draftScale: 0.2 })

      return merge({
        uuid: layerUuid(),
        name: '新视频',
        data,
        type,
        base: true,
        muted: true,
        loop: true,
        autoplay: true,
        style: {
          width: `${width}px`,
          height: `${height}px`,
        },
      }, options as IDesignVideo)
    },
  })
}
