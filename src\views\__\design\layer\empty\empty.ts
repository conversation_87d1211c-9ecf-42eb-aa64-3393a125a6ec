import type { IDesignLayer, IDesignSetup } from '../../types'
import { merge } from 'lodash-es'
import { BisTypes } from '../..'
import { layerUuid } from '../../utils'
import CompSetting from './empty-setting.vue'
import Comp from './empty.vue'

// 类型
export const type = 'empty'

// 数据类型约束
export interface IDesignEmpty extends IDesignLayer {
  type: typeof type
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.common,
    type,
    base: true,
    name: '空',
    thumbnail: new URL('./empty.png', import.meta.url).href,
    Comp,
    CompSetting,
    defaultData(options) {
      return merge({
        uuid: layerUuid(),
        name: '空白',
        type,
        style: {
          width: '100px',
          height: '100px',
          background: '#3C55FF',
        },
      }, options as IDesignEmpty)
    },
  })
}
