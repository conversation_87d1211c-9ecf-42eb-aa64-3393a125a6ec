<script setup lang="ts">
import { useDataAttr } from '../..'
import { defaultData, type IDesignIngRank2 } from './ingrank2'

const layer = defineModel<IDesignIngRank2>('layer', { required: true })
const dataOptions = ref([
  { label: '本题', value: 'subject' },
  { label: '本轮', value: 'round' },
])

const avatarSizeBind = useDataAttr(layer.value, 'avatarSize', defaultData.avatarSize)
const nameTextSizeBind = useDataAttr(layer.value, 'nameTextSize', defaultData.nameTextSize)
const nameTextColorBind = useDataAttr(layer.value, 'nameTextColor', defaultData.nameTextColor)
const socreTextSizeBind = useDataAttr(layer.value, 'socreTextSize', defaultData.socreTextSize)
const socreTextColorBind = useDataAttr(layer.value, 'socreTextColor', defaultData.socreTextColor)
const maxCountBind = useDataAttr(layer.value, 'maxCount', defaultData.maxCount)
// const showTimeBind = useDataAttr(layer.value, 'showTime', defaultData.showTime)
const dataSourceBind = useDataAttr(layer.value, 'dataSource', defaultData.dataSource)
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>数据来源</h3>
        <el-select v-model="dataSourceBind" placeholder="请选择数据源" style="width: 120px">
          <el-option v-for="item in dataOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="setting-item">
        <h3>展示数量</h3>
        <el-input-number v-model="maxCountBind" v-input-number controls-position="right" :max="50" :min="1" :step="1" />
      </div>
      <div class="setting-item">
        <h3>头像大小</h3>
        <el-input-number v-model="avatarSizeBind" v-input-number controls-position="right" :min="1" :step="1" />
      </div>
      <div class="setting-item">
        <h3>昵称字号</h3>
        <div class="flex">
          <el-input-number v-model="nameTextSizeBind" v-input-number controls-position="right" :min="1" :step="1" />
        </div>
      </div>
      <div class="setting-item">
        <h3>昵称字色</h3>
        <hi-color v-model="nameTextColorBind" />
      </div>
      <div class="setting-item">
        <h3>分数字号</h3>
        <div class="flex">
          <el-input-number v-model="socreTextSizeBind" v-input-number controls-position="right" :min="1" :step="1" />
        </div>
      </div>
      <div class="setting-item">
        <h3>分数字色</h3>
        <hi-color v-model="socreTextColorBind" />
      </div>
      <!-- <div class="setting-item">
        <h3>展示用时</h3>
        <el-switch v-model="showTimeBind" />
      </div> -->
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
