<script setup lang="ts">
import type { IDesignRotate } from './rotate'
import { getDefaultMaterial, openSelectMaterial } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'

const layer = defineModel<IDesignRotate>('layer', { required: true })

async function updateMaterialFn(isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, `data.src`)
    : await openSelectMaterial('PIC')
  if (result || isReset) {
    layer.value.data.src = result
  }
}
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <div class="thumbnail-box bgblank">
          <MaterialThumbnail @select="updateMaterialFn()" @reset="updateMaterialFn(true)">
            <img :src="layer.data.src">
          </MaterialThumbnail>
        </div>
      </div>
      <div class="setting-item">
        <div class="setting-item-label">持续时间</div>
        <div class="setting-item-content">
          <el-input-number v-model="layer.data.duration" v-input-number :min="1" :max="60" :controls="false"></el-input-number>
          s
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.thumbnail-box {
  width: 120px;
  height: 120px;
  outline: 1px solid #e6ebed;
  border-radius: 4px;
  position: relative;

  img {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }

  > img {
    object-fit: contain;
  }
}
</style>
