<script setup lang="ts">
import type { IDesignWallqrcode } from './wallqrcode'
import { useDesignState } from '../..'
import miniprogram from './components/miniprogram.vue'

// const layer = defineModel<IDesignWallqrcode>('layer', { required: true })
// const scale = injectScale()
const designState = useDesignState()

interface QrcodeOption {
  actQrcodeUrl: string
  isShowDesignQrcode: boolean
  wallConfigQrCode: string
  miniprogramLogo: string
}
const domRef = ref<HTMLElement>()

const qrcodeOptions = computed<QrcodeOption>(() => {
  return designState.getLayerData('qrcodeOptions') || {} as QrcodeOption
})

const isShowDesignQrcode = computed(() => {
  return qrcodeOptions.value?.isShowDesignQrcode ?? false
})
const defaultQrcodeImg = new URL('./assets/qrcode.png', import.meta.url).href

const actQrcodeUrl = computed(() => {
  return qrcodeOptions.value.actQrcodeUrl || defaultQrcodeImg
})
const wallConfigQrCode = computed(() => {
  return qrcodeOptions.value.wallConfigQrCode || ''
})
</script>

<template>
  <div ref="domRef" class="wallqrcode-box">
    <template v-if="isShowDesignQrcode">
      <miniprogram v-if="wallConfigQrCode.includes('wxminiprogram/qrcode')" :qrcode-img="wallConfigQrCode" :logo="qrcodeOptions.miniprogramLogo">
      </miniprogram>
      <img v-else :src="wallConfigQrCode" alt="扫描二维码1" />
    </template>
    <template v-else>
      <miniprogram v-if="actQrcodeUrl.includes('proutilsalias/fetchQrcode')" :qrcode-img="actQrcodeUrl" :logo="qrcodeOptions.miniprogramLogo">
      </miniprogram>
      <img v-else :key="actQrcodeUrl" :src="actQrcodeUrl" alt="扫描二维码2" />
    </template>
  </div>
</template>

<style scoped lang="scss">
.wallqrcode-box {
  width: 100%;
  height: 100%;
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
</style>
