<script setup lang="ts">
import HiAudioSel from 'design/components/audio.vue'
import { getDefaultMaterial, openSelectMaterial, useDataAttr } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'
import { getImageInfo } from '../../utils'
import { DEFAULT_DATA, type IDesignMobileGoldcoinIng } from './mobile-goldcoin-ing'

const layer = defineModel<IDesignMobileGoldcoinIng>('layer', { required: true })

const bombMusicBind = useDataAttr(layer.value.data, 'bombMusic', DEFAULT_DATA.bombMusic)
const bombImageBind = useDataAttr(layer.value.data, 'bombImage', DEFAULT_DATA.bombImage)
const successBombImageBind = useDataAttr(layer.value.data, 'successBombImage', DEFAULT_DATA.successBombImage)
const successMusicBind = useDataAttr(layer.value.data, 'successMusic', DEFAULT_DATA.successMusic)
const receiverBind = useDataAttr(layer.value.data, 'receiver', DEFAULT_DATA.receiver)
const fallingsBind = useDataAttr(layer.value.data, 'fallings', DEFAULT_DATA.fallings)
const fallingsDensityBind = useDataAttr(layer.value.data, 'fallingsDensity', DEFAULT_DATA.fallingsDensity)
const fallingSpeedBind = useDataAttr(layer.value.data, 'fallingSpeed', DEFAULT_DATA.fallingSpeed)

async function updateMaterialFn(name: string, index?: number, isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, `data.${name}${index !== undefined ? `.${index}` : ''}`)
    : await openSelectMaterial('PIC')
  // 上传的时候提前计算图片真实宽高
  const { width, height } = await getImageInfo(result)
  switch (name) {
    case 'fallings':
      if (index !== undefined) {
        fallingsBind.value[index] = {
          ...fallingsBind.value[index],
          imgUrl: result,
          width,
          height,
        }
      }
      break
    case 'successBombImage':
      successBombImageBind.value = result
      break
    case 'bombImage':
      bombImageBind.value = result
      break
    case 'receiver':
      receiverBind.value = {
        ...layer.value.data.receiver,
        imgUrl: result,
        width,
        height,
      }
      break
  }
}

function removeFallings(index: number) {
  fallingsBind.value.splice(index, 1)
}

function addFallings(index: number, defaultValue: any) {
  fallingsBind.value.splice(index + 1, 0, defaultValue)
}
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item flex-items-start!">
        <h3>接收者</h3>
        <div>
          <div
            class="relative mb-10 h-60 w-160 flex items-center"
          >
            <MaterialThumbnail @select="updateMaterialFn('receiver')" @reset="updateMaterialFn('receiver', undefined, true)">
              <img :src="receiverBind.imgUrl" class="bgblank mr-6px size-60px object-contain">
            </MaterialThumbnail>
            <div class="ml-10px flex flex-col items-center justify-center">
              <span>自动旋转</span>
              <el-switch v-model="receiverBind.rotate" class="mt-5" />
            </div>
          </div>
        </div>
      </div>
      <div class="setting-item flex-items-start!">
        <h3>掉落物</h3>
        <div>
          <div
            v-for="(item, index) in fallingsBind"
            :key="index"
            class="relative mb-10 h-60 w-160 flex items-center"
          >
            <MaterialThumbnail @select="updateMaterialFn('fallings', index)" @reset="updateMaterialFn('fallings', index, true)">
              <img :src="item.imgUrl" class="bgblank mr-6px size-60px object-contain">
            </MaterialThumbnail>
            <div class="item-score">
              <span class="ml-6px">接中得分</span>
              <el-input-number v-model="item.score" v-input-number :min="-10" :max="10" controls-position="right" class="mt-5 w-80" />
            </div>
            <div class="ml-6 w-20 pt-10">
              <icon-ph:minus-bold v-if="fallingsBind.length > 1" @click.stop="removeFallings(index)" />
              <icon-ph:plus-bold v-if="fallingsBind.length < 10" @click.stop="addFallings(index, item)" />
            </div>
          </div>
        </div>
      </div>
      <div class="setting-item flex-items-start!">
        <h3>得分效果</h3>
        <div class="flex-1">
          <MaterialThumbnail class="mr-6px" @select="updateMaterialFn('successBombImage')" @reset="updateMaterialFn('successBombImage', undefined, true)">
            <img :src="successBombImageBind" class="bgblank size-60px object-contain">
          </MaterialThumbnail>
        </div>
      </div>
      <div class="setting-item flex-items-start!">
        <h3>得分音效</h3>
        <div class="flex-1">
          <HiAudioSel :url="successMusicBind" />
        </div>
      </div>
      <div class="setting-item flex-items-start!">
        <h3>扣分效果</h3>
        <div class="flex-1">
          <MaterialThumbnail class="mr-6px" @select="updateMaterialFn('bombImage')" @reset="updateMaterialFn('bombImage', undefined, true)">
            <img :src="bombImageBind" class="bgblank size-60px object-contain">
          </MaterialThumbnail>
        </div>
      </div>
      <div class="setting-item flex-items-start!">
        <h3>扣分音效</h3>
        <div class="flex-1">
          <HiAudioSel :url="bombMusicBind" />
        </div>
      </div>
      <div class="setting-item flex-items-start!">
        <h3>掉落速度</h3>
        <div class="mx-10px flex-1">
          <el-slider v-model="fallingSpeedBind" :step="1" :min="1" :max="10" />
        </div>
      </div>
      <div class="setting-item flex-items-start!">
        <h3>掉落密度</h3>
        <div class="mx-10px flex-1">
          <el-slider v-model="fallingsDensityBind" :step="1" :min="1" :max="10" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
