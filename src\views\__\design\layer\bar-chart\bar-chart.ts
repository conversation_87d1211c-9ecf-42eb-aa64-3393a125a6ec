import type { IDesignLayer, IDesignSetup } from '../../types'
import { BisTypes } from '../..'
import { useDesignData } from '../../index'
import { InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './bar-chart-setting.vue'
import Comp from './bar-chart.vue'

// 类型
export const type = 'bar-chart'
export const defaultColor = '#000'
export const defaultDirection = 'horizontal'
export const defaultBarColorsType = 'all'
export const defaultBarColor = '#18cfa1'
export const defaultShowSort = false
export const defalutLableRotate = 0
export const defalutLableLenth = 4

// 数据类型约束
export interface IDesignBarChart extends IDesignLayer {
  type: typeof type
  dataSource?: string
  labelFontSize: number
  labelFontColor: string
  valueFontSize: number
  valueFontColor: string
  barWidth: number
  barRadius: number
  direction?: string
  barColorsType?: string
  barColor?: string
  showSort?: boolean
  lableRotate?: number
  lableLenth?: number
  barColorsAll: string[]
  barColorsOnes: string[]
  data: string | number
}

// 注册组件
export function setup(app: IDesignSetup) {
  const designData = useDesignData()
  app.registry({
    bisType: BisTypes.sportsIng,
    showType: ['pcwall'],
    status: ['ing'],
    showInteractive: [InteractiveEnum.performancev3],
    type,
    name: '柱状图',
    thumbnail: new URL('./bar-chart.png', import.meta.url).href,
    Comp,
    CompSetting,
    defaultData() {
      return {
        type,
        uuid: layerUuid(),
        name: '柱状图',
        data: 0,
        labelFontSize: 14,
        labelFontColor: '#fff',
        valueFontSize: 14,
        valueFontColor: '#fff',
        barWidth: 20,
        barRadius: 0,
        barColorsAll: [],
        barColorsOnes: [defaultBarColor],
        style: {
          left: `${designData.option.drafts[0] * 0.1}px`,
          top: `${designData.option.drafts[1] * 0.1}px`,
          width: `${designData.option.drafts[0] * 0.8}px`,
          height: `${designData.option.drafts[1] * 0.8}px`,
          background: 'rgba(0, 0, 0, 0.3)',
        },
      }
    },
  })
}
