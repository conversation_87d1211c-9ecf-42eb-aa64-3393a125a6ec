kind: pipeline
type: kubernetes
name: build
trigger:
  ref:
    - refs/heads/uat-*
    - refs/tags/*
concurrency:
  limit: 1
image_pull_secrets:
  - DOCKER_CONFIG_JSON
steps:
  - name: prepare
    image: ccr.ccs.tencentyun.com/hxc-base/dk-prepare:v3.1.2
    pull: if-not-exists

  - name: cache-restore
    image: ccr.ccs.tencentyun.com/hxc-base/dk-minio-client:v3.1.0
    pull: if-not-exists

  - name: build
    image: ccr.ccs.tencentyun.com/hxc-base/dk-node:v4.1.0
    pull: if-not-exists
    commands:
      - /opt/build.sh
    resources:
      requests:
        cpu: 1000
        memory: 4000MiB

  - name: cache-rebuild
    image: ccr.ccs.tencentyun.com/hxc-base/dk-minio-client:v3.1.0
    pull: if-not-exists
    environment:
      SOURCE_FILE: __store

  - name: qiniu-restore
    image: ccr.ccs.tencentyun.com/hxc-base/dk-minio-client:v3.1.0
    pull: if-not-exists
    environment:
      REMOTE_FILE: fe_${DRONE_REPO_NAME}_cache_v2.json

  - name: qiniu
    image: ccr.ccs.tencentyun.com/hxc-base/dk-qiniu:v3.3.12
    pull: if-not-exists
    environment:
      QINIU_CACHE_FILE: fe_${DRONE_REPO_NAME}_cache_v2.json

  - name: qiniu-rebuild
    image: ccr.ccs.tencentyun.com/hxc-base/dk-minio-client:v3.1.0
    pull: if-not-exists
    environment:
      SOURCE_FILE: fe_${DRONE_REPO_NAME}_cache_v2.json

  - name: deploy
    image: ccr.ccs.tencentyun.com/hxc-base/dk-deploy:v3.1.0
    pull: if-not-exists

  - name: notice-success
    image: ccr.ccs.tencentyun.com/hxc-base/dk-notice:v3.3.2
    pull: if-not-exists
    when:
      status:
        - success

  - name: notice-failure
    image: ccr.ccs.tencentyun.com/hxc-base/dk-notice:v3.3.2
    pull: if-not-exists
    environment:
      DRONE_STAGE_STATUS: failure
    when:
      status:
        - failure
---
kind: secret
name: DOCKER_CONFIG_JSON
get:
  path: base-drone-secret
  name: DOCKER_CONFIG_JSON
