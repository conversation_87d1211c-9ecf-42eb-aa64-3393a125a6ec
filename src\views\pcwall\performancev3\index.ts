import type { ILayerTypes } from '../../__/design/types'
import { useInstanceRouter } from '@/hooks/useInstanceRouter'
import { timer } from '@/utils'
import { useDesignData, useDesignState, useDesignTemp } from '@/views/__/design'
import { set } from 'lodash-es'
import { useImData } from '~/src/hooks/useImData'
import { useParticipantStore, usePcwallStore } from '..'

interface IConfig {
  performanceV3Id: number
  performanceThemeLimit: 'N' | 'Y'
  performanceAdvancedLimit: 'N' | 'Y'
}
interface IRound {
  id: number
  themeId: number
  title: string
  startTime?: number
  endTime?: number
}
interface FormItem {
  id?: number
  name: string
  type: string
  [key: string]: any
}
interface IstatisticsItem {
  title: FormItem[]
  data: { [key: string]: string | number }[]
}

interface DtaSourceItem {
  id: number
  name: string
  type: string
  [key: number | string]: any
}

export function usePcwallPerformancev3() {
  const router = useInstanceRouter()
  const route = router.currentRoute.value
  const isManage = route.path.startsWith('/manage/')
  // const timestamp = useTimestamp()
  // 逻辑 /////////////////////
  const designTemp = useDesignTemp()
  const designData = useDesignData()
  const designState = useDesignState()

  const pcwallStore = usePcwallStore()
  const participantStore = useParticipantStore()

  const isDesignEdit = designTemp.isEdit

  const config = ref<IConfig>()
  const round = ref<IRound>()
  const status = ref<string>('ing')
  const dataSourceBindList = ref<string[]>([])
  const bindNumList = ref<string[]>([])
  const allTableStatistics = ref<{ [key: number | string]: IstatisticsItem }>({})
  const dataSourceList = ref<DtaSourceItem[]>([])
  const formlist = ref<FormItem[]>([])
  let pollTimer: NodeJS.Timeout | undefined

  const formObj = computed(() => {
    return formlist.value.reduce((obj, item: FormItem) => {
      item.key && (obj[item.key] = item)
      return obj
    }, {} as { [key: string]: FormItem })
  })
  const dataSourceListObj = computed(() => {
    return dataSourceList.value.reduce((obj, item: DtaSourceItem) => {
      obj[item.key] = item
      return obj
    }, {} as {
      [key: string]: DtaSourceItem
    })
  })

  // 表格类型的源数据
  const dataSourceListType = computed(() => {
    return dataSourceList.value.filter(item => item.type === 'LIST')
  })
  // 数字类型的源数据
  const dataSourceNumType = computed(() => {
    return dataSourceList.value.filter(item => item.type === 'NUM')
  })
  // 表格类型的源数据的title
  const ruleTitleObj = computed(() => {
    const obj: { [key: string]: FormItem[] } = {}
    dataSourceListType.value.forEach((item) => {
      const showArr = item.show.split(',')
      const titleArr: FormItem[] = []
      showArr.forEach((key: string) => {
        if (key === 'wx_user_id') {
          titleArr.push({ key, name: '头像', type: 'COMMON' })
        } else if (key === 'id') {
          titleArr.push({ key, name: '序号', type: 'COMMON' })
        } else {
          titleArr.push(formObj.value[key])
        }
      })
      obj[item.key] = titleArr
    })
    return obj
  })

  // 数字类型的数据
  const dataNumObj = ref<{ [key: string]: number | string }>({})
  async function fetchRound() {
    round.value = undefined
    if (isDesignEdit) {
      await timer(200)
      round.value = {
        id: 1,
        themeId: 1,
        title: '业绩目标会',
      }
    }
  }
  async function fetchFormlist() {
    // 查询表头
    const apiUrl = isManage ? api.man.performancev3.formlist : api.pcwall.performancev3.formlist
    const data = await apiUrl({})
    formlist.value = data
  }
  // 接口定义
  async function fetchConfig() {
    if (isDesignEdit) {
      await timer(200)
      config.value = { performanceV3Id: 1, performanceThemeLimit: 'Y', performanceAdvancedLimit: 'Y' }
    }
  }

  async function fetchTheme() {
    // 查询主题
    if (isDesignEdit) {
      designTemp.fetchTheme()
    } else {
      const { themeId } = round.value || {}
      if (!themeId) return
      const theme = await api.pcwall.theme.readSimple({ where: { id: themeId } })
      designTemp.theme = theme
      if (theme) {
        designData.setState(JSON.parse(theme.webContent))
      }
    }
  }
  async function fetchRulelist() {
    const apiUrl = isManage ? api.man.performancev3.rulelist : api.pcwall.performancev3.rulelist
    const data = await apiUrl({})
    dataSourceList.value = data
  }
  // 收集绑定数字变量的富文本
  function collectAllBindNum() {
    const richLayers: ILayerTypes[] = []
    const walk = (list: ILayerTypes[]) => {
      for (const item of list) {
        if (item.type === 'text-rich') {
          richLayers.push(item)
        }
        if (item.type === 'group') {
          walk(item.layers)
        }
      }
    }
    walk(designData.layers)
    const numSource: { [key: string]: any } = {}
    dataSourceNumType.value.forEach((item) => {
      numSource[`#${item.name}#`] = item.key
    })
    if (richLayers.length) {
      richLayers.forEach((item) => {
        const match = (item as any).data?.match(/#.+#/g)
        if (match) {
          match.forEach((key: any) => {
            const numKey = numSource[key]
            if (numKey && !bindNumList.value.includes(numKey)) {
              bindNumList.value.push(numKey)
            }
          })
        }
      })
    }
  }
  async function fetchStatistics(keyList: string[]) {
    if (!keyList.length) return
    keyList = keyList.filter(key => dataSourceListObj.value[key])

    const apiUrl = isManage ? api.man.performancev3.recordquery : api.pcwall.performancev3.recordquery
    const resData = await apiUrl({ where: { keyList } })

    keyList.forEach(async (key) => {
      const type = dataSourceListObj.value[key]?.type
      let data: any = resData[key] || []
      if (type === 'NUM') {
        const show = dataSourceListObj.value[key]?.show
        dataNumObj.value[key] = data[show]
      } else {
        if (data.length && data.some((item: any) => item.wx_user_id)) {
          const wxUserIdList = data.map((item: any) => item.wx_user_id).filter((item: number) => item)
          await participantStore.relationParticipant(wxUserIdList)
          data = data.map((item: any) => {
            item.avatar = participantStore.getAvatar(item.wx_user_id)
            item.wxName = participantStore.getWxName(item.wx_user_id)
            return item
          })
        }
        allTableStatistics.value[key].data = data
      }
    })
  }
  async function intitAllStatistics() {
    clearInterval(pollTimer)

    if (isDesignEdit) {
      const keyList = dataSourceList.value.map(item => item.key)
      await fetchStatistics(keyList)
    } else {
      await fetchStatistics([...dataSourceBindList.value, ...bindNumList.value])

      pollTimer = setInterval(() => {
        fetchStatistics([...dataSourceBindList.value, ...bindNumList.value])
      }, 2000)
    }
  }
  function initTitie() {
    dataSourceList.value.forEach(async (item) => {
      const key = item.key
      if (key) {
        const title = ruleTitleObj.value[key] || []
        allTableStatistics.value[key] = {
          title,
          data: [],
        }
      }
    })
    dataSourceNumType.value.forEach((item) => {
      const key = item.key
      if (key) {
        dataNumObj.value = set(dataNumObj.value, key, 0)
      }
    })
  }
  // 主要作用是将数据暴露给图层组件
  designState
    .setStatusList([
      { label: '进行中', value: 'ing' },
    ])
    // 图层组件事件
    .setLayerEventList([
      {
        eventId: 'dataSourceBlind',
        value(data: string) {
          if (!dataSourceBindList.value.includes(data)) {
            dataSourceBindList.value.push(data)
            // fetchStatistics(data)
          }
        },
      },
      {
        eventId: 'richtextBlind',
        value(data: string) {
          console.log('----richtextBlind', data)
        },
      },
    ])
    .setLayerData({
      allTableStatistics: computed(() => {
        return allTableStatistics.value
      }),
      listDataSourceList: computed(() => {
        return dataSourceListType.value
      }),
    })
    .setStatus(status.value)

  // 初始化设计状态变量
  function initDesignStateVariable() {
    dataSourceNumType.value.forEach(async (item) => {
      if (!designState.layerData[item.name]) {
        designState.addLayerData({
          [`#${item.name}#`]: computed(() => {
            return dataNumObj.value[item.key]
          }),
        })
      }
    })
  }

  watch(() => config.value?.performanceV3Id, fetchRound)
  watch(() => round.value?.id, async () => {
    // if (isDesignEdit) return
    // 重置数据
  })
  watch(() => round.value?.themeId, async () => {
    await fetchTheme()
    await fetchFormlist()
    await fetchRulelist()
    initTitie()
    collectAllBindNum()
    initDesignStateVariable()
    await intitAllStatistics()
  })
  // 数据同步
  useImData({
    'im:performancev3:config': config,
    'im:performancev3': computed({
      get() { return round.value },
      async set(v) {
        if (v?.id !== round.value?.id) {
          round.value = undefined
          await nextTick()
        }
        console.log('im:performancev3', v)
        round.value = v
      },
    }),
  })
  ///////////////////////
  tryOnMounted(async () => {
    await pcwallStore.fetchApplySignConfig()
    if (isDesignEdit) {
      fetchConfig()
      dataSourceBindList.value = []
    }
  })
  tryOnBeforeUnmount(() => {
    clearInterval(pollTimer)
  })
}
