<script setup lang="ts">
import type { IDesignImage, IMode, IRepeat } from './image'
import { openSelectMaterial, useDataAttr, useDesignTemp } from '../../index'
import { defaultMode, defaultPosition, defaultRepeat, defaultSize } from './image'

const layer = defineModel<IDesignImage>('layer', { required: true })

const designTemp = useDesignTemp()
const isMobileMode = computed(() => designTemp.showType === 'mobile')

const modes: { label: string, value: IMode }[] = [
  { label: '手动', value: 'none' },
  { label: '适应', value: 'contain' },
  { label: '拉伸', value: 'fill' },
  { label: '裁剪', value: 'cover' },
  { label: '自动', value: 'auto' },
]
const repeats: { label: string, value: IRepeat }[] = [
  { label: '无', value: 'no-repeat' },
  { label: '横向', value: 'repeat-x' },
  { label: '纵向', value: 'repeat-y' },
  { label: '平铺', value: 'repeat' },
]

async function updateMaterialFn() {
  const result = await openSelectMaterial('PIC')
  if (result) {
    layer.value.data = result
  }
}

const modeBind = useDataAttr(layer.value, 'mode', defaultMode)
const repeatBind = useDataAttr(layer.value, 'repeat', defaultRepeat)
const sizeXBind = useDataAttr(layer.value, 'sizeX', defaultSize)
const sizeYBind = useDataAttr(layer.value, 'sizeY', defaultSize)
const positionXBind = useDataAttr(layer.value, 'posX', defaultPosition)
const positionYBind = useDataAttr(layer.value, 'posY', defaultPosition)
const colorBind = useDataAttr(layer.value, 'color', '')
const hasQrcodeBind = useDataAttr(layer.value, 'hasQrcode', false)
const clickPreviewSwitchBind = useDataAttr(layer.value, 'clickPreviewSwitch', false)
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <div class="thumbnail-box bgblank" @click="updateMaterialFn">
          <img :src="layer.data">
        </div>
      </div>
      <div class="setting-item">
        <h3>显示模式</h3>
        <el-select v-model="modeBind" placeholder="选择填充方式" class="w-100">
          <el-option v-for="item in modes" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div v-if="modeBind === 'none'" class="setting-item">
        <h3>横向大小</h3>
        <el-slider v-model="sizeXBind" :min="0" :max="100" :step="1" :format-tooltip="v => `${v}%`" class="mx-10 flex-1" />
      </div>
      <div v-if="modeBind === 'none'" class="setting-item">
        <h3>纵向大小</h3>
        <el-slider v-model="sizeYBind" :min="0" :max="100" :step="1" :format-tooltip="v => `${v}%`" class="mx-10 flex-1" />
      </div>
      <template v-if="modeBind === 'none' || modeBind === 'contain' || modeBind === 'auto'">
        <div class="setting-item">
          <h3>平铺方式</h3>
          <el-select v-model="repeatBind" placeholder="选择平铺方式" class="w-100">
            <el-option v-for="item in repeats" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="setting-item">
          <h3>横向位置</h3>
          <el-slider v-model="positionXBind" :min="0" :max="100" :step="1" :format-tooltip="v => `${v}%`" class="mx-10 flex-1" />
        </div>
        <div class="setting-item">
          <h3>纵向位置</h3>
          <el-slider v-model="positionYBind" :min="0" :max="100" :step="1" :format-tooltip="v => `${v}%`" class="mx-10 flex-1" />
        </div>
      </template>
      <div class="setting-item">
        <div class="flex items-center">
          图片颜色
          <el-tooltip effect="dark">
            <template #content>注意：<br>1. 该设置项只对透明图片有效<br>2. 设置了颜色二维码长按识会失效</template>
            <icon-ph-question-bold class="ml-5" />
          </el-tooltip>
        </div>
        <hi-color v-model="colorBind" type="both" />
      </div>
      <div v-if="modeBind === 'fill'" class="setting-item">
        <div class="flex items-center">
          含有二维码
          <el-tooltip effect="dark">
            <template #content>注意：主要为了解决 ios 中二维码识别不到问题</template>
            <icon-ph-question-bold class="ml-5" />
          </el-tooltip>
        </div>
        <el-switch
          v-model="hasQrcodeBind"
          size="default"
          inline-prompt
          inactive-text="无"
          active-text="有"
        />
      </div>
      <div v-if="isMobileMode" class="setting-item">
        <div class="flex items-center">
          点击放大预览
        </div>
        <el-switch
          v-model="clickPreviewSwitchBind"
          size="default"
          inline-prompt
          inactive-text="关"
          active-text="开"
        />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.thumbnail-box {
  width: 120px;
  height: 120px;
  outline: 1px solid #e6ebed;
  border-radius: 4px;
  position: relative;

  img {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }

  > img {
    object-fit: contain;
  }
}
</style>
