import type { IDesignTemplateSetup } from '../../types'
import { envUtils } from '~/src/utils/env'
import { layerUuid } from '../../utils'

export function setup(app: IDesignTemplateSetup) {
  if (!envUtils.isDev) return
  app.registry({
    thumbnail: new URL(`./template1.png`, import.meta.url).href,
    defaultData() {
      return {
        uuid: layerUuid(),
        name: `模板`,
        type: 'group',
        templateId: `template-ef4njl6uo1`,
        style: { width: '100px', height: '100px', background: 'green' },
        layers: [
          {
            uuid: layerUuid(),
            type: 'text',
            name: '文本',
            data: '活动介绍',
            style: {},
          },
        ],
      }
    },
  })
}
