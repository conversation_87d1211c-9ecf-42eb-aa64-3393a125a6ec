<script setup lang="ts">
import type { IDesignListlotteryIng3 } from './listlottery-ing3'
import gsap from 'gsap'
import { sample, throttle } from 'lodash-es'
import * as THREE from 'three'
import { CSS3DObject, CSS3DRenderer, CSS3DSprite } from 'three/examples/jsm/renderers/CSS3DRenderer.js'
import { useDesignState } from '../..'
import { defalutTextStyle, defaultAnimateSpeed, defaultHeadSizeH, defaultHeadSizeW } from './listlottery-ing3'

const layer = defineModel<IDesignListlotteryIng3>('layer', { required: true })
const designState = useDesignState()
const status = computed(() => designState.status)

const timeline = gsap.timeline({ repeat: -1, paused: false })
const headSizeW = computed(() => layer.value.headSizeW ?? defaultHeadSizeW)
const headSizeH = computed(() => layer.value.headSizeH ?? defaultHeadSizeH)
const animiteSpeed = computed(() => layer.value.animiteSpeed ?? defaultAnimateSpeed)

// 参与人奖池
const regeditList = computed(() => {
  return designState.getLayerData('regeditList') || []
})
const importformShowOption = computed<Record<number, boolean>>(() => {
  return designState.getLayerData('importformShowOption') || []
})

const threeRoot = ref<HTMLElement>()
const threeRootSize = useElementSize(threeRoot)

let requestAnimationFrameId: number | null = null

let camera: THREE.PerspectiveCamera
let scene: THREE.Scene
let renderer: CSS3DRenderer
let group: THREE.Group | null = null
const gridDimensions: { wc: number, hc: number, dc: number } = { wc: 5, hc: 5, dc: 10 }
const gridSpacing = 40 * 3
let tickerCallback: ((time: number, deltaTime: number) => void) | null = null
const cameraZ = 300 // 相机 Z 位置

function getItem() {
  const tem = sample(regeditList.value)
  const itemData = Object.assign({}, tem)
  return itemData
}
function getItemBgColor() {
  const arr = layer.value.itemBgColors as string[]
  const index = Math.floor(Math.random() * arr.length)
  return arr[index]
}
function createElement(itemData?: any) {
  const element = document.createElement('div')
  element.setAttribute('class', 'css3d')
  element.style = `
    width: ${headSizeW.value}px;
    height:${headSizeH.value}px;
    font-size: 40px;
    background: ${getItemBgColor()};
  `
  const arr: string[] = []
  layer.value.contentStyle.forEach((item, index) => {
    if (importformShowOption.value[index]) {
      if (itemData[`nameD${index}`]) {
        arr.push(`
      <div style="font-size:${item.fontSize}px;color:${item.fontColor};font-weight:${item.fonBold ? 'bold' : 'normal'}">${itemData[`nameD${index}`]}</div>
      `)
      }
    }
  })
  const str = arr.join('')
  element.innerHTML = str
  return element
}

function initShape() {
  if (!scene) {
    return
  }
  if (group) {
    const children = group.children
    scene.remove(group)
    group = null
    for (const item of children) {
      if (item instanceof CSS3DObject || item instanceof CSS3DSprite) {
        item.element.remove()
      }
    }
  }

  group = new THREE.Group()
  scene.add(group)

  const { wc, hc, dc } = gridDimensions

  for (let d = 0; d < dc; d++) {
    for (let h = 0; h < hc; h++) {
      for (let w = 0; w < wc; w++) {
        const itemData = getItem()
        if (!itemData) continue

        const element = createElement(itemData)
        const object = new CSS3DObject(element)
        // 计算初始位置
        const x = (w - wc / 2 + 0.5) * gridSpacing
        const y = (h - hc / 2 + 0.5) * gridSpacing
        const z = (d - dc / 2 + 0.5) * gridSpacing // Z 轴从负到正排列
        object.position.set(x, y, z)
        object.scale.set(0.1, 0.1, 0.1)

        // object.lookAt(camera.position)

        group.add(object)
      }
    }
  }
  timeline.clear()
  play()
  timeline.timeScale(animiteSpeed.value)
}

function play() {
  if (!group) {
    return
  }
  // --- 从配置计算动画参数 ---
  const { dc } = gridDimensions
  const size = gridSpacing
  const totalDepth = dc * size
  // 调整阈值，让元素在更靠近相机（甚至稍微穿过相机）后才循环，避免在视野边缘闪烁
  const frontThreshold = cameraZ - size * 0.5 // 当元素的中心点到达相机位置再往前一点

  const centerToleranceFactor = 0.6
  // 计算最后层后面的 Z 位置，确保元素生成在视图之外
  // backPosition 应该是整个深度范围的最远端
  const backPosition = (0 - dc / 2) * size - size // 确保元素从足够远的地方开始

  const baseEffectiveDuration = 20 // Base duration for one cycle
  const centerTolerance = size * centerToleranceFactor // 中心判断的具体像素容差
  const fadeInEndProgress = 0.3 // 透明度渐变结束点
  const fadeOutStartProgress = 0.95 // 透明度渐变开始点
  const maxScaleValue = 0.2 // 定义一个最大缩放值，

  // 移除旧的 ticker 回调
  if (tickerCallback) {
    gsap.ticker.remove(tickerCallback)
    tickerCallback = null
  }

  // 创建新的 ticker 回调
  tickerCallback = (time: number, deltaTime: number) => {
    if (!group || !group.visible) return

    const dtSeconds = deltaTime / 1000 // 增量时间 (秒)
    const currentAnimSpeed = animiteSpeed.value <= 0 ? 0.0001 : animiteSpeed.value
    const moveSpeed = totalDepth / (baseEffectiveDuration / currentAnimSpeed)

    group.children.forEach((object) => {
      // 动画逻辑应该针对 CSS3DObject
      if (!(object instanceof CSS3DObject)) return

      // 1. 更新 Z 轴位置
      object.position.z += moveSpeed * dtSeconds

      // 判断是否为中心元素
      const isCenter = Math.abs(object.position.x) < centerTolerance && Math.abs(object.position.y) < centerTolerance

      // 2. 检查是否超过前方阈值，需要循环
      if (object.position.z > frontThreshold) {
        // 重置到后方
        // 将超出的部分从 backPosition 开始计算，而不是简单取模
        // 这样可以避免元素在 frontThreshold 附近“跳跃”
        object.position.z = backPosition + (object.position.z - (frontThreshold + Number.EPSILON))

        // 更新 DOM 元素内容
        const itemData = getItem()
        if (itemData) {
          // const newContentStr = layer.value.contentStyle.map((item) => {
          //   return `<div style="font-size:${item.fontSize}px;color:${item.fontColor}">${itemData.name}</div>`
          // }).join('')
          // object.element.innerHTML = newContentStr
          // object.element.style.background = getItemBgColor()
        }

        // 重置缩放和透明度
        object.scale.set(0.1, 0.1, 0.1) // CSS3DObject inherits scale from Object3D
        object.element.style.opacity = '0' // Opacity for DOM elements
      }

      // 3. 根据 Z 轴位置更新缩放和透明度
      const progress = THREE.MathUtils.inverseLerp(backPosition, frontThreshold, object.position.z)

      let scaleProgress = 0
      const growEndProgress = 0.7 // Point where max scale is reached
      if (progress < growEndProgress) {
        const growPhaseProgress = THREE.MathUtils.mapLinear(progress, 0, growEndProgress, 0, 1)
        // 使用 maxScaleValue 替换原先的硬编码 2
        scaleProgress = THREE.MathUtils.lerp(0.1, maxScaleValue, Math.sqrt(growPhaseProgress))
      } else {
        const shrinkPhaseProgress = THREE.MathUtils.mapLinear(progress, growEndProgress, 1, 0, 1)
        // 使用 maxScaleValue 替换原先的硬编码 2
        scaleProgress = THREE.MathUtils.lerp(maxScaleValue, maxScaleValue * 0.8, shrinkPhaseProgress)
      }
      // 使用 maxScaleValue 替换原先的硬编码 2
      scaleProgress = THREE.MathUtils.clamp(scaleProgress, 0.1, maxScaleValue) // Ensure bounds
      object.scale.set(scaleProgress, scaleProgress, scaleProgress) // This will be translated to CSS scale by the renderer

      let targetOpacity = 1.0
      if (progress < fadeInEndProgress) {
        const fadeIn = THREE.MathUtils.smoothstep(progress, 0, fadeInEndProgress) // Use smoothstep for smoother fade
        targetOpacity = THREE.MathUtils.lerp(0.1, 1.0, fadeIn)
      } else if (isCenter && progress > fadeOutStartProgress) { // Only fade out center items if they are past the threshold
        const fadeOut = THREE.MathUtils.smoothstep(progress, fadeOutStartProgress, 1.0) // Use smoothstep
        targetOpacity = THREE.MathUtils.lerp(1.0, 0.0, fadeOut)
      }
      targetOpacity = THREE.MathUtils.clamp(targetOpacity, 0.0, 1.0)

      // Set opacity on the DOM element
      object.element.style.opacity = targetOpacity.toString()
    })
  }

  // 添加 ticker 回调
  gsap.ticker.add(tickerCallback)
}
function init() {
  if (!threeRoot.value) {
    return
  }
  scene = new THREE.Scene()
  camera = new THREE.PerspectiveCamera(45, window.innerWidth / window.innerHeight, 1, 1000)
  camera.position.z = cameraZ
  renderer = new CSS3DRenderer()
  renderer.setSize(0, 0) // Initial size, will be updated by resize
  threeRoot.value.appendChild(renderer.domElement)
}

function resize(width: number, height: number) {
  if (renderer) {
    renderer.setSize(width, height)
  }
  if (camera) {
    camera.aspect = width / height
    camera.updateProjectionMatrix()
  }
}

function render() {
  requestAnimationFrameId = requestAnimationFrame(render)
  renderer.render(scene, camera)
}

watch(
  () => [threeRootSize.width.value, threeRootSize.height.value],
  ([w, h]) => {
    resize(w, h)
  },
)

const initReady = ref(false)
watch(
  () => [regeditList.value, status.value, headSizeH.value, headSizeW.value, layer.value.itemBgColors, layer.value.contentStyle],
  () => {
    if (!initReady.value || !regeditList.value.length) {
      return
    }
    throttle(() => {
      initShape()
    }, 200, { leading: false })()
  },
  { deep: true },
)

function initContentStyle() {
  for (let i = 0; i < 3; i++) {
    if (!layer.value.contentStyle[i]) {
      layer.value.contentStyle[i] = Object.assign({}, defalutTextStyle)
    } else {
      layer.value.contentStyle[i] = Object.assign({}, defalutTextStyle, layer.value.contentStyle[i])
    }
  }
}

onMounted(async () => {
  initContentStyle()
  init()
  render()
  initReady.value = true
})
onUnmounted(() => {
  // 清理资源
  if (requestAnimationFrameId) {
    cancelAnimationFrame(requestAnimationFrameId)
    requestAnimationFrameId = null
  }
})
</script>

<template>
  <div ref="threeRoot" class="three-root"></div>
</template>

<style scoped lang="scss">
.three-root {
  width: 100%;
  height: 100%;
  :deep() {
    .css3d {
      border-radius: 5%;
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      color: #fff;
      text-align: center;
      transform: translateZ(0);
      will-change: transform;
      backface-visibility: hidden;
      > div {
        width: 80%;
        max-height: 100%;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        line-height: 1.3;
      }
    }
  }
}
</style>
