import type { IDesignLayer, IDesignSetup } from '../../types'
import { BisTypes, useDesignData } from '../..'
import { InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './lottery-ing3-setting.vue'
import Comp from './lottery-ing3.vue'
// 类型
export const type = 'lottery-ing3'
export const defaultHeadCount = 250
export const defaultrotateSpeed = 5
export const defaultPlaceHolderHeadImg = new URL('./assets/placehoder.png', import.meta.url).href
// 数据类型约束
export interface IDesignLotteryIng3 extends IDesignLayer {
  type: typeof type
  headCount?: number
  rotateSpeed?: number
  placeHolderHeadImg?: string
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.sportsIng,
    showType: ['pcwall'],
    status: ['ing', 'ready'],
    showInteractive: [InteractiveEnum.lotteryv3],
    type,
    name: '抽奖3d效果',
    thumbnail: new URL('./lottery-ing3.png', import.meta.url).href,
    Comp,
    CompSetting,
    defaultData() {
      const designData = useDesignData()
      const [width, height] = designData.option.drafts
      return {
        type,
        uuid: layerUuid(),
        name: '抽奖3d效果',
        style: {
          width: `${width}px`,
          height: `${height}px`,
          top: 0,
          left: 0,
        },
      }
    },
  })
}
