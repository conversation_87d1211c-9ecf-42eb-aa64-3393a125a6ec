<script setup lang="ts">
import type { BehitItem, IDesignDiglettIng } from './diglett-ing'
import HiAudioSel from 'design/components/audio.vue'
import { getDefaultMaterial, openSelectMaterial, useDataAttr } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'
import { useDesignTemp } from '../../index'
import { defaultData } from './diglett-ing'

const layer = defineModel<IDesignDiglettIng>('layer', { required: true })

const designTemp = useDesignTemp()
const isMobile = computed(() => designTemp.showType === 'mobile')

const holeImgBind = useDataAttr(layer.value, 'holeImg', defaultData.holeImg)
const hitImgBind = useDataAttr(layer.value, 'hitImg', defaultData.hitImg)
const behitBtomPosBind = useDataAttr(layer.value, 'behitBtomPos', defaultData.behitBtomPos)
const scoreColorBlind = useDataAttr(layer.value, 'scoreColor', defaultData.scoreColor || '')
const gameModeBind = useDataAttr(layer.value, 'gameMode', 'hit')
const bownImgBind = useDataAttr(layer.value, 'bownImg', defaultData.bownImg)
const behitHideBind = useDataAttr(layer.value, 'behitHide', defaultData.behitHide)
const successAudioBind = useDataAttr(layer.value, 'successAudio', defaultData.successAudio)
const failAudioBind = useDataAttr(layer.value, 'failAudio', defaultData.failAudio)

type IType = 'holeImg' | 'hitImg' | 'bownImg'
async function updateMaterialFn(name: IType, isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, name)
    : await openSelectMaterial('PIC')
  if (result) {
    layer.value[name] = result
  } else if (isReset) {
    delete layer.value[name]
  }
}

async function updateMaterialFn2(name: 'img_normal' | 'img_yun', index: number, isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, `${name}.${index}`)
    : await openSelectMaterial('PIC')
  if (result || (isReset && index === undefined)) {
    const arr = layer.value.beHitList as BehitItem[]
    arr[index][name] = result
  }
}

function removeBehit(index: number) {
  const arr = layer.value.beHitList as BehitItem[]
  arr.splice(index, 1)
}
function addBehit(index: number, defaultValue: any) {
  const arr = layer.value.beHitList as BehitItem[]
  arr.splice(index + 1, 0, { ...defaultValue })
}

const options = [
  { value: 'hit', label: '打' },
  { value: 'pull', label: '拔' },
]

type musicType = 'failAudio' | 'successAudio'
function changeMusic(name: musicType, result: string) {
  layer.value[name] = result
}
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item flex-items-start!">
        <h3>洞口</h3>
        <div class="bgblank flex flex-d-c flex-a-fe">
          <MaterialThumbnail @select="updateMaterialFn('holeImg')" @reset="updateMaterialFn('holeImg', true)">
            <img :src="holeImgBind" class="w-60 object-contain">
          </MaterialThumbnail>
        </div>
      </div>
      <div class="setting-item flex-items-start!">
        <h3>敲击物</h3>
        <div class="flex flex-d-c flex-a-fe">
          <MaterialThumbnail @select="updateMaterialFn('hitImg')" @reset="updateMaterialFn('hitImg', true)">
            <img :src="hitImgBind" class="bgblank w-60 object-contain">
          </MaterialThumbnail>
        </div>
      </div>
      <div class="setting-item flex-items-start!">
        <h3>击中动效</h3>
        <div class="flex items-end">
          <div class="bgblank d" @click="layer.bownImg = ''"></div>
          <MaterialThumbnail @select="updateMaterialFn('bownImg')" @reset="updateMaterialFn('bownImg', true)">
            <img v-if="bownImgBind" :src="bownImgBind" class="bgblank w-60 object-contain">
          </MaterialThumbnail>
        </div>
      </div>
      <div v-if="isMobile" class="setting-item flex-items-start!">
        <h3>击中音效</h3>
        <div class="flex items-end">
          <HiAudioSel :url="successAudioBind" @selection="result => changeMusic('successAudio', result)" />
        </div>
      </div>
      <div v-if="isMobile" class="setting-item flex-items-start!">
        <h3>未击中音效</h3>
        <div class="flex items-end">
          <HiAudioSel :url="failAudioBind" @selection="result => changeMusic('failAudio', result)" />
        </div>
      </div>

      <div class="setting-item flex-items-start!">
        <h3>被击物</h3>
        <div>
          <div v-for="(item, index) in layer.beHitList" :key="index">
            <div class="relative mb-10 w-160 flex justify-end!">
              <div class="flex">
                <div class="flex flex-d-c flex-a-c">
                  <h3>普通</h3>
                  <MaterialThumbnail @select="updateMaterialFn2('img_normal', index)" @reset="updateMaterialFn2('img_normal', index, true)">
                    <img :src="item.img_normal" class="bgblank w-60 object-contain">
                  </MaterialThumbnail>
                </div>
                <div class="ml-10 flex flex-d-c flex-a-c">
                  <h3>被击中</h3>
                  <MaterialThumbnail @select="updateMaterialFn2('img_yun', index)" @reset="updateMaterialFn2('img_yun', index, true)">
                    <img :src="item.img_yun" class="bgblank w-60 object-contain">
                  </MaterialThumbnail>
                </div>
              </div>
            </div>
            <div class="item-score flex flex-a-c justify-end!">
              <span>分数</span>
              <el-input-number v-model="item.score" v-input-number :min="0" controls-position="right" class="mt-5 w-80" />
              <div class="ml-10 w-40 flex cursor-pointer flex-a-c justify-end!">
                <icon-ph:minus-bold v-if="layer.beHitList.length > 1" @click.stop="removeBehit(index)" />
                <icon-ph:plus-bold @click.stop="addBehit(index, item)" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="setting-item flex-items-start!">
        <h3>被击物位置</h3>
        <div class="flex flex-a-c justify-end!">
          <el-input-number v-model="behitBtomPosBind" v-input-number controls-position="right" :min="0" :max="100" :step="1" />
        </div>
      </div>
      <div class="setting-item items-center">
        <span>分数色</span>
        <hi-color v-model="scoreColorBlind" type="both" />
      </div>
      <div class="setting-item items-center">
        <span>游戏模式</span>
        <el-select v-model="gameModeBind" placeholder="Select" style="width: 80px">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div v-if="gameModeBind === 'hit'" class="setting-item items-center">
        <span>敲击物甩动</span>
        <el-switch v-model="layer.hitShake" />
      </div>
      <div v-if="gameModeBind === 'hit'" class="setting-item items-center">
        <span>被击中消失</span>
        <el-switch v-model="behitHideBind" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.d {
  width: 25px;
  height: 25px;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
  &:first-child {
    position: relative;
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 90%;
      height: 90%;
      margin: 5%;
      mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1em' height='1em' viewBox='0 0 256 256'%3E%3Cpath fill='%23000' d='m203.63 62.65l13.25-14.58a12 12 0 0 0-17.76-16.14l-13.24 14.56A100 100 0 0 0 52.37 193.35l-13.25 14.58a12 12 0 1 0 17.76 16.14l13.24-14.56A100 100 0 0 0 203.63 62.65M52 128a75.94 75.94 0 0 1 117.58-63.57l-100.91 111A75.6 75.6 0 0 1 52 128m76 76a75.5 75.5 0 0 1-41.58-12.43l100.91-111A75.94 75.94 0 0 1 128 204'/%3E%3C/svg%3E");
      mask-size: 100%;
      background-color: #999999;
      cursor: pointer;
    }
  }
}
</style>
