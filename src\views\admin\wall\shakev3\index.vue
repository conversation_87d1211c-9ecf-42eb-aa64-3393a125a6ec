<script setup lang="ts">
// todo 当前页面不应该叫index，应该是类似design的名字
import HiDesign from 'design/index.vue'
import { useDesignTemp } from '~/src/views/__/design'
import { ModeEnum } from '~/src/views/__/design/types'
import { useMobileShakev3 } from '~/src/views/mobile/wall/shakev3'
import { usePcwallShakev3 } from '~/src/views/pcwall/shakev3'

definePage({ meta: { label: '摇一摇' } })

const interactive = 'shakev3'
const mode = ModeEnum.edit

const designTemp = useDesignTemp()

watch(
  () => designTemp.showType,
  (v) => {
    if (v === 'mobile') {
      useMobileShakev3()
    } else {
      usePcwallShakev3()
    }
  },
  { immediate: true },
)
</script>

<template>
  <HiDesign :interactive="interactive" :mode="mode" />
</template>

<style scoped lang="scss">
</style>
