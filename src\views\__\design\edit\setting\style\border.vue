<script setup lang="ts">
import type { IDesignLayer } from '../../../types'
import { fourValueBind, fourValueExpand, useDataAttr } from '../../..'

defineOptions({ label: '边框' })

const layer = defineModel<IDesignLayer>('layer', { required: true })

const defaultColor = 'rgb(0, 0, 0)'

const borderStyle = ref([
  { name: '无', value: 'none' },
  { name: '实线', value: 'solid' },
  { name: '虚线', value: 'dashed' },
  { name: '点状', value: 'dotted' },
  { name: '双划线', value: 'double' },
  { name: '3D凹槽', value: 'groove' },
  { name: '3D凸槽', value: 'ridge' },
  { name: '3D内嵌', value: 'inset' },
  { name: '3D外嵌', value: 'outset' },
])

const borderWidthExpand = fourValueExpand(layer, 'borderWidth')
const borderWidthLT = fourValueBind(layer, 'borderWidth', 0)
const borderWidthRT = fourValueBind(layer, 'borderWidth', 1)
const borderWidthRB = fourValueBind(layer, 'borderWidth', 2)
const borderWidthLB = fourValueBind(layer, 'borderWidth', 3)

const borderStyleBind = useDataAttr(layer.value.style, 'borderStyle', 'none')
const borderColorBind = useDataAttr(layer.value.style, 'borderColor', defaultColor)
</script>

<template>
  <div class="setting-wrap">
    <div class="setting-item">
      <h3>样式</h3>
      <el-select v-model="borderStyleBind" class="w-100">
        <el-option v-for="item in borderStyle" :key="item.value" :label="item.name" :value="item.value" />
      </el-select>
    </div>
    <template v-if="borderStyleBind !== 'none'">
      <div class="setting-item">
        <h3>颜色</h3>
        <hi-color v-model="borderColorBind" />
      </div>

      <div class="setting-item items-center justify-between">
        <h3>尺寸</h3>
        <div v-if="!borderWidthExpand" class="flex-1 text-right">
          <el-input-number v-model="borderWidthLT" v-input-number :min="0" controls-position="right" />
        </div>
        <div class="icon-box ml-2" @click="borderWidthExpand = !borderWidthExpand">
          <icon-custom-borderWidth />
        </div>
      </div>
      <template v-if="borderWidthExpand">
        <div class="setting-item justify-end!">
          <el-input-number v-model="borderWidthLT" v-input-number :min="0" controls-position="right" />
          <el-input-number v-model="borderWidthRT" v-input-number :min="0" controls-position="right" />
        </div>
        <div class="setting-item justify-end!">
          <el-input-number v-model="borderWidthLB" v-input-number :min="0" controls-position="right" />
          <el-input-number v-model="borderWidthRB" v-input-number :min="0" controls-position="right" />
        </div>
      </template>
    </template>
  </div>
</template>

<style scoped lang="scss">
.icon-box {
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 3px;
  &:hover {
    background-color: var(--el-color-primary-dark-2);
    color: #fff;
  }
}
</style>
