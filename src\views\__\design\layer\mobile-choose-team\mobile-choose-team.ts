import { BisTypes, useDesignData, useDesignState } from '../..'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './mobile-choose-team-setting.vue'
import Comp from './mobile-choose-team.vue'

export const type = 'mobile-choose-team'

export interface IDesignMobileChooseTeam extends IDesignLayer {
  type: typeof type
  data: {
    primaryColor: string
  }
}

export function setup(app: IDesignSetup) {
  const designData = useDesignData()

  app.registry({
    type,
    bisType: BisTypes.ready,
    showType: ['mobile'],
    showInteractive: [InteractiveEnum.diglettv3, InteractiveEnum.firev3, InteractiveEnum.goldcoinv3, InteractiveEnum.moneyv3, InteractiveEnum.shakev3],
    name: '选择队伍',
    thumbnail: new URL('./mobile-choose-team.png', import.meta.url).href,
    status: ['ready'],
    // 渲染前置条件
    mountedCondition: computed(() => {
      const designState = useDesignState()
      const teamList = designState.getLayerData('teamList')
      return !!teamList
    }),
    Comp,
    CompSetting,
    defaultData(): IDesignMobileChooseTeam {
      return {
        uuid: layerUuid(),
        name: '选择队伍',
        type,
        style: {
          width: `${designData.option.drafts[0] || 375}px`,
          height: `${designData.option.drafts[1] || 630}px`,
          backgroundColor: 'rgba(0, 0, 0, 0.4)',
        },
        data: {
          primaryColor: 'rgba(9, 121, 223, 1)',
        },
      }
    },
  })
}
