<script setup lang="ts">
import type { CSSProperties } from 'vue'
import type { IDesignShakeIng1 } from './shake-ing1'
import { envUtils } from '~/src/utils/env'
import { injectComputed, useDesignState } from '../..'

const layer = defineModel<IDesignShakeIng1>('layer', { required: true })
const scale = injectComputed('scale', 1)

const designState = useDesignState()
const shakeIngRef = ref<HTMLElement>()

const timeProgress = computed(() => {
  if (envUtils.isPlayWright) {
    return 0.5
  }
  return designState.getLayerData('ingTimeProgress') ?? 0
})

const displayData = computed(() => {
  const dataList = designState.getLayerData('ingRankings') || []

  // 时时计算显示信息
  let minScore = 0
  let maxScore = 0

  dataList.forEach((item: any) => {
    minScore = Math.min(minScore, item.score)
    maxScore = Math.max(maxScore, item.score)
  })

  const minP = [0, 0.1]
  const maxP = [0.1, 1]

  const result: { name: string, avatar: string, score: number, progress: string }[] = []
  dataList.forEach((item: any) => {
    const _minP = minP[0] + (minP[1] - minP[0]) * timeProgress.value
    const _maxP = maxP[0] + (maxP[1] - maxP[0]) * timeProgress.value

    const x = (_maxP - _minP) / (maxScore - minScore)
    const p = _minP + (item.score - minScore) * x
    result.push({
      name: item.name,
      avatar: item.avatar,
      score: item.score,
      progress: `${p * 100}%`,
    })
  })

  return result
})

const shakeIngStyle = computed(() => {
  return {
    '--scale': scale.value,
    '--avatar-size': `${scale.value * 40}px`,
    'fontSize': `${scale.value * 18}px`,
  }
})

const hasFinish = ref(false)

function columnWrapStyle(item: any) {
  const style: CSSProperties = {
    height: item.progress,
    backgroundColor: layer.value.columnBgcolor,
  }
  if (!hasFinish.value) {
    style.transition = '1s linear'
  }
  return style
}
function getColumBarStyle(index: number) {
  return {
    backgroundColor: layer.value.columnColors[index % layer.value.columnColors.length],
  }
}
</script>

<template>
  <div class="design-shake-ing1">
    <ul ref="shakeIngRef" :style="shakeIngStyle">
      <li v-for="(item, index) in displayData" :key="item.name">
        <div class="wrap">
          <div class="top animate__animated animate__bounce animate__infinite">
            <div class="name-box">
              <div class="count">{{ item.score }}</div>
              <div class="name">{{ item.name }}</div>
            </div>
            <div class="avatar-box">
              <img :src="item.avatar" class="avatar" alt="">
            </div>
            <div v-if="layer.flag[index]" class="flag-box">
              <img :src="layer.flag[index]" alt="">
            </div>
          </div>
          <div class="column-wrap" :style="columnWrapStyle(item)">
            <div class="bar" :style="getColumBarStyle(index)"> </div>
            <div class="arrow-box">
              <div class="arrow">
              </div>
            </div>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>

<style scoped lang="scss">
.design-shake-ing1 {
  width: 100%;
  height: 100%;
  position: relative;
  --avatar-size: 40px;
  ul {
    width: 100%;
    height: 100%;
    display: flex;
    padding: 30px;
    justify-content: space-around;
  }
  li {
    width: 55px;
    height: 100%;
    position: relative;
  }

  .wrap {
    width: 100%;
    height: 100%;
    position: absolute;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
  }
  .column-wrap {
    width: 100%;
    max-height: calc(100% - 135px);
    width: 100%;
    position: relative;
    overflow: hidden;
    display: flex;
    justify-content: center;
    padding: 3px 0;
  }
  .bar {
    width: 100%;
    height: 100%;
    mask-image: url('./assets/ing_bar.svg');
    mask-repeat: repeat-y;
    mask-position: center;
    mask-size: contain;
    position: relative;
    overflow: hidden;
  }
  .arrow-box {
    width: 100%;
    height: 100%;
    position: absolute;
    overflow: hidden;
    bottom: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    padding: 3px 0;
  }
  .arrow {
    width: 100%;
    height: 200%;
    background: url('./assets/arrow.svg') repeat-y;
    animation: sparkle 1s linear infinite;
    background-size: contain;
    background-position: center;
  }
  @keyframes sparkle {
    from {
      background-position: 0 0;
    }
    to {
      background-position: 0 -100px;
    }
  }

  .top {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    height: 135px;
    align-items: center;
    position: relative;
    .name-box {
      font-weight: 400;
      text-align: center;
      margin-top: 15%;
      margin-right: 3%;

      .count {
        color: #f4ff48;
        font-size: 1.2em;
        text-shadow: 1px 1px 2px #000;
      }

      .name {
        color: #fff;
        white-space: nowrap;
        text-shadow: 1px 1px 2px #000;
      }
    }

    .avatar-box {
      position: relative;
      margin-top: 15%;
      width: var(--avatar-size);
      height: var(--avatar-size);
      border-radius: 50%;
      overflow: hidden;
      border: 2px solid #f4ff48;
      .avatar {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        aspect-ratio: 1;
        border-radius: 50%;
      }
    }
    .flag-box {
      position: absolute;
      top: 0;
      img {
        max-height: 40px;
        object-fit: contain;
      }
    }
  }
}
</style>
