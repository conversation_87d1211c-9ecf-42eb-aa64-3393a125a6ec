<script setup lang="ts">
import type { CSSProperties, TransitionProps } from 'vue'
import { timer } from '@/utils'
import { cloneDeep } from 'lodash-es'
import { hasAuth } from '~/src/utils/auth'
import { injectScale, useDesignData, useDesignSetup, useDesignState, useDesignTemp } from '..'
import { initKeybindScript, useShortcutKey } from '../hooks/useShortcutKey'
import { type IDesignLayer, type IDesignLayerEventShowHide, ModeEnum } from '../types'
import { processStyle, toAct, toLink } from '../utils'
import { defaultLayoutDirection, defaultOverflow, type IDesignGroup } from './group/group'

const props = defineProps<{
  dataIndex: number
}>()

const scale = injectScale()
const sectionLayerRef = ref<HTMLElement>()
const designData = useDesignData()
const designTemp = useDesignTemp()
const designState = useDesignState()
const layer = defineModel<IDesignLayer>('layer', { required: true })
const uuid = layer.value.uuid

// layerOption
const designSetup = useDesignSetup()
const setupOption = computed(() => {
  return designSetup.getComponentSetupOption(layer.value.type)
})

// 渲染条件
const mountedCondition = computed(() => {
  if (setupOption.value?.mountedCondition) {
    return setupOption.value.mountedCondition.value
  }
  return true
})

const isActived = computed(() => {
  return designTemp.activeList.includes(uuid)
})

// 存储临时显示状态
const layerTempShow = ref<boolean | undefined>()

// 状态发生变化时临时状态需要重置
watch(() => designState.status, () => {
  layerTempShow.value = undefined
})

// 图层是否显示
const isLayerStatusShow = computed(() => {
  if (designState.status === '') return false
  if (layerTempShow.value !== undefined) {
    return layerTempShow.value
  }

  const setupOption = designSetup.getComponentSetupOption(layer.value.type)
  if (setupOption?.status && !setupOption.status.includes(designState.status)) {
    // 不显示
    return false
  }

  if (layer.value.show && !layer.value.show.includes(designState.status)) {
    // 不显示
    return false
  }

  // 业务事件决定是否显示
  const layerEventList = designState.layerEventList?.filter((it) => {
    if (!layer.value.events) return false
    return layer.value.events?.some(e => e.value === it.eventId)
  })

  if (!layerEventList?.length) return true

  return layerEventList.some((i) => {
    if (i.status === undefined) {
      return true
    }
    return unref(i.status).includes(designState.status)
  })
})

// 图层业务控制是否显示
const isLayerDisplayShow = computed(() => {
  // if (designTemp.mode === ModeEnum.edit) return true

  const { display } = layer.value
  if (!display) return true

  const option = designState.getLayerData(display)
  if (option === undefined) return true
  return toValue(option)
})

// 图层最终显示态, 有一个不显示就是不显示，最终显示
const isLayerShow = computed(() => {
  if (!isLayerStatusShow.value) {
    return false
  }
  if (!isLayerDisplayShow.value) {
    return false
  }
  return true
})

// 告诉其他业务图层组件是否显示，目前是告诉moveable组件
watch(() => setupOption.value && isLayerShow.value, async (show) => {
  await nextTick()
  designTemp.bus.emit(`${uuid}:event:layerShowToggle`, show)
}, {
  immediate: true,
})

const hasActionAuth = computed(() => {
  const layerAuth = hasAuth(['man', 'oem', 'spo.hi.vip', 'spo.ft']) || layer.value.spoVisible
  if (layerAuth) {
    return true
  }
  if (layer.value.type === 'group') {
    // 如果是组，判断组内是否有显示权限的图层
    return (layer.value as IDesignGroup).layers.some(i => i.spoVisible)
  }
  return false
})

// NOTE: 目前moveable组件依靠事件传递决定是否显示，添加权限判断后layer(即这里)渲染早于moveable组件，导致moveable组件无法监听获取到是否显示的状态。这里监控权限变化，延迟等待moveable组件渲染完成再通知一次
watch(() => hasActionAuth.value, async (v) => {
  if (v) {
    await nextTick()
    designTemp.bus.emit(`${uuid}:event:layerShowToggle`, setupOption.value && isLayerShow.value)
  }
})

// 适合当前图层相关的事件列表
const showEvents = computed(() => {
  const eventList = designState.layerEventList
  if (!eventList) return []
  return eventList.filter((i) => {
    if (i.status) {
      return unref(i.status).includes(designState.status)
    }
    return true
  })
})

// 图层点击事件
const clickIng: Record<string, boolean> = {}
async function designLayerClickFn() {
  if (designTemp.isEdit) return
  // 匹配出当前层已配置的所有click事件
  const events = (layer.value.events || []).filter(i => i.event === 'click')

  try {
    if (clickIng[uuid]) return
    clickIng[uuid] = true

    const effectiveEvents = showEvents.value.filter(i => events.some(j => j.value === i.eventId))
    for (const item of effectiveEvents) {
      await item.value()
    }

    // 图层显示隐藏控制
    const showHideEvent = events.find(i => i.type === 'show-hide') as IDesignLayerEventShowHide
    if (showHideEvent?.value) {
      for (const item of showHideEvent.value) {
        const { uuid, action } = item
        designTemp.bus.emit(`${uuid}:event:show-hide`, action)
      }
    }

    // 互动跳转，优先通过iframe事件跳转
    const interactEvent = events.find(i => i.type === 'interact')
    if (interactEvent?.value) {
      const { module, moduleId } = interactEvent.value
      toAct(module, {
        id: `${moduleId}`,
      })
    }

    // 微站切换不同页面显示
    const statusEvent = events.find(i => i.type === 'status')
    if (statusEvent?.value) {
      const status = statusEvent.value
      if (designState.statusList.find(i => i.value === status)) {
        designState.setStatus(status)
      }
    }

    // 链接跳转
    const linkEvent = events.find(i => i.type === 'link')
    if (linkEvent?.value) {
      toLink(linkEvent?.value)
    }
  } finally {
    clickIng[uuid] = false
  }
}

// 图层快捷键
if (!designTemp.isEdit) {
  try {
    initKeybindScript()
  } catch (err) {
    console.error(err)
  }
  if (layer.value.events?.length) {
    const keyboard: Record<string, {
      checkIsWork: () => boolean
      callback: () => void
    }> = {}
    const events = layer.value.events.filter(i => i.type === 'business' && i?.keyboard !== undefined)

    for (const item of events) {
      if (item.type === 'business' && item.keyboard) {
        keyboard[item.keyboard] = {
          // 图层显示时才触发
          checkIsWork: () => isLayerShow.value,
          callback: () => {
            showEvents.value.find(i => i.eventId === item.value)?.value()
          },
        }
      }
    }
    if (Object.keys(keyboard).length) {
      useShortcutKey(keyboard)
    }
  }
}

// 动画相关
const transitionAttr = computed(() => {
  const result: TransitionProps = {}
  const inAnimate = layer.value.animate?.find(i => i.type === 'In')
  if (inAnimate) {
    result.enterActiveClass = `animate__animated ${inAnimate.animate}`
  }
  const outAnimate = layer.value.animate?.find(i => i.type === 'Out')
  if (outAnimate) {
    result.leaveActiveClass = `animate__animated ${outAnimate.animate}`
  }
  return result
})
function setAnimateProperty(el: HTMLElement | undefined, type?: string) {
  if (el) {
    if (type) {
      const tmpAnimate = layer.value.animate?.find(i => i.type === type)
      if (tmpAnimate) {
        el.style.setProperty('--animate-duration', `${tmpAnimate.duration || 1}s`)
        el.style.setProperty('--animate-delay', `${tmpAnimate.delay || 0}s`)
        el.style.setProperty('--animate-repeat', `${tmpAnimate.repeat || '1'}`)
      }
    } else {
      el.style.removeProperty('--animate-duration')
      el.style.removeProperty('--animate-delay')
      el.style.removeProperty('--animate-repeat')
    }
  }
}
const emphaAnimate = computed(() => {
  return layer.value.animate?.find(i => i.type === 'Empha')
})
const transitionEvent = {
  beforeEnter(el?: HTMLElement) {
    setAnimateProperty(el, 'In')
  },
  beforeLeave(el?: HTMLElement) {
    setAnimateProperty(el, 'Out')
  },
  afterEnter(el?: HTMLElement) {
    setAnimateProperty(el)
  },
  afterLeave(el?: HTMLElement) {
    setAnimateProperty(el)
  },
}

const timerHandler = ref<NodeJS.Timeout>()
async function animatePreviewFn(e: any) {
  if (!designTemp.isEdit) return
  const { detail } = e
  const { animate, duration = 1, delay = 0, repeat = 1 } = detail || {}
  if (!animate) return
  const el = sectionLayerRef.value
  if (el) {
    // 清理所有 animate__ 开头的类名
    el.className = el.className.replace(/animate__\w+/g, '')
    setAnimateProperty(el)

    await timer(100)

    el.classList.add('animate__animated', animate)
    el.style.setProperty('--animate-duration', `${duration}s`)
    el.style.setProperty('--animate-delay', `${delay}s`)
    el.style.setProperty('--animate-repeat', repeat)

    clearTimeout(timerHandler.value)
    // 计算总时间
    const totalDuration = duration * 1000 + delay * 1000
    timerHandler.value = setTimeout(() => {
      setAnimateProperty(el)
      el.classList.remove('animate__animated', animate)
      timerHandler.value = undefined
    }, totalDuration)
  }
}

////
const designLayerClass = computed(() => {
  return ['design-layer', hasActionAuth.value ? 'has-auth' : '']
})

const windowSize = designTemp.mode === ModeEnum.preview
  ? useWindowSize()
  : {
      width: computed(() => designTemp.designSize.width),
      height: computed(() => designTemp.designSize.height),
    }

const designLayerStyle = computed(() => {
  // 只设置尺寸和位置信息
  let result: CSSProperties = {
    position: 'absolute',
    zIndex: props.dataIndex,
  }

  for (const item of ['width', 'height', 'top', 'left', 'right', 'bottom', 'pointerEvents', 'position'] as const) {
    if (layer.value.style[item] !== undefined && layer.value.style[item] !== '') {
      result[item] = layer.value.style[item] as any
    }
  }
  // to 如果已经有了left就不能有right，同理top和bottom
  // todo 应该挪到 themeUpgrade 中进行脏数据处理
  if (result.left) {
    delete result.right
  }
  if (result.top) {
    delete result.bottom
  }

  // 转缩放比例， 保留整数，解决图片滚动组件偶尔出现一像素问题
  result = processStyle(result, scale, 0)

  // 如果宽高设置的百分比，需要重新计算
  const { width, height, left, right, top, bottom } = layer.value.style

  // 尺寸和位置百分比适配 //////////////////
  // 获取父级的宽高，根元素时获取窗口的宽高
  const { isPercent = 0 } = layer.value
  if (isPercent) {
    if (layer.value.$parent) {
      // 不是根节点, 转成百分比
      const pWidth = Number.parseFloat(`${layer.value.$parent.style.width}`)
      const pHeight = Number.parseFloat(`${layer.value.$parent.style.height}`)

      if (pWidth) {
        result.width = `${Number.parseFloat(`${width}`) / pWidth * 100}%`
        if ('left' in layer.value.style) {
          result.left = `${Number.parseFloat(`${left}`) / pWidth * 100}%`
        }
        if ('right' in layer.value.style) {
          result.right = `${Number.parseFloat(`${right}`) / pWidth * 100}%`
        }
      }
      if (pHeight) {
        result.height = `${Number.parseFloat(`${height}`) / pHeight * 100}%`
        if ('top' in layer.value.style) {
          result.top = `${Number.parseFloat(`${top}`) / pHeight * 100}%`
        }
        if ('bottom' in layer.value.style) {
          result.bottom = `${Number.parseFloat(`${bottom}`) / pHeight * 100}%`
        }
      }
    } else {
      // 如果是根节点
      const [dW, dH] = designData.option.drafts
      // 获取父级的宽高
      const pW = windowSize.width.value
      const pH = windowSize.height.value

      // 相对的都是设计稿的宽高
      if (width) {
        const resultWidth = Number.parseFloat(`${width}`) / dW * pW
        result.width = `${resultWidth}px`
      }
      if (height) {
        const resultHeight = Number.parseFloat(`${height}`) / dH * pH
        result.height = `${resultHeight}px`
      }
      if ('left' in layer.value.style) {
        const resultLeft = Number.parseFloat(`${left}`) / dW * pW
        result.left = `calc((${resultLeft}px - (var(--window-width) - var(--design-width)) * 0.5px))`
      }
      if ('right' in layer.value.style) {
        const resultRight = Number.parseFloat(`${right}`) / dW * pW
        result.right = `calc((${resultRight}px - (var(--window-width) - var(--design-width)) * 0.5px))`
      }
      if ('top' in layer.value.style) {
        const resultTop = Number.parseFloat(`${top}`) / dH * pH
        result.top = `calc((${resultTop}px - (var(--window-height) - var(--design-height)) * 0.5px))`
      }
      if ('bottom' in layer.value.style) {
        const resultBottom = Number.parseFloat(`${bottom}`) / dH * pH
        result.bottom = `calc((${resultBottom}px - (var(--window-height) - var(--design-height)) * 0.5px))`
      }
    }
  }

  if (isActived.value) {
    result.outline = 0
  }
  // 如果有前置条件，需要先预渲染，不做视觉展示
  if (!mountedCondition.value) {
    result.visibility = 'hidden'
  }
  // 绑定了事件的增加鼠标手型样式
  const events = (layer.value.events || []).filter(i => i.event === 'click')
  if (events.length) {
    result.cursor = 'pointer'
  }

  // 如果图层在group中并且设置了布局， 布局中相关属性需要在此设置 ///////
  const { layout } = layer.value.$parent as IDesignGroup || {}
  if (layout) {
    // 当前图层在布局中
    result.position = 'relative'
    const config = layout.items[uuid]
    if (config) {
      const { flex } = config
      result.flex = flex
    }
    const { direction = defaultLayoutDirection } = layout
    if (direction === 'column') {
      delete result.top
      if (layout.items[uuid]) {
        result.width = '100%'
        delete result.height
        delete result.left
      }
    } else if (direction === 'row') {
      delete result.left
      if (layout.items[uuid]) {
        delete result.width
        result.height = '100%'
        delete result.top
      }
    }
  }
  /////////////////////
  // 文本组件改为高度自适应
  if (layer.value.type === 'text') {
    if (layer.value.style.writingMode) {
      result.width = 'auto'
    } else {
      result.height = 'auto'
    }
  }

  return result
})
const layerWrapClass = computed(() => {
  const result: string[] = []
  // 常驻动画
  if (emphaAnimate.value && !timerHandler.value) {
    result.push('animate__animated', emphaAnimate.value.animate)
  }
  return result
})
const layerWrapStyle = computed(() => {
  const transformStyle = layer.value.style.transform

  const copyStyle = cloneDeep(layer.value.style)
  // 去除位置尺寸信息
  for (const item of ['width', 'height', 'top', 'left', 'bottom', 'right', 'boxShadow', 'transform', 'pointerEvents']) {
    delete copyStyle[item as keyof CSSProperties]
  }
  if (designTemp.isPreview) {
    copyStyle.outlineWidth = '0'
  }
  if (emphaAnimate.value) {
    copyStyle['--animate-duration'] = `${emphaAnimate.value.duration || 1}s`
    copyStyle['--animate-delay'] = `${emphaAnimate.value.delay || 0}s`
    copyStyle['--animate-repeat'] = `${emphaAnimate.value.repeat || 'infinite'}`
  }
  // overflow 的处理，需要考虑圆角
  const { overflow, overflowX, overflowY, borderRadius } = copyStyle
  if (overflow) {
    const arr = overflow.replace(/\s+/g, ' ').split(' ')
    if (!overflowX) {
      copyStyle.overflowX = arr[0] as CSSProperties['overflowX']
    }
    if (!overflowY) {
      copyStyle.overflowY = (arr[1] || arr[0]) as CSSProperties['overflowY']
    }
    delete copyStyle.overflow
  }
  if (layer.value.type === 'group') {
    if (!overflowX) {
      copyStyle.overflowX = defaultOverflow
    }
    if (!overflowY) {
      copyStyle.overflowY = defaultOverflow
    }
  } else {
    if (borderRadius && !overflow && !overflowX && !overflowY) {
      copyStyle.overflow = 'hidden'
    }
  }

  // 处理group的布局逻辑 ///////////////////
  const { layout } = layer.value as IDesignGroup
  if (layout) {
    copyStyle.display = 'flex'
    copyStyle.flexDirection = layout.direction || defaultLayoutDirection
    copyStyle.gap = `${layout.gap || 0}px`
  }
  // 如果当前节点的父节点是布局逻辑，同时当前是flex，需要overflow: auto
  const { layout: pLayout } = layer.value.$parent as IDesignGroup || {}
  if (pLayout && pLayout.items[uuid]) {
    if (pLayout.direction === 'column') {
      copyStyle.overflowY = 'auto'
    } else {
      copyStyle.overflowX = 'auto'
    }
  }
  /////////////////////
  // 编辑模式下模版图层的子图层禁止选中
  if (designTemp.isEdit && layer.value.type === 'group' && (layer.value as IDesignGroup).templateId) {
    copyStyle.pointerEvents = 'none'
  }

  if (layer.value.type === 'text') {
    if (layer.value.style.writingMode) {
      copyStyle.width = 'auto'
    } else {
      copyStyle.height = 'auto'
    }
  }

  const styleNew = processStyle(copyStyle, scale)
  if (transformStyle) {
    styleNew.transform = transformStyle
  }
  return styleNew
})

const layerBoxShowStyle = computed(() => {
  if (!layer.value.style.boxShadow) return null
  const style: CSSProperties = {
    boxShadow: layer.value.style.boxShadow,
  }
  if (layer.value.style.borderRadius) {
    style.borderRadius = layer.value.style.borderRadius
  }
  return processStyle(style, scale)
})

function eventShowHideFn(e: any) {
  if (designTemp.isEdit) return
  if (e === 'auto') {
    layerTempShow.value = layerTempShow.value === undefined ? !isLayerShow.value : !layerTempShow.value
    return
  }
  layerTempShow.value = e === 'show'
}

// 自定义事件广播
function eventCustomFn({ eventId, data }: any) {
  // 匹配出当前层已配置的所有事件
  const events = showEvents.value.filter(i => i.eventId === eventId)
  for (const item of events) {
    item.value(data)
  }
}

function wheelFn(e: WheelEvent) {
  // 图层是group并且overflow是auto时阻止冒泡
  if (layer.value.type === 'group') {
    const target = sectionLayerRef.value?.querySelector('.layer-wrap') as HTMLElement | undefined
    if (!target) return
    const [overflowX, overflowY] = target.style.overflow.split(' ')
    if (
      (e.shiftKey && overflowX && overflowX !== 'hidden')
      || (!e.shiftKey && overflowY && overflowY !== 'hidden')
    ) {
      e.stopPropagation()
    }
  }
}

// 数据如果被重写，需要重新赋值
watch(
  () => [layer.value.$dom, sectionLayerRef.value],
  ([d1, d2]) => {
    if (d1 === d2) return
    layer.value.$dom = sectionLayerRef.value
  },
)

// 图层内的输入mousedown事件编辑模式时需要阻止冒泡
function mousedownStop(e: MouseEvent) {
  if (!designTemp.isEdit) return
  const target = e.target as HTMLElement
  if (target) {
    if (target.closest('input') || target.closest('select')) {
      e.stopPropagation()
    }
  }
}

onBeforeMount(() => {
  if (isLayerShow.value) {
    transitionEvent.afterEnter()
  }
  // 编辑模式监控动画预览事件
  designTemp.bus.on(`${uuid}:animate:preview`, animatePreviewFn)
  designTemp.bus.on(`${uuid}:event:show-hide`, eventShowHideFn)
  designTemp.bus.on(`${uuid}:event:custom`, eventCustomFn)
})
onBeforeUnmount(() => {
  // 取消事件
  designTemp.bus.off(`${uuid}:animate:preview`, animatePreviewFn)
  designTemp.bus.off(`${uuid}:event:show-hide`, eventShowHideFn)
  designTemp.bus.off(`${uuid}:event:custom`, eventCustomFn)
})
onUnmounted(() => {
  layer.value.$dom = undefined
})
</script>

<template>
  <Transition v-bind="transitionAttr" appear v-on="transitionEvent">
    <!-- key 方便做预渲染后重新渲染，能够保证动画的正常运行 -->
    <section
      v-if="setupOption && isLayerShow"
      ref="sectionLayerRef"
      :class="designLayerClass"
      :style="designLayerStyle"
      :data-type="layer.type"
      :data-name="layer.name"
      :data-uuid="layer.uuid"
      @click="designLayerClickFn"
    >
      <div v-if="false" class="layer-log">{{ layer }}</div>
      <div
        class="layer-wrap"
        :class="layerWrapClass"
        :style="layerWrapStyle"
        @wheel="wheelFn"
        @mousedown="mousedownStop"
      >
        <component :is="setupOption.Comp" :key="layer.uuid" v-model:layer="layer" />
      </div>
      <div v-if="layerBoxShowStyle" class="box-show" :style="layerBoxShowStyle"></div>
    </section>
  </Transition>
</template>

<style scoped lang="scss">
section[data-type='group'] {
  .layer-wrap {
    overflow: visible;
  }
}
.layer-wrap {
  position: relative;
  width: 100%;
  height: 100%;
}
.box-show {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
}
.layer-log {
  position: absolute;
  top: -20px;
  left: 0;
  z-index: 999;
  background-color: rgba(255, 255, 255, 0.5);
  padding: 2px;
  font-size: 12px;
  white-space: nowrap;
}
</style>
