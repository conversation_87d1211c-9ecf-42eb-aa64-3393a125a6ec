import { merge } from 'lodash-es'
import { BisTypes } from '../../'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './winning-list-setting.vue'
import Comp from './winning-list.vue'

// 类型
export const type = 'winning-list'

// 数据类型约束
export interface IDesignWinningList extends IDesignLayer {
  type: typeof type
  data: {
    // 头像大小、间隙、默认头像、头像点缀，奖品文本颜色、奖品文本大小
    avatarSize?: number
    avatarGap?: number
    defaultAvatar?: string
    avatarDecoration?: string
    avatarDecorationRotation?: boolean
    avatarDecorationRotationSpeed?: number
    prizeColor?: string
    nameColor?: string
  }
}

export const DEFAULT_DATA = {
  avatarSize: 30,
  avatarGap: 10,
  defaultAvatar: new URL(`./assets/default.png`, import.meta.url).href,
  avatarDecoration: new URL(`./assets/headborder.png`, import.meta.url).href,
  avatarDecorationRotation: false,
  avatarDecorationRotationSpeed: 1, // 多少秒转完一圈
  prizeColor: '#fff',
  nameColor: '#fff',
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.result,
    showType: ['pcwall'],
    showInteractive: [InteractiveEnum.lotteryv3],
    type,
    name: '中奖名单',
    Comp,
    CompSetting,
    thumbnail: new URL('./winning-list.png', import.meta.url).href,
    defaultData(options): IDesignWinningList {
      return merge({
        uuid: layerUuid(),
        name: '中奖名单',
        type,
        data: {},
        style: {
          width: '960px',
          height: '170px',
          top: '160px',
          left: '130px',
        },
      }, options as IDesignWinningList)
    },
  })
}
