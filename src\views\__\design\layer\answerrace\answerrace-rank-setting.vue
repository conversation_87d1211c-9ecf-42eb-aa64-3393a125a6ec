<script setup lang="ts">
import { getDefaultMaterial, openSelectMaterial, useDataAttr } from '../..'
import { defaultData, type IDesignAnswerraceRank } from './answerrace-rank'

const layer = defineModel<IDesignAnswerraceRank>('layer', { required: true })

const titleFontSizeBind = useDataAttr(layer.value, 'titleFontSize', defaultData.titleFontSize)
const titleColorBind = useDataAttr(layer.value, 'titleColor', defaultData.titleColor)
const contentTextAlignBind = useDataAttr(layer.value, 'contentTextAlign', defaultData.contentTextAlign)
const sortColorBind = useDataAttr(layer.value, 'sortColor', defaultData.sortColor)
const sortFontSizeBind = useDataAttr(layer.value, 'sortFontSize', defaultData.sortFontSize)
const headerSizeBind = useDataAttr(layer.value, 'headerSize', defaultData.headerSize)
const contentFontSizeBind = useDataAttr(layer.value, 'contentFontSize', defaultData.contentFontSize)
const contentColorBind = useDataAttr(layer.value, 'contentColor', defaultData.contentColor)
const showCountBind = useDataAttr(layer.value, 'showCount', defaultData.showCount)

const aglinOptions = [
  { label: '左对齐', value: 'left' },
  { label: '居中对齐', value: 'center' },
  { label: '右对齐', value: 'right' },
]
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>展示数量</h3>
        <el-input-number v-model="showCountBind" v-input-number controls-position="right" :min="1" :max="200" :step="1" />
      </div>
      <div class="setting-item">
        <h3>题目字号</h3>
        <el-input-number v-model="titleFontSizeBind" v-input-number controls-position="right" :min="1" :step="1" />
      </div>
      <div class="setting-item">
        <h3>题目字色</h3>
        <hi-color v-model="titleColorBind" />
      </div>
      <div class="setting-item">
        <h3>内容字号</h3>
        <el-input-number v-model="contentFontSizeBind" v-input-number controls-position="right" :min="1" :step="1" />
      </div>
      <div class="setting-item">
        <h3>内容对齐</h3>
        <el-select v-model="contentTextAlignBind" placeholder="请选择题目对齐" style="width: 120px">
          <el-option v-for="item in aglinOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="setting-item">
        <h3>题目字色</h3>
        <hi-color v-model="contentColorBind" />
      </div>
      <div class="setting-item">
        <h3>头像尺寸</h3>
        <el-input-number v-model="headerSizeBind" v-input-number controls-position="right" :min="10" :step="1" />
      </div>
      <div class="setting-item">
        <h3>序号字号</h3>
        <el-input-number v-model="sortFontSizeBind" v-input-number controls-position="right" :min="10" :step="1" />
      </div>
      <div class="setting-item">
        <h3>序号字色</h3>
        <hi-color v-model="sortColorBind" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
