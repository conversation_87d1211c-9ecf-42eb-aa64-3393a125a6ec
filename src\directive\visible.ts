import type { DirectiveBinding } from 'vue'

function deal(el: HTMLElement, open: boolean) {
  el.style.visibility = open ? 'visible' : 'hidden'
}
export default {
  mounted: (el: HTMLElement, binding: DirectiveBinding) => {
    deal(el, !!binding.value)
  },
  updated: (el: HTMLElement, binding: DirectiveBinding) => {
    deal(el, !!binding.value)
  },
  unmounted: (el: HTMLElement) => {
    el.style.visibility = ''
  },
}
