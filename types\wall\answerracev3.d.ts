type RoundState =
  // 未开始
  'NOT_STARTED'
  // 中奖名单
  | 'WIN_LIST'
  // 进行中
  | 'IN'
  // 已结束
  | 'FINISH'

type JoinType =
  // 个人
  'PERSONAL'
  // 团队
  | 'TEAM'

type OptionType =
  // 单选
  'RADIO'
  // 多选
  | 'CHECKBOX'

type SubmitType =
  // 手动提交
  'MANUAL_SUBMIT'
  // 自动提交
  | 'AUTO_SUBMIT'

type AnswerTimeType =
  // 统一答题时间
  'UNIFIED_TIME'
  // 每题独立答题时间
  | 'INDIVIDUAL_TIME'

// ------------------- pro_answerracev3_subject -------------------

export interface ProAnswerracev3Subject {
  // 主键
  id: string
  // 创建时间
  createDate: Date
  // 更新时间
  updateDate: Date
  // 删除标记
  deleteTag: string
  // 所属用户 id
  userId: string
  // 活动 ID
  wallId: string
  // 答题轮次 ID
  answerracev3Id: string
  // 题目内容
  subjectContent: string
  // 类型
  type: OptionType
  // 选项, { img: '', rightAnswer: 'N', title: '' },
  options: string
  // 答案
  answer: string
  // 注释
  remark?: string
  // 答对得分
  rightScore: number
  // 答错扣分
  wrongScore: number
  // 答题时间（秒）
  answerTime: number
  // 当前题目状态；暂停时为 "PAUSE"，其他情况为 null
  state?: string
  // 题目开始时间点（毫秒时间戳）
  startTime?: string
  // 暂停恢复时间点（毫秒时间戳）
  resumeTime?: string
  // 题目正常运行累计时长（毫秒）
  runTime?: string
  // 排序值
  sort: number
}

// ------------------- pro_answerracev3 -------------------

export interface ProAnswerracev3 {
  id: string
  createDate: Date
  updateDate: Date
  deleteTag: string
  wallId: string
  userId: string
  themeId: string
  // 标题
  title: string
  // 规则
  rule: string
  // 状态
  state: RoundState
  // 当前题目 id
  subjectId?: string
  // 轮次开始时间（毫秒时间戳）
  startTime?: number
  // 参与方式
  joinType: JoinType
  // 轮次开始后参与开关
  ingJoinSwitch: string
  // 看题时间（秒）
  watchTime: number
  // 提交方式(手动/自动)
  submitType: SubmitType
  // 答题时间设置方式
  answerTimeType: AnswerTimeType
  // 统一答题时间（秒）
  answerTime?: number
  // 答案展示方式
  showAnswerType: string
  // 答案展示时间（秒）
  showAnswerTime?: number
  // 每题得分展示方式
  showResultType: string
  // 每题得分展示时间（秒）
  showResultTime?: number
  // 注释展示开关
  showCommentSwitch: string
  // 选择题目方式
  chooseSubjectType: string
  // 随机题目规则
  subjectContent?: string
  // 分值计算方式
  scoreType: string
  // 统一分值
  score?: number
  // 连对题目个数
  rightPlusCnt?: number
  // 连对加分分值
  rightPlusScore?: number
  // 获胜加分排名
  winPlusRank?: number
  // 获胜加分分值
  winPlusScore?: number
  // 答错扣分方式
  wrongScoreType: string
  // 答错统一扣分
  wrongScore?: number
  // 淘汰开关
  outSwitch: string
  // 淘汰答错个数
  outWrongCnt?: number
  // 淘汰范围
  outRange?: string
  // 淘汰人员是否参与排名
  outRankSwitch?: string
  // 排序值
  sort: number
}

// ------------------- pro_answerracev3_team -------------------

export interface ProAnswerracev3Team {
  id: string
  createDate: Date
  updateDate: Date
  deleteTag: string // 默认 'N'
  userId: string
  wallId: string
  answerracev3Id?: string
  // 队伍名称
  teamName?: string
  // 队伍头像
  teamHeadImg?: string
}

// ------------------- pro_answerracev3_record -------------------

export interface ProAnswerracev3Record {
  id: string
  createDate: Date
  updateDate: Date
  deleteTag: string
  userId: string
  wallId: string
  answerracev3Id: string
  // 微信用户 ID
  wxUserId: string
  subjectId: string
  // 回答选项 （逗号分隔/JSON，具体取决后端实现）
  option: string
  // 是否答对 (Y/N)
  answerResult: string
  // 耗时 (毫秒)
  useTime: string
  // 本题得分
  score: number
  // 连对个数
  comboCnt: number
  // 错误个数
  wrongCnt: number
}

// ------------------- pro_answerracev3_config -------------------

export interface ProAnswerracev3Config {
  // 主键
  id: string
  // 创建时间
  createDate: Date
  // 更新时间
  updateDate: Date
  // 删除标记，默认 'N'
  deleteTag: string
  // 所属用户 id
  userId: string
  // 墙 id（唯一索引）
  wallId: string
  // 开启状态
  openState?: string
  // 轮次 id
  answerracev3Id?: string
  // 主题设置解锁开关
  answerraceThemeLimit?: string
  // 高级设置解锁开关
  answerraceAdvancedLimit?: string
}
