import type { IDesignLayer, IDesignSetup } from '../../types'
import { BisTypes, useDesignData } from '../..'
import { layerUuid } from '../../utils'
import CompSetting from './mobile-rankinglist-setting.vue'
import Comp from './mobile-rankinglist.vue'

export const type = 'mobile-rankinglist'

export const defaultData: IDesignMobileRankinglist['data'] = {
  unit: '次',
  topOneIconImg: new URL(`./assets/topOneIconImg.png`, import.meta.url).href,
  topTwoIconImg: new URL(`./assets/topTwoIconImg.png`, import.meta.url).href,
  topThreeIconImg: new URL(`./assets/topThreeIconImg.png`, import.meta.url).href,
  topOtherIconImg: new URL(`./assets/topOtherIconImg.png`, import.meta.url).href,
  titleImg: new URL(`./assets/titleImg.png`, import.meta.url).href,
  bgImg: new URL(`./assets/bgImg.png`, import.meta.url).href,
  closeImg: new URL(`./assets/closeImg.png`, import.meta.url).href,
  rankingColor: 'rgba(113, 186, 170, 1)',
  nameColor: 'rgba(113, 186, 170, 1)',
  borderColor: 'rgba(113, 186, 170, 1)',
  currentNameColor: 'rgba(113, 186, 170, 1)',
  itemRankingColor: 'rgba(138, 108, 35, 1)',
  avatarSize: 20,
  avatarRadius: 50,
}

export interface IDesignMobileRankinglist extends IDesignLayer {
  type: typeof type
  data: Partial<{
    unit: string
    topOneIconImg: string
    topTwoIconImg: string
    topThreeIconImg: string
    topOtherIconImg: string
    titleImg: string
    bgImg: string
    closeImg: string
    // 自己名次颜色
    rankingColor: string
    // 列表名次颜色
    itemRankingColor: string
    // 分割线颜色
    borderColor: string
    // 昵称 / 成绩颜色
    nameColor: string
    // 自己昵称 / 成绩颜色
    currentNameColor: string
    avatarSize: number // 不带px
    avatarRadius: number // 不带px
  }>
}

export function setup(app: IDesignSetup) {
  const designData = useDesignData()

  app.registry({
    type,
    bisType: BisTypes.rankingList,
    showType: ['mobile'],
    name: '移动端-排行榜',
    thumbnail: new URL('./mobile-rankinglist.png', import.meta.url).href,
    status: ['finish'],
    Comp,
    CompSetting,
    defaultData(): IDesignMobileRankinglist {
      return {
        uuid: layerUuid(),
        name: '移动端-排行榜',
        type,
        style: {
          width: `${designData.option.drafts[0] || 375}px`,
          height: `${designData.option.drafts[1] || 630}px`,
          backgroundColor: 'rgba(0, 0, 0, 0.4)',
        },
        data: {},
      }
    },
  })
}
