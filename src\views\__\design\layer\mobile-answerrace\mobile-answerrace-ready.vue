<script setup lang="ts">
import { injectScale, useDesignState, useDesignTemp } from '../../index'
import { processStyle } from '../../utils'
import { DEFAULT_DATA, type IDesignMobileAnswerraceReady } from './mobile-answerrace-ready'
import { readyMockConfig } from './mock'

const layer = defineModel<IDesignMobileAnswerraceReady>('layer', { required: true })

const designTemp = useDesignTemp()
const designState = useDesignState()

const data = computed(() => {
  return {
    ...DEFAULT_DATA,
    ...layer.value.data,
  }
})

const status = computed(() => {
  if (designTemp.isEdit && readyMockConfig.value.mockState) {
    return readyMockConfig.value.mockState
  }
  return designState.getLayerData('currentSubject') ? 'roundStarted' : 'waitingToStart'
})

const statusText = computed(() => {
  return {
    waitingToStart: data.value.waitingToStartText,
    roundStarted: data.value.roundStartedText,
  }[status.value as string] || ''
})

const isTeam = computed(() => {
  if (designTemp.isEdit && readyMockConfig.value.mockJoinType) {
    return readyMockConfig.value.mockJoinType === 'TEAM'
  }
  return designState.getLayerData('isTeamMode')
})

const teamList = computed(() => {
  const data = designState.getLayerData('teamList')
  if (data?.length || !designTemp.isEdit) {
    return data
  }
  return ['队伍A', '队伍B', '队伍C', '队伍D'].map((name, index) => ({
    id: `${index + 1}`,
    name,
  }))
})

const rulesText = computed(() => {
  if (designTemp.isEdit) {
    return '规则内容，需要在后台管理配置'
  }
  return designState.getLayerData('rulesText')
})

const scale = injectScale()
const rootVars = computed(() => {
  return processStyle({
    '--team-selection-background': data.value.teamSelectionBackground,
    '--rules-background': data.value.rulesBackground,
    '--team-selection-text-color': data.value.teamSelectionTextColor,
    '--rules-text-font-size': `${data.value.rulesTextFontSize}px`,
    '--rules-text-color': data.value.rulesTextColor,
    '--status-text-color': data.value.statusTextColor,
    '--status-text-font-size': `${data.value.statusTextFontSize}px`,
  }, scale.value)
})
</script>

<template>
  <div class="mobile-answerrace-ready-box h-full w-full overflow-auto">
    <div class="flex flex-col items-center justify-between gap-10px py-20px pt-80px text-white" :style="rootVars">
      <img :src="data.decorationImage" alt="主元素图" class="h-140px w-full object-contain" />
      <div class="w-full flex-1">
        <!-- 选择队伍 -->
        <div v-if="isTeam" class="mx-auto my-20px min-h-100px w-90% flex flex-col items-center rd-4px bg-[var(--team-selection-background)] p-20px text-white">
          <p class="mb-10px text-20px text-[var(--team-selection-text-color)] font-bold">{{ data.teamSelectionText }}</p>
          <select
            v-model="designState.layerData['%teamId%']"
            class="mx-auto my-10px h-44px w-90% b-none bg-#F6F6F6 px-6px text-14px outline-none"
            :disabled="status !== 'waitingToStart'"
          >
            <option disabled value="">请选择队伍</option>
            <option v-for="item in teamList" :key="item.id" :value="item.id">{{ item.teamName }}</option>
          </select>
        </div>
        <!-- 规则 -->
        <div v-if="rulesText" class="mx-auto my-20px min-h-100px w-90% break-all rd-4px bg-[var(--rules-background)] p-14px font-size-[var(--rules-text-font-size)] text-[var(--rules-text-color)] line-height-tight">
          <p class="whitespace-pre-wrap break-all">规则：{{ rulesText }}</p>
        </div>
      </div>
      <!-- 状态 -->
      <div class="text-center font-size-[var(--status-text-font-size)] text-[var(--status-text-color)] line-height-normal">
        <p class="my-14px whitespace-pre-wrap break-all">{{ statusText }}</p>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
