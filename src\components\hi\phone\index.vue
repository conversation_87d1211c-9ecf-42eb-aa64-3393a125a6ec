<script setup lang="ts">
</script>

<template>
  <div class="phone">
    <div class="screen">
      <slot />
    </div>
  </div>
</template>

<style scoped>
.phone {
  background: #fff;
  border-radius: 40px;
  position: relative;
  box-shadow: 0 2px 15px rgba(0,0,0,0.1);
}

/* 超细边框实现 */
.phone::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 40px;
  transform: scale(0.98);
}

.screen {
  width: 92%;
  height: 94%;
  background: #f2f2f2;
  border-radius: 25px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  overflow: hidden;
}
</style>
