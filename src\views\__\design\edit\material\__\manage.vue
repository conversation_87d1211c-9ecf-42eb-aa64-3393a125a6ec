<script setup lang="ts">
import type { IDesignMaterialType, IPage } from '@/views/__/design/types'
import type { MaterialLinkLabel } from './editmaterial.vue'
import type { ImageMaterial } from './image.vue'
import type { Label } from './label.vue'
import type { MaterialLabel } from './uploaddia.vue'
import HiAudio from './audio.vue'
import { classicsBlueIconUrls } from './data'
import HiEditmaterial from './editmaterial.vue'
import HiImage from './image.vue'
import HiLabel from './label.vue'
import HiUploaddia from './uploaddia.vue'
import HiVideo from './video.vue'

const props = defineProps<{
  materialType: IDesignMaterialType
}>()
const emits = defineEmits<{
  (e: 'cancle'): void
  (e: 'selection', val: ImageMaterial): void
}>()

const router = useRouter()
const isManage = computed(() => router.currentRoute.value.path.startsWith('/manage') || router.currentRoute.value.path.startsWith('/test'))
const isOem = computed(() => router.currentRoute.value.path.startsWith('/oem'))

const actionUrl = computed(() => {
  if (isManage.value) {
    return '/manage/admincommonfile/material/upload.htm'
  }
  if (isOem.value) {
    return '/oem/admin/procommonfile/material/upload.htm'
  }
  return ''
})
const isLoading = ref(false)
const uploadVisible = ref(false)
const dialogVisible = ref(false)

const materialData = ref<IPage<ImageMaterial>>({
  pageSize: 50,
  pageIndex: 1,
  total: 0,
  totalPages: 0,
  dataList: [],
})

// 菜单
const menuType = ref('ON')
// const menu = [{ name: '已上架', value: 'ON' }, { name: '未上架', value: 'OFF' }]
const menu = [{ name: isOem.value ? '我的素材' : '官方素材', value: 'ON' }]

const isMusic = computed(() => props.materialType === 'MUSIC')
const isVideo = computed(() => props.materialType === 'VIDEO')
const isPic = computed(() => props.materialType === 'PIC')
const designMaterialComp = computed(() => {
  if (isMusic.value) return HiAudio
  if (isVideo.value) return HiVideo
  return HiImage
})

if (isPic.value) {
  menu.push({
    name: '互动图标',
    value: 'icon_material',
  })
}

// 上传素材
async function postMaterial(data: MaterialLabel) {
  const temp: any = {}
  if (data.dataList?.length) {
    temp.dataList = data.dataList
  }
  if (data.labelIdList?.length) {
    temp.labelIdList = data.labelIdList
  }
  if (!temp.dataList) return
  if (isManage.value) {
    await api.man.material.batchAdd(temp)
  } else if (isOem.value) {
    await api.oem.material.materialownBatchAdd(temp)
  } else {
    throw new Error('未知的角色')
  }
  uploadVisible.value = false
  ElMessage.success('保存成功')
  // menuType.value = 'OFF'
  renderPage()
}

// 标签
const labelRefs = ref()
const labelList = ref<Label[]>([])
const labelSelIdList = ref<number[]>([])
function onLabelChange(isList: number[]) {
  labelSelIdList.value = isList
  materialData.value.pageIndex = 1
  renderPage()
}
async function fetchTagsList() {
  try {
    const apiBody = {
      where: {
        type: props.materialType,
      },
    }
    let data
    if (isManage.value) {
      data = await api.man.label.list(apiBody)
    } else if (isOem.value) {
      data = await api.oem.material.materiallabelownList(apiBody)
    } else {
      throw new Error('未知的角色')
    }
    labelList.value = data
  } catch (err) {
    console.log(err)
  }
}
async function renderPage() {
  try {
    const where: {
      type: IDesignMaterialType
      labelIdList?: number[]
    } = {
      type: props.materialType,
      // state: menuType.value,
    }
    if (labelSelIdList.value.length) {
      where.labelIdList = [labelSelIdList.value.at(-1)] as number[]
    }
    isLoading.value = true
    const apiBody = {
      where,
      pageSize: materialData.value.pageSize,
      pageIndex: materialData.value.pageIndex,
      sort: {
        createDate: 'desc',
        id: 'desc',
      },
    }
    if (isManage.value) {
      const data = await api.man.material.page(apiBody)
      materialData.value = data
    } else if (isOem.value) {
      const data = await api.oem.material.materialownPage(apiBody)
      materialData.value = data
    } else {
      throw new Error('未知的角色')
    }
  } catch (err) {
    console.log(err)
  } finally {
    isLoading.value = false
  }
}
async function deleteMaterial(id: string) {
  try {
    await ElMessageBox.confirm('是否删除该素材', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    const apiBody = {
      where: { id },
    }
    if (isManage.value) {
      await api.man.material.delete(apiBody)
    } else if (isOem.value) {
      await api.oem.material.materialownDelete(apiBody)
    } else {
      throw new Error('未知的角色')
    }
    renderPage()
    ElMessage.success('删除成功')
  } catch (err) {
    console.log(err)
  }
}
async function deteleLabel(id: number) {
  try {
    await ElMessageBox.confirm('是否删除该标签', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    if (isManage.value) {
      await api.man.label.delete({
        where: { id },
      })
    } else if (isOem.value) {
      await api.oem.material.materiallabelownDelete({
        where: { id },
      })
    } else {
      throw new Error('未知的角色')
    }
    ElMessage.success('删除成功')
    labelSelIdList.value = []
    renderPage()
    fetchTagsList()
  } catch (err) {
    console.log(err)
  }
}

// 二级标签，批量增删
async function onLabelUpsert(data: any) {
  try {
    if (!data.id) {
      let res
      if (isManage.value) {
        res = await api.man.label.add({
          name: data.name,
          type: props.materialType,
        })
      } else if (isOem.value) {
        res = await api.oem.material.materiallabelownAdd({
          name: data.name,
          type: props.materialType,
        })
      } else {
        throw new Error('未知的角色')
      }
      const { addArr } = data.children
      const bacthData: any = []
      if (addArr.length) {
        const arr: any[] = []
        addArr.forEach((item: any) => {
          arr.push({
            name: item.name,
            pId: res,
            type: props.materialType,
          })
        })
        bacthData.push({
          type: 'add',
          data: arr,
        })
      }
      if (bacthData.length) {
        if (isManage.value) {
          await api.man.label.batch(bacthData)
        } else if (isOem.value) {
          await api.oem.material.materiallabelownBatch(bacthData)
        } else {
          throw new Error('未知的角色')
        }
      }
    } else {
      const name = labelList.value?.find(item => item?.id === data.id)?.name
      if (name !== data.name) {
        const apiBody = {
          where: { id: data.id },
          update: {
            name: data.name,
          },
        }
        if (isManage.value) {
          await api.man.label.update(apiBody)
        } else if (isOem.value) {
          await api.oem.material.materiallabelownUpdate(apiBody)
        } else {
          throw new Error('未知的角色')
        }
      }
      const { delArr, addArr } = data.children
      const bacthData: any = []
      if (addArr.length) {
        const arr: any[] = []
        addArr.forEach((item: any) => {
          arr.push({
            name: item.name,
            pId: data.id,
            type: props.materialType,
          })
        })
        bacthData.push({
          type: 'add',
          data: arr,
        })
      }
      if (delArr.length) {
        bacthData.push({
          type: 'del',
          data: delArr,
        })
      }
      if (bacthData.length) {
        if (isManage.value) {
          await api.man.label.batch(bacthData)
        } else if (isOem.value) {
          await api.oem.material.materiallabelownBatch(bacthData)
        } else {
          throw new Error('未知的角色')
        }
      }
    }
    fetchTagsList()
    ElMessage.success('保存成功')
  } catch (err) {
    console.log(err)
  }
}
async function handleSwitchMenu(item: { name: string, value: string }) {
  labelSelIdList.value = []
  if (item.value === 'icon_material') {
    menuType.value = item.value
    materialData.value.dataList = classicsBlueIconUrls.map(itme => ({
      id: itme.url,
      name: itme.name,
      type: 'ICON',
      state: 'ON',
      url: itme.url,
    }))
    return
  }
  await Promise.all([renderPage(), fetchTagsList()])
  menuType.value = item.value
}

// 编辑素材
const editLabelList = ref<MaterialLinkLabel[]>([])
const editDialogVisible = ref(false)
const editObj = ref<ImageMaterial>()
async function editMsterial(item: ImageMaterial) {
  editObj.value = item
  if (isManage.value) {
    const data = await api.man.materiallabel.list({
      where: {
        materialId: item.id,
      },
    })
    editLabelList.value = data
  } else if (isOem.value) {
    const data = await api.oem.material.materialmateriallabelList({
      where: {
        materialId: item.id,
      },
    })
    editLabelList.value = data
  } else {
    throw new Error('未知的角色')
  }
  await nextTick()
  editDialogVisible.value = true
}

async function saveEditMsterial(data: any) {
  try {
    const { name, labelUpdate } = data
    if (name) {
      if (isManage.value) {
        await api.man.material.update({
          where: { id: editObj.value?.id },
          update: { name },
        })
      } else if (isOem.value) {
        await api.oem.material.materialownUpdate({
          where: { id: editObj.value?.id },
          update: { name },
        })
      } else {
        throw new Error('未知的角色')
      }
    }
    if (labelUpdate) {
      if (isManage.value) {
        await api.man.materiallabel.batch(labelUpdate)
      } else if (isOem.value) {
        await api.oem.material.materialmateriallabelBatch(labelUpdate)
      } else {
        throw new Error('未知的角色')
      }
    }
    ElMessage.success('保存成功')
    editDialogVisible.value = false
    renderPage()
  } catch (error) {
    console.log(error)
  }
}

// 上架素材
const toUpWallList = ref<string[]>([])
async function upWall(id?: string) {
  try {
    const idList: any = []
    if (id) {
      idList.push(id)
    } else {
      idList.push(...toUpWallList.value)
    }
    if (!idList.length) {
      ElMessage.warning('请选择素材')
      return
    }
    if (isManage.value) {
      await api.man.material.update({
        where: {
          idList,
        },
        update: {
          state: 'ON',
        },
      })
    } else if (isOem.value) {
      await api.oem.material.materialownUpdate({
        where: {
          idList,
        },
        update: {
          state: 'ON',
        },
      })
    } else {
      throw new Error('未知的角色')
    }
    ElMessage.success('保存成功')
    renderPage()
  } catch (err) {
    console.log(err)
  }
}
function clearUpWallList() {
  toUpWallList.value = []
}
function confirmFn(val: ImageMaterial) {
  if (menuType.value === 'OFF') {
    if (toUpWallList.value.includes(val.id)) {
      toUpWallList.value = toUpWallList.value.filter(item => item !== val.id)
    } else {
      toUpWallList.value.push(val.id)
    }
    return
  }
  emits('selection', val)
  dialogVisible.value = false
}
function selAllPage() {
  materialData.value.dataList.forEach((item) => {
    toUpWallList.value.push(item.id)
  })
}

async function init() {
  isLoading.value = true
  const fetchTagsPromise = fetchTagsList()
  const labelSelPromise = Promise.resolve()

  // if (props.materiaId) {
  //   labelSelPromise = api.man.materiallabel.list({
  //     where: {
  //       materialId: props.materiaId,
  //     },
  //   }).then((data) => {
  //     labelSelIdList.value = data.map((item: any) => item.labelId)
  //     materialData.value.pageIndex = 1
  //     labelRefs.value?.resetLabelRefFn(labelSelIdList.value)
  //   })
  // }

  await Promise.all([fetchTagsPromise, labelSelPromise])
  await renderPage()
  dialogVisible.value = true
}

onMounted(() => {
  init()
})
</script>

<template>
  <div class="material-box">
    <div class="material-btns">
      <div>
        <p v-for="item of menu" :key="item.value">
          <el-button
            class="mb-10 h-40 w-100"
            :type="menuType === item.value ? 'primary' : ''"
            :text="menuType !== item.value"
            @click="handleSwitchMenu(item)"
          >
            {{ item.name }}
          </el-button>
        </p>
      </div>
      <div v-if="menuType === 'OFF'" class="flex flex-d-c flex-a-c">
        <el-button class="mb-10 w-100 ml-0!" :disabled="!toUpWallList.length" type="success" @click="upWall()">全部上架</el-button>
        <el-button class="mb-10 w-60" type="text" @click="selAllPage()">全选本页</el-button>
        <el-button class="mb-10 w-60" type="text" @click="clearUpWallList()">取消选择</el-button>
      </div>
      <p>
        <el-button class="mb-10 w-100" type="primary" @click="uploadVisible = true">
          <span>上传素材</span><icon-ph-plus-bold class="ml-3 size-15" />
        </el-button>
      </p>
    </div>
    <!-- 音乐素材 -->
    <div v-loading="isLoading" class="right-box">
      <HiLabel
        v-if="menuType !== 'icon_material'"
        ref="labelRefs"
        :label-list="labelList"
        @upsert="onLabelUpsert"
        @remove="deteleLabel"
        @change="onLabelChange"
      ></HiLabel>
      <el-scrollbar v-if="materialData.dataList" class="w-full">
        <component
          :is="designMaterialComp"
          :to-up-wall-list="toUpWallList"
          :menu-type="menuType"
          :material-list="materialData.dataList"
          @delete="deleteMaterial"
          @up-wall="upWall"
          @selection="confirmFn"
          @edit="editMsterial"
        ></component>
      </el-scrollbar>
      <el-pagination
        v-if="menuType !== 'icon_material'"
        v-model:current-page="materialData.pageIndex"
        v-model:page-size="materialData.pageSize"
        class="my-10 flex justify-center"
        :page-sizes="[20, 40, 50, 60, 80, 100]"
        background
        layout="total, prev, pager, next, jumper"
        :total="materialData.total"
        @size-change="renderPage"
        @current-change="renderPage"
      />
    </div>
    <HiUploaddia
      v-if="uploadVisible"
      :label-list="labelList"
      :material-type="materialType"
      :action-url="actionUrl"
      @cancle="uploadVisible = false"
      @save="postMaterial"
    />
    <HiEditmaterial
      v-if="editDialogVisible"
      :edit-obj="editObj"
      :label-list="labelList"
      :sel-label-list="editLabelList"
      @cancle="editDialogVisible = false"
      @save="saveEditMsterial"
    />
  </div>
</template>

<style scoped lang="scss">
.material-box {
  height: 70vh;
  display: flex;
  position: relative;

  .material-btns {
    min-height: 200px;
    border-right: 1px solid #e6ebed;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 10px;
  }

  .right-box {
    flex: 1;
    display: flex;
    width: 100%;
    position: relative;
    overflow: hidden;
    flex-direction: column;
  }
}

.progress-container {
  --progress-bar-width: 0;

  width: 100%;
  height: 5px;
  background-color: #e6ebed;
  border-radius: 2.5px;
  overflow: hidden;
  margin-top: 5px;

  .progress-bar {
    height: 100%;
    background-color: #1261ff;
    width: var(--progress-bar-width);
  }
}
.pagination {
  justify-content: flex-end;
}
</style>
