import { merge } from 'lodash-es'
import { BisTypes } from '../../'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './mobile-performance-setting.vue'
import Comp from './mobile-performance.vue'

// 类型
export const type = 'mobile-performance'

export interface IPerformanceForm {
  key: string
  name: string
  type: string
  value?: string | null
  unit?: string | null
  conf?: string | null
  remark?: string | null
}

// 数据类型约束
export interface IDesignMobilePerformance extends IDesignLayer {
  type: typeof type
  data: {
    cancelButtonImg: string
    saveButtonImg: string
    updateButtonImg: string
    // 是否允许修改
    isAllowEdit: boolean
    // 表单排序
    formSort?: {
      [key: string]: number
    }
  }
}

export const defaultData: IDesignMobilePerformance['data'] = {
  cancelButtonImg: new URL('./assets/cancel-button.png', import.meta.url).href,
  saveButtonImg: new URL('./assets/save-button.png', import.meta.url).href,
  updateButtonImg: new URL('./assets/update-button.png', import.meta.url).href,
  isAllowEdit: true,
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.sportsIng,
    type,
    name: '业绩目标会',
    showInteractive: [InteractiveEnum.performancev3],
    thumbnail: new URL('./mobile-performance.png', import.meta.url).href,
    showType: ['mobile'],
    Comp,
    CompSetting,
    defaultData(options): IDesignMobilePerformance {
      return merge({
        uuid: layerUuid(),
        name: '业绩目标会',
        type,
        style: {
          width: '80%',
          height: '400px',
          borderRadius: '10px',
          background: 'rgba(0,0,0,0.4)',
        },
        data: {},
      }, options as IDesignMobilePerformance)
    },
  })
}
