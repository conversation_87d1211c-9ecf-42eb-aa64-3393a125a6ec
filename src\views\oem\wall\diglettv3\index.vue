<script setup lang="ts">
import HiDesign from 'design/index.vue'
import { useDesignTemp } from '~/src/views/__/design'
import { useMobileDiglettv3 } from '~/src/views/mobile/wall/diglettv3'
import { usePcwallDiglettv3 } from '~/src/views/pcwall/diglettv3'

definePage({ meta: { label: '敲敲乐' } })

const interactive = 'diglettv3'
const designTemp = useDesignTemp()

watch(
  () => designTemp.showType,
  (v) => {
    if (v === 'mobile') {
      useMobileDiglettv3()
    } else {
      usePcwallDiglettv3()
    }
  },
  { immediate: true },
)
</script>

<template>
  <HiDesign :interactive="interactive" />
</template>

<style scoped lang="scss">
</style>
