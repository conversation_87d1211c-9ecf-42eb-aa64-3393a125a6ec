import { HiRequest } from '../request'

export default {
  configRead: (params: any) => HiRequest.post('/pro/hxc/mobile/progoldcoinv3config/read.htm', params),
  read: (params: any) => HiRequest.post('/pro/hxc/mobile/progoldcoinv3/read.htm', params),
  ranking: (params: any) => HiRequest.post('/pro/hxc/mobile/progoldcoinv3/ranking.htm', params),

  reportRecord: (params: any) => HiRequest.post('/pro/hxc/mobile/progoldcoinv3record/report.htm', params),
  readRecord: (params: any) => HiRequest.post('/pro/hxc/mobile/progoldcoinv3record/read.htm', params),

  readRegedit: (params: any) => HiRequest.post('/pro/hxc/mobile/progoldcoinv3regedit/read.htm', params),
  insertRegedit: (params: any) => HiRequest.post('/pro/hxc/mobile/progoldcoinv3regedit/insert.htm', params),
  quitRegedit: (params: any) => HiRequest.post('/pro/hxc/mobile/progoldcoinv3regedit/quit.htm', params),

  listTeam: (params: any) => HiRequest.post('/pro/hxc/mobile/progoldcoinv3team/list.htm', params),
  readTeam: (params: any) => HiRequest.post('/pro/hxc/mobile/progoldcoinv3team/read.htm', params),

  curScore: (params: any) => HiRequest.post('/pro/hxc/mobile/progoldcoinv3record/curScore.htm', params),
}
