<script setup lang="ts">
import type { IDesignImgVideo } from './img-video'
import { defaultAutoplay, defaultControls, defaultLoop, defaultMuted, defaultVideoObjectFit } from './img-video'

const layer = defineModel<IDesignImgVideo>('layer', { required: true })

const videoRef = ref<HTMLVideoElement | null>(null)

const isPlaying = ref(false)
const controller = new AbortController()
const controls = computed(() => {
  return layer.value.controls ?? defaultControls
})
const autoplay = computed(() => {
  return layer.value.autoplay ?? defaultAutoplay
})
const loop = computed(() => {
  return layer.value.loop ?? defaultLoop
})
const muted = computed(() => {
  return layer.value.muted ?? defaultMuted
})
// 尝试播放
function addEventTryPlay() {
  ['touchstart', 'click', 'WeixinJSBridgeReady'].forEach((event) => {
    document.addEventListener(event, () => {
      videoRef.value?.load()
      videoRef.value?.play().catch(() => {})
    }, { signal: controller.signal })
  })
}

async function tryPlay() {
  if (isPlaying.value) return
  try {
    videoRef.value?.load()
    await videoRef.value?.play()
    const paused = videoRef.value?.paused
    if (paused) {
      addEventTryPlay()
    }
  } catch {
    addEventTryPlay()
  }
}

function playSuccess() {
  isPlaying.value = true
  controller.abort()
  if (videoRef.value) {
    videoRef.value.removeAttribute('poster')
  }
}

function togglePlay() {
  if (autoplay.value) return
  if (isPlaying.value) {
    videoRef.value?.pause()
  } else {
    videoRef.value?.play()
  }
  isPlaying.value = !isPlaying.value
}

const videoStyle = computed(() => {
  const style = {
    objectFit: layer.value.videoObjectFit || defaultVideoObjectFit,
  }
  return style
})

onMounted(async () => {
  // setPoster() //不设置封面图即可自动获取第一帧，先保留获取第一帧的代码
  if (layer.value.autoplay) {
    await nextTick()
    tryPlay()
  }
})
</script>

<template>
  <video
    :id="layer.uuid"
    ref="videoRef"
    :controls="controls"
    :autoplay="autoplay"
    :loop="loop"
    :muted="muted"
    :src="layer.videoData"
    :style="videoStyle"
    class="design-video"
    crossOrigin="anonymous"
    disablepictureinpicture
    playsinline
    preload="auto"
    webkit-playsinline
    x-webkit-airplay
    x5-playsinline
    x5-video-player-type="h5-page"
    @click="togglePlay"
    @play="playSuccess"
  />
</template>

<style scoped lang="scss">
.design-video {
  width: 100% !important;
  height: 100% !important;
  object-fit: fill;
}
</style>
