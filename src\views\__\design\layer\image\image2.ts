import type { IDesignLayer, IDesignSetup } from '../../types'
import { merge } from 'lodash-es'
import { openSelectMaterial, ResourceUtil } from '../..'

import { layerUuid } from '../../utils'
import CompSetting from './image2-setting.vue'
import Comp from './image2.vue'

export const type = 'image2'

export type IMode = 'none' | 'contain' | 'fill' | 'cover'
export type IRepeat = 'no-repeat' | 'repeat-x' | 'repeat-y' | 'repeat'
export type IDrirection = 'none' | 'x' | 'y'

export const defaultMode = 'contain'
export const defaultRepeat = 'no-repeat'
export const defaultSize = 100
export const defaultPosition = 50

export const defaultDirection = 'none'
export const defaultDuration = 5

export interface IDesignImage2 extends IDesignLayer {
  type: typeof type
  data: string

  mode?: IMode
  sizeX?: number // 百分比, 0 为 auto
  sizeY?: number // 百分比, 0 为 auto

  repeat?: IRepeat
  posX?: number // x 位置百分数
  posY?: number // y 位置百分数

  direction?: IDrirection
  reverse?: boolean
  duration?: number
  playStates?: string[]
  forceStop?: boolean
}

export function setup(app: IDesignSetup) {
  app.registry({
    type,
    base: true,
    name: '图片',
    Comp,
    CompSetting,
    async defaultData(options) {
      const data = (options as IDesignImage2)?.data || await openSelectMaterial()
      if (!data) return
      const { width, height } = await ResourceUtil.loadImage(data, { draftScale: 0.2 })

      return merge({
        uuid: layerUuid(),
        name: '新图片',
        data,
        type,
        style: {
          width: `${width}px`,
          height: `${height}px`,
        },
      }, options as IDesignImage2)
    },
  })
}
