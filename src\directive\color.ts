import type { DirectiveBinding } from 'vue'
import randomcolor from 'randomcolor'

interface RandomColorOptionsSingle {
  hue?: number | string | undefined
  luminosity?: 'bright' | 'light' | 'dark' | 'random' | undefined
  seed?: number | string | undefined
  format?: 'hsvArray' | 'hslArray' | 'hsl' | 'hsla' | 'rgbArray' | 'rgb' | 'rgba' | 'hex' | undefined
  alpha?: number | undefined
}

export default {
  mounted(el: HTMLElement, binding: DirectiveBinding<RandomColorOptionsSingle>) {
    el.style.backgroundColor = randomcolor(
      binding.value || {
        luminosity: 'light',
        hue: 'green',
      },
    )
    el.style.color = '#34495e'
    el.style.fontSize = '18px'
  },
}
