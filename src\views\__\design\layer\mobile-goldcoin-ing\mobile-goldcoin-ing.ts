import { merge } from 'lodash-es'
import { BisTypes, useDesignData } from '../../'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './mobile-goldcoin-ing-setting.vue'
import Comp from './mobile-goldcoin-ing.vue'

// 类型
export const type = 'mobile-goldcoin-ing'

// 数据类型约束
interface Receiver {
  imgUrl: string
  width: number
  height: number
  rotate: boolean
}
interface Falling {
  score: number
  imgUrl: string
  width: number
  height: number
}

export const DEFAULT_DATA = {
  bombMusic: new URL(`./assets/bomb.mp3`, import.meta.url).href,
  bombImage: new URL(`./assets/bomb-animation.png`, import.meta.url).href,
  successBombImage: new URL(`./assets/success-animation.png`, import.meta.url).href,
  successMusic: new URL(`./assets/success.mp3`, import.meta.url).href,
  receiver: {
    imgUrl: new URL(`./assets/receiver.png`, import.meta.url).href,
    rotate: true,
    width: 242,
    height: 284,
  },
  fallings: [
    {
      imgUrl: new URL(`./assets/fallings1.png`, import.meta.url).href,
      score: 1,
      width: 96,
      height: 96,
    },
    {
      imgUrl: new URL(`./assets/fallings2.png`, import.meta.url).href,
      score: 2,
      width: 96,
      height: 96,
    },
    {
      imgUrl: new URL(`./assets/fallings3.png`, import.meta.url).href,
      score: -3,
      width: 96,
      height: 96,
    },
  ],
  fallingsDensity: 3, // 掉落密度，每隔多久创建一个物体
  fallingSpeed: 2, // 物体掉落速度，单位秒，实际使用的时候会 10 - fallingSpeed ，且±2 随机取范围
}

export interface IDesignMobileGoldcoinIng extends IDesignLayer {
  type: typeof type
  data: {
    // 接住炸弹特效
    bombMusic: string
    // 接住炸弹音效
    bombImage: string
    // 成功接住特效
    successBombImage: string
    // 成功接住音效
    successMusic: string
    // 游戏主体
    receiver: Receiver
    // 加分元素，分钱模式不显示减分元素，积分模式需要开启才显示减分元素
    fallings: Falling[]
    // 密度, 是每隔多久出现一个新物体
    fallingsDensity: number
    // 掉落速度, 单个物体从出现到消失的速度
    fallingSpeed: number
  }
}

// 注册组件
export function setup(app: IDesignSetup) {
  const designData = useDesignData()

  app.registry({
    bisType: BisTypes.sportsIng,
    type,
    showType: ['mobile'],
    status: ['ing'],
    name: '手机端-接金币',
    thumbnail: new URL('mobile-goldcoin-ing.png', import.meta.url).href,
    Comp,
    CompSetting,
    showInteractive: [InteractiveEnum.goldcoinv3],
    defaultData(options): IDesignMobileGoldcoinIng {
      return merge({
        uuid: layerUuid(),
        name: '手机端-接金币',
        type,
        style: {
          width: `${designData.option.drafts[0] || 375}px`,
          height: `${(designData.option.drafts[1] || 630) - 60}px`,
          top: '60px',
        },
        data: {},
      }, options as IDesignMobileGoldcoinIng)
    },
  })
}
