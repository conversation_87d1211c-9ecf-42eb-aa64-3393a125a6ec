<script setup lang="ts">
// const layer = defineModel<IDesignMobileAwards>('layer', { required: true })
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.setting-wrap {
  padding: 10px 0;
}
.thumbnail-box {
  width: 120px;
  height: 120px;
  outline: 1px solid #e6ebed;
  border-radius: 4px;
  position: relative;

  img {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }
  > img {
    object-fit: contain;
  }
}
</style>
