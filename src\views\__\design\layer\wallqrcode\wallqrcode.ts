import type { IDesignLayer, IDesignSetup } from '../../types'
import { BisTypes } from '../..'
import { InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './wallqrcode-setting.vue'
import Comp from './wallqrcode.vue'
// 类型
export const type = 'wallqrcode'

// 数据类型约束
export interface IDesignWallqrcode extends IDesignLayer {
  type: typeof type
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.common,
    showType: ['pcwall'],
    status: ['ready'],
    showInteractive: [InteractiveEnum.lotteryv3],
    type,
    name: '大屏幕二维码',
    thumbnail: new URL('./wallqrcode.png', import.meta.url).href,
    Comp,
    CompSetting,
    defaultData() {
      return {
        type,
        uuid: layerUuid(),
        name: '大屏幕二维码',
        data: [],
        style: {
          left: '100px',
          top: '10px',
          width: '300px',
          height: '300px',
          borderRadius: '10px',
          padding: '20px',
        },
      }
    },
  })
}
