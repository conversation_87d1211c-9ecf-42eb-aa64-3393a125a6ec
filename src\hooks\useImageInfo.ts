import type { MaybeRef } from 'vue'

export function useImageInfo(src: MaybeRef<string | undefined>) {
  const width = ref(0)
  const height = ref(0)

  watch(
    () => toValue(src),
    (url) => {
      width.value = 0
      height.value = 0
      if (url) {
        const image = new Image()
        image.src = url
        image.onload = () => {
          width.value = image.width
          height.value = image.height
        }
      }
    },
    { immediate: true },
  )

  return {
    width,
    height,
    ratio: computed(() => width.value / height.value),
  }
}
