import { defineStore } from 'pinia'

interface IWxUser {
  id: number
  nickName: string
  imgpath: string
  showName: string
}

interface IParticipant {
  wxUserObj: {
    [key: number]: IWxUser
  }
  signUserObj: {
    [key: number]: {
      noteName: string
      name: string
      selfieImg: string
    }
  }
}

// 参与者
export const useParticipantStore = defineStore('participantStore', {
  state: (): IParticipant => {
    return {
      wxUserObj: {},
      signUserObj: {},
    }
  },
  actions: {
    async relationParticipant(list: (number | { wxUserId: number })[]) {
      if (!Array.isArray(list) || list.length === 0) return
      const originIdList: number[] = []
      list.forEach((item) => {
        if (typeof item === 'number') {
          if (item) {
            originIdList.push(item)
          }
        } else {
          if (item.wxUserId) {
            originIdList.push(item.wxUserId)
          }
        }
      })

      {
        const idList = originIdList.filter(item => !this.$state.wxUserObj[item])
        if (!idList.length) return

        const wxUserList = await api.mobile.wxuser.list({ where: { idList }, include: ['id', 'imgpath', 'nickName', 'showName'] })
        wxUserList.forEach((item: IWxUser) => {
          this.$state.wxUserObj[item.id] = item
        })
      }

      // 关联签到信息
      {
        const idList = originIdList.filter(item => !this.$state.signUserObj[item])
        if (!idList.length) return

        const applysignList = await api.mobile.applysign.list({
          where: { wxUserIdList: idList, status: 'alreadySign' },
          include: ['id', 'wxUserId', 'noteName', 'name', 'blessingContent', 'signatureImg', 'selfieImg', 'phone'],
        })
        applysignList.forEach((item: any) => {
          this.$state.signUserObj[item.wxUserId] = item
        })
      }
    },

    getWxUser(id: number) {
      return this.$state.wxUserObj[id]
    },
    getSignUser(id: number) {
      return this.$state.signUserObj[id]
    },
    getWxName(id: number) {
      const wxUser = this.getWxUser(id)
      return wxUser?.showName || wxUser?.nickName || ''
    },
    getSignName(id: number) {
      const signUser = this.getSignUser(id)
      return signUser?.noteName || signUser?.name || ''
    },
    getAvatar(id: number) {
      const wxUser = this.getWxUser(id)
      const signUser = this.getSignUser(id)
      return signUser?.selfieImg || wxUser?.imgpath || ''
    },
  },
})
