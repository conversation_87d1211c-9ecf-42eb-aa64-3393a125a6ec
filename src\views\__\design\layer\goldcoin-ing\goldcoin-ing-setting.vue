<script setup lang="ts">
import { getDefaultMaterial, openSelectMaterial } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'
import { useDataAttr } from '../../index'
import { defaultAvatarSize, defaultAvatarX, defaultAvatarY, defaultAvatarZIndex, defaultBackground, defaultBgImgHeight, defaultBgImgWidth, defaultBgImgX, defaultBgImgY, defaultBorderColor, defaultColor, defaultHeightPadding, type IDesignGoldcoinIng } from './goldcoin-ing'

const layer = defineModel<IDesignGoldcoinIng>('layer', { required: true })
type IType = 'trackTop' | 'track' | 'trackBottom' | 'decoration' | 'drops' | 'bgImg'
async function updateMaterialFn(name: IType, isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, `${name}`)
    : await openSelectMaterial('PIC')
  if (result) {
    layer.value[name] = result
  } else if (isReset) {
    delete layer.value[name]
  }
}

const colorBind = useDataAttr(layer.value, 'color', defaultColor)
const borderColorBind = useDataAttr(layer.value, 'borderColor', defaultBorderColor)
const avatarXBind = useDataAttr(layer.value, 'avatarX', defaultAvatarX)
const avatarYBind = useDataAttr(layer.value, 'avatarY', defaultAvatarY)
const avatarSizeBind = useDataAttr(layer.value, 'avatarSize', defaultAvatarSize)
const backgroundBind = useDataAttr(layer.value, 'background', defaultBackground)
const avatarZIndexBind = useDataAttr(layer.value, 'avatarZIndex', defaultAvatarZIndex)
const bgImgXBind = useDataAttr(layer.value, 'bgImgX', defaultBgImgX)
const bgImgYBind = useDataAttr(layer.value, 'bgImgY', defaultBgImgY)
const bgImgWidthBind = useDataAttr(layer.value, 'bgImgWidth', defaultBgImgWidth)
const bgImgHeightBind = useDataAttr(layer.value, 'bgImgHeight', defaultBgImgHeight)
const heightPaddingBind = useDataAttr(layer.value, 'heightPadding', defaultHeightPadding)
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>字体颜色</h3>
        <hi-color v-model="colorBind" />
      </div>
      <div class="setting-item">
        <h3>边框颜色</h3>
        <hi-color v-model="borderColorBind" />
      </div>
      <div class="setting-item">
        <h3>背景颜色</h3>
        <hi-color v-model="backgroundBind" />
      </div>
      <div class="setting-item">
        <h3>掉落物</h3>
        <div class="relative flex items-end">
          <div class="d bgblank" @click="layer.drops = ''"></div>
          <div class="bgblank h-40 w-64">
            <MaterialThumbnail @select="updateMaterialFn('drops')" @reset="updateMaterialFn('drops', true)">
              <img v-if="layer.drops" :src="layer.drops">
            </MaterialThumbnail>
          </div>
        </div>
      </div>
      <div class="setting-item">
        <h3>顶部</h3>
        <div class="relative flex items-end">
          <div class="d bgblank" @click="layer.trackTop = ''"></div>
          <div class="bgblank h-40 w-64">
            <MaterialThumbnail @select="updateMaterialFn('trackTop')" @reset="updateMaterialFn('trackTop', true)">
              <img v-if="layer.trackTop" :src="layer.trackTop">
            </MaterialThumbnail>
          </div>
        </div>
      </div>
      <div class="setting-item">
        <h3>中间</h3>
        <div class="relative flex items-end">
          <div class="d bgblank" @click="layer.track = ''"></div>
          <div class="bgblank h-40 w-64">
            <MaterialThumbnail @select="updateMaterialFn('track')" @reset="updateMaterialFn('track', true)">
              <img v-if="layer.track" :src="layer.track">
            </MaterialThumbnail>
          </div>
        </div>
      </div>
      <div class="setting-item">
        <h3>底部</h3>
        <div class="relative flex items-end">
          <div class="d bgblank" @click="layer.trackBottom = ''"></div>
          <div class="bgblank h-40 w-64">
            <MaterialThumbnail @select="updateMaterialFn('trackBottom')" @reset="updateMaterialFn('trackBottom', true)">
              <img v-if="layer.trackBottom" :src="layer.trackBottom">
            </MaterialThumbnail>
          </div>
        </div>
      </div>
      <div class="setting-item">
        <h3>头像背景</h3>
        <div class="bgblank relative h-60 w-100">
          <MaterialThumbnail @select="updateMaterialFn('decoration')" @reset="updateMaterialFn('decoration', true)">
            <img :src="layer.decoration">
          </MaterialThumbnail>
        </div>
      </div>
      <!-- 大小 -->
      <div class="setting-item">
        <h3>头像区域</h3>
        <div class="flex">
          <h3 class="ml-10">大小</h3>
          <el-input-number v-model="avatarSizeBind" v-input-number controls-position="right" :min="1" :step="1" />
        </div>
      </div>
      <div class="setting-item justify-end!">
        <h3 class="ml-10">层级</h3>
        <el-select v-model="avatarZIndexBind" class="ml-10 w-60">
          <el-option label="上层" :value="10" />
          <el-option label="下层" :value="1" />
        </el-select>
      </div>
      <div class="setting-item justify-end!">
        <h3 class="ml-10">X</h3>
        <el-input-number v-model="avatarXBind" v-input-number controls-position="right" :min="0" :max="100" :step="1" />
        <h3 class="ml-10">Y</h3>
        <el-input-number v-model="avatarYBind" v-input-number controls-position="right" :min="0" :max="100" :step="1" />
      </div>
      <div class="setting-item">
        <h3>柱子背景</h3>
        <div class="relative flex items-end">
          <div class="d bgblank" @click="layer.bgImg = ''"></div>
          <div class="bgblank h-40 w-64">
            <MaterialThumbnail @select="updateMaterialFn('bgImg')" @reset="updateMaterialFn('bgImg', true)">
              <img v-if="layer.bgImg" :src="layer.bgImg">
            </MaterialThumbnail>
          </div>
        </div>
      </div>
      <!-- 大小 -->
      <div class="setting-item">
        <h3>柱子背景区域</h3>
      </div>
      <div class="setting-item justify-end!">
        <h3 class="ml-10">宽</h3>
        <el-input-number v-model="bgImgWidthBind" v-input-number controls-position="right" :min="0" :max="500" :step="1" />
        <h3 class="ml-10">高</h3>
        <el-input-number v-model="bgImgHeightBind" v-input-number controls-position="right" :min="0" :max="200" :step="1" />
      </div>
      <div class="setting-item justify-end!">
        <h3 class="ml-10">X</h3>
        <el-input-number v-model="bgImgXBind" v-input-number controls-position="right" :min="-100" :max="100" :step="1" />
        <h3 class="ml-10">Y</h3>
        <el-input-number v-model="bgImgYBind" v-input-number controls-position="right" :min="-100" :max="100" :step="1" />
      </div>
      <div class="setting-item">
        <h3>最大高度预留</h3>
        <el-input-number v-model="heightPaddingBind" v-input-number controls-position="right" :min="1" :step="1" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.d {
  width: 25px;
  height: 25px;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
  &:first-child {
    position: relative;
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 90%;
      height: 90%;
      margin: 5%;
      mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1em' height='1em' viewBox='0 0 256 256'%3E%3Cpath fill='%23000' d='m203.63 62.65l13.25-14.58a12 12 0 0 0-17.76-16.14l-13.24 14.56A100 100 0 0 0 52.37 193.35l-13.25 14.58a12 12 0 1 0 17.76 16.14l13.24-14.56A100 100 0 0 0 203.63 62.65M52 128a75.94 75.94 0 0 1 117.58-63.57l-100.91 111A75.6 75.6 0 0 1 52 128m76 76a75.5 75.5 0 0 1-41.58-12.43l100.91-111A75.94 75.94 0 0 1 128 204'/%3E%3C/svg%3E");
      mask-size: 100%;
      background-color: #999999;
      cursor: pointer;
    }
  }
}
</style>
