<script setup lang="ts">
import type { InputInstance } from 'element-plus/es/components/input/index.mjs'
import type { CSSProperties } from 'vue'
import type { TextArtMaterial } from '../../hooks/useFontLoader'
import type { IDesignText } from './text'
import { useDataAttr, useDesignState } from '../..'
import TextArtPicker from '../../components/TextArtPicker.vue'
import TextImageEdit from '../../components/textImageEdit.vue'
import TextShadowEditor from '../../components/textShadowEdit.vue'
import TextStrokeEdit from '../../components/textStrokeEdit.vue'
import { useFontLoader } from '../../hooks/useFontLoader'
import { defaultStyle } from './text'

const layer = defineModel<IDesignText>('layer', { required: true })

const designState = useDesignState()

const placeholderList = computed(() => {
  return Object.keys(designState.layerData).filter(i => i.startsWith('#') && i.endsWith('#'))
})
const textInputRef = ref<InputInstance>()
function insertText(tag: string) {
  const box = textInputRef.value
  if (!box) return

  const dom = box.textarea
  if (!dom) return

  let start = dom.selectionStart
  let end = dom.selectionEnd

  // 如果选中的是text-box
  if (document.activeElement && document.activeElement.classList.contains('text-box')) {
    start = window.getSelection()?.anchorOffset || 0
    end = window.getSelection()?.focusOffset || 0
  }

  const value = dom.value
  const newValue = value.substring(0, start) + tag + value.substring(end, value.length)
  layer.value.data = newValue
}

const fontSizeBind = useDataAttr(layer.value.style, 'fontSize', defaultStyle.fontSize, 'px')
const colorBind = useDataAttr(layer.value.style, 'color', defaultStyle.color)
const fontWeightBind = useDataAttr(layer.value.style, 'fontWeight', 'normal')
const fontStyleBind = useDataAttr(layer.value.style, 'fontStyle', 'normal')
const textAlignBind = useDataAttr(layer.value.style, 'textAlign', defaultStyle.textAlign)
const textIndentBind = useDataAttr(layer.value.style, 'textIndent', 0, 'em')

function textDecorationBind(key: 'underline' | 'line-through') {
  return computed({
    get() {
      return `${layer.value.style.textDecoration || ''}`.includes(key)
    },
    set(v) {
      const textDecoration = `${layer.value.style.textDecoration || ''}`
      const arr = textDecoration.split(' ')
      if (v) {
        if (arr.includes(key)) return
        arr.push(key)
        layer.value.style.textDecoration = arr.join(' ')
      } else {
        if (arr.includes(key)) {
          layer.value.style.textDecoration = arr.filter(i => i !== key).join(' ')
        }
      }
      console.log(layer.value.style.textDecoration)
    },
  })
}
const underlineBind = textDecorationBind('underline')
const lineThroughBind = textDecorationBind('line-through')

const lineHeightBind = useDataAttr(layer.value.style, 'lineHeight', defaultStyle.lineHeight)
const letterSpacingBind = useDataAttr(layer.value.style, 'letterSpacing', 0, 'px')
const writingModeBind = useDataAttr(layer.value.style, 'writingMode', '')
const textShadowBind = useDataAttr(layer.value.style, 'textShadow', '')
const textImageBind = useDataAttr(layer.value.style, 'backgroundImage', '')
const fontBind = useDataAttr(layer.value.style, 'fontFamily', 'initial')
const textStrokeBind = useDataAttr(layer.value.style, '-webkit-text-stroke', '')

async function handleSelect(item: TextArtMaterial) {
  const currentStyle = toValue(layer.value.style)
  const style = ['position', 'top', 'left', 'width', 'height', 'zIndex', 'fontSize', 'lineHeight', 'letterSpacing', 'fontFamily', 'display', 'flex', 'alignItems', 'justifyContent']

  for (const key in currentStyle) {
    if (!style.includes(key)) {
      delete currentStyle[key as keyof CSSProperties]
    }
  }

  layer.value.style = Object.assign(currentStyle, item.style)
}

const { textArtList, fontList, loadFont } = useFontLoader()
const isBgImage = computed(() => layer.value.style.backgroundImage)

async function handleFontChange(val: string) {
  const fontItem = fontList.find(item => item.value === val)
  if (fontItem?.url) {
    await loadFont({
      name: fontItem.name,
      url: fontItem.url,
      value: fontItem.value,
    })
  }
  fontBind.value = fontItem?.value || 'initial'
}
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <div class="flex-1">
          <el-input ref="textInputRef" v-model="layer.data" size="default" type="textarea" :rows="5" />
          <div class="tag-box">
            <el-tag v-for="item of placeholderList" :key="item" type="primary" class="cursor-pointer" @click="insertText(item)">{{ item }}</el-tag>
          </div>
        </div>
      </div>
      <div class="setting-item">
        <span></span>
        <ul class="style-box">
          <li
            data-tip="加粗"
            :class="{ cur: fontWeightBind === 'bold' }"
            @click="fontWeightBind = fontWeightBind === 'bold' ? 'normal' : 'bold'"
          >
            <icon-ph-text-bolder-bold />
          </li>
          <li
            data-tip="斜体"
            :class="{ cur: fontStyleBind === 'italic' }"
            @click="fontStyleBind = fontStyleBind === 'italic' ? 'normal' : 'italic'"
          >
            <icon-ph-text-italic-bold />
          </li>
          <li
            data-tip="下划线"
            :class="{ cur: underlineBind }"
            @click="underlineBind = !underlineBind"
          >
            <icon-ph-text-underline-bold />
          </li>
          <li
            data-tip="删除线"
            :class="{ cur: lineThroughBind }"
            @click="lineThroughBind = !lineThroughBind"
          >
            <icon-ph-text-strikethrough-bold />
          </li>
          <li
            data-tip="左对齐"
            :class="{ cur: textAlignBind === 'left' }"
            @click="textAlignBind = 'left'"
          >
            <icon-ph-text-align-left-bold />
          </li>
          <li
            data-tip="居中对齐"
            :class="{ cur: textAlignBind === 'center' }"
            @click="textAlignBind = 'center'"
          >
            <icon-ph-text-align-center-bold />
          </li>
          <li
            data-tip="右对齐"
            :class="{ cur: textAlignBind === 'right' }"
            @click="textAlignBind = 'right'"
          >
            <icon-ph-text-align-right-bold />
          </li>
          <li
            data-tip="缩进"
            :class="{ cur: textIndentBind === 2 }"
            @click="textIndentBind = textIndentBind === 2 ? 0 : 2"
          >
            <icon-ph-text-indent-bold />
          </li>
          <li
            data-tip="竖排"
            :class="{ cur: writingModeBind }"
            @click="writingModeBind = writingModeBind ? '' : 'vertical-rl'"
          >
            <icon-custom-text-direction />
          </li>
        </ul>
      </div>
      <div class="setting-item">
        <span>字体</span>
        <el-select :model-value="fontBind" class="w-120" placeholder="请选择字体" @update:model-value="handleFontChange">
          <el-option v-for="item in fontList" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </div>
      <div class="setting-item">
        <span>字号</span>
        <el-input-number
          v-model="fontSizeBind"
          v-input-number
          :min="1"
          class="w-50"
          controls-position="right"
        />
      </div>
      <div class="setting-item">
        <span>文本颜色</span>
        <hi-color v-model="colorBind" />
      </div>
      <div class="setting-item items-center justify-between">
        <h3>行高</h3>
        <el-input-number v-model="lineHeightBind" v-input-number :min="1" :step="0.1" class="w-50" controls-position="right" />
      </div>
      <div class="setting-item items-center justify-between">
        <h3>字间距</h3>
        <el-input-number v-model="letterSpacingBind" v-input-number :min="0" :step="0.1" class="w-50" controls-position="right" />
      </div>
      <div class="setting-item">
        <span>艺术字</span>
        <el-popover placement="right" :width="400" trigger="click">
          <template #reference>
            <el-button>设置字体</el-button>
          </template>
          <TextArtPicker :text-art-list="textArtList" @select="handleSelect" />
        </el-popover>
      </div>
      <TextImageEdit v-if="isBgImage" v-model="textImageBind" />
      <TextStrokeEdit v-model="textStrokeBind" />
      <div class="setting-item">
        <span>文字阴影</span>
      </div>
      <TextShadowEditor v-model="textShadowBind" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.tag-box {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  margin-top: 5px;
}
ul.style-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: end;
  gap: 2px;
  font-size: 14px;

  li {
    width: 26px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background-color: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    background-color: #f5f5f5;
    transition: 0.2s;
    position: relative;
    cursor: pointer;
    &.cur {
      background-color: var(--el-color-primary);
      color: #fff;
    }
    &:hover {
      background-color: var(--el-color-primary-dark-2);
      color: #fff;
      &::before,
      &::after {
        display: block;
      }
    }
    &::before,
    &::after {
      position: absolute;
      z-index: 100;
      left: 50%;
      transform: translateX(-50%);
    }
    &::before {
      // 三角
      content: '';
      display: none;
      top: -6px;
      border: 6px solid transparent;
      border-top-color: rgba(0, 0, 0, 0.8);
    }
    &::after {
      content: attr(data-tip);
      display: none;
      top: -34px;
      padding: 8px 10px;
      color: #fff;
      font-size: 12px;
      line-height: 12px;
      border-radius: 4px;
      white-space: nowrap;
      background-color: rgba(0, 0, 0, 0.8);
    }
  }
}
</style>
