import type { HowlOptions } from 'howler'
import { Howl, Howler } from 'howler'

// 存储音频实例的 Map
const audioMap = new Map<string, Howl>()

/**
 * 播放音频
 */
export function playAudio(url: string, options?: Omit<HowlOptions, 'src'>): Howl {
  const existingAudio = audioMap.get(url)

  if (existingAudio) {
    return existingAudio
  }
  const audio = new Howl({
    src: [url],
    ...options,
    onloaderror: (_, error) => {
      console.error('音频加载失败:', error)
    },
    onplayerror: (_, error) => {
      console.error('音频播放失败:', error)
    },
  })

  audioMap.set(url, audio)

  return audio
}

/**
 * 停止播放音频
 */
export function stopAudio(url: string): void {
  audioMap.get(url)?.stop()
}

/**
 * 暂停播放音频
 */
export function pauseAudio(url: string): void {
  audioMap.get(url)?.pause()
}

/**
 * 检查音频是否正在播放
 */
export function isPlayingAudio(url: string): boolean {
  return audioMap.get(url)?.playing() || false
}

/**
 * 释放指定音频资源
 */
export function releaseAudio(url: string): void {
  const audio = audioMap.get(url)
  if (audio) {
    audio.unload()
    audioMap.delete(url)
  }
}

/**
 * 释放所有音频资源
 */
export function releaseAllAudio(): void {
  audioMap.forEach(audio => audio.unload())
  audioMap.clear()
}

export function changeMuteAudio(state: boolean): void {
  Howler.mute(state)
}
