import type { IDesignLayer, IDesignSetup } from '../../types'
import { BisTypes } from '../..'
import { InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './lottery-ready-avatar-setting.vue'
import Comp from './lottery-ready-avatar.vue'
// 类型
export const type = 'lottery-ready-avatar'
export const defaultHeadSize = 150
export const defaultGapRow = 10
export const defaultGapColumn = 10
export const defaultDecorateAnimation = true
// 数据类型约束
export interface IDesignLotteryAreadyAvatar extends IDesignLayer {
  type: typeof type
  headSize?: number
  gapRow?: number
  gapColumn?: number
  decorateAnimation?: boolean
  avatarImg?: string
  decorateImg?: string
}

export const maxCout: Record<string, number> = reactive({})
// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.ready,
    showType: ['pcwall'],
    status: ['ready'],
    showInteractive: [InteractiveEnum.lotteryv3],
    type,
    name: '等待页头像',
    thumbnail: new URL('./lottery-ready-avatar.png', import.meta.url).href,
    Comp,
    CompSetting,
    defaultData() {
      const uuid = layerUuid()
      maxCout[uuid] = 0
      return {
        type,
        uuid,
        name: '等待页头像',
        data: [],
        decorateImg: new URL('./assets/head-light.png', import.meta.url).href,
        avatarImg: new URL('./assets/default.png', import.meta.url).href,
        style: {
          width: `400px`,
          height: `300px`,
          top: 0,
          left: 0,
        },
      }
    },
  })
}
