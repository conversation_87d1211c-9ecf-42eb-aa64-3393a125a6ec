<script setup lang="ts">
import type { EnemysItem, IDesignFireIng } from './fire-ing'
import HiAudioSel from 'design/components/audio.vue'
import { getDefaultMaterial, openSelectMaterial } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'

const layer = defineModel<IDesignFireIng>('layer', { required: true })

type IType = 'enemys' | 'bullet' | 'bulletSuper' | 'plane' | 'bombImgs'
async function updateMaterialFn(name: IType, index?: number, isReset: boolean = false) {
  const result = isReset
    ? await getDefaultMaterial(layer.value, `${name}.${index}`)
    : await openSelectMaterial('PIC')
  if (result || (isReset && index === undefined)) {
    if (name === 'enemys') {
      const arr = layer.value[name] as EnemysItem[]
      if (index !== undefined) {
        arr[index] = Object.assign(arr[index], { img: result })
      }
    } else if (name === 'bombImgs') {
      const arr = layer.value[name]
      if (index !== undefined) {
        arr[index] = result
      }
    } else {
      layer.value[name] = result
    }
  }
}
function remove(name: IType, index: number) {
  const arr = layer.value[name] as any
  arr.splice(index, 1)
}
function addEnemy(name: IType, index: number, defaultValue: EnemysItem) {
  const arr = layer.value[name] as EnemysItem[]
  arr.splice(index + 1, 0, defaultValue)
}
function add(name: IType | 'bombAudios', index: number, defaultValue: string) {
  const arr = layer.value[name] as string[]
  arr.splice(index + 1, 0, defaultValue)
}

type musicType = 'bulletAudio' | 'bulletSuperAudio'
function changeMusic(name: musicType, result: string) {
  layer.value[name] = result
}
function removeBombAudios(index: number) {
  layer.value.bombAudios.splice(index, 1)
}
function addBombAudios(index: number, defaultValue: string) {
  layer.value.bombAudios.splice(index + 1, 0, defaultValue)
}
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item flex-items-start!">
        <h3>掉落物</h3>
        <div>
          <div
            v-for="(item, index) in layer.enemys"
            :key="index"
            class="relative mb-10 h-60 w-160 flex"
          >
            <MaterialThumbnail @select="updateMaterialFn('enemys', index)" @reset="updateMaterialFn('enemys', index, true)">
              <img :src="item.img" class="bgblank w-90 object-contain">
            </MaterialThumbnail>
            <div class="item-score">
              <span>击败得分</span>
              <el-input-number v-model="item.score" v-input-number :min="0" controls-position="right" class="mt-5 w-80" />
            </div>
            <div class="ml-6 w-20 pt-10">
              <icon-ph:minus-bold v-if="layer.enemys.length > 1" @click.stop="remove('enemys', index)" />
              <icon-ph:plus-bold @click.stop="addEnemy('enemys', index, item)" />
            </div>
          </div>
        </div>
      </div>
      <div class="setting-item flex-items-start!">
        <h3>扣分</h3>
        <div>
          <el-input-number v-model="layer.failScore" v-input-number :min="0" controls-position="right" class="w-80" />
        </div>
      </div>
      <div class="setting-item">
        <h3>密度</h3>
        <el-slider v-model.number="layer.enemyDensity" class="mx-10" :min="1" :max="10" :step="1"></el-slider>
      </div>
      <div class="setting-item">
        <h3>速度</h3>
        <el-slider v-model.number="layer.enemySpeed" class="mx-10" :min="1" :max="10" :step="1"></el-slider>
      </div>
      <div class="setting-item flex-items-start!">
        <h3>爆炸</h3>
        <div>
          <div
            v-for="(item, index) in layer.bombImgs"
            :key="index"
            class="relative mb-10 h-60 w-120 flex"
          >
            <MaterialThumbnail @select="updateMaterialFn('bombImgs', index)" @reset="updateMaterialFn('bombImgs', index, true)">
              <img :src="item" class="bgblank w-90 object-contain">
            </MaterialThumbnail>
            <div class="ml-6 w-20 pt-10">
              <icon-ph:minus-bold v-if="layer.bombImgs.length > 1" @click.stop="remove('bombImgs', index)" />
              <icon-ph:plus-bold @click.stop="add('bombImgs', index, item)" />
            </div>
          </div>
        </div>
      </div>
      <div class="setting-item flex-items-start!">
        <h3>爆炸音效</h3>
        <div>
          <div
            v-for="(item, index) in layer.bombAudios"
            :key="index"
            class="relative mb-10 h-60 w-150 flex flex-a-c"
          >
            <HiAudioSel :url="item" />
            <div class="ml-6 w-20 pt-10">
              <icon-ph:minus-bold v-if="layer.bombAudios.length > 1" @click.stop="removeBombAudios(index)" />
              <icon-ph:plus-bold @click.stop="addBombAudios(index, item)" />
            </div>
          </div>
        </div>
      </div>
      <div class="setting-item flex-items-start!">
        <h3>参赛物</h3>
        <MaterialThumbnail @select="updateMaterialFn('plane')" @reset="updateMaterialFn('plane', undefined, true)">
          <img :src="layer.plane" class="bgblank w-90 object-contain">
        </MaterialThumbnail>
      </div>
      <div class="setting-item flex-items-start!">
        <h3>普通子弹</h3>
        <div class="flex flex-d-c flex-a-fe">
          <div><img :src="layer.bullet" class="bgblank h-40 w-60 object-contain" @click="updateMaterialFn('bullet')"></div>
          <div class="flex flex-a-c">
            <h3>音效</h3>
            <HiAudioSel :url="layer.bulletAudio" @selection="result => changeMusic('bulletAudio', result)" />
          </div>
        </div>
      </div>
      <div class="setting-item flex-items-start!">
        <h3>超级子弹</h3>
        <div class="flex flex-d-c flex-a-fe">
          <div class="flex">
            <div class="d bgblank" @click="layer.bulletSuper = ''"></div>
            <div class="bgblank h-60 w-40">
              <MaterialThumbnail @select="updateMaterialFn('bulletSuper')" @reset="updateMaterialFn('bulletSuper', undefined, true)">
                <img v-if="layer.bulletSuper" :src="layer.bulletSuper" class="bgblank w-60 object-contain">
              </MaterialThumbnail>
            </div>
          </div>
          <div class="flex flex-a-c">
            <h3>音效</h3>
            <HiAudioSel :url="layer.bulletSuperAudio" @selection="result => changeMusic('bulletSuperAudio', result)" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.item-score {
  span {
    margin-left: 10px;
  }
}
.d {
  width: 25px;
  height: 25px;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
  &:first-child {
    position: relative;
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 90%;
      height: 90%;
      margin: 5%;
      mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1em' height='1em' viewBox='0 0 256 256'%3E%3Cpath fill='%23000' d='m203.63 62.65l13.25-14.58a12 12 0 0 0-17.76-16.14l-13.24 14.56A100 100 0 0 0 52.37 193.35l-13.25 14.58a12 12 0 1 0 17.76 16.14l13.24-14.56A100 100 0 0 0 203.63 62.65M52 128a75.94 75.94 0 0 1 117.58-63.57l-100.91 111A75.6 75.6 0 0 1 52 128m76 76a75.5 75.5 0 0 1-41.58-12.43l100.91-111A75.94 75.94 0 0 1 128 204'/%3E%3C/svg%3E");
      mask-size: 100%;
      background-color: #999999;
      cursor: pointer;
    }
  }
}
</style>
