<script setup lang="ts">
import type { Arrayable } from 'element-plus/es/utils/index.mjs'
import type { IDesignTextRich } from './text-rich'
import { useDesignData, useDesignState } from '../..'
import { useQuillManager } from './text-rich-utils'

const layer = defineModel<IDesignTextRich>('layer', { required: true })
const designData = useDesignData()

///////////////////////////

const quillManager = useQuillManager(layer)

const { sectionStyle, currentImageWidth } = quillManager
function setStyle(name: string, value: boolean | string | number | Arrayable<number> | undefined) {
  quillManager.setStyle(name, value)
}

function insertText(val: string) {
  quillManager.insertText(val)
}
///////////////////////////

const designState = useDesignState()
const placeholderList = computed(() => {
  return Object.keys(designState.layerData).filter(i => i.startsWith('#') && i.endsWith('#'))
})

const editorToolbarStyle = computed(() => {
  // 如果当前节点在屏幕右半边，工具栏在左边显示
  const left = Number.parseInt(`${layer.value.style.left || '0'}`)
  const halfWidth = designData.option.drafts[0] / 2

  if (left > halfWidth) {
    return { right: 0 }
  }
  return { left: 0 }
})
</script>

<template>
  <div class="text-rich-attachment">
    <div v-if="true" class="moveable-ignore editor-toolbar" :style="editorToolbarStyle">
      <el-tooltip effect="dark" content="字体大小" placement="top">
        <el-input-number
          v-model="sectionStyle.fontSize"
          v-input-number
          :min="1"
          class="mr-5 w-90"
          controls-position="right"
          @change="v => setStyle('fontSize', v)"
        />
      </el-tooltip>
      <hi-color v-model="sectionStyle.color" @change="v => setStyle('color', v)" />
      <ul class="style-box">
        <li data-tip="加粗" :class="{ cur: sectionStyle.isBold }" @click="setStyle('bold', !sectionStyle.isBold)">
          <icon-ph-text-bolder-bold />
        </li>
        <li data-tip="斜体" :class="{ cur: sectionStyle.isItalic }" @click="setStyle('italic', !sectionStyle.isItalic)">
          <icon-ph-text-italic-bold />
        </li>
        <li data-tip="下划线" :class="{ cur: sectionStyle.isUnderline }" @click="setStyle('underline', !sectionStyle.isUnderline)">
          <icon-ph-text-underline-bold />
        </li>
        <li data-tip="删除线" :class="{ cur: sectionStyle.isStrike }" @click="setStyle('strike', !sectionStyle.isStrike)">
          <icon-ph-text-strikethrough-bold />
        </li>
        <li data-tip="左对齐" :class="{ cur: sectionStyle.align === 'left' }" @click="setStyle('align', '')">
          <icon-ph-text-align-left-bold />
        </li>
        <li data-tip="居中对齐" :class="{ cur: sectionStyle.align === 'center' }" @click="setStyle('align', 'center')">
          <icon-ph-text-align-center-bold />
        </li>
        <li data-tip="右对齐" :class="{ cur: sectionStyle.align === 'right' }" @click="setStyle('align', 'right')">
          <icon-ph-text-align-right-bold />
        </li>
        <li data-tip="缩进" :class="{ cur: sectionStyle.textIndent }" @click="setStyle('indent', !sectionStyle.textIndent)">
          <icon-ph-text-indent-bold />
        </li>
        <li data-tip="竖排" :class="{ cur: sectionStyle.writingMode }" @click="sectionStyle.writingMode = sectionStyle.writingMode ? '' : 'vertical-rl'">
          <icon-custom-text-direction />
        </li>
        <li data-tip="顶对齐" :class="{ cur: sectionStyle.alignItems === 'start' }" @click="sectionStyle.alignItems = 'start'">
          <icon-ph-arrow-line-up-bold />
        </li>
        <li data-tip="居中对齐" :class="{ cur: sectionStyle.alignItems === 'center' }" @click="sectionStyle.alignItems = 'center'">
          <icon-ph-arrows-in-line-vertical-bold />
        </li>
        <li data-tip="底端对齐" :class="{ cur: sectionStyle.alignItems === 'end' }" @click="sectionStyle.alignItems = 'end'">
          <icon-ph-arrow-line-down-bold />
        </li>
      </ul>
      <el-tooltip v-if="currentImageWidth" effect="dark" content="当前图片尺寸" placement="top">
        <el-input-number
          v-model="currentImageWidth"
          v-input-number
          :min="1"
          class="mr-5 w-90"
          controls-position="right"
        />
      </el-tooltip>
    </div>
    <div v-if="placeholderList.length" class="moveable-ignore placeholder">
      <el-tag v-for="item of placeholderList" :key="item" type="primary" @click="insertText(item)">{{ item }}</el-tag>
    </div>
  </div>
</template>

<style scoped lang="scss">
.editor-toolbar {
  position: absolute;
  top: 0;
  transform: translateY(-120%);
  display: flex;
  align-items: center;
  box-shadow: 0 0 16px rgba(0, 0, 0, 0.16);
  border-radius: 4px;
  background-color: #fff;
  padding: 8px 10px;
  z-index: 99;
  pointer-events: all;
  :deep() {
    .el-select {
      margin-right: 1px;
    }
    .el-select__wrapper {
      height: 28px;
    }
  }
}

ul.style-box {
  display: flex;
  font-size: 16px;

  li {
    padding: 4px 6px 0;
    margin: 1px;
    text-align: center;
    background-color: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    background-color: #f5f5f5;
    transition: 0.2s;
    position: relative;
    &.cur {
      background-color: var(--el-color-primary);
      color: #fff;
    }
    &:hover {
      background-color: var(--el-color-primary-dark-2);
      color: #fff;
      &::before,
      &::after {
        display: block;
      }
    }
    &::before,
    &::after {
      position: absolute;
      z-index: 100;
      left: 50%;
      transform: translateX(-50%);
    }
    &::before {
      // 三角
      content: '';
      display: none;
      top: -6px;
      border: 6px solid transparent;
      border-top-color: rgba(0, 0, 0, 0.8);
    }
    &::after {
      content: attr(data-tip);
      display: none;
      top: -34px;
      padding: 8px 10px;
      color: #fff;
      font-size: 12px;
      line-height: 12px;
      border-radius: 4px;
      white-space: nowrap;
      background-color: rgba(0, 0, 0, 0.8);
    }
  }
}

.placeholder {
  // 底部占位符
  position: absolute;
  bottom: 0;
  left: 0;
  transform: translateY(46px);
  box-shadow: 0 0 16px rgba(0, 0, 0, 0.16);
  border-radius: 4px;
  background-color: #fff;
  padding: 8px 10px;
  pointer-events: all;

  display: flex;
  flex-wrap: nowrap;
  gap: 2px;

  :deep() {
    .el-tag {
      cursor: default;
    }
  }
}
</style>
