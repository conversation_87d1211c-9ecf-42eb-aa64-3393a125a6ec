<script setup lang="ts">
import type { CSSProperties } from 'vue'
import { cloneDeep } from 'lodash-es'
import { envUtils } from '~/src/utils/env'
import { defineCustomEmits, injectComputed, useDesignState } from '../..'
import { defaultAvatarDecoration, defaultCount, defaultNameColor, defaultScoreColor, type IDesignShakeIng } from './shake-ing'

const layer = defineModel<IDesignShakeIng>('layer', { required: true })
const customEmits = defineCustomEmits(layer)
const scale = injectComputed('scale', 1)

const designState = useDesignState()
const shakeIngRef = ref<HTMLElement>()

interface IDisplayData {
  id: number
  name: string
  avatar: string
  score: number
  progress?: string
  rank: number // 名次
}
/// 数据开始 ///////////////////
const timeProgress = computed(() => {
  if (envUtils.isPlayWright) {
    return 0.5
  }
  return designState.getLayerData('ingTimeProgress')
})
const outerData = computed<IDisplayData[]>(() => {
  return designState.getLayerData('ingRankings') || []
})

const displayData = ref<(IDisplayData | null)[]>([])

watch(
  () => layer.value.count || defaultCount,
  (count) => {
    customEmits('rankingCount', count)

    const resultCount = displayData.value.length
    if (resultCount < count) {
      displayData.value.push(...Array.from({ length: count - resultCount }, () => null))
    } else {
      displayData.value.splice(count)
    }
  },
  { immediate: true },
)

function updateProgress() {
  // 时时计算显示信息
  let minScore = 0
  let maxScore = 0

  displayData.value.forEach((item) => {
    if (item) {
      minScore = Math.min(minScore, item.score)
      maxScore = Math.max(maxScore, item.score)
    }
  })

  const minP = [0, 0.1]
  const maxP = [0.1, 1]

  displayData.value.forEach((item) => {
    if (item) {
      const _minP = minP[0] + (minP[1] - minP[0]) * timeProgress.value
      const _maxP = maxP[0] + (maxP[1] - maxP[0]) * timeProgress.value

      const x = (_maxP - _minP) / (maxScore - minScore)
      const p = _minP + (item.score - minScore) * x

      item.progress = `${(1 - p) * 100}%`
    }
  })
}
function updateList(newList: IDisplayData[]) {
  const copyList = cloneDeep(newList).sort((a, b) => b.score - a.score)
  // 排序设置名次
  copyList.forEach((item, index) => {
    item.rank = index + 1
  })

  const resultObj: Record<number, (IDisplayData | null)> = {}
  displayData.value.forEach((item) => {
    if (item) {
      resultObj[item.id] = item
    }
  })

  const newObj: Record<number, IDisplayData> = {}
  copyList.forEach((item) => {
    if (newObj[item.id]) {
      console.error('id重复', item.id)
    }
    newObj[item.id] = item
  })

  const waitRemoveList: { score?: number, index: number }[] = []
  // 1.将已经存在的坑位数据进行更新
  // 2.挑出旧列表中需要移除的数据, 并排序
  displayData.value.forEach((item, index) => {
    if (item) {
      if (newObj[item.id]) {
        Object.assign(item, newObj[item.id])
      } else {
        waitRemoveList.push({ score: item.score, index })
      }
    } else {
      waitRemoveList.push({ index })
    }
  })
  // 排序
  waitRemoveList.sort((a, b) => {
    if (!a.score || !b.score) {
      if (envUtils.isPlayWright) {
        return -1
      }
      return Math.random() - 0.5
    }
    return b.score - a.score
  })

  // 3.挑出新列表不在就列表中的数据，并排序
  const waitAddList: IDisplayData[] = []
  copyList.forEach((item) => {
    if (!resultObj[item.id]) {
      waitAddList.push(item)
    }
  })
  waitAddList.sort((a, b) => b.score - a.score)
  // 4.将新列表中的数据按照排序替换到旧列表中
  waitAddList.forEach((item) => {
    if (waitRemoveList.length) {
      const { index } = waitRemoveList.pop()!
      displayData.value[index] = item
    } else {
      console.warn('新增的多')
    }
  })
  if (waitRemoveList.length) {
    console.warn('删除的多')
    waitRemoveList.forEach(({ index }) => {
      displayData.value[index] = null
    })
  }
}

watch(
  () => outerData.value,
  (list) => {
    updateList(list)
    updateProgress()
  },
  { immediate: true },
)
/// 数据结束 ///////////////////
const shakeIngStyle = computed(() => {
  return {
    '--scale': scale.value,
    '--avatar-size': `${scale.value * 40}px`,
    '--item-height': `${100 / (layer.value.count || defaultCount)}%`,
    '--score-color': layer.value.scoreColor ?? defaultScoreColor,
    '--name-color': layer.value.nameColor ?? defaultNameColor,
    'fontSize': `${scale.value * 18}px`,
    '--avatar-decoration': `url(${layer.value.avatarDecoration ?? defaultAvatarDecoration})`,
  }
})

function wrapStyle(item: IDisplayData | null) {
  const style: CSSProperties = {
    right: item?.progress ?? '100%',
  }
  return style
}
</script>

<template>
  <div class="design-shake-ing" :style="shakeIngStyle" :class="{ reverse: layer.reverse }">
    <ul ref="shakeIngRef">
      <li v-for="(item, index) in displayData" :key="index">
        <div class="wrap" :style="wrapStyle(item)">
          <template v-if="item?.id">
            <div v-if="layer.flag[item.rank - 1]" class="flag-box">
              <img :src="layer.flag[item.rank - 1]" alt="">
            </div>
            <div class="name-box">
              <div class="count">{{ item?.score }}</div>
              <div class="name">{{ item?.name }}</div>
            </div>
            <div class="avatar-box">
              <div class="decoration"></div>
              <img :src="item?.avatar" class="avatar" alt="">
            </div>
            <img :key="index" :src="layer.contestant[index % layer.contestant.length]" class="contestant" alt="">
          </template>
        </div>
      </li>
    </ul>
  </div>
</template>

<style scoped lang="scss">
.design-shake-ing {
  width: 100%;
  height: 100%;
  position: relative;

  --avatar-size: 40px;
  --score-color: #f4ff48;
  --name-color: #fff;

  ul {
    height: 100%;
  }
  li {
    width: 100%;
    height: var(--item-height);
    position: relative;
  }

  .wrap {
    width: 230px;
    height: 172px;
    position: absolute;
    display: inline-block;
    bottom: 0;
    right: 0;
    display: flex;
    justify-content: center;
    transform: translateX(25%);
    transition: right 1s linear;
  }

  .flag-box {
    position: absolute;
    left: -50%;
    top: 12%;
    width: 45px;
    img {
      width: 100%;
      object-fit: contain;
    }
  }

  .name-box {
    position: absolute;
    left: -36%;
    top: 15%;
    width: calc(var(--avatar-size) * 2);

    font-weight: 400;
    text-align: right;

    .count {
      color: var(--score-color);
      font-size: 1.2em;
      text-shadow: 1px 1px 2px #000;
    }

    .name {
      color: var(--name-color);
      white-space: nowrap;
      overflow: hidden;
      // text-overflow: ellipsis;
      text-shadow: 1px 1px 2px #000;
    }
  }

  .avatar-box {
    position: absolute;
    left: 0%;
    top: 12%;
    width: calc(var(--avatar-size) * 1.6);
    height: calc(var(--avatar-size) * 1.6);
    display: flex;
    align-items: center;
    justify-content: center;

    .decoration {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      mask-image: var(--avatar-decoration);
      mask-size: 100% auto;
      mask-repeat: no-repeat;
      background-color: var(--score-color);
    }
    .avatar {
      width: 72%;
      aspect-ratio: 1;
      border-radius: 50%;
      position: relative;
      z-index: 0;
    }
  }

  .contestant {
    width: 100%;
  }
}
.design-shake-ing.reverse {
  transform: rotateY(180deg);
  .flag-box,
  .name-box,
  .avatar-box .avatar {
    transform: rotateY(180deg);
  }
  .name-box {
    text-align: left;
  }
}
</style>
