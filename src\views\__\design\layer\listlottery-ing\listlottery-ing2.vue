<script setup lang="ts">
import type { IDesignListlotteryIng2 } from './listlottery-ing2'
import Gsap from 'gsap'
import { sample, throttle } from 'lodash-es'
import * as THREE from 'three'
import { CSS3DObject, CSS3DRenderer, CSS3DSprite } from 'three/examples/jsm/renderers/CSS3DRenderer.js'
import { useDesignState } from '../..'
import { defalutTextStyle, defaultAnimateSpeed, defaultHeadCount, defaultHeadSize } from './listlottery-ing2'

const layer = defineModel<IDesignListlotteryIng2>('layer', { required: true })
const designState = useDesignState()
const status = computed(() => designState.status)

const timeline = Gsap.timeline({ repeat: -1, paused: false })
const headCount = computed(() => layer.value.headCount ?? defaultHeadCount)
const headSize = computed(() => layer.value.headSize ?? defaultHeadSize)
const animiteSpeed = computed(() => layer.value.animiteSpeed ?? defaultAnimateSpeed)

// 参与人奖池
const regeditList = computed(() => {
  return designState.getLayerData('regeditList') || []
})
const importformShowOption = computed<Record<number, boolean>>(() => {
  return designState.getLayerData('importformShowOption') || []
})

const threeRoot = ref<HTMLElement>()
const threeRootSize = useElementSize(threeRoot)

let requestAnimationFrameId: number | null = null

let camera: THREE.PerspectiveCamera
let scene: THREE.Scene
let renderer: CSS3DRenderer
let group: THREE.Group | null = null

function getItem() {
  const tem = sample(regeditList.value)
  const itemData = Object.assign({}, tem)
  return itemData
}
function getItemBgColor() {
  const arr = layer.value.itemBgColors as string[]
  const index = Math.floor(Math.random() * arr.length)
  return arr[index]
}
function createElement(itemData?: any) {
  const element = document.createElement('div')
  element.setAttribute('class', 'css3d')
  element.style = `
    width: ${headSize.value}px;
    height:${headSize.value}px;
    font-size: 40px;
    background: ${getItemBgColor()};
  `
  const arr: string[] = []
  layer.value.contentStyle.forEach((item, index) => {
    if (importformShowOption.value[index]) {
      if (itemData[`nameD${index}`]) {
        arr.push(`
      <div style="font-size:${item.fontSize}px;color:${item.fontColor};font-weight:${item.fonBold ? 'bold' : 'normal'}">${itemData[`nameD${index}`]}</div>
      `)
      }
    }
  })
  const str = arr.join('')
  element.innerHTML = str
  return element
}

function initShape() {
  if (!scene) {
    return
  }
  if (group) {
    const children = group.children
    scene.remove(group)
    group = null
    for (const item of children) {
      if (item instanceof CSS3DObject || item instanceof CSS3DSprite) {
        item.element.remove()
      }
    }
  }

  group = new THREE.Group()
  scene.add(group)

  const count = headCount.value
  const vector = new THREE.Vector3()

  for (let i = 0; i < count; i++) {
    const itemData = getItem()

    if (!itemData) return
    const element = createElement(itemData)
    const object = new CSS3DObject(element)
    const x = 1500 * Math.sin(Math.PI * 2 * Math.random())
    const y = 1500 * Math.cos(Math.PI * 2 * Math.random())
    object.position.set(x, y, -15000)
    object.scale.set(0.1, 0.1, 0.1)
    object.lookAt(vector)
    group.add(object)
  }
  timeline.clear()
  play()
  timeline.timeScale(animiteSpeed.value)
}
function play() {
  timeline.clear()
  timeline.addLabel('start')
  if (!group) {
    return
  }
  group.children.forEach((object) => {
    const duration = Math.random() * 10 + 10
    timeline.to(object.position, { z: 1500, repeat: Infinity, duration, ease: 'none' }, 'start')
    timeline.to(object.scale, { x: 2, y: 2, z: 2, repeat: Infinity, duration, ease: 'none', onRepeat: () => {
      // if (Math.random() > 0.8 && object instanceof THREE.Mesh) {
      //   const itemData = getItem()
      //   if (!itemData) return
      //   object.material.map = createTexture(itemData.avatar)
      // }
    } }, 'start')
  })
  timeline.progress(0.6)
}
function init() {
  if (!threeRoot.value) {
    return
  }
  scene = new THREE.Scene()
  camera = new THREE.PerspectiveCamera(45, window.innerWidth / window.innerHeight, 1, 1000)
  camera.position.z = 1200
  renderer = new CSS3DRenderer()
  renderer.setSize(0, 0)
  threeRoot.value.appendChild(renderer.domElement)
}

function resize(width: number, height: number) {
  if (renderer) {
    renderer.setSize(width, height)
  }
  if (camera) {
    camera.aspect = width / height
    camera.updateProjectionMatrix()
  }
}

function render() {
  requestAnimationFrameId = requestAnimationFrame(render)
  renderer.render(scene, camera)
}

watch(
  () => [threeRootSize.width.value, threeRootSize.height.value],
  ([w, h]) => {
    resize(w, h)
  },
)

watch(
  () => [animiteSpeed.value],
  () => {
    timeline.timeScale(animiteSpeed.value)
  },
)

const initReady = ref(false)
watch(
  () => [regeditList.value, status.value, headCount.value, headSize.value, layer.value.itemBgColors, layer.value.contentStyle],
  () => {
    if (!initReady.value || !regeditList.value.length) {
      return
    }
    throttle(() => {
      initShape()
    }, 200, { leading: false })()
  },
  { deep: true },
)

function initContentStyle() {
  for (let i = 0; i < 3; i++) {
    if (!layer.value.contentStyle[i]) {
      layer.value.contentStyle[i] = Object.assign({}, defalutTextStyle)
    } else {
      layer.value.contentStyle[i] = Object.assign({}, defalutTextStyle, layer.value.contentStyle[i])
    }
  }
}

onMounted(async () => {
  initContentStyle()
  init()
  render()
  initReady.value = true
})
onUnmounted(() => {
  // 清理资源
  if (requestAnimationFrameId) {
    cancelAnimationFrame(requestAnimationFrameId)
    requestAnimationFrameId = null
  }
})
</script>

<template>
  <div ref="threeRoot" class="three-root"></div>
</template>

<style scoped lang="scss">
.three-root {
  width: 100%;
  height: 100%;
  :deep() {
    .css3d {
      border-radius: 50%;
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      color: #fff;
      text-align: center;
      transform: translateZ(0);
      will-change: transform;
      backface-visibility: hidden;
      > div {
        width: 80%;
        max-height: 100%;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        line-height: 1.3;
      }
    }
  }
}
</style>
