<script setup lang="ts">
import { useDesignState } from '..'
import HiRightLayer from './right-layer.vue'
import HiRightMusic from './right-music/index.vue'
import HiRightPage from './right-page.vue'

const designState = useDesignState()

const compObj: Record<string, Component> = {
  page: HiRightPage,
  music: HiRightMusic,
  layer: HiRightLayer,
}
const interactive = inject<string>('interactive', '')
const isMicrosite = computed(() => interactive === 'microsite')

const tabs = computed(() => {
  return [
    {
      name: 'page',
      label: '页面',
    },
    {
      name: 'layer',
      label: '图层',
    },
    {
      name: 'music',
      label: '音效',
    },
  ].filter((item) => {
    if (isMicrosite.value) {
      return item.name !== 'music'
    }
    return true
  })
})

const activeName = ref(tabs.value[0].name)

watch(
  () => designState.status,
  () => {
    if (tabs.value.some(item => item.name === 'layer')) {
      activeName.value = 'layer'
    }
  },
)
</script>

<template>
  <div class="design-right">
    <el-tabs v-model="activeName" type="border-card" class="moveable-ignore h-full">
      <el-tab-pane v-for="item of tabs" :key="item.name" :label="item.label" :name="item.name">
        <component :is="compObj[item.name]" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<style scoped lang="scss">
.design-right {
  width: var(--right-width);
  background-color: #fff;
  z-index: 10;
}
</style>
