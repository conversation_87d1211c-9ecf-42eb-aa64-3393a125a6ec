import { merge } from 'lodash-es'
import { BisTypes } from '../../'
import { type IDesignLayer, type IDesignSetup, InteractiveEnum } from '../../types'
import { layerUuid } from '../../utils'
import CompSetting from './mobile-lottery-result-setting.vue'
import Comp from './mobile-lottery-result.vue'

// 类型
export const type = 'mobile-lottery-result'

export const DEFAULT_DATA: IDesignMobileLotteryResult['data'] = {
  backgroundColor: '#FF0C0C',
}

// 数据类型约束
export interface IDesignMobileLotteryResult extends IDesignLayer {
  type: typeof type
  data: {
    backgroundColor: string
  }
}

// 注册组件
export function setup(app: IDesignSetup) {
  app.registry({
    bisType: BisTypes.result,
    type,
    name: '名单类抽奖移动端查询中奖',
    Comp,
    CompSetting,
    showInteractive: [InteractiveEnum.listlotteryv3, InteractiveEnum.piclotteryv3, InteractiveEnum.seglottery],
    showType: ['mobile'],
    status: ['finish'],
    thumbnail: new URL('./mobile-lottery-result.png', import.meta.url).href,
    defaultData(options): IDesignMobileLotteryResult {
      return merge({
        uuid: layerUuid(),
        name: '名单类查询中奖',
        type,
        style: {
          width: '340px',
          height: '700px',
          top: '70px',
          left: '17.5px',
          background: 'white',
          borderRadius: '6px',
        },
        data: {},
        isPercent: 1,
      }, options as IDesignMobileLotteryResult)
    },
  })
}
