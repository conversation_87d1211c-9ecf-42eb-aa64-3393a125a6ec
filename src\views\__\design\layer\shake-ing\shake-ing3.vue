<script setup lang="ts">
import type { CSSProperties } from 'vue'
import { cloneDeep } from 'lodash-es'
import { useImageInfo } from '~/src/hooks/useImageInfo'
import { envUtils } from '~/src/utils/env'
import { defineCustomEmits, useDesignState } from '../..'
import { defaultBorderColor, defaultColor, defaultCount, type IDesignShakeIng3 } from './shake-ing3'

const layer = defineModel<IDesignShakeIng3>('layer', { required: true })
const customEmits = defineCustomEmits(layer)

const designState = useDesignState()
const ingRef = ref<HTMLElement>()
const shakeIngSize = useElementSize(ingRef)

interface IDisplayData {
  id: number
  name: string
  avatar: string
  score: number
  progress?: string
  rank: number // 名次
}
const count = computed(() => layer.value.count || defaultCount)
/// 数据开始 ///////////////////
const timeProgress = computed(() => {
  if (envUtils.isPlayWright) {
    return 0.5
  }
  return designState.getLayerData('ingTimeProgress')
})
const outerData = computed<IDisplayData[]>(() => {
  return designState.getLayerData('ingRankings') || []
})

const displayData = ref<(IDisplayData | null)[]>([])

watch(
  () => count.value,
  (c) => {
    customEmits('rankingCount', c)

    const resultCount = displayData.value.length
    if (resultCount < c) {
      displayData.value.push(...Array.from({ length: c - resultCount }, () => null))
    } else {
      displayData.value.splice(c)
    }
  },
  { immediate: true },
)

function updateProgress() {
  // 时时计算显示信息
  let minScore = 0
  let maxScore = 0

  displayData.value.forEach((item) => {
    if (item) {
      minScore = Math.min(minScore, item.score)
      maxScore = Math.max(maxScore, item.score)
    }
  })

  const minP = [0, 0.1]
  const maxP = [0.1, 1]

  displayData.value.forEach((item) => {
    if (item) {
      const _minP = minP[0] + (minP[1] - minP[0]) * timeProgress.value
      const _maxP = maxP[0] + (maxP[1] - maxP[0]) * timeProgress.value

      const x = (_maxP - _minP) / (maxScore - minScore)
      const p = _minP + (item.score - minScore) * x

      let rp = 1 - p
      if (rp > 0.8) {
        rp = 0.8
      }
      item.progress = `${rp * 100}%`
    }
  })
}
function updateList(newList: IDisplayData[]) {
  const copyList = cloneDeep(newList).sort((a, b) => b.score - a.score)
  // 排序设置名次
  copyList.forEach((item, index) => {
    item.rank = index + 1
  })

  const resultObj: Record<number, (IDisplayData | null)> = {}
  displayData.value.forEach((item) => {
    if (item) {
      resultObj[item.id] = item
    }
  })

  const newObj: Record<number, IDisplayData> = {}
  copyList.forEach((item) => {
    if (newObj[item.id]) {
      console.error('id重复', item.id)
    }
    newObj[item.id] = item
  })

  const waitRemoveList: { score?: number, index: number }[] = []
  // 1.将已经存在的坑位数据进行更新
  // 2.挑出旧列表中需要移除的数据, 并排序
  displayData.value.forEach((item, index) => {
    if (item) {
      if (newObj[item.id]) {
        Object.assign(item, newObj[item.id])
      } else {
        waitRemoveList.push({ score: item.score, index })
      }
    } else {
      waitRemoveList.push({ index })
    }
  })
  // 排序
  waitRemoveList.sort((a, b) => {
    if (!a.score || !b.score) {
      if (envUtils.isPlayWright) {
        return -1
      }
      return Math.random() - 0.5
    }
    return b.score - a.score
  })

  // 3.挑出新列表不在就列表中的数据，并排序
  const waitAddList: IDisplayData[] = []
  copyList.forEach((item) => {
    if (!resultObj[item.id]) {
      waitAddList.push(item)
    }
  })
  waitAddList.sort((a, b) => b.score - a.score)
  // 4.将新列表中的数据按照排序替换到旧列表中
  waitAddList.forEach((item) => {
    if (waitRemoveList.length) {
      const { index } = waitRemoveList.pop()!
      displayData.value[index] = item
    } else {
      console.warn('新增的多')
    }
  })
  if (waitRemoveList.length) {
    waitRemoveList.forEach(({ index }) => {
      displayData.value[index] = null
    })
  }
}

watch(
  () => outerData.value,
  (list) => {
    updateList(list)
    updateProgress()
  },
  { immediate: true },
)
/// 数据结束 ///////////////////
const baseWidth = computed(() => shakeIngSize.width.value / count.value)
const { width: trackWidth, height: trackHeight } = useImageInfo(layer.value.trackBottom)
// 显示宽度
const resultTrackHeight = computed(() => {
  return baseWidth.value / trackWidth.value * trackHeight.value
})

const shakeIngStyle = computed(() => {
  const style: CSSProperties = {
    '--color': layer.value.style.color || defaultColor,
    '--border-color': layer.value.style.borderColor || defaultBorderColor,
    '--base-width': `${baseWidth.value}px`,
    '--track-bottom-offset': `${resultTrackHeight.value / 2}px`,
  }
  return style
})

function itemInfoStyle(item: IDisplayData | null) {
  return {
    top: item?.progress ?? '100%',
  }
}
function getTrack(idx: number) {
  return layer.value.track[idx % layer.value.track.length]
}
</script>

<template>
  <ul ref="ingRef" class="ul-list" :style="shakeIngStyle">
    <li v-for="(item, index) in displayData" :key="index" class="li-item">
      <div class="wrap">
        <div v-if="item" class="item-info" :style="itemInfoStyle(item)">
          <span class="count">{{ item.score }}</span>
          <div
            class="avatar"
            :class="{ flag: item?.rank <= (layer.rankColor?.length || 0) }"
            :data-rank="`NO.${item.rank}`"
            :style="{ '--rank-color': layer.rankColor?.[item.rank - 1] }"
          >
            <img :src="item.avatar" />
          </div>
          <span class="name">{{ item.name }}</span>
          <img class="track" :src="getTrack(index)" />
        </div>
      </div>
      <img class="track-bottom" :src="layer.trackBottom" />
    </li>
  </ul>
</template>

<style scoped lang="scss">
.ul-list {
  --color: #fff000;
  --border-color: #ffc000;

  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  height: 100%;

  .li-item {
    flex: 1;
    height: 100%;
    position: relative;

    .wrap {
      width: 100%;
      height: calc(100% - var(--track-bottom-offset));
      text-align: center;
      position: absolute;
      z-index: 2;
      overflow: hidden;
      border-radius: 0 0 50px 50px;
    }
    .track-bottom {
      width: 100%;
      bottom: 0;
      position: absolute;
      z-index: 1;
    }
  }
}
.item-info {
  position: absolute;
  transition: top 1s linear;
  font-size: calc(var(--base-width) * 0.18);
  font-weight: bold;
  text-shadow: 1px 1px 1px red;

  .count {
    color: var(--color);
    margin-bottom: calc(var(--base-width) * 0.03);
  }
  .avatar {
    position: relative;
    margin-bottom: calc(var(--base-width) * 0.1);

    &.flag::after {
      content: attr(data-rank);
      display: block;
      position: absolute;
      bottom: -10%;
      left: calc(var(--base-width) * 0.3);
      width: calc(var(--base-width) * 0.4);
      height: calc(var(--base-width) * 0.13);
      line-height: calc(var(--base-width) * 0.14);
      border: calc(var(--base-width) * 0.01) solid var(--border-color);
      border-radius: calc(var(--base-width) * 0.13);
      font-size: calc(var(--base-width) * 0.09);
      background-color: var(--rank-color);
      color: #fff;
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    img {
      width: calc(var(--base-width) * 0.5);
      height: calc(var(--base-width) * 0.5);
      border: calc(var(--base-width) * 0.02) solid var(--border-color);
      border-radius: 50%;
    }
  }
  .name {
    color: #fff;
  }
  .track {
    margin-top: 10px;
    width: 100%;
  }
}
</style>
