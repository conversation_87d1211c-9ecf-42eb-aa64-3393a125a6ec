<script setup lang="ts">
import type { IDesignGroup } from '../../../layer/group/group'
import type { IDesignLayer, IDesignLayerEventShowHide } from '../../../types'
import { useDataAttr, useDesignData, useDesignState } from '../../..'

defineOptions({ label: '显示/隐藏' })

const layer = defineModel<IDesignLayer>('layer', { required: true })
const event = defineModel<IDesignLayerEventShowHide>('event', { required: true })

const designData = useDesignData()
const designState = useDesignState()

interface IResult { uuid: string, name: string, depth: number }
interface ILayer { uuid: string, name: string, layers?: ILayer[] }
function treeToArray(tree: ILayer[], depth = 0): IResult[] {
  return tree.reduce<IResult[]>((res, item) => {
    const uuid = item.uuid
    const name = item.name
    const layers = item.layers || []
    return res.concat(
      { uuid, name, depth },
      treeToArray(layers, depth + 1),
    )
  }, [])
}

// 可以控制的图层列表, 非disable的图层列表
const controlLayerList = computed(() => {
  // 展平图层为一级结构
  return treeToArray(designData.layers).filter((i) => {
    // 排除模板的子图层
    const parent = designData.getLayerByUuid(i.uuid)?.$parent
    if (!parent || parent.type !== 'group') {
      return true
    }
    return !(parent as IDesignGroup)?.templateId
  }).sort()
})

const selectUuids = ref<string[]>([])

watch(
  () => event.value,
  ({ value }) => {
    if (value) {
      selectUuids.value = value.map(i => i.uuid)
    }
  },
  { immediate: true, deep: true },
)
watch(
  () => selectUuids.value,
  () => {
    if (event.value.value === null) {
      event.value.value = []
    }
    const { value } = event.value
    // 少了补充
    selectUuids.value.forEach((item) => {
      if (!value.find(i => i.uuid === item)) {
        value.push({ uuid: item, action: 'auto' })
      }
    })
    // 多了删除
    value.forEach((item, index) => {
      if (!selectUuids.value.includes(item.uuid)) {
        value.splice(index, 1)
      }
    })
  },
  { deep: true },
)

// 图层已删除自动删除设置项
watch(
  () => designData.layers.map(i => i.uuid),
  () => {
    // 遍历设置
    const { value } = event.value
    if (!value) return
    const uuids = value.map(i => i.uuid)
    const newUuids = designData.getLayerByUuids(uuids).map(i => i.uuid)

    // 删除不存在的
    value.forEach((item, index) => {
      if (!newUuids.includes(item.uuid)) {
        value.splice(index, 1)
      }
    })
  },
  { immediate: true },
)

// 列出designState.layerData中所有开头为display的列表
const displayLayerList = computed(() => {
  const keyList = Object.keys(designState.layerData).filter(i => i.startsWith('$') && i.endsWith('$'))
  if (!keyList.length) {
    return []
  }
  const list: { label: string, value: string }[] = [
    { label: '无', value: '' },
  ]
  for (const key of keyList) {
    list.push({ label: key.slice(1, -1), value: key })
  }
  return list
})

const displayBind = useDataAttr(layer.value as IDesignLayer, 'display', '')

onMounted(() => {
  console.log(designState)
})
</script>

<template>
  <ul v-if="layer" class="box">
    <li v-if="displayLayerList.length" class="item">
      <p class="my-6">逻辑中控制显示/隐藏</p>
      <el-select v-model="displayBind" placeholder="请选择逻辑">
        <el-option v-for="item of displayLayerList" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </li>
    <li class="item">
      <p class="my-6">点击当前图层，显示/隐藏以下图层</p>
      <el-select
        v-model="selectUuids"
        multiple
        collapse-tags
        collapse-tags-tooltip
        placeholder="请选择图层"
      >
        <el-option
          v-for="item in controlLayerList"
          :key="item.uuid"
          :label="item.name"
          :value="item.uuid"
        >
          <span :style="{ 'padding-left': `${item.depth * 16}px` }">{{ item.name }}</span>
        </el-option>
      </el-select>
      <div v-for="item in event.value" :key="item.uuid" class="item-box">
        <span>{{ controlLayerList.find(i => i.uuid === item.uuid)?.name }}</span>
        <el-select v-model="item.action" class="w-60">
          <el-option label="自动" value="auto" />
          <el-option label="显示" value="show" />
          <el-option label="隐藏" value="hide" />
        </el-select>
      </div>
    </li>
  </ul>
</template>

<style scoped lang="scss">
.box {
  .item {
    border-radius: 6px;
    padding: 10px;
    background-color: #fff;
    &:not(:first-child) {
      margin-top: 10px;
    }
    p {
      font-size: 14px;
      color: #666;
    }
  }
}
.item-box {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 10px;
  padding: 0 10px;

  span {
    flex: 1;
    white-space: nowrap;
  }
}
</style>
