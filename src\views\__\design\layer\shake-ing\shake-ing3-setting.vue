<script setup lang="ts">
import { getDefaultMaterial, openSelectMaterial, useDataAttr } from '../..'
import MaterialThumbnail from '../../components/material-thumbnail.vue'
import { defaultBorderColor, defaultColor, defaultCount, type IDesignShakeIng3 } from './shake-ing3'

const layer = defineModel<IDesignShakeIng3>('layer', { required: true })

type IType = 'trackBottom' | 'track'
async function updateMaterialFn(name: IType, index?: number, isReset: boolean = false) {
  const key = name === 'track' && index !== undefined ? `${name}.${index}` : name
  const result = isReset
    ? await getDefaultMaterial(layer.value, key)
    : await openSelectMaterial('PIC')
  if (result || (isReset && index === undefined)) {
    if (name === 'track') {
      const arr = layer.value[name] as string[]
      if (index !== undefined) {
        arr[index] = result
      }
    } else {
      layer.value[name] = result
    }
  }
}

function remove(name: IType, index: number) {
  const arr = layer.value[name] as string[]
  arr.splice(index, 1)
}
function add(name: IType, index: number, defaultValue: string) {
  const arr = layer.value[name] as string[]
  arr.splice(index + 1, 0, defaultValue)
}

const countBind = useDataAttr(layer.value, 'count', defaultCount)
const colorBind = useDataAttr(layer.value.style, 'color', defaultColor)
const borderColorBind = useDataAttr(layer.value.style, 'borderColor', defaultBorderColor)
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>数量</h3>
        <el-input-number v-model="countBind" v-input-number :min="0" controls-position="right" class="mt-5 w-80" />
      </div>
      <div class="setting-item">
        <h3>字体颜色</h3>
        <hi-color v-model="colorBind" />
      </div>
      <div class="setting-item">
        <h3>边框颜色</h3>
        <hi-color v-model="borderColorBind" />
      </div>
      <div class="setting-item flex-items-start!">
        <h3>轨道</h3>
        <div>
          <div
            v-for="(item, index) in layer.track"
            :key="index"
            class="relative mb-10 h-60 w-120 flex"
          >
            <MaterialThumbnail @select="updateMaterialFn('track', index)" @reset="updateMaterialFn('track', index, true)">
              <img :src="item" class="bgblank w-90 object-contain">
            </MaterialThumbnail>
            <div class="ml-6 w-20 pt-10">
              <icon-ph:minus-bold v-if="layer.track.length > 1" @click.stop="remove('track', index)" />
              <icon-ph:plus-bold @click.stop="add('track', index, item)" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="setting-item">
      <h3>轨道底部</h3>
      <div class="thumbnail-box bgblank group relative">
        <div class="bgblank relative h-50 w-64">
          <MaterialThumbnail @select="updateMaterialFn('trackBottom')" @reset="updateMaterialFn('trackBottom', undefined, true)">
            <img v-if="layer.trackBottom" :src="layer.trackBottom">
          </MaterialThumbnail>
        </div>
        <div class="absolute bottom-0 left-0 right-0 top-0 hidden items-center justify-around rd-2 bg-[rgba(0,0,0,0.6)] text-20px text-white font-bold group-hover:flex">
          <icon-ph:upload-simple-fill class="cursor-pointer" />
          <icon-ph:arrows-counter-clockwise-light class="cursor-pointer" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
