import type { ModelRef } from 'vue'
import type { IDesignShape } from './shape'
import DOMPurify from 'dompurify'
import { cloneDeep } from 'lodash-es'

export function useShapeUtils(layer: ModelRef<IDesignShape, string>) {
  const isLoading = ref(false)
  const svgData = ref<string>('')

  const getSvgDomColor = (ele: SVGAElement) => {
    let fill = ele.getAttribute('fill') as string
    if (!fill || fill === 'none') {
      fill = ele.style.fill
    }
    if (!fill || fill === 'none') {
      fill = ele.getAttribute('stop-color') as string
    }
    if (fill.includes('url(')) return
    return fill
  }

  const svgColors = computed(() => {
    const sColors: string[] = []

    const data = svgData.value
    if (!data) return sColors
    const svgDoc = new DOMParser().parseFromString(data, 'image/svg+xml')

    // 查找所有含有fill属性的元素
    const fillElements = svgDoc.querySelectorAll('[fill], [style*="fill:"], [stop-color]')
    for (let i = 0; i < fillElements.length; i++) {
      const fillElement = fillElements[i] as SVGAElement
      const fill = getSvgDomColor(fillElement)
      if (!fill) continue
      if (!sColors.includes(fill)) {
        sColors.push(fill)
      }
    }

    // 解析 style 中的 fill
    const styleElements = svgDoc.querySelectorAll('style')
    for (let i = 0; i < styleElements.length; i++) {
      const styleElement = styleElements[i] as HTMLStyleElement
      const style = styleElement.innerHTML
      style.match(/fill:(.*);/g)?.forEach((item) => {
        const fill = item.split(/:|;/)[1]
        if (!sColors.includes(fill)) {
          sColors.push(fill)
        }
      })
    }

    return sColors
  })

  const renderSvg = computed(() => {
    const data = svgData.value
    if (!data) return

    const svgDoc = new DOMParser().parseFromString(data, 'image/svg+xml')

    const svg = svgDoc.querySelector('svg')
    if (svg) {
      // 设置属性
      svg.setAttribute('width', '100%')
      svg.setAttribute('height', '100%')
      svg.setAttribute('preserveAspectRatio', 'none')
    }

    // 替换颜色
    const fillElements = svgDoc.querySelectorAll('[fill], [style*="fill:"], [stop-color]')

    const colors = cloneDeep(layer.value.colors || [])
    svgColors.value.forEach((item, index) => {
      if (!colors[index]) {
        colors[index] = item
      }
    })

    for (let i = 0; i < fillElements.length; i++) {
      const fillElement = fillElements[i] as SVGAElement
      const fill = getSvgDomColor(fillElement)
      if (!fill) continue
      const index = svgColors.value.indexOf(fill)
      fillElement.setAttribute('style', `fill: ${colors[index]}`)
    }

    // 替换style中的颜色
    const styleElements = svgDoc.querySelectorAll('style')
    for (let i = 0; i < styleElements.length; i++) {
      const styleElement = styleElements[i] as HTMLStyleElement
      let style = styleElement.innerHTML
      style.match(/fill:(.*);/g)?.forEach((item) => {
        const fill = item.split(/:|;/)[1]
        if (fill) {
          const index = svgColors.value.indexOf(fill)
          style = style.replace(/fill:(.*);/g, `fill: ${colors[index]}}`)
        }
      })
      styleElement.innerHTML = style
    }

    const serializer = new XMLSerializer()
    const svgHtml = serializer.serializeToString(svgDoc)
    return svgHtml
  })

  const colors = ref<string[]>([])
  watch(
    svgColors,
    (v = [], o = []) => {
      if (v.join(',') === o.join(',')) return

      const arr: string[] = []
      const _colors = layer.value.colors || []
      for (let i = 0; i < svgColors.value.length; i++) {
        arr.push(_colors[i] || svgColors.value[i] || '#000')
      }
      colors.value = arr
    },
    { immediate: true, deep: true },
  )
  watch(
    colors,
    () => {
      if (!colors.value) return
      if (colors.value.join(',') !== layer.value.colors?.join(',')) {
        layer.value.colors = colors.value
      }
    },
    { deep: true },
  )

  const svgUrl = layer.value.data
  if (!isLoading.value && svgUrl) {
    isLoading.value = true
    fetch(svgUrl)
      .then(res => res.text())
      .then((data) => {
        svgData.value = DOMPurify.sanitize(data, {})
      })
      .finally(() => {
        isLoading.value = false
      })
  }

  const reset = () => {
    layer.value.colors = []
  }
  return {
    svgColors,
    renderSvg,
    colors,
    reset,
  }
}
