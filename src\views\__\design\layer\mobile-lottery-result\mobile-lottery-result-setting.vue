<script setup lang="ts">
import { useDataAttr } from '../..'
import { DEFAULT_DATA, type IDesignMobileLotteryResult } from './mobile-lottery-result'

const layer = defineModel<IDesignMobileLotteryResult>('layer', { required: true })

const backgroundColorBind = useDataAttr(layer.value.data, 'backgroundColor', DEFAULT_DATA.backgroundColor)
</script>

<template>
  <div class="setting-block">
    <div class="setting-wrap">
      <div class="setting-item">
        <h3>按钮颜色</h3>
        <hi-color v-model="backgroundColorBind" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
