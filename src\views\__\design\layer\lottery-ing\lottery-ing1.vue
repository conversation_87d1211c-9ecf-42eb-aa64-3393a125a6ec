<script setup lang="ts">
import type { IDesignLotteryIng1 } from './lottery-ing1'
import useThree from '@/views/__/three/useThree'
import BezierEasing from 'bezier-easing'
import gsap from 'gsap'
import { sample } from 'lodash-es'
import * as THREE from 'three'
import { useDesignState } from '../..'
import { defaultHeadCount, defaultHeadSize, defaultPlaceHolderHeadImg, defaultrotateSpeed } from './lottery-ing1'

const layer = defineModel<IDesignLotteryIng1>('layer', { required: true })
const designState = useDesignState()

// 参与人奖池
const regeditList = computed(() => {
  return designState.getLayerData('regeditList') || []
})

const headCount = computed(() => layer.value.headCount ?? defaultHeadCount)
const headSize = computed(() => layer.value.headSize ?? defaultHeadSize)
const rotateSpeed = computed(() => layer.value.rotateSpeed ?? defaultrotateSpeed)

const threeRef = ref<HTMLElement | null>(null)
const { scene, loadTexture, camera } = useThree({
  threeRef,
  hasHelp: false,
  hasControls: false,
})

const cache: Record<string, any> = {}
// 生成纹理
function createTexture(url: string) {
  let texture = cache[url]
  if (texture) {
    return texture
  }
  texture = loadTexture(url)
  cache[url] = texture
  return texture
}
class BaseShape {
  name = 'BaseShape'
  group: THREE.Group
  intervalHandler: NodeJS.Timeout | null = null
  constructor() {
    this.group = new THREE.Group()
    this.group.visible = false
    scene.add(this.group)
  }

  disposeItem(item: THREE.Object3D<THREE.Object3DEventMap>) {
    if (item instanceof THREE.Mesh || item instanceof THREE.Sprite) {
      item.material?.dispose()
      item.geometry?.dispose()
    } else if (item instanceof THREE.Group) {
      item.children.forEach((child) => {
        this.disposeItem(child)
      })
    }
  }

  init() {
    this.intervalHandler && clearInterval(this.intervalHandler)
    this.group.traverse((item) => {
      this.disposeItem(item)
    })
    this.group.clear()
  }

  play() {
  }

  getEase(value = [0.66, 0.2, 0.45, 0.79] as [number, number, number, number]) {
    return BezierEasing(...value)
  }

  destory() {
    this.intervalHandler && clearInterval(this.intervalHandler)
    this.group.traverse((item) => {
      this.disposeItem(item)
    })
    this.group.clear()
    this.group.visible = false
  }
}

class ShapeGalaxy extends BaseShape {
  constructor() {
    super()
    this.name = 'ShapeGalaxy'
  }

  async init() {
    super.init()
    // 相机
    camera.fov = 80
    camera.position.set(0, 200, 1150)

    camera.updateProjectionMatrix()
    camera.lookAt(scene.position)
    scene.rotation.x = Math.PI / 180 * 15
    scene.rotation.z = Math.PI / 180 * 10
    // 可视区大小
    const count = headCount.value

    const vector = new THREE.Vector3()
    const radius = 600
    const itemPI = (2 * Math.PI) / count
    for (let i = 0; i < count; i++) {
      const x = radius * Math.sin(itemPI * i) + this.wave(400)
      const z = radius * Math.cos(itemPI * i) + this.wave(400)
      const y = 200
      const object = new THREE.Object3D()

      const itemData = getItem()

      if (!itemData) return
      const geometry = new THREE.CircleGeometry(headSize.value, 32)
      const threeSprite = new THREE.Sprite(
        new THREE.SpriteMaterial({
          map: createTexture(itemData.avatar),
          fog: false,
          transparent: true,
        }),
      )
      threeSprite.geometry = geometry
      threeSprite.position.set(x, y, z)
      vector.copy(object.position).multiplyScalar(2)
      this.group.add(threeSprite)
    }
  }

  wave(size: number) {
    return ((Math.random() * size) | 0) - size / 2
  }

  randomThreeObject() {
    const arr = this.group.children.filter((item) => {
      return (item instanceof THREE.Mesh || item instanceof THREE.Sprite) && !item.userData.animateFlag
    })
    return arr[(Math.random() * arr.length) | 0]
  }

  play() {
    gsap.to(this.group.rotation, { y: 2 * Math.PI, repeat: Infinity, duration: 100 / rotateSpeed.value, ease: 'none' })
    this.intervalHandler = setInterval(() => {
      const object = this.randomThreeObject()
      if (!object) return
      object.userData.animateFlag = true
      // 缩小放大进入
      const item = getItem()
      gsap.to(object.scale, { x: 0, y: 0, z: 0, duration: 0.2, ease: 'power2.out', onComplete: () => {
        (object as THREE.Sprite).material.map = createTexture(item.avatar)
        gsap.to(object.scale, { x: 1, y: 1, z: 1, duration: 0.5, ease: 'power2.in', onComplete: () => {
          object.userData.animateFlag = false
        } })
      } })
    }, 60)
  }
}

const shapeObj = ref<BaseShape | null>(null)
async function runAnimit() {
  shapeObj.value = new ShapeGalaxy()
  shapeObj.value.group.visible = true
  shapeObj.value.init()
  shapeObj.value.play()
}
const defaultHeadImg = computed(() => {
  return layer.value.placeHolderHeadImg ?? defaultPlaceHolderHeadImg
})
function getItem() {
  if (regeditList.value.length === 0) {
    return {
      avatar: defaultHeadImg.value,
    }
  }
  const tem = sample(regeditList.value)
  const itemData = Object.assign({}, tem)
  return itemData
}

watch(
  () => [headCount.value, headSize.value, rotateSpeed.value],
  () => {
    shapeObj.value?.destory()
    runAnimit()
  },
  { deep: true, immediate: true },
)

onUnmounted(() => {
  shapeObj.value?.destory()
})
</script>

<template>
  <div class="lottery-ing1-box">
    <div ref="threeRef" class="three-box"></div>
  </div>
</template>

<style scoped lang="scss">
.lottery-ing1-box {
  width: 100%;
  height: 100%;
  .three-box {
    width: 100%;
    height: 100%;
  }
}
</style>
